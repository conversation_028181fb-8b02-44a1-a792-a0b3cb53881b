import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { 
  CreditCard, 
  Users, 
  TrendingUp, 
  AlertCircle, 
  DollarSign, 
  Calendar,
  ArrowRight,
  Settings,
  Calculator
} from 'lucide-react';


// Simple widget card component
const StatCard: React.FC<{
  title: string;
  value: string | number;
  icon?: React.ReactNode;
  colorClass?: string;
  trend?: { value: number; isPositive: boolean };
  onClick?: () => void;
}> = ({ title, value, icon, colorClass = "bg-white", trend, onClick }) => (
  <div 
    className={`rounded-lg shadow ${colorClass} p-4 sm:p-6 ${onClick ? 'cursor-pointer hover:shadow-md transition-shadow' : ''}`}
    onClick={onClick}
  >
    <div className="flex items-center justify-between">
      <div>
        <p className="text-sm font-medium text-gray-500">{title}</p>
        <p className="text-2xl font-semibold">{value}</p>
        {trend && (
          <div className={`flex items-center mt-1 text-xs sm:text-sm ${trend.isPositive ? 'text-green-600' : 'text-red-600'}`}>
            <TrendingUp className={`w-4 h-4 mr-1 ${trend.isPositive ? '' : 'transform rotate-180'}`} />
            <span>{trend.isPositive ? '+' : '-'}{Math.abs(trend.value)}% this month</span>
          </div>
        )}
      </div>
      {icon && <div className="text-primary">{icon}</div>}
    </div>
  </div>
);

// Quick Action Card component
const QuickActionCard: React.FC<{
  title: string;
  description: string;
  icon: React.ReactNode;
  href: string;
  color: string;
}> = ({ title, description, icon, href, color }) => (
  <Link
    to={href}
    className="block p-4 bg-white rounded-lg shadow hover:shadow-md transition-shadow border border-gray-200"
  >
    <div className="flex items-center sm:items-start gap-4">
      <div className={`p-3 rounded-lg ${color} hidden sm:block`}>
        {icon}
      </div>
      <div className="flex-1">
        <div className="flex items-center gap-2 sm:gap-0">
          <div className={`p-2 rounded-lg ${color} sm:hidden`}>
            {icon}
          </div>
          <h3 className="font-semibold text-gray-800">{title}</h3>
        </div>
        <p className="text-sm text-gray-600 mt-2 hidden sm:block">{description}</p>
        <div className="flex items-center text-sm text-primary font-medium mt-2">
          <span>Open</span>
          <ArrowRight className="w-4 h-4 ml-1" />
        </div>
      </div>
    </div>
  </Link>
);

const Dashboard: React.FC = () => {
  const { user, profile, logout, isLoggingOut } = useAuth();
  const [emiStats, setEmiStats] = useState({
    activeEMIs: 145,
    totalRevenue: 2850000,
    overduePayments: 8,
    familyTypes: 12
  });
  
  const handleLogout = async () => {
    try {
      await logout();
    } catch (err) {
      console.error('Logout error:', err);
    }
  };

  const formatCurrency = (amount: number) => {
    return `₹${(amount / 100000).toFixed(1)}L`;
  };

  useEffect(() => {
    // Load EMI stats
    // This would be replaced with actual API calls
    const loadEMIStats = async () => {
      // Mock data loading
      setEmiStats({
        activeEMIs: 145,
        totalRevenue: 2850000,
        overduePayments: 8,
        familyTypes: 12
      });
    };

    loadEMIStats();
  }, []);

  return (
    <div className="p-4 sm:p-6 lg:p-8">
      {/* Welcome section */}
      <div className="bg-gradient-to-r from-primary to-blue-500 text-white p-4 sm:p-6 rounded-lg mb-8">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center">
          <div>
            <h1 className="text-xl sm:text-2xl font-bold">
              Welcome, {profile?.full_name ?? user?.email ?? 'User'}
            </h1>
            <p className="text-blue-100 mt-1 text-sm sm:text-base">Here's what's happening with your travel leads and EMI plans today.</p>
          </div>
          <button
            onClick={handleLogout}
            disabled={isLoggingOut}
            className={`mt-4 sm:mt-0 px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 focus:outline-none ${
              isLoggingOut ? 'opacity-50 cursor-not-allowed' : ''
            }`}
          >
            {isLoggingOut ? 'Logging out…' : 'Log Out'}
          </button>
        </div>
      </div>

      {/* Stats grid */}
      <div className="grid grid-cols-2 md:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 mb-8">
        <StatCard 
          title="Active Leads" 
          value="24" 
          colorClass="bg-white border-l-4 border-primary"
          trend={{ value: 8.2, isPositive: true }}
        />
        <StatCard 
          title="Quotes Sent" 
          value="18" 
          colorClass="bg-white border-l-4 border-blue-500"
          trend={{ value: 12.5, isPositive: true }}
        />
        <StatCard 
          title="Confirmed Bookings" 
          value="7" 
          colorClass="bg-white border-l-4 border-green-500"
          trend={{ value: 15.3, isPositive: true }}
        />
        <StatCard 
          title="Pending Follow-ups" 
          value="12" 
          colorClass="bg-white border-l-4 border-yellow-500"
          trend={{ value: 5.1, isPositive: false }}
        />
      </div>

      {/* EMI Management Stats */}
      <div className="mb-8">
        <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-4">
          <h2 className="text-lg sm:text-xl font-semibold text-gray-800">EMI Management Overview</h2>
          <Link 
            to="/emi/dashboard" 
            className="text-primary hover:text-primary-dark font-medium text-sm flex items-center gap-1"
          >
            View Details <ArrowRight className="w-4 h-4" />
          </Link>
        </div>
        
        <div className="grid grid-cols-2 md:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6">
          <StatCard 
            title="Active EMIs" 
            value={emiStats.activeEMIs}
            icon={<CreditCard className="w-8 h-8" />}
            colorClass="bg-blue-50 border-l-4 border-blue-500"
            trend={{ value: 12.5, isPositive: true }}
            onClick={() => window.location.href = '/emi/dashboard'}
          />
          <StatCard 
            title="Total Revenue" 
            value={formatCurrency(emiStats.totalRevenue)}
            icon={<DollarSign className="w-8 h-8" />}
            colorClass="bg-green-50 border-l-4 border-green-500"
            trend={{ value: 18.3, isPositive: true }}
          />
          <StatCard 
            title="Overdue Payments" 
            value={emiStats.overduePayments}
            icon={<AlertCircle className="w-8 h-8" />}
            colorClass="bg-red-50 border-l-4 border-red-500"
            trend={{ value: 3.2, isPositive: false }}
            onClick={() => window.location.href = '/emi/transactions'}
          />
          <StatCard 
            title="Family Types" 
            value={emiStats.familyTypes}
            icon={<Users className="w-8 h-8" />}
            colorClass="bg-purple-50 border-l-4 border-purple-500"
            onClick={() => window.location.href = '/family-types'}
          />
        </div>
      </div>

      {/* Quick Actions */}
      <div className="mb-8">
        <h2 className="text-lg sm:text-xl font-semibold text-gray-800 mb-4">Quick Actions</h2>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
          <QuickActionCard
            title="EMI Dashboard"
            description="Monitor EMI payments, overdue accounts, and revenue tracking"
            icon={<Settings className="w-6 h-6 text-white" />}
            href="/emi/dashboard"
            color="bg-blue-500"
          />
          
          <QuickActionCard
            title="Family Type Management"
            description="Configure family types, pricing, and travel configurations"
            icon={<Users className="w-6 h-6 text-white" />}
            href="/family-types"
            color="bg-purple-500"
          />
          
          <QuickActionCard
            title="Prepaid EMI Calculator"
            description="Calculate EMI plans with advance payments and discounts"
            icon={<Calculator className="w-6 h-6 text-white" />}
            href="/emi/calculator"
            color="bg-green-500"
          />
          
          <QuickActionCard
            title="Payment Transactions"
            description="View all EMI transactions, payments, and outstanding amounts"
            icon={<CreditCard className="w-6 h-6 text-white" />}
            href="/emi/transactions"
            color="bg-orange-500"
          />
          
          <QuickActionCard
            title="New Quote Generator"
            description="Generate quotes with EMI options and family type pricing"
            icon={<TrendingUp className="w-6 h-6 text-white" />}
            href="/quotes/new"
            color="bg-indigo-500"
          />
          
          <QuickActionCard
            title="Lead Management"
            description="Manage leads, follow-ups, and convert to EMI customers"
            icon={<Calendar className="w-6 h-6 text-white" />}
            href="/leads"
            color="bg-teal-500"
          />
        </div>
      </div>

      {/* Recent activity section */}
      <div className="bg-white rounded-lg shadow overflow-hidden">
        <div className="px-4 sm:px-6 py-4 border-b border-gray-200">
          <h2 className="text-lg font-medium text-gray-800">Recent Activity</h2>
        </div>
        <div className="divide-y divide-gray-200">
          {[1, 2, 3, 4, 5].map((_, index) => (
            <div key={index} className="px-4 sm:px-6 py-4">
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                <div className="mb-2 sm:mb-0">
                  <p className="text-sm font-medium text-gray-800">
                    {["Quote sent to Rahul Sharma", "New lead from Instagram", "Call scheduled with Priya", "Booking confirmed for Maldives trip", "Follow-up reminder for Goa package"][index]}
                  </p>
                  <p className="text-xs text-gray-500 mt-1">
                    {["10 minutes ago", "1 hour ago", "3 hours ago", "Yesterday", "2 days ago"][index]}
                  </p>
                </div>
                <span className={`self-start sm:self-auto px-2 py-1 text-xs rounded-full ${
                  ["bg-blue-100 text-blue-800", "bg-green-100 text-green-800", "bg-yellow-100 text-yellow-800", "bg-purple-100 text-purple-800", "bg-gray-100 text-gray-800"][index]
                }`}>
                  {["Quote", "New Lead", "Call", "Booking", "Follow-up"][index]}
                </span>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
