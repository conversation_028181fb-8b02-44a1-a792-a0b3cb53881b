import React, { useState, useEffect, useRef } from 'react';

const PACKAGE_NAMES = [
  "Manali Himalayan Escape",
  "Goa Beach Getaway",
  "Kashmir Winter Wonderland",
  "Andaman Island Explorer",
  "Jaipur Royal Retreat",
  "Kerala Backwater Bliss",
  "Leh-Ladakh Adventure Trail",
  "Ooty & Kodaikanal Hills Tour",
  "Rishikesh Spiritual Escape",
  "Coorg Nature Retreat"
];

interface PackageAutocompleteProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
}

export const PackageAutocomplete: React.FC<PackageAutocompleteProps> = ({
  value,
  onChange,
  placeholder = "Enter package name"
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [suggestions, setSuggestions] = useState<string[]>([]);
  const wrapperRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (wrapperRef.current && !wrapperRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleInputChange = (inputValue: string) => {
    onChange(inputValue);
    if (inputValue.trim() === '') {
      setSuggestions(PACKAGE_NAMES);
    } else {
      const filtered = PACKAGE_NAMES.filter(name =>
        name.toLowerCase().includes(inputValue.toLowerCase())
      );
      setSuggestions(filtered);
    }
    setIsOpen(true);
  };

  const handleFocus = () => {
    setSuggestions(PACKAGE_NAMES);
    setIsOpen(true);
  };

  return (
    <div ref={wrapperRef} className="relative">
      <input
        type="text"
        value={value}
        onChange={(e) => handleInputChange(e.target.value)}
        onFocus={handleFocus}
        placeholder={placeholder}
        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
      />
      {isOpen && (
        <ul className="absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-auto">
          {suggestions.map((suggestion, index) => (
            <li
              key={index}
              onClick={() => {
                onChange(suggestion);
                setIsOpen(false);
              }}
              className="px-4 py-2 hover:bg-blue-50 cursor-pointer"
            >
              {suggestion}
            </li>
          ))}
        </ul>
      )}
    </div>
  );
};
