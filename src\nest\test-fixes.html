<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Database Fixes Verification - TripXplo CRM</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .warning { background-color: #fff3cd; border-color: #ffeaa7; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #0056b3; }
        .log-output {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
        }
        .status { font-weight: bold; margin: 10px 0; }
        .fix-item { 
            margin: 10px 0; 
            padding: 10px; 
            background: #f8f9fa; 
            border-left: 4px solid #007bff; 
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Database Fixes Verification</h1>
        <p>This page verifies that all database errors have been resolved in the TripXplo CRM system.</p>
        
        <div class="test-section info">
            <h3>🛠️ Fixes Applied</h3>
            <div class="fix-item">
                <strong>✅ HTTP 400 Error Fix:</strong> Added UUID validation and composition-based family type matching
            </div>
            <div class="fix-item">
                <strong>✅ HTTP 406 Error Fix:</strong> Separated package details and EMI plans queries
            </div>
            <div class="fix-item">
                <strong>✅ Family Type Mismatch Fix:</strong> Added intelligent field mapping between CRM and Quote databases
            </div>
            <div class="fix-item">
                <strong>✅ Enhanced Error Logging:</strong> Added detailed error information for debugging
            </div>
            <div class="fix-item">
                <strong>✅ Fallback Mechanisms:</strong> Added multiple fallback strategies for robust operation
            </div>
        </div>
        
        <div class="test-section info">
            <h3>🧪 Test Controls</h3>
            <button onclick="runAllTests()">Run All Tests</button>
            <button onclick="testSpecificScenarios()">Test Error Scenarios</button>
            <button onclick="testPackageSearch()">Test Package Search (Goa)</button>
            <button onclick="testPackageDetails()">Test Package Details</button>
            <button onclick="clearLogs()">Clear Logs</button>
        </div>
        
        <div class="test-section">
            <h3>📊 Test Status</h3>
            <div id="status" class="status">Ready to test...</div>
        </div>
        
        <div class="test-section">
            <h3>📋 Test Results</h3>
            <div id="test-results" class="log-output">
                Test results will appear here...
            </div>
        </div>
        
        <div class="test-section">
            <h3>🔍 Console Output</h3>
            <div id="console-output" class="log-output">
                Console logs will appear here...
            </div>
        </div>
    </div>

    <!-- Include required scripts -->
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
    <script src="js/config.js"></script>
    <script src="js/databaseService.js"></script>

    <script>
        // Initialize database service
        const dbService = new DatabaseService();
        
        // Capture console logs
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;
        
        function logToPage(message, type = 'log') {
            const output = document.getElementById('console-output');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${type.toUpperCase()}: ${message}\n`;
            output.textContent += logEntry;
            output.scrollTop = output.scrollHeight;
        }
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            logToPage(args.join(' '), 'log');
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            logToPage(args.join(' '), 'error');
        };
        
        console.warn = function(...args) {
            originalWarn.apply(console, args);
            logToPage(args.join(' '), 'warn');
        };
        
        function updateStatus(message, type = 'info') {
            const status = document.getElementById('status');
            status.textContent = message;
            status.className = `status ${type}`;
        }
        
        function addTestResult(test, result, details = '') {
            const results = document.getElementById('test-results');
            const timestamp = new Date().toLocaleTimeString();
            const status = result ? '✅ PASS' : '❌ FAIL';
            const resultEntry = `[${timestamp}] ${test}: ${status}\n${details}\n\n`;
            results.textContent += resultEntry;
            results.scrollTop = results.scrollHeight;
        }
        
        async function runAllTests() {
            updateStatus('Running comprehensive tests...', 'info');
            
            try {
                // Test 1: Database connections
                await dbService.testConnections();
                addTestResult('Database Connections', true, 'Both CRM and Quote databases connected successfully');
                
                // Test 2: Family types
                const familyTypesResult = await dbService.getFamilyTypes();
                addTestResult('Family Types Loading', familyTypesResult.success, 
                    familyTypesResult.success ? `Loaded ${familyTypesResult.data.length} family types` : familyTypesResult.error);
                
                // Test 3: Destinations
                const destinationsResult = await dbService.getDestinations();
                addTestResult('Destinations Loading', destinationsResult.success,
                    destinationsResult.success ? `Loaded ${destinationsResult.data.length} destinations` : destinationsResult.error);
                
                // Test 4: Package search with different family compositions
                const searchTests = [
                    { destination: 'Goa', adults: 2, children: 1, infants: 0 },
                    { destination: 'Kerala', adults: 2, children: 0, infants: 0 },
                    { destination: 'Rajasthan', adults: 4, children: 2, infants: 1 }
                ];
                
                for (const searchParams of searchTests) {
                    const searchResult = await dbService.searchPackages(searchParams);
                    addTestResult(`Package Search (${searchParams.destination})`, searchResult.success,
                        searchResult.success ? `Found ${searchResult.packages.length} packages` : searchResult.error);
                }
                
                updateStatus('All tests completed successfully!', 'success');
                
            } catch (error) {
                addTestResult('Test Suite', false, `Error: ${error.message}`);
                updateStatus('Test suite failed', 'error');
            }
        }
        
        async function testSpecificScenarios() {
            updateStatus('Testing specific error scenarios...', 'info');
            
            try {
                // Test family type detection with edge cases
                const edgeCases = [
                    { adults: 1, children: 0, infants: 0 },
                    { adults: 6, children: 4, infants: 2 },
                    { adults: 2, children: 3, infants: 1 }
                ];
                
                for (const testCase of edgeCases) {
                    const familyType = await dbService.detectFamilyType(testCase.adults, testCase.children, testCase.infants);
                    addTestResult(`Family Type Detection (${testCase.adults}A ${testCase.children}C ${testCase.infants}I)`, 
                        !!familyType, familyType ? `Matched: ${familyType.family_type}` : 'No match found');
                }
                
                updateStatus('Error scenario tests completed', 'success');
                
            } catch (error) {
                addTestResult('Error Scenarios', false, `Error: ${error.message}`);
                updateStatus('Error scenario tests failed', 'error');
            }
        }
        
        async function testPackageSearch() {
            updateStatus('Testing package search for Goa...', 'info');
            
            try {
                const searchParams = {
                    destination: 'Goa',
                    adults: 2,
                    children: 1,
                    infants: 0
                };
                
                const result = await dbService.searchPackages(searchParams);
                
                if (result.success) {
                    addTestResult('Goa Package Search', true, 
                        `Found ${result.packages.length} packages\nFamily Type: ${result.matched_family_type?.family_type || 'Custom'}`);
                    
                    // Test package details if packages found
                    if (result.packages.length > 0) {
                        const firstPackage = result.packages[0];
                        const detailsResult = await dbService.getPackageDetails(firstPackage.id);
                        addTestResult('Package Details', detailsResult.success,
                            detailsResult.success ? `Loaded details for: ${detailsResult.package.title}` : detailsResult.error);
                    }
                    
                    updateStatus('Package search completed successfully', 'success');
                } else {
                    addTestResult('Goa Package Search', false, result.error);
                    updateStatus('Package search failed', 'error');
                }
                
            } catch (error) {
                addTestResult('Package Search', false, `Error: ${error.message}`);
                updateStatus('Package search failed', 'error');
            }
        }
        
        async function testPackageDetails() {
            updateStatus('Testing package details functionality...', 'info');
            
            try {
                // First get some packages
                const searchResult = await dbService.searchPackages({
                    destination: 'Goa',
                    adults: 2,
                    children: 0,
                    infants: 0
                });
                
                if (searchResult.success && searchResult.packages.length > 0) {
                    const packageId = searchResult.packages[0].id;
                    const detailsResult = await dbService.getPackageDetails(packageId);
                    
                    addTestResult('Package Details Test', detailsResult.success,
                        detailsResult.success ? 
                        `Successfully loaded package details\nTitle: ${detailsResult.package.title}\nEMI Plans: ${detailsResult.package.emi_options?.length || 0}` :
                        detailsResult.error);
                    
                    updateStatus('Package details test completed', 'success');
                } else {
                    addTestResult('Package Details Test', false, 'No packages found to test details');
                    updateStatus('Package details test skipped', 'warning');
                }
                
            } catch (error) {
                addTestResult('Package Details Test', false, `Error: ${error.message}`);
                updateStatus('Package details test failed', 'error');
            }
        }
        
        function clearLogs() {
            document.getElementById('console-output').textContent = 'Console logs will appear here...';
            document.getElementById('test-results').textContent = 'Test results will appear here...';
            updateStatus('Logs cleared', 'info');
        }
        
        // Auto-run basic tests on page load
        window.addEventListener('load', () => {
            setTimeout(() => {
                console.log('🚀 Starting automatic verification tests...');
                runAllTests();
            }, 1000);
        });
    </script>
</body>
</html>
