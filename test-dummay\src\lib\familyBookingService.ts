import { supabase } from './supabaseClient';

export interface Destination {
  id: string;
  destination: string;
  category: string;
  packages_available?: number;
}

export interface FamilyType {
  family_id: string;
  family_type: string;
  composition: string;
  no_of_adults: number;
  no_of_children: number;
  no_of_infants: number;
}

export interface SearchParams {
  destination: string;
  travel_date: string;
  adults: number;
  children: number;
  infants: number;
}

export interface PackageResult {
  id: string;
  title: string;
  destination: string;
  duration_days: number;
  total_price: number;
  emi_options: EMIOption[];
  images: string[];
  inclusions: string[];
  description: string;
}

export interface EMIOption {
  id: string;
  months: number;
  monthly_amount: number;
  total_amount: number;
  processing_fee: number;
  is_featured: boolean;
  label?: string;
}

export interface SearchResponse {
  success: boolean;
  packages: PackageResult[];
  matched_family_type: FamilyType | null;
  search_params: SearchParams;
  message?: string;
}

class FamilyBookingService {
  /**
   * Load destinations from the database
   */
  async getDestinations(): Promise<{ success: boolean; data: Destination[]; error?: string }> {
    try {
      const { data, error } = await supabase
        .from('quote_mappings')
        .select('destination, category')
        .not('destination', 'is', null);

      if (error) {
        console.error('Error loading destinations:', error);
        return { success: false, data: [], error: error.message };
      }

      // Remove duplicates and format
      const uniqueDestinations = data?.reduce((acc: Destination[], curr) => {
        if (!acc.find(item => item.destination === curr.destination)) {
          acc.push({
            id: curr.destination.toLowerCase().replace(/\s+/g, '-'),
            destination: curr.destination,
            category: curr.category || 'General'
          });
        }
        return acc;
      }, []) || [];

      return { success: true, data: uniqueDestinations };
    } catch (error: any) {
      console.error('Error in getDestinations:', error);
      return { success: false, data: [], error: error.message };
    }
  }

  /**
   * Load family types from the database
   */
  async getFamilyTypes(): Promise<{ success: boolean; data: FamilyType[]; error?: string }> {
    try {
      const { data, error } = await supabase
        .from('family_types')
        .select('*')
        .order('family_type', { ascending: true });

      if (error) {
        console.error('Error loading family types:', error);
        return { success: false, data: [], error: error.message };
      }

      return { success: true, data: data || [] };
    } catch (error: any) {
      console.error('Error in getFamilyTypes:', error);
      return { success: false, data: [], error: error.message };
    }
  }

  /**
   * Find matching family type based on traveler counts
   */
  findMatchingFamilyType(
    familyTypes: FamilyType[],
    adults: number,
    children: number,
    infants: number
  ): FamilyType | null {
    if (familyTypes.length === 0) return null;

    // Find exact match first
    let match = familyTypes.find(ft =>
      ft.no_of_adults === adults &&
      ft.no_of_children === children &&
      ft.no_of_infants === infants
    );

    // If no exact match, find closest match
    if (!match) {
      match = familyTypes.find(ft =>
        ft.no_of_adults === adults &&
        ft.no_of_children >= children &&
        ft.no_of_infants >= infants
      );
    }

    // Default to first family type if no match
    return match || familyTypes[0] || null;
  }

  /**
   * Search for packages based on criteria
   */
  async searchPackages(searchParams: SearchParams): Promise<SearchResponse> {
    try {
      const { destination, travel_date, adults, children, infants } = searchParams;

      // First, get family types to find matching family type
      const familyTypesResponse = await this.getFamilyTypes();
      const matchedFamilyType = familyTypesResponse.success 
        ? this.findMatchingFamilyType(familyTypesResponse.data, adults, children, infants)
        : null;

      // Search for packages in family_type_prices table
      let query = supabase
        .from('family_type_prices')
        .select(`
          id,
          quote_id,
          family_type_name,
          destination_category,
          package_duration_days,
          total_price,
          hotel_cost,
          vehicle_cost,
          additional_costs,
          discount_amount,
          emi_enabled,
          is_public_visible,
          created_at
        `)
        .eq('emi_enabled', true)
        .eq('is_public_visible', true);

      // Filter by destination if provided
      if (destination) {
        query = query.ilike('destination_category', `%${destination}%`);
      }

      // Filter by family type if matched
      if (matchedFamilyType) {
        query = query.eq('family_type_id', matchedFamilyType.family_id);
      }

      const { data: packagesData, error: packagesError } = await query
        .order('public_display_order', { ascending: true })
        .order('created_at', { ascending: false })
        .limit(10);

      if (packagesError) {
        console.error('Error searching packages:', packagesError);
        return {
          success: false,
          packages: [],
          matched_family_type: matchedFamilyType,
          search_params: searchParams,
          message: packagesError.message
        };
      }

      if (!packagesData || packagesData.length === 0) {
        return {
          success: true,
          packages: [],
          matched_family_type: matchedFamilyType,
          search_params: searchParams,
          message: 'No packages found for your search criteria'
        };
      }

      // Get EMI plans for found packages
      const packageIds = packagesData.map(pkg => pkg.id);
      const { data: emiPlansData } = await supabase
        .from('family_type_emi_plans')
        .select(`
          id,
          family_price_id,
          emi_months,
          monthly_amount,
          total_amount,
          processing_fee,
          is_featured,
          marketing_label
        `)
        .in('family_price_id', packageIds)
        .order('emi_months', { ascending: true });

      // Format packages for response
      const packages: PackageResult[] = packagesData.map(pkg => {
        const emiOptions: EMIOption[] = (emiPlansData || [])
          .filter(plan => plan.family_price_id === pkg.id)
          .map(plan => ({
            id: plan.id,
            months: plan.emi_months,
            monthly_amount: plan.monthly_amount,
            total_amount: plan.total_amount,
            processing_fee: plan.processing_fee,
            is_featured: plan.is_featured,
            label: plan.marketing_label
          }));

        return {
          id: pkg.id,
          title: `${pkg.family_type_name} - ${pkg.destination_category}`,
          destination: pkg.destination_category,
          duration_days: pkg.package_duration_days || 5,
          total_price: pkg.total_price,
          emi_options: emiOptions,
          images: ['img/rectangle-14.png'], // Default image
          inclusions: ['Flights', 'Hotels', 'Meals', 'Sightseeing'],
          description: `Experience the best of ${pkg.destination_category} with our ${pkg.family_type_name} package.`
        };
      });

      return {
        success: true,
        packages,
        matched_family_type: matchedFamilyType,
        search_params: searchParams
      };

    } catch (error: any) {
      console.error('Error in searchPackages:', error);
      return {
        success: false,
        packages: [],
        matched_family_type: null,
        search_params: searchParams,
        message: error.message
      };
    }
  }

  /**
   * Submit a quote request
   */
  async submitQuoteRequest(quoteData: {
    customer_name: string;
    customer_email: string;
    customer_phone: string;
    destination: string;
    travel_date: string;
    adults: number;
    children: number;
    infants: number;
    selected_package_id?: string;
    selected_emi_plan_id?: string;
  }): Promise<{ success: boolean; message: string; quote_id?: string }> {
    try {
      // Insert into leads table (assuming this is where quote requests go)
      const { data, error } = await supabase
        .from('leads')
        .insert([
          {
            name: quoteData.customer_name,
            email: quoteData.customer_email,
            phone: quoteData.customer_phone,
            destination: quoteData.destination,
            travel_date: quoteData.travel_date,
            adults: quoteData.adults,
            children: quoteData.children,
            infants: quoteData.infants,
            package_id: quoteData.selected_package_id,
            emi_plan_id: quoteData.selected_emi_plan_id,
            status: 'new',
            source: 'family_booking_website',
            created_at: new Date().toISOString()
          }
        ])
        .select()
        .single();

      if (error) {
        console.error('Error submitting quote request:', error);
        return { success: false, message: error.message };
      }

      return {
        success: true,
        message: 'Quote request submitted successfully! Our team will contact you soon.',
        quote_id: data?.id
      };

    } catch (error: any) {
      console.error('Error in submitQuoteRequest:', error);
      return { success: false, message: error.message };
    }
  }
}

export const familyBookingService = new FamilyBookingService();
