<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Database Connection Test - TripXplo CRM</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .warning { background-color: #fff3cd; border-color: #ffeaa7; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .log-output {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        .status {
            font-weight: bold;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Database Connection Test</h1>
        <p>This page tests the database connections and queries for the TripXplo CRM system.</p>
        
        <div class="test-section info">
            <h3>Test Controls</h3>
            <button onclick="testDatabaseConnections()">Test Database Connections</button>
            <button onclick="testFamilyTypes()">Test Family Types</button>
            <button onclick="testDestinations()">Test Destinations</button>
            <button onclick="testPackageSearch()">Test Package Search</button>
            <button onclick="clearLogs()">Clear Logs</button>
        </div>
        
        <div class="test-section">
            <h3>Test Status</h3>
            <div id="status" class="status">Ready to test...</div>
        </div>
        
        <div class="test-section">
            <h3>Console Output</h3>
            <div id="console-output" class="log-output">
                Console logs will appear here...
            </div>
        </div>
        
        <div class="test-section">
            <h3>Test Results</h3>
            <div id="test-results" class="log-output">
                Test results will appear here...
            </div>
        </div>
    </div>

    <!-- Include required scripts -->
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
    <script src="js/config.js"></script>
    <script src="js/databaseService.js"></script>

    <script>
        // Initialize database service
        const dbService = new DatabaseService();
        
        // Capture console logs
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;
        
        function logToPage(message, type = 'log') {
            const output = document.getElementById('console-output');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${type.toUpperCase()}: ${message}\n`;
            output.textContent += logEntry;
            output.scrollTop = output.scrollHeight;
        }
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            logToPage(args.join(' '), 'log');
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            logToPage(args.join(' '), 'error');
        };
        
        console.warn = function(...args) {
            originalWarn.apply(console, args);
            logToPage(args.join(' '), 'warn');
        };
        
        function updateStatus(message, type = 'info') {
            const status = document.getElementById('status');
            status.textContent = message;
            status.className = `status ${type}`;
        }
        
        function addTestResult(test, result, details = '') {
            const results = document.getElementById('test-results');
            const timestamp = new Date().toLocaleTimeString();
            const status = result ? '✅ PASS' : '❌ FAIL';
            const resultEntry = `[${timestamp}] ${test}: ${status}\n${details}\n\n`;
            results.textContent += resultEntry;
            results.scrollTop = results.scrollHeight;
        }
        
        async function testDatabaseConnections() {
            updateStatus('Testing database connections...', 'info');
            try {
                await dbService.testConnections();
                addTestResult('Database Connections', true, 'Both CRM and Quote databases connected successfully');
                updateStatus('Database connections tested successfully', 'success');
            } catch (error) {
                addTestResult('Database Connections', false, `Error: ${error.message}`);
                updateStatus('Database connection test failed', 'error');
            }
        }
        
        async function testFamilyTypes() {
            updateStatus('Testing family types...', 'info');
            try {
                const result = await dbService.getFamilyTypes();
                if (result.success) {
                    addTestResult('Family Types', true, `Loaded ${result.data.length} family types`);
                    updateStatus('Family types loaded successfully', 'success');
                } else {
                    addTestResult('Family Types', false, `Error: ${result.error}`);
                    updateStatus('Family types test failed', 'error');
                }
            } catch (error) {
                addTestResult('Family Types', false, `Error: ${error.message}`);
                updateStatus('Family types test failed', 'error');
            }
        }
        
        async function testDestinations() {
            updateStatus('Testing destinations...', 'info');
            try {
                const result = await dbService.getDestinations();
                if (result.success) {
                    addTestResult('Destinations', true, `Loaded ${result.data.length} destinations`);
                    updateStatus('Destinations loaded successfully', 'success');
                } else {
                    addTestResult('Destinations', false, `Error: ${result.error}`);
                    updateStatus('Destinations test failed', 'error');
                }
            } catch (error) {
                addTestResult('Destinations', false, `Error: ${error.message}`);
                updateStatus('Destinations test failed', 'error');
            }
        }
        
        async function testPackageSearch() {
            updateStatus('Testing package search...', 'info');
            try {
                const searchParams = {
                    destination: 'Goa',
                    adults: 2,
                    children: 1,
                    infants: 0
                };
                
                const result = await dbService.searchPackages(searchParams);
                if (result.success) {
                    addTestResult('Package Search', true, `Found ${result.packages.length} packages for Goa`);
                    updateStatus('Package search completed successfully', 'success');
                } else {
                    addTestResult('Package Search', false, `Error: ${result.error}`);
                    updateStatus('Package search failed', 'error');
                }
            } catch (error) {
                addTestResult('Package Search', false, `Error: ${error.message}`);
                updateStatus('Package search failed', 'error');
            }
        }
        
        function clearLogs() {
            document.getElementById('console-output').textContent = 'Console logs will appear here...';
            document.getElementById('test-results').textContent = 'Test results will appear here...';
            updateStatus('Logs cleared', 'info');
        }
        
        // Auto-run connection test on page load
        window.addEventListener('load', () => {
            setTimeout(testDatabaseConnections, 1000);
        });
    </script>
</body>
</html>
