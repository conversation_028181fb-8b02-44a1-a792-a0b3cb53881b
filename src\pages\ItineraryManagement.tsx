import React, { useState, useEffect } from 'react';
import { Plus, Edit, Trash2, Save, X, Search, RotateCcw, MapPin, Calendar } from 'lucide-react';
import {
  fetchItineraryTemplates,
  fetchDestinations,
  createItineraryTemplate,
  updateItineraryTemplate,
  deleteItineraryTemplate,
  ItineraryTemplate
} from '../services/itineraryService';

const NIGHTS_DAYS_OPTIONS = [
  '1N/ 2D', '2N/ 3D', '3N/ 4D', '4N/ 5D', '5N/ 6D', 
  '6N/ 7D', '7N/ 8D', '8N/ 9D', '9N/ 10D', '10N/ 11D', 'Custom'
];

const ItineraryManagement: React.FC = () => {
  const [templates, setTemplates] = useState<ItineraryTemplate[]>([]);
  const [destinations, setDestinations] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [showAddModal, setShowAddModal] = useState(false);
  const [editingTemplate, setEditingTemplate] = useState<ItineraryTemplate | null>(null);
  const [searchTerm, setSearchTerm] = useState('');

  // Form state
  const [formData, setFormData] = useState({
    title: '',
    destination: '',
    customDestination: '',
    nights_days: '',
    customNightsDays: '',
    content: ''
  });

  // Fetch templates and destinations on component mount
  useEffect(() => {
    loadTemplates();
    loadDestinations();
  }, []);

  const loadTemplates = async () => {
    setIsLoading(true);
    try {
      const data = await fetchItineraryTemplates();
      setTemplates(data);
    } catch (error) {
      console.error('Error fetching templates:', error);
      alert('Failed to fetch itinerary templates');
    } finally {
      setIsLoading(false);
    }
  };

  const loadDestinations = async () => {
    try {
      const data = await fetchDestinations();
      setDestinations(data);
    } catch (error) {
      console.error('Error fetching destinations:', error);
      // Error handling is already done in the service
    }
  };

  const resetForm = () => {
    setFormData({
      title: '',
      destination: '',
      customDestination: '',
      nights_days: '',
      customNightsDays: '',
      content: ''
    });
  };

  const handleAdd = () => {
    resetForm();
    setEditingTemplate(null);
    setShowAddModal(true);
  };

  const handleEdit = (template: ItineraryTemplate) => {
    setFormData({
      title: template.title,
      destination: template.destination,
      customDestination: '',
      nights_days: template.nights_days,
      customNightsDays: '',
      content: template.content || ''
    });
    setEditingTemplate(template);
    setShowAddModal(true);
  };

  const handleDelete = async (id: string) => {
    if (!confirm('Are you sure you want to delete this itinerary template?')) {
      return;
    }

    try {
      await deleteItineraryTemplate(id);
      setTemplates(templates.filter(t => t.id !== id));
      alert('Template deleted successfully!');
    } catch (error) {
      console.error('Error deleting template:', error);
      alert('Failed to delete template');
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    const finalDestination = formData.destination === 'Custom'
      ? formData.customDestination
      : formData.destination;

    const finalNightsDays = formData.nights_days === 'Custom'
      ? formData.customNightsDays
      : formData.nights_days;

    if (!formData.title || !finalDestination || !finalNightsDays) {
      alert('Please fill in all required fields');
      return;
    }

    try {
      const templateData = {
        title: formData.title,
        destination: finalDestination,
        nights_days: finalNightsDays,
        content: formData.content
      };

      if (editingTemplate) {
        // Update existing template
        await updateItineraryTemplate(editingTemplate.id, templateData);
        alert('Template updated successfully!');
      } else {
        // Create new template
        await createItineraryTemplate(templateData);
        alert('Template created successfully!');
      }

      setShowAddModal(false);
      resetForm();
      loadTemplates();
    } catch (error) {
      console.error('Error saving template:', error);
      alert('Failed to save template');
    }
  };

  const filteredTemplates = templates.filter(template =>
    (template.title || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
    (template.destination || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
    (template.nights_days || '').toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
      <div className="mb-8 flex flex-col items-center">
        <h1 className="text-3xl font-bold text-[#00B69B] mb-2 text-center">Itinerary Management</h1>
        <p className="text-gray-600 text-center">Manage your itinerary templates</p>
      </div>

      {/* Header Actions */}
      <div className="bg-white rounded-lg shadow p-6 mb-6">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <h2 className="text-xl font-bold text-gray-800">Itinerary Templates</h2>
          <div className="flex items-center gap-2 flex-wrap">
            <button
              onClick={loadTemplates}
              className="px-4 py-2 flex items-center gap-2 bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
            >
              <RotateCcw size={14} />
              Refresh
            </button>
            <button 
              onClick={handleAdd}
              className="px-4 py-2 bg-[#00B69B] text-white rounded-md hover:bg-[#008577] transition-colors flex items-center gap-2"
            >
              <Plus size={16} />
              Add Template
            </button>
          </div>
        </div>

        {/* Search */}
        <div className="mt-4">
          <div className="relative">
            <input
              type="text"
              placeholder="Search by title, destination, or duration..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#00B69B] focus:border-transparent"
            />
            <Search className="absolute left-3 top-2.5 h-5 w-5 text-gray-400" />
          </div>
        </div>
      </div>

      {/* Templates List */}
      <div className="bg-white rounded-lg shadow">
        {isLoading ? (
          <div className="flex justify-center py-16">
            <div className="animate-spin rounded-full h-16 w-16 border-t-2 border-b-2 border-[#00B69B]"></div>
          </div>
        ) : filteredTemplates.length === 0 ? (
          <div className="text-center py-16">
            <div className="text-gray-400 mb-3">
              <Calendar size={48} className="mx-auto" />
            </div>
            <h3 className="text-lg font-medium text-gray-700 mb-2">No Templates Found</h3>
            <p className="text-gray-500 mb-4">
              {searchTerm ? 'No templates match your search criteria.' : 'Create your first itinerary template to get started.'}
            </p>
            {!searchTerm && (
              <button 
                onClick={handleAdd}
                className="px-4 py-2 bg-[#00B69B] text-white rounded-md hover:bg-[#008577] transition-colors"
              >
                Add Template
              </button>
            )}
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Title
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Destination
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Duration
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Created
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredTemplates.map((template, index) => (
                  <tr key={template.id} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">{template.title}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center text-sm text-gray-500">
                        <MapPin size={14} className="mr-1" />
                        {template.destination}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                        {template.nights_days}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {new Date(template.created_at).toLocaleDateString()}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex items-center gap-2">
                        <button
                          onClick={() => handleEdit(template)}
                          className="text-indigo-600 hover:text-indigo-900 p-1 rounded hover:bg-indigo-50"
                        >
                          <Edit size={16} />
                        </button>
                        <button
                          onClick={() => handleDelete(template.id)}
                          className="text-red-600 hover:text-red-900 p-1 rounded hover:bg-red-50"
                        >
                          <Trash2 size={16} />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* Add/Edit Modal */}
      {showAddModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex justify-between items-center mb-6">
                <h3 className="text-lg font-semibold text-gray-800">
                  {editingTemplate ? 'Edit Itinerary Template' : 'Add New Itinerary Template'}
                </h3>
                <button
                  onClick={() => setShowAddModal(false)}
                  className="text-gray-500 hover:text-gray-700 p-1 rounded hover:bg-gray-100"
                >
                  <X size={20} />
                </button>
              </div>

              <form onSubmit={handleSubmit} className="space-y-4">
                {/* Title */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Title *
                  </label>
                  <input
                    type="text"
                    value={formData.title}
                    onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#00B69B] focus:border-transparent"
                    placeholder="Enter itinerary title"
                    required
                  />
                </div>

                {/* Destination */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Destination *
                  </label>
                  <select
                    value={formData.destination}
                    onChange={(e) => setFormData({ ...formData, destination: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#00B69B] focus:border-transparent"
                    required
                  >
                    <option value="">Select Destination</option>
                    {destinations.map((dest) => (
                      <option key={dest} value={dest}>{dest}</option>
                    ))}
                    <option value="Custom">Custom</option>
                  </select>
                </div>

                {/* Custom Destination */}
                {formData.destination === 'Custom' && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Custom Destination *
                    </label>
                    <input
                      type="text"
                      value={formData.customDestination}
                      onChange={(e) => setFormData({ ...formData, customDestination: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#00B69B] focus:border-transparent"
                      placeholder="Enter custom destination"
                      required
                    />
                  </div>
                )}

                {/* Nights/Days */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Nights/Days *
                  </label>
                  <select
                    value={formData.nights_days}
                    onChange={(e) => setFormData({ ...formData, nights_days: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#00B69B] focus:border-transparent"
                    required
                  >
                    <option value="">Select Duration</option>
                    {NIGHTS_DAYS_OPTIONS.map((option) => (
                      <option key={option} value={option}>{option}</option>
                    ))}
                  </select>
                </div>

                {/* Custom Nights/Days */}
                {formData.nights_days === 'Custom' && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Custom Duration *
                    </label>
                    <input
                      type="text"
                      value={formData.customNightsDays}
                      onChange={(e) => setFormData({ ...formData, customNightsDays: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#00B69B] focus:border-transparent"
                      placeholder="e.g., 12N/ 13D"
                      required
                    />
                  </div>
                )}

                {/* Content */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Itinerary Content
                  </label>
                  <textarea
                    value={formData.content}
                    onChange={(e) => setFormData({ ...formData, content: e.target.value })}
                    rows={8}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#00B69B] focus:border-transparent"
                    placeholder="Enter detailed itinerary content..."
                  />
                </div>

                {/* Form Actions */}
                <div className="flex justify-end gap-3 pt-4">
                  <button
                    type="button"
                    onClick={() => setShowAddModal(false)}
                    className="px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    className="px-4 py-2 bg-[#00B69B] text-white rounded-md hover:bg-[#008577] transition-colors flex items-center gap-2"
                  >
                    <Save size={16} />
                    {editingTemplate ? 'Update Template' : 'Create Template'}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ItineraryManagement;
