"use client"

import React from "react"
import { TemplateElement, TravelData } from "../template/types"
import { defaultCurrency } from "../template/constants"

interface DraggableElementProps {
  element: TemplateElement
  travelData: TravelData
  selectedElement: string | null
  isDragging: string | null
  isResizing: string | null
  isExporting: boolean
  currentLayout: any
  onMouseDown: (e: React.MouseEvent, elementId: string) => void
  onTouchStart: (e: React.TouchEvent, elementId: string) => void
  onResizeStart: (e: React.MouseEvent | React.TouchEvent, elementId: string, handle: string) => void
  onClick: (elementId: string) => void
  onDoubleClick?: (elementId: string) => void
}

export function DraggableElement({
  element,
  travelData,
  selectedElement,
  isDragging,
  isExporting,
  currentLayout,
  onMouseDown,
  onTouchStart,
  onResizeStart,
  onClick,
  onDoubleClick,
}: DraggableElementProps) {
  const getElementContent = (element: TemplateElement): string => {
    if (element.field && travelData[element.field]) {
      return travelData[element.field] as string
    }
    return element.content
  }

  const getCurrencySymbol = (): string => {
    return travelData.currency ? travelData.currency : defaultCurrency
  }

  const content = getElementContent(element)
  const isSelected = selectedElement === element.id && !isExporting
  const isDraggingThis = isDragging === element.id

  const scale = Math.floor(currentLayout.displaySize.width * 1.5) / currentLayout.displaySize.width

  const maxWidth = Math.floor(currentLayout.displaySize.width * 1.5)
  const maxHeight = Math.floor(currentLayout.displaySize.height * 1.5)
  const left = Math.max(0, Math.min(element.position.x * scale, maxWidth - element.size.width * scale))
  const top = Math.max(0, Math.min(element.position.y * scale, maxHeight - element.size.height * scale))
  const width = Math.min(element.size.width * scale, maxWidth - left)
  const height = Math.min(element.size.height * scale, maxHeight - top)

  const elementStyle: React.CSSProperties = {
    position: "absolute",
    left,
    top,
    width,
    height,
    fontFamily: element.style.fontFamily,
    fontSize: element.style.fontSize * scale,
    fontWeight: element.style.fontWeight,
    color: element.style.color,
    background: element.style.backgroundColor.startsWith('linear-gradient') 
      ? element.style.backgroundColor 
      : element.style.backgroundColor,
    backgroundColor: element.style.backgroundColor.startsWith('linear-gradient') 
      ? undefined 
      : element.style.backgroundColor,
    padding: element.style.padding * scale,
    borderRadius: element.style.borderRadius * scale,
    textAlign: element.style.textAlign,
    opacity: element.style.opacity,
    cursor: element.locked ? "default" : "move",
    border: isSelected 
      ? `${3 * scale}px solid #3b82f6` 
      : element.style.border 
        ? element.style.border 
        : `1px solid transparent`,
    outline: isSelected ? `${2 * scale}px solid #ffffff` : "none",
    outlineOffset: isSelected ? `${1 * scale}px` : "0",
    zIndex: isSelected ? 1000 : isDraggingThis ? 999 : 1,
    userSelect: "none",
    display: element.type === "image" ? "flex" : "block",
    alignItems: element.type === "image" ? "center" : undefined,
    justifyContent: element.type === "image" ? "center" : undefined,
    lineHeight: (element.style as any).lineHeight || "normal",
    textTransform: (element.style as any).textTransform || "none",
    letterSpacing: (element.style as any).letterSpacing || "normal",
    textShadow: (element.style as any).textShadow || "none",
    boxShadow: isSelected 
      ? `0 0 0 ${2 * scale}px rgba(59, 130, 246, 0.2)` 
      : (element.style as any).boxShadow || "none",
    whiteSpace: element.type === "text" && content.includes("\n") ? "pre-wrap" : "normal",
    overflow: "hidden",
    // Improve touch targets on mobile
    minHeight: scale < 1 ? "44px" : undefined,
    minWidth: scale < 1 ? "44px" : undefined,
    // Add hover effect
    transition: "all 0.2s ease",
  }

  if (element.type === "divider") {
    return (
      <div
        style={elementStyle}
        onMouseDown={(e) => {
          e.stopPropagation()
          onMouseDown(e, element.id)
        }}
        onTouchStart={(e) => {
          e.stopPropagation()
          onTouchStart(e, element.id)
        }}
        onClick={(e) => {
          e.stopPropagation()
          onClick(element.id)
        }}
        onDoubleClick={onDoubleClick ? () => onDoubleClick(element.id) : undefined}
        className={`cursor-pointer template-element ${isSelected ? 'selected' : ''}`}
      />
    )
  }

  return (
    <div
      style={elementStyle}
      onMouseDown={(e) => {
        e.stopPropagation()
        onMouseDown(e, element.id)
      }}
      onTouchStart={(e) => {
        e.stopPropagation()
        onTouchStart(e, element.id)
      }}
      onClick={(e) => {
        e.stopPropagation()
        onClick(element.id)
      }}
      onDoubleClick={onDoubleClick ? () => onDoubleClick(element.id) : undefined}
      className={`touch-manipulation cursor-pointer template-element ${isSelected ? 'selected' : ''}`}
    >
      {element.type === "image" ? (
        <img
          src={content}
          alt="Brand logo"
          style={{
            width: '100%',
            height: '100%',
            objectFit: 'contain',
            borderRadius: element.style.borderRadius * scale,
          }}
        />
      ) : (
        <div className="template-element-content" style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: element.style.textAlign === 'center' ? 'center' : element.style.textAlign === 'right' ? 'flex-end' : 'flex-start',
          textAlign: element.style.textAlign,
          width: '100%',
          height: '100%',
          whiteSpace: 'pre-wrap',
          lineHeight: (element.style as any).lineHeight || "normal",
        }}>
          <div>
            {`${element.type === "price" && !content.match(/^[₹$€£¥A\$C\$]/) ? `${getCurrencySymbol()} ` : ""}${content}`}
          </div>
        </div>
      )}
      {isSelected && !element.locked && (
        <>
          {/* Selection indicator */}
          <div
            className="absolute bg-blue-500 rounded-full border-2 border-white pointer-events-none"
            style={{
              top: -1 * scale,
              right: -1 * scale,
              width: Math.max(8, 3 * scale),
              height: Math.max(8, 3 * scale),
            }}
          />
          
          {/* Resize handles */}
          {/* Corner handles */}
          <div
            className="absolute bg-blue-500 border-2 border-white cursor-nw-resize"
            style={{
              top: -4 * scale,
              left: -4 * scale,
              width: Math.max(8, 8 * scale),
              height: Math.max(8, 8 * scale),
            }}
            onMouseDown={(e) => onResizeStart(e, element.id, 'nw')}
            onTouchStart={(e) => onResizeStart(e, element.id, 'nw')}
          />
          <div
            className="absolute bg-blue-500 border-2 border-white cursor-ne-resize"
            style={{
              top: -4 * scale,
              right: -4 * scale,
              width: Math.max(8, 8 * scale),
              height: Math.max(8, 8 * scale),
            }}
            onMouseDown={(e) => onResizeStart(e, element.id, 'ne')}
            onTouchStart={(e) => onResizeStart(e, element.id, 'ne')}
          />
          <div
            className="absolute bg-blue-500 border-2 border-white cursor-sw-resize"
            style={{
              bottom: -4 * scale,
              left: -4 * scale,
              width: Math.max(8, 8 * scale),
              height: Math.max(8, 8 * scale),
            }}
            onMouseDown={(e) => onResizeStart(e, element.id, 'sw')}
            onTouchStart={(e) => onResizeStart(e, element.id, 'sw')}
          />
          <div
            className="absolute bg-blue-500 border-2 border-white cursor-se-resize"
            style={{
              bottom: -4 * scale,
              right: -4 * scale,
              width: Math.max(8, 8 * scale),
              height: Math.max(8, 8 * scale),
            }}
            onMouseDown={(e) => onResizeStart(e, element.id, 'se')}
            onTouchStart={(e) => onResizeStart(e, element.id, 'se')}
          />
          
          {/* Edge handles */}
          <div
            className="absolute bg-blue-500 border-2 border-white cursor-n-resize"
            style={{
              top: -4 * scale,
              left: '50%',
              transform: 'translateX(-50%)',
              width: Math.max(8, 8 * scale),
              height: Math.max(4, 4 * scale),
            }}
            onMouseDown={(e) => onResizeStart(e, element.id, 'n')}
            onTouchStart={(e) => onResizeStart(e, element.id, 'n')}
          />
          <div
            className="absolute bg-blue-500 border-2 border-white cursor-s-resize"
            style={{
              bottom: -4 * scale,
              left: '50%',
              transform: 'translateX(-50%)',
              width: Math.max(8, 8 * scale),
              height: Math.max(4, 4 * scale),
            }}
            onMouseDown={(e) => onResizeStart(e, element.id, 's')}
            onTouchStart={(e) => onResizeStart(e, element.id, 's')}
          />
          <div
            className="absolute bg-blue-500 border-2 border-white cursor-w-resize"
            style={{
              top: '50%',
              left: -4 * scale,
              transform: 'translateY(-50%)',
              width: Math.max(4, 4 * scale),
              height: Math.max(8, 8 * scale),
            }}
            onMouseDown={(e) => onResizeStart(e, element.id, 'w')}
            onTouchStart={(e) => onResizeStart(e, element.id, 'w')}
          />
          <div
            className="absolute bg-blue-500 border-2 border-white cursor-e-resize"
            style={{
              top: '50%',
              right: -4 * scale,
              transform: 'translateY(-50%)',
              width: Math.max(4, 4 * scale),
              height: Math.max(8, 8 * scale),
            }}
            onMouseDown={(e) => onResizeStart(e, element.id, 'e')}
            onTouchStart={(e) => onResizeStart(e, element.id, 'e')}
          />
        </>
      )}
    </div>
  )
}
