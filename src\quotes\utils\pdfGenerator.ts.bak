import { jsPD<PERSON> } from 'jspdf';
import 'jspdf-autotable';
import { formatPrice } from './formatters';

// --- Asset Imports ---
// Assuming your build tool handles these imports (e.g., Vite, Webpack)
import bgPage1 from '../assets/tripxplo-pdf-bg-page-1.jpg';
import bgPage2 from '../assets/tripxplo-pdf-bg-page-2.jpg'; // Background for page 2+ (except last)
import bgPage3 from '../assets/tripxplo-pdf-bg-page-3.jpg'; // Background for last page
import logoImage from '../assets/tripxplo.com.png';
import optionIcon from '../assets/option-icon.png';
import hotelIcon from '../assets/hotel-icon.png';
import starIcon from '../assets/star-icon.png';
import startupIndiaLogo from '../assets/startupindia-logo.png';
import startupTNLogo from '../assets/startupTN-logo.png';
import montserratRegular from '../assets/fonts/montserrat-regular.base64';
import montserratBold from '../assets/fonts/montserrat-bold.base64';
import montserratItalic from '../assets/fonts/montserratItalicBase64Data';
import ruthlessly from '../assets/fonts/ruthlessly.base64';
import saira from '../assets/fonts/saira.base64';

// Type definitions
declare module 'jspdf' {
  interface jsPDF {
    autoTable: {
      (options: any): any;
      previous: any;
    }
  }
}

// Enhanced jsPDF type
export interface EnhancedPDF extends jsPDF {
  getFilename?: () => string;
}

// Interfaces (Keep existing definitions)
interface HotelRow {
  hotelName: string;
  roomType: string;
  noOfRooms: number;
  mealPlan: string;
}
interface TripDetails {
  customerName: string;
  destination: string;
  packageName: string;
  quoteDate: string;
  validityDate: string;
  noOfPersons: number;
  children: number;
  hotelRows: HotelRow[];
  calculateTotals: () => { grandTotal: number; perPersonCost: number; gst: number; discount?: number };
  familyType?: string;
  packageId?: string;
  tripDuration?: string;
  packageType?: string;
  travelDate?: string;
  cabDetails?: {
    type: string;
    seats: string;
  };
  inclusions?: string[];
  exclusions?: string[];
  paymentOptions?: string[];
  paymentPolicies?: string[][];
  refundPolicies?: string[][];
  terms?: string[];
}
interface TextStyle {
  size: number;
  color: string;
  font: string;
}
interface TextSegment { text: string; style: TextStyle; }
interface ContentItem { segments?: TextSegment[]; text?: string; style?: TextStyle; }
interface Line { segments: TextSegment[]; width: number; }

// --- Font Constants (Registered Names) ---
const FONTS = {
  MONTSERRAT: 'montserrat',
  MONTSERRAT_BOLD: 'montserrat-bold',
  MONTSERRAT_ITALIC: 'montserrat-italic',
  ANEK_BANGLA: 'anek-bangla',
  RUTHLESSLY: 'ruthlessly',
  SAIRA: 'saira'
} as const;

// --- Font Style Constants ---
const FONT_STYLE_NORMAL = 'normal';
const FONT_STYLE_BOLD = 'bold';
const FONT_STYLE_ITALIC = 'italic';

// --- Theme Colors ---
type RGBColor = [number, number, number];

const THEME = {
  primary: [0, 182, 155] as RGBColor,
  primaryLight: [231, 249, 247] as RGBColor,
  primaryDark: [0, 155, 132] as RGBColor,
  text: {
    light: [255, 255, 255] as RGBColor,
    dark: [45, 55, 72] as RGBColor,
    muted: [127, 140, 141] as RGBColor,
    about: [27, 101, 96] as RGBColor
  },
  background: {
    light: [249, 249, 249] as RGBColor,
    highlight: [232, 248, 245] as RGBColor
  },
  border: [14, 92, 87] as RGBColor
} as const;

// Use imported variables directly
const bgPage1Base64 = bgPage1;
const bgPage2Base64 = bgPage2;
const bgPage3Base64 = bgPage3;

// --- Helper Function: Hex to RGB ---
const hexToRgb = (hex: string): [number, number, number] => {
    const fallback: [number, number, number] = [0, 0, 0];
    if (!hex) return fallback;
    const shorthandRegex = /^#?([a-f\d])([a-f\d])([a-f\d])$/i;
    hex = hex.replace(shorthandRegex, (m, r, g, b) => r + r + g + g + b + b);
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    return result ? [parseInt(result[1], 16), parseInt(result[2], 16), parseInt(result[3], 16)] : fallback;
};

// Add type declarations for helper functions
let currentY = 0;
const margin = 15;
const pageWidth = 210; // A4 width in mm
const pageHeight = 297; // A4 height in mm

// Add helper function to convert RGB to hex
const rgbToHex = (rgb: RGBColor): string => {
  return `#${rgb.map(x => {
    const hex = x.toString(16);
    return hex.length === 1 ? '0' + hex : hex;
  }).join('')}`;
};

// Fix color-related functions
const setFillColor = (doc: jsPDF, rgb: RGBColor) => {
  const [r, g, b] = rgb;
  doc.setFillColor(r, g, b);
};

const setTextColor = (doc: jsPDF, rgb: RGBColor) => {
  const [r, g, b] = rgb;
  doc.setTextColor(r, g, b);
};

/**
 * Generates the Trip Quotation PDF document.
 */
export const generatePDF = (details: TripDetails): EnhancedPDF => {
  const {
    customerName, destination, packageName, quoteDate, validityDate,
    noOfPersons, children, hotelRows, calculateTotals, familyType,
    packageId, tripDuration, packageType, travelDate, cabDetails,
    inclusions: customInclusions, exclusions: customExclusions,
    paymentOptions: customPaymentOptions, paymentPolicies: customPaymentPolicies,
    refundPolicies: customRefundPolicies, terms: customTerms
  } = details;

  // --- Document Setup ---
  const doc = new jsPDF({
    orientation: 'portrait',
    unit: 'mm',
    format: 'a4',
    putOnlyUsedFonts: true,
    floatPrecision: 16
  });
  const pageWidth = doc.internal.pageSize.width;
  const pageHeight = doc.internal.pageSize.height;
  const margin = 15;
  const contentWidth = pageWidth - (margin * 2);
  let lastY = 0;
  const footerHeight = 20;
  const pageContentStartY = margin; // Content starts at margin on pages 2+
  let currentPageNumber = 1;

  // --- Color Conversion ---
  const themeRgb = {
    primary: [0, 182, 155] as RGBColor,
    primaryLight: [231, 249, 247] as RGBColor,
    primaryDark: [0, 155, 132] as RGBColor,
    textLight: [255, 255, 255] as RGBColor,
    textDark: [45, 55, 72] as RGBColor,
    border: [14, 92, 87] as RGBColor
  };

  // --- Register Custom Fonts ---
  try {
    // Montserrat (Ensure Base64 is provided)
    if (montserratRegular && montserratRegular !== 'PASTE_MONTSERRAT_REGULAR_BASE64_HERE') {
        doc.addFileToVFS('montserrat-regular.ttf', montserratRegular);
        doc.addFont('montserrat-regular.ttf', FONTS.MONTSERRAT, FONT_STYLE_NORMAL);
    } else { throw new Error('Montserrat Regular Base64 missing'); }
    if (montserratBold && montserratBold !== 'YOUR_MONTSERRAT_BOLD_BASE64_STRING') {
        doc.addFileToVFS('montserrat-bold.ttf', montserratBold);
        doc.addFont('montserrat-bold.ttf', FONTS.MONTSERRAT_BOLD, FONT_STYLE_BOLD);
    } else { throw new Error('Montserrat Bold Base64 missing'); }
    if (montserratItalic && montserratItalic !== 'PASTE_MONTSERRAT_ITALIC_BASE64_HERE') {
        doc.addFileToVFS('montserrat-italic.ttf', montserratItalic);
        doc.addFont('montserrat-italic.ttf', FONTS.MONTSERRAT_ITALIC, FONT_STYLE_ITALIC);
    } else { throw new Error('Montserrat Italic Base64 missing'); }
    console.log('Custom fonts registered (assuming Base64 provided).');
  } catch (e) {
    console.error("CRITICAL: Error registering fonts:", e);
    alert("Error loading custom fonts. PDF text will not use the correct fonts.");
    doc.setFont('helvetica', 'normal'); // Fallback
  }

  // --- Helper: Add Background Image ---
  const addBackgroundImage = (pageNumber: number, totalPages: number | null = null) => {
      let bgData: string | null = null;
      let bgFormat = 'JPEG';
      if (pageNumber === 1) {
          bgData = bgPage1Base64;
      } else if (totalPages !== null && pageNumber === totalPages && bgPage3Base64 && !bgPage3Base64.includes('PASTE_')) {
          bgData = bgPage3Base64;
      } else if (bgPage2Base64 && !bgPage2Base64.includes('PASTE_')) {
          bgData = bgPage2Base64;
      }
      if (bgData) {
          try {
              if (bgData.startsWith('data:image/png')) bgFormat = 'PNG';
              else if (bgData.startsWith('data:image/jpeg')) bgFormat = 'JPEG';
              doc.addImage(bgData, bgFormat, 0, 0, pageWidth, pageHeight, `bg_page_${pageNumber}`, 'NONE');
          } catch (e) { console.error(`Error adding background image for page ${pageNumber}:`, e); }
      } else { console.warn(`Background image data missing or placeholder used for page ${pageNumber} or dependent pages.`); }
  };

  // --- Helper: Add Page with Background (No Header for Pages 2+) ---
   const addNewPage = (): number => {
       doc.addPage();
       currentPageNumber++;
       addBackgroundImage(currentPageNumber, null);
       return pageContentStartY; // Return new starting Y (top margin)
   };

  // --- Helper: Center Text ---
   const centerText = ( text: string, y: number, fontSize?: number, fontName = FONTS.MONTSERRAT, fontStyle = FONT_STYLE_NORMAL ) => {
    if (fontSize) doc.setFontSize(fontSize);
     try { doc.setFont(fontName, fontStyle); }
     catch (e) { console.warn(`Font ${fontName} (${fontStyle}) not found, falling back.`); doc.setFont('helvetica', fontStyle); }
    const textWidth = doc.getStringUnitWidth(text) * doc.getFontSize() / doc.internal.scaleFactor;
    const x = (pageWidth - textWidth) / 2;
    const finalX = Math.max(margin, x);
    doc.text(text, finalX, y);
  };

  // --- Helper: Add Section Title (Using Bold Style for Page 2+) ---
  const addSectionTitle = (title: string, y: number): number => {
    const titleFont = FONTS.MONTSERRAT; // Use Montserrat for consistency
    const titleSize = 16;
    const titleColor = hexToRgb('#0e5c57'); // Using #0e5c57 color
    try { doc.setFont(titleFont, FONT_STYLE_BOLD); } // Changed to BOLD
    catch (e) { console.warn(`Font ${titleFont} not found, falling back.`); doc.setFont('helvetica', FONT_STYLE_BOLD); }
    doc.setFontSize(titleSize);
    doc.setTextColor(...titleColor);
    doc.text(title, margin, y); // Keep left aligned at margin
    return y + 8; // Return position below title (original spacing)
  };

  // --- Helper: Draw Text Block (Using Montserrat Regular) ---
  const drawTextBlock = ( textLines: string[], startY: number, fontSize: number, lineHeightFactor: number = 1.5, indent: number = 0, fontName = FONTS.MONTSERRAT, fontStyle = FONT_STYLE_NORMAL, color = themeRgb.textDark ): number => {
    // <<< MODIFIED: Signature changed to match original snippet's call pattern >>>
    try { doc.setFont(fontName, fontStyle); }
    catch (e) { console.warn(`Font ${fontName} (${fontStyle}) not found, falling back.`); doc.setFont('helvetica', fontStyle); }
    doc.setFontSize(fontSize);
    doc.setTextColor(...color);
    let currentY = startY;
    const lineSpacing = fontSize * lineHeightFactor / doc.internal.scaleFactor;
    textLines.forEach(line => {
        const splitLines = doc.splitTextToSize(line, contentWidth - indent);
        const neededHeight = splitLines.length * lineSpacing;
        if (currentY + neededHeight > pageHeight - footerHeight - margin) {
            currentY = addNewPage();
            // Redraw font settings after adding page
            try { doc.setFont(fontName, fontStyle); } catch (e) { doc.setFont('helvetica', fontStyle); }
            doc.setFontSize(fontSize); doc.setTextColor(...color);
        }
        doc.text(splitLines, margin + indent, currentY);
        currentY += neededHeight;
    });
    return currentY;
  };

  // --- Helper: Check Space and Add Page ---
  const checkAndAddPage = (currentY: number, estimatedHeight: number): number => {
    // Use original snippet's logic (adds spacing if no new page)
    if (currentY + estimatedHeight + footerHeight > pageHeight - margin) {
        return addNewPage();
    }
    return currentY + 8; // Add consistent spacing if not adding page
  };

  // =============================================
  // START PDF GENERATION - PAGE 1
  // (Keep the existing Page 1 logic as finalized previously)
  // =============================================

  addBackgroundImage(1, null);
  lastY = margin + 35;

  // Title
  try { doc.setFont(FONTS.MONTSERRAT, FONT_STYLE_ITALIC); doc.setFontSize(14); doc.setTextColor(...hexToRgb(THEME.text.about)); doc.setCharSpace(0.3); const titleText = "Customize & Book Your Holiday!"; centerText(titleText, lastY, 14, FONTS.MONTSERRAT, FONT_STYLE_ITALIC); doc.setCharSpace(0); lastY += 6; } catch (e) { console.error("Error rendering Page 1 title:", e); lastY += 6; }

  // Logo
  try { const logoWidth = 40; const logoHeight = 10; const logoX = (pageWidth - logoWidth) / 2; const format = logoImage.startsWith('data:image/png') ? 'PNG' : 'JPEG'; doc.addImage(logoImage, format, logoX, lastY, logoWidth, logoHeight, 'logo', 'FAST'); lastY += logoHeight + 15; } catch (e) { console.error("Error loading/adding logo image:", e); lastY += 15; }

  // "Why TripXplo?" Content Array
  const whyTripXploContent = [
    { segments: [ { text: "TripXplo's user-friendly web app", style: { size: 18, color: THEME.text.about, font: FONT_STYLE_BOLD } }, { text: " offers travellers an all-in-one solution to plan and book their dream vacations. Our innovative web app simplifies the entire travel experience, from destination selection to receiving ", style: { size: 18, color: THEME.text.about, font: FONT_STYLE_NORMAL } }, { text: "personalized trip recommendations,", style: { size: 18, color: THEME.text.about, font: FONT_STYLE_BOLD } }, { text: " all while prioritizing traveller safety and security.", style: { size: 18, color: THEME.text.about, font: FONT_STYLE_NORMAL } } ] },
    { segments: [ { text: "Tripmilestone Tours Pvt Limited", style: { size: 18, color: THEME.text.about, font: FONT_STYLE_BOLD } }, { text: ", the parent company of TripXplo, is recognized by ", style: { size: 18, color: THEME.text.about, font: FONT_STYLE_NORMAL } }, { text: "Startup India", style: { size: 18, color: THEME.text.about, font: FONT_STYLE_BOLD } }, { text: " and the ", style: { size: 18, color: THEME.text.about, font: FONT_STYLE_NORMAL } }, { text: "Department for Promotion of Industry and Internal Trade (DPIIT) under the Ministry of Commerce & Industry.", style: { size: 18, color: THEME.text.about, font: FONT_STYLE_BOLD } } ] },
    { segments: [ { text: "TripXplo Online Platform Officially launched by", style: { size: 18, color: THEME.text.about, font: FONT_STYLE_NORMAL } }, { text: " StartupTN,", style: { size: 18, color: THEME.text.about, font: FONT_STYLE_BOLD } }, { text: " Govt. of Tamil Nadu.", style: { size: 18, color: THEME.text.about, font: FONT_STYLE_BOLD } } ] },
    { text: "Transparent Pricing | No Hidden Costs", style: { size: 16, color: THEME.primary } },
    { text: "Customize: Choose from 100+ Hotels/Resorts", style: { size: 16, color: THEME.primary } },
    { text: "4.9 Star ratings on Google with 100+ reviews", style: { size: 16, color: THEME.primary } }
  ];

  // Render Paragraphs 1, 2, and 3
  for (let i = 0; i < 3; i++) {
      const content = whyTripXploContent[i];
      if (content.segments) {
          let currentY = lastY; let lines: Line[] = []; let currentLine: Line = { segments: [], width: 0 };
          content.segments.forEach(segment => { const fontStyle = segment.style.font || FONT_STYLE_NORMAL; const fontSize = segment.style.size; try { doc.setFont(FONTS.MONTSERRAT, fontStyle); } catch (e) { doc.setFont('helvetica', fontStyle); } doc.setFontSize(fontSize); const words = segment.text.trim().split(/ +/); words.forEach(word => { if (!word) return; const wordWithSpace = word + ' '; const wordWidth = doc.getStringUnitWidth(wordWithSpace) * fontSize / doc.internal.scaleFactor; if (currentLine.width + wordWidth > contentWidth && currentLine.segments.length > 0) { lines.push(currentLine); currentLine = { segments: [], width: 0 }; } currentLine.segments.push({ text: word, style: segment.style }); currentLine.width += wordWidth; }); });
          if (currentLine.segments.length > 0) lines.push(currentLine);
          const lineHeight = (18 * 1.4) / doc.internal.scaleFactor;
          lines.forEach((line, lineIndex) => { let x = margin; line.segments.forEach((seg, segIndex) => { const textToDraw = segIndex === 0 ? seg.text : ' ' + seg.text; const style = seg.style.font || FONT_STYLE_NORMAL; const size = seg.style.size; try { doc.setFont(FONTS.MONTSERRAT, style); } catch(e){ doc.setFont('helvetica', style); } doc.setFontSize(size); doc.setTextColor(...hexToRgb(seg.style.color)); doc.text(textToDraw, x, currentY + (lineIndex * lineHeight)); x += doc.getStringUnitWidth(textToDraw) * size / doc.internal.scaleFactor; }); });
          lastY += (lines.length * lineHeight) + 5;
      }
  }

  lastY += 12; // Vertical space before icon points

  // Render Icon Points (Center Block, Align Left Edges)
  const pointIconSize = 6; const pointIconPadding = 4; const pointFontSize = 16;
  let maxBlockWidth = 0;
  try { doc.setFont(FONTS.MONTSERRAT, FONT_STYLE_NORMAL); } catch(e){ doc.setFont('helvetica'); } doc.setFontSize(pointFontSize);
  for (let i = 3; i < whyTripXploContent.length; i++) { const content = whyTripXploContent[i]; if (content.text) { const text = content.text; const tempTextAvailableWidth = contentWidth - pointIconSize - pointIconPadding; const splitTempText = doc.splitTextToSize(text, tempTextAvailableWidth); const textDims = doc.getTextDimensions(splitTempText); const itemWidth = pointIconSize + pointIconPadding + textDims.w; maxBlockWidth = Math.max(maxBlockWidth, itemWidth); } }
  maxBlockWidth = Math.min(maxBlockWidth, contentWidth); const blockStartX = Math.max(margin, (pageWidth - maxBlockWidth) / 2);

  for (let i = 3; i < whyTripXploContent.length; i++) {
       const content = whyTripXploContent[i];
        if (content.text && content.style?.color) {
            let iconData: string | null = null; if (i === 3) iconData = optionIcon; else if (i === 4) iconData = hotelIcon; else if (i === 5) iconData = starIcon;
            const text = content.text; const pointLineHeight = (pointFontSize * 1.4) / doc.internal.scaleFactor; const iconX = blockStartX; const textX = blockStartX + pointIconSize + pointIconPadding; const textAvailableWidth = pageWidth - margin - textX; const iconY = lastY + pointLineHeight / 2 - pointIconSize / 2 ; const textBaselineY = lastY + pointLineHeight * 0.8;
            if (iconData) { try { let iconFormat = 'PNG'; if (iconData.startsWith('data:image/jpeg')) iconFormat = 'JPEG'; doc.addImage(iconData, iconFormat, iconX, iconY, pointIconSize, pointIconSize); } catch (e) { console.error("Error adding icon:", i, e); doc.text("•", iconX, textBaselineY); } } else { doc.text("•", iconX, textBaselineY); console.warn(`Icon data missing: ${i}`); }
            try { doc.setFont(FONTS.MONTSERRAT, FONT_STYLE_NORMAL); } catch(e){ doc.setFont('helvetica'); } doc.setFontSize(pointFontSize); doc.setTextColor(...hexToRgb(THEME.primary)); const splitText = doc.splitTextToSize(text, textAvailableWidth); doc.text(splitText, textX, textBaselineY);
            const textHeight = doc.getTextDimensions(splitText).h; lastY += Math.max(textHeight, pointIconSize) + 6;
        }
  }
  lastY += 8;

   // Add Footer Logos to Page 1 Bottom (Above Footer Bar Area)
    const footerLogoH = 12; const footerLogoW = 36; const footerLogoSpacing = 4; const footerLogosY = pageHeight - footerHeight - footerLogoH - 5; const totalFooterLogosWidth = (footerLogoW * 2) + footerLogoSpacing; let footerLogoX = Math.max(margin, (pageWidth - totalFooterLogosWidth) / 2);
    if (startupIndiaLogo) { try { let format = 'PNG'; if (startupIndiaLogo.startsWith('data:image/jpeg')) format = 'JPEG'; doc.addImage(startupIndiaLogo, format, footerLogoX, footerLogosY, footerLogoW, footerLogoH); } catch (e) { console.error("Error adding startupindia logo:", e); } } else { console.warn("Startup India Logo not imported."); }
    footerLogoX += footerLogoW + footerLogoSpacing; if (startupTNLogo) { try { if (footerLogoX + footerLogoW <= pageWidth - margin) { let format = 'PNG'; if (startupTNLogo.startsWith('data:image/jpeg')) format = 'JPEG'; doc.addImage(startupTNLogo, format, footerLogoX, footerLogosY, footerLogoW, footerLogoH); } else { console.warn("Second footer logo would exceed page width."); } } catch (e) { console.error("Error adding startupTN logo:", e); } } else { console.warn("StartupTN Logo not imported."); }


  // =============================================
  // START PDF GENERATION - PAGE 2 (Quote Details)
  // =============================================
  lastY = addNewPage();
  lastY = 30; // Set top margin to 30px

  // --- Main Title ---
  doc.setTextColor(...hexToRgb('#0e5c57'));
  try { doc.setFont(FONTS.MONTSERRAT, FONT_STYLE_BOLD); }
  catch(e){ doc.setFont('helvetica', FONT_STYLE_BOLD); }
  doc.setFontSize(20);
  centerText("TripXplo Package Quote", lastY);
  lastY += 12; // Reduced spacing for subtitle

  // Add Package Name as subtitle
  doc.setFontSize(16);
  centerText(packageName || '-', lastY);
  lastY += 12; // Space after subtitle

  // --- Package Details Box ---
  const boxHeight = 45; // Keep exact 45 pixels height
  doc.setFillColor(themeRgb.primary[0], themeRgb.primary[1], themeRgb.primary[2]);
  doc.roundedRect(margin, lastY, contentWidth, boxHeight, 3, 3, 'F');
  doc.setTextColor(...themeRgb.textLight);
  try { doc.setFont(FONTS.MONTSERRAT, FONT_STYLE_BOLD); }
  catch(e){ doc.setFont('helvetica', FONT_STYLE_BOLD); }
  doc.setFontSize(14);
  doc.text("Package Details", margin + 5, lastY + 8);

  try { doc.setFont(FONTS.MONTSERRAT, FONT_STYLE_BOLD); }
  catch(e){ doc.setFont('helvetica', FONT_STYLE_BOLD); }
  doc.setFontSize(10);
  const detailsStartY = lastY + 18; // Adjusted to start content closer to title
  const colWidth = contentWidth / 2;

  // Left column
  doc.text(`Package ID: ${packageId || '-'}`, margin + 5, detailsStartY);
  doc.text(`Package Type: ${packageType || '-'}`, margin + 5, detailsStartY + 8);
  doc.text(`Customer Name: ${customerName || '-'}`, margin + 5, detailsStartY + 16);

  // Right column
  doc.text(`Destination: ${destination || '-'}`, margin + colWidth, detailsStartY);
  doc.text(`No of Adults: ${noOfPersons || '-'}`, margin + colWidth, detailsStartY + 8);
  doc.text(`No of Children: ${children || '-'}`, margin + colWidth, detailsStartY + 16);

  // Family Type (full width, bottom)
  doc.text(`Family Type: ${familyType || '-'}`, margin + 5, detailsStartY + 24);

  lastY += boxHeight + 8; // Keep 8 pixels spacing after box

  // --- Trip Details Box ---
  const tripBoxHeight = 25; // Reduced height
  doc.setFillColor(themeRgb.primary[0], themeRgb.primary[1], themeRgb.primary[2]);
  doc.roundedRect(margin, lastY, contentWidth, tripBoxHeight, 3, 3, 'F');
  doc.setTextColor(...themeRgb.textLight);
  try { doc.setFont(FONTS.MONTSERRAT, FONT_STYLE_BOLD); }
  catch(e){ doc.setFont('helvetica', FONT_STYLE_BOLD); }
  doc.setFontSize(14);
  doc.text("Trip Details", margin + 5, lastY + 8);
  
  try { doc.setFont(FONTS.MONTSERRAT, FONT_STYLE_NORMAL); }
  catch(e){ doc.setFont('helvetica'); }
  doc.setFontSize(10);
  const tripInfo = [
    [`Trip Duration: ${tripDuration || '-'}`, `Travel Date: ${travelDate || '-'}`],
    [`Package Type: ${packageType || '-'}`, `Family Type: ${familyType || '-'}`]
  ];
  tripInfo.forEach((row, index) => {
    doc.text(row[0], margin + 5, lastY + 18 + (index * 6));
    doc.text(row[1], margin + contentWidth / 2, lastY + 18 + (index * 6));
  });
  lastY += tripBoxHeight + 8;

  // --- Accommodation Details Table ---
  lastY = checkAndAddPage(lastY, 40);
  doc.setTextColor(...hexToRgb('#0e5c57'));
  lastY = addSectionTitle("Accommodation Details", lastY);
  doc.setTextColor(...themeRgb.textDark);
  const hotelDetailsBody = hotelRows.map(row => [
    row.hotelName || 'N/A',
    row.roomType || 'N/A',
    `${row.noOfRooms} Room${row.noOfRooms > 1 ? 's' : ''}`,
    row.mealPlan || 'N/A'
  ]);

  (doc as any).autoTable({
    startY: lastY + 2,
    head: [["Hotel", "Room Category", "Rooms", "Meal Plan"]],
    body: hotelDetailsBody,
    theme: 'grid',
    headStyles: {
      fillColor: themeRgb.primary, 
      textColor: themeRgb.textLight, 
      font: FONTS.MONTSERRAT, 
      fontStyle: FONT_STYLE_BOLD, 
      fontSize: 11,
      halign: 'left', 
      cellPadding: { top: 4, right: 4, bottom: 4, left: 4 },
      overflow: 'linebreak',
      valign: 'middle'
    },
    styles: {
      fontSize: 10,
      font: FONTS.MONTSERRAT, 
      fontStyle: FONT_STYLE_NORMAL,
      textColor: themeRgb.textDark, 
      lineColor: themeRgb.primary, 
      lineWidth: 0.1, 
      halign: 'left', 
      valign: 'middle',
      cellPadding: { top: 4, right: 4, bottom: 4, left: 4 },
      overflow: 'linebreak'
    },
    columnStyles: {
      0: { cellWidth: contentWidth * 0.40 },
      1: { cellWidth: contentWidth * 0.25 },
      2: { cellWidth: contentWidth * 0.15, halign: 'center' },
      3: { cellWidth: contentWidth * 0.20 }
    },
    alternateRowStyles: { fillColor: themeRgb.primaryLight },
    margin: { left: margin, right: margin },
    didDrawCell: function(data: any) {
      // Add cell padding and word wrap
      if (data.cell.text) {
        data.cell.styles.cellPadding = 4;
        data.cell.styles.overflow = 'linebreak';
      }
    }
  });
  lastY = (doc as any).autoTable.previous.finalY + 8; // Reduced spacing

  // --- Cab Details (if available) ---
  if (cabDetails) {
    const cabBoxHeight = 20; // Reduced height
    doc.setFillColor(themeRgb.primary[0], themeRgb.primary[1], themeRgb.primary[2]);
    doc.roundedRect(margin, lastY, contentWidth, cabBoxHeight, 3, 3, 'F');
    doc.setTextColor(...themeRgb.textLight);
    try { doc.setFont(FONTS.MONTSERRAT, FONT_STYLE_BOLD); }
    catch(e){ doc.setFont('helvetica', FONT_STYLE_BOLD); }
    doc.setFontSize(12);
    doc.text("Cab Details", margin + 5, lastY + 8);
    
    try { doc.setFont(FONTS.MONTSERRAT, FONT_STYLE_NORMAL); }
    catch(e){ doc.setFont('helvetica'); }
    doc.setFontSize(9);
    doc.text(`Cab Type: ${cabDetails.type || 'N/A'}`, margin + 5, lastY + 15);
    doc.text(`No of Seater: ${cabDetails.seats || 'N/A'}`, margin + contentWidth / 2, lastY + 15);
    lastY += cabBoxHeight + 8; // Reduced spacing
  }

  // --- Package Inclusions ---
  doc.setTextColor(...hexToRgb('#0e5c57'));
  lastY = addSectionTitle("Package Inclusions", lastY);
  doc.setTextColor(...hexToRgb('#039674'));
  const inclusions = [
    "• Welcome Drink on Arrival",
    "• Daily Breakfast & Dinner Included",
    "• Private Cab for Transfer + Sightseeing",
    "• Free ICICI Lombard - 10 Days Domestic Travel Insurance (Offer by Tripmilestone)"
  ];
  lastY = drawTextBlock(inclusions, lastY + 2, 12, 1.2, 5); // Reduced line height
  lastY += 8; // Reduced spacing

  // --- Cost Summary Box ---
  const costBoxHeight = 25; // Reduced height
  doc.setFillColor(themeRgb.primary[0], themeRgb.primary[1], themeRgb.primary[2]);
  doc.roundedRect(margin, lastY, contentWidth, costBoxHeight, 3, 3, 'F');
  doc.setTextColor(...themeRgb.textLight);
  try { doc.setFont(FONTS.MONTSERRAT, FONT_STYLE_BOLD); }
  catch(e){ doc.setFont('helvetica', FONT_STYLE_BOLD); }
  doc.setFontSize(12);
  doc.text("Cost Summary", margin + 5, lastY + 8);
  
  try { doc.setFont(FONTS.MONTSERRAT, FONT_STYLE_NORMAL); }
  catch(e){ doc.setFont('helvetica'); }
  doc.setFontSize(9);
  const totals = calculateTotals();
  const costDetails = [
    [`Per Person Cost: ${formatPrice(totals.perPersonCost)}`, `GST: ${formatPrice(totals.gst)}`],
    [`Discount: ${formatPrice(totals.discount || 0)}`, `Total Cost: ${formatPrice(totals.grandTotal)}`]
  ];
  costDetails.forEach((row, index) => {
    doc.text(row[0], margin + 5, lastY + 18 + (index * 6)); // Adjusted spacing
    doc.text(row[1], margin + contentWidth / 2, lastY + 18 + (index * 6));
  });
  lastY += costBoxHeight + 8; // Reduced spacing

  // =============================================
  // START PDF GENERATION - PAGE 3 (Package Details)
  // =============================================
  lastY = addNewPage();
  lastY = 30; // Set top margin to 15px

  // --- Exclusions ---
  doc.setTextColor(...hexToRgb('#0e5c57'));
  lastY = addSectionTitle("Exclusions", lastY);
  doc.setTextColor(...hexToRgb('#039674'));
  const exclusions = [
    "• Train or airfare.",
    "• Personal expenses (entry charges, telephone, internet, laundry, etc.).",
    "• Adventure activities unless specified.",
    "• Additional sightseeing.",
    "• Museum/park entry fees, Jeep safari, room heater charges.",
    "• Snow vehicle fare.",
    "• Travel insurance unless specified.",
    "• Anything not mentioned in inclusion"
  ];
  lastY = drawTextBlock(exclusions, lastY + 2, 12, 1.5, 5);
  lastY += 10;

  // --- Payment Options ---
  doc.setTextColor(...hexToRgb('#0e5c57'));
  lastY = addSectionTitle("Payment Options", lastY);
  doc.setTextColor(...hexToRgb('#039674'));
  const paymentOptions = [
    "• 50% advance payment at the time of booking",
    "• Balance 50% payment 15 days before travel",
    "• Payment can be made via bank transfer, UPI, or credit card"
  ];
  lastY = drawTextBlock(paymentOptions, lastY + 2, 12, 1.5, 5);
  lastY += 10;

  // --- Payment Policy Table ---
  doc.setTextColor(...hexToRgb('#0e5c57'));
  lastY = addSectionTitle("Payment Policy", lastY);
  doc.setTextColor(...hexToRgb('#039674'));
  const paymentPoliciesBody = [
    ["Cancellation", "100%", "50%", "25%", "No Refund"],
    ["Refund", "Full", "50%", "25%", "No Refund"]
  ];
  (doc as any).autoTable({
    startY: lastY + 2,
    head: [["", "Hotel & Other Confirmation", "30 Days Before", "21 Days Before", "7 Days Before"]],
    body: paymentPoliciesBody,
    theme: 'grid',
    headStyles: { 
      fillColor: themeRgb.primary, 
      textColor: themeRgb.textLight, 
      font: FONTS.MONTSERRAT, 
      fontStyle: FONT_STYLE_BOLD, 
      fontSize: 8, 
      halign: 'center', 
      cellPadding: 2 
    },
    styles: { 
      fontSize: 7, 
      font: FONTS.MONTSERRAT, 
      textColor: hexToRgb('#039674'), 
      lineColor: hexToRgb(THEME.border),
      lineWidth: 0.1, 
      halign: 'center', 
      valign: 'middle' 
    },
    alternateRowStyles: { fillColor: themeRgb.primaryLight },
    columnStyles: { 0: { halign: 'left', fontStyle: FONT_STYLE_BOLD, cellWidth: contentWidth * 0.25 } },
    margin: { left: margin, right: margin }
  });
  lastY = (doc as any).autoTable.previous.finalY + 10;

  // --- Refund Policy Table ---
  doc.setTextColor(...hexToRgb('#0e5c57'));
  lastY = addSectionTitle("Refund Policy", lastY);
  doc.setTextColor(...hexToRgb('#039674'));
  const refundPoliciesBody = [
    ["More than 30 days before trip", "Full Refund"],
    ["15-30 days before trip", "50% Refund"],
    ["Within 15 days before trip", "No Refund"]
  ];
  (doc as any).autoTable({
    startY: lastY + 2,
    head: [["Time Before Trip", "Refund Eligibility"]],
    body: refundPoliciesBody,
    theme: 'grid',
    headStyles: { 
      fillColor: themeRgb.primary, 
      textColor: themeRgb.textLight, 
      font: FONTS.MONTSERRAT, 
      fontStyle: FONT_STYLE_BOLD, 
      fontSize: 8, 
      halign: 'center', 
      cellPadding: 2 
    },
    styles: { 
      fontSize: 8, 
      font: FONTS.MONTSERRAT, 
      textColor: hexToRgb('#039674'), 
      lineColor: hexToRgb(THEME.border),
      lineWidth: 0.1, 
      halign: 'left', 
      valign: 'middle' 
    },
    alternateRowStyles: { fillColor: themeRgb.primaryLight },
    columnStyles: {
      0: { cellWidth: contentWidth * 0.5 }, 
      1: { cellWidth: contentWidth * 0.5, fontStyle: FONT_STYLE_BOLD } 
    },
    margin: { left: margin, right: margin }
  });
  lastY = (doc as any).autoTable.previous.finalY + 10;

  // --- General Terms & Conditions ---
  doc.setTextColor(...hexToRgb('#0e5c57'));
  lastY = addSectionTitle("General Terms & Conditions", lastY);
  doc.setTextColor(...hexToRgb('#039674'));
  const terms = [
    "• Prices are subject to change without prior notice",
    "• Hotel check-in/check-out times are as per hotel policy",
    "• Rates are valid for the mentioned dates only",
    "• All disputes are subject to Chennai jurisdiction"
  ];
  lastY = drawTextBlock(terms, lastY + 2, 12, 1.5, 5);

  // --- Final Price Section (Using Style from New Snippet) ---
  const finalPriceBoxHeight_pg2 = 20; // Renamed
  lastY = checkAndAddPage(lastY, finalPriceBoxHeight_pg2 + 5); // Use current helper, add buffer
  doc.setFillColor(themeRgb.primary[0], themeRgb.primary[1], themeRgb.primary[2]);
  doc.roundedRect(margin, lastY, contentWidth, finalPriceBoxHeight_pg2, 3, 3, 'F');
  doc.setTextColor(...themeRgb.textLight);
  try { doc.setFont(FONTS.MONTSERRAT, FONT_STYLE_BOLD); } catch(e){ doc.setFont('helvetica', FONT_STYLE_BOLD); } // Use current font setup
  doc.setFontSize(11); // Use 11pt (from snippet)
  const labelY_pg2 = lastY + (finalPriceBoxHeight_pg2 / 2) + (doc.getFontSize() / 3); // Use snippet vertical centering logic
  doc.text("FINAL PRICE (Incl. GST)", margin + 7, labelY_pg2); // Use 7mm padding (from snippet)
  doc.setFontSize(16); // Use 16pt (from snippet)
  const totalPrice_pg2 = formatPrice(calculateTotals().grandTotal); // Renamed
  const priceTextWidth_pg2 = doc.getStringUnitWidth(totalPrice_pg2) * doc.getFontSize() / doc.internal.scaleFactor; // Renamed
  doc.text(totalPrice_pg2, pageWidth - margin - 7 - priceTextWidth_pg2, labelY_pg2); // Align right, use 7mm padding
  lastY += finalPriceBoxHeight_pg2 + 5; // Use 5mm spacing (from snippet)


  // --- PDF Footer Drawing (Remains Modified to Skip Page 1) ---
  const footerStartY = pageHeight - footerHeight;
  const totalPages = doc.getNumberOfPages();

   for (let i = 1; i <= totalPages; i++) {
       doc.setPage(i); // Go to page i
       // Only draw footer on page 1
       if (i === 1) {
           doc.setFillColor(...themeRgb.primaryDark);
           doc.rect(0, footerStartY, pageWidth, footerHeight, 'F');
           doc.setTextColor(...themeRgb.textLight);
           try { doc.setFont(FONTS.MONTSERRAT, FONT_STYLE_NORMAL); } catch(e){ doc.setFont('helvetica'); }
           doc.setFontSize(8);
           centerText("TripXplo | Book Smart, Travel Easy | <EMAIL>", footerStartY + 5, 8, FONTS.MONTSERRAT);
           centerText("Phone: 9442424492, 7695993808 | WhatsApp: 9444041468 | Web: tripxplo.com", footerStartY + 10, 8, FONTS.MONTSERRAT);
           doc.setFontSize(7);
           centerText("TripXplo by Tripmilestone (P) Ltd.", footerStartY + 15, 7, FONTS.MONTSERRAT);
           doc.setFontSize(8); doc.text(`Page ${i} of ${totalPages}`, pageWidth - margin, footerStartY + 15);
       }
   }


  // --- Handle Last Page Background (Post-Generation) ---
   if (totalPages > 1) { doc.setPage(totalPages); addBackgroundImage(totalPages, totalPages); }
   doc.setPage(totalPages); // Ensure context is on the last page


  // --- Filename Generation ---
  const dateSegments = quoteDate?.split('-') || ['', '', '']; const month = quoteDate ? new Date(quoteDate).toLocaleString('default', { month: 'short' }) : 'Mon'; const day = quoteDate ? parseInt(dateSegments[2]) : 1; const daySuffix = getDaySuffix(day);
  return Object.assign(doc, { getFilename: () => { const cleanDestination = destination?.split('-')[0].trim().replace(/ /g, '_') || 'Destination'; const cleanCustomerName = customerName?.trim().replace(/ /g, '_') || 'Customer'; const familySegment = familyType ? `-${familyType.replace(/ /g, '_')}` : ''; const refId = packageId || 'Quote'; return `${cleanCustomerName}-${cleanDestination}-${refId}-${day}${daySuffix}-${month}${familySegment}-Quote.pdf`; } });
};

// Helper function to get day suffix (th, st, nd, rd)
function getDaySuffix(day: number): string { if (!day) return ''; if (day > 3 && day < 21) return 'th'; switch (day % 10) { case 1: return 'st'; case 2: return 'nd'; case 3: return 'rd'; default: return 'th'; } }

// Function to handle empty or N/A values
const formatValue = (value: string | number | undefined): string => {
  if (value === undefined || value === null || value === '' || value === 'N/A') {
    return '-';
  }
  return value.toString();
};

// Draw Package Details Box
const drawPackageDetails = (doc: jsPDF, tripDetails: TripDetails, themeRgb: typeof THEME) => {
  const boxHeight = 120;
  const boxY: number = currentY;
  
  // Draw green background
  setFillColor(doc, themeRgb.primary);
  doc.roundedRect(margin, boxY, pageWidth - 2 * margin, boxHeight, 5, 5, 'F');
  
  // Set text style
  const titleStyle = getTextStyle(themeRgb.text.light, 16, FONTS.MONTSERRAT);
  doc.setFontSize(titleStyle.size);
  doc.setTextColor(titleStyle.color);
  try { doc.setFont(titleStyle.font, FONT_STYLE_BOLD); } catch(e){ doc.setFont('helvetica', 'bold'); }
  
  // Update currentY
  currentY = boxY + boxHeight + 10;
};

// Draw Trip Details Box
const drawTripDetails = (doc: jsPDF, tripDetails: TripDetails, themeRgb: typeof THEME) => {
  const boxHeight = 80;
  const boxY: number = currentY;
  let detailsY: number = boxY + 20;
  
  // Set text style
  const titleStyle = getTextStyle(themeRgb.text.dark, 16, FONTS.MONTSERRAT);
  doc.setFontSize(titleStyle.size);
  doc.setTextColor(titleStyle.color);
  try { doc.setFont(titleStyle.font, FONT_STYLE_BOLD); } catch(e){ doc.setFont('helvetica', 'bold'); }
  
  // Update currentY
  currentY = boxY + boxHeight + 10;
};

// Draw Cost Summary Box
const drawCostSummary = (doc: jsPDF, tripDetails: TripDetails, themeRgb: typeof THEME, calculateTotalCost: () => number) => {
  const boxHeight = 100;
  const boxY: number = currentY;
  
  // Set text style
  const titleStyle = getTextStyle(themeRgb.text.light, 16, FONTS.MONTSERRAT);
  doc.setFontSize(titleStyle.size);
  doc.setTextColor(titleStyle.color);
  try { doc.setFont(titleStyle.font, FONT_STYLE_BOLD); } catch(e){ doc.setFont('helvetica', 'bold'); }
  
  // Update currentY
  currentY = boxY + boxHeight + 10;
};

// Fix text style helper
const getTextStyle = (rgb: RGBColor, size: number, font: string): TextStyle => {
  const [r, g, b] = rgb;
  return {
    size,
    color: `rgb(${r}, ${g}, ${b})`,
    font
  };
};
