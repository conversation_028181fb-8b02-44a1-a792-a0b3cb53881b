-- CREATE FAMILY EMI TABLES FOR SUPABASE
-- This script creates the required tables for Family EMI functionality
-- Run this in your Supabase SQL Editor

-- =============================================================================
-- MAIN TABLE: Family Type Prices (Standalone Version)
-- =============================================================================
CREATE TABLE family_type_prices (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    quote_id UUID, -- Nullable for now, can be updated later
    family_type_id VARCHAR(50) NOT NULL,
    family_type_name VARCHAR(255) NOT NULL,
    
    -- Family Composition
    no_of_adults INTEGER NOT NULL,
    no_of_children INTEGER DEFAULT 0,
    no_of_child INTEGER DEFAULT 0,
    no_of_infants INTEGER DEFAULT 0,
    family_count INTEGER NOT NULL,
    
    -- Room and Vehicle Info
    rooms_need INTEGER NOT NULL,
    cab_type VARCHAR(255),
    cab_capacity INTEGER,
    
    -- Calculated Costs
    hotel_cost DECIMAL(10,2) NOT NULL DEFAULT 0,
    vehicle_cost DECIMAL(10,2) NOT NULL DEFAULT 0,
    additional_costs DECIMAL(10,2) NOT NULL DEFAULT 0,
    basic_costs DECIMAL(10,2) DEFAULT 0,
    addon_costs DECIMAL(10,2) DEFAULT 0,
    optional_costs DECIMAL(10,2) DEFAULT 0,
    
    -- Final Pricing
    subtotal DECIMAL(10,2) NOT NULL DEFAULT 0,
    discount_amount DECIMAL(10,2) DEFAULT 0,
    commission_amount DECIMAL(10,2) DEFAULT 0,
    total_price DECIMAL(10,2) NOT NULL DEFAULT 0,
    
    -- EMI Support Fields
    emi_enabled BOOLEAN DEFAULT true,
    min_emi_months INTEGER DEFAULT 3,
    max_emi_months INTEGER DEFAULT 24,
    emi_processing_fee_percent DECIMAL(5,2) DEFAULT 2.5,
    emi_interest_rate_percent DECIMAL(5,2) DEFAULT 12.0,
    
    -- Public Website Support
    is_public_visible BOOLEAN DEFAULT true,
    destination_category VARCHAR(100),
    season_category VARCHAR(50),
    package_duration_days INTEGER,
    public_display_order INTEGER DEFAULT 0,
    
    -- Room Calculation Details
    extra_adults INTEGER DEFAULT 0,
    children_charged INTEGER DEFAULT 0,
    infants_free INTEGER DEFAULT 0,
    room_type VARCHAR(255),
    
    -- Metadata
    baseline_quote_data JSONB,
    quote_mapping_data JSONB,
    calculation_notes TEXT[],
    public_metadata JSONB DEFAULT '{}',
    seo_keywords TEXT[],
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT unique_quote_family UNIQUE (quote_id, family_type_id),
    CONSTRAINT valid_emi_months CHECK (min_emi_months <= max_emi_months),
    CONSTRAINT valid_emi_rates CHECK (emi_interest_rate_percent >= 0 AND emi_processing_fee_percent >= 0)
);

-- =============================================================================
-- EMI PLANS TABLE
-- =============================================================================
CREATE TABLE family_type_emi_plans (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    family_price_id UUID NOT NULL,
    
    -- EMI Configuration
    emi_months INTEGER NOT NULL,
    monthly_amount DECIMAL(10,2) NOT NULL,
    total_amount DECIMAL(10,2) NOT NULL,
    processing_fee DECIMAL(10,2) NOT NULL,
    total_interest DECIMAL(10,2) NOT NULL,
    
    -- EMI Details
    first_payment_amount DECIMAL(10,2),
    subsequent_payment_amount DECIMAL(10,2),
    final_payment_amount DECIMAL(10,2),
    
    -- Customer Savings Display
    savings_vs_full_payment DECIMAL(10,2) DEFAULT 0,
    effective_annual_rate DECIMAL(5,2),
    
    -- Marketing & Display
    is_featured BOOLEAN DEFAULT false,
    marketing_label VARCHAR(100),
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT fk_family_price_id FOREIGN KEY (family_price_id) REFERENCES family_type_prices(id) ON DELETE CASCADE,
    CONSTRAINT valid_emi_months CHECK (emi_months > 0 AND emi_months <= 60),
    CONSTRAINT valid_amounts CHECK (monthly_amount > 0 AND total_amount > 0)
);

-- =============================================================================
-- PUBLIC WEBSITE QUOTES TABLE
-- =============================================================================
CREATE TABLE public_family_quotes (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    
    -- Customer Input
    customer_email VARCHAR(255),
    customer_phone VARCHAR(20),
    customer_name VARCHAR(255),
    
    -- Trip Details
    destination VARCHAR(255) NOT NULL,
    travel_date DATE,
    duration_days INTEGER,
    
    -- Family Composition
    no_of_adults INTEGER NOT NULL,
    no_of_children INTEGER DEFAULT 0,
    no_of_child INTEGER DEFAULT 0,
    no_of_infants INTEGER DEFAULT 0,
    
    -- Matched Results
    matched_family_type_id VARCHAR(50),
    matched_price_id UUID,
    estimated_total_cost DECIMAL(10,2),
    
    -- Selected EMI Plan
    selected_emi_plan_id UUID,
    selected_emi_months INTEGER,
    monthly_emi_amount DECIMAL(10,2),
    
    -- Lead Status
    quote_status VARCHAR(50) DEFAULT 'generated',
    follow_up_date DATE,
    notes TEXT,
    
    -- Session & Tracking
    session_id VARCHAR(255),
    utm_source VARCHAR(100),
    utm_medium VARCHAR(100),
    utm_campaign VARCHAR(100),
    referrer_url TEXT,
    
    -- Communication Log
    emails_sent INTEGER DEFAULT 0,
    last_email_sent TIMESTAMP WITH TIME ZONE,
    whatsapp_sent BOOLEAN DEFAULT false,
    sms_sent BOOLEAN DEFAULT false,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE DEFAULT (NOW() + INTERVAL '30 days'),
    
    -- Constraints
    CONSTRAINT fk_matched_price_id FOREIGN KEY (matched_price_id) REFERENCES family_type_prices(id),
    CONSTRAINT fk_selected_emi_plan_id FOREIGN KEY (selected_emi_plan_id) REFERENCES family_type_emi_plans(id),
    CONSTRAINT valid_family_composition CHECK (no_of_adults > 0)
);

-- =============================================================================
-- INDEXES FOR PERFORMANCE
-- =============================================================================
CREATE INDEX idx_family_type_prices_family_type_id ON family_type_prices(family_type_id);
CREATE INDEX idx_family_type_prices_public_visible ON family_type_prices(is_public_visible) WHERE is_public_visible = true;
CREATE INDEX idx_family_type_prices_destination ON family_type_prices(destination_category);
CREATE INDEX idx_family_type_prices_created_at ON family_type_prices(created_at);

CREATE INDEX idx_emi_plans_family_price_id ON family_type_emi_plans(family_price_id);
CREATE INDEX idx_emi_plans_months ON family_type_emi_plans(emi_months);
CREATE INDEX idx_emi_plans_featured ON family_type_emi_plans(is_featured) WHERE is_featured = true;

CREATE INDEX idx_public_quotes_destination ON public_family_quotes(destination);
CREATE INDEX idx_public_quotes_status ON public_family_quotes(quote_status);
CREATE INDEX idx_public_quotes_created_at ON public_family_quotes(created_at);
CREATE INDEX idx_public_quotes_email ON public_family_quotes(customer_email);

-- =============================================================================
-- UPDATE TRIGGERS
-- =============================================================================
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_family_type_prices_updated_at
    BEFORE UPDATE ON family_type_prices
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_emi_plans_updated_at
    BEFORE UPDATE ON family_type_emi_plans
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_public_quotes_updated_at
    BEFORE UPDATE ON public_family_quotes
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- =============================================================================
-- SAMPLE DATA FOR TESTING
-- =============================================================================
-- Insert some sample family types for testing
INSERT INTO family_type_prices (
    family_type_id, family_type_name, no_of_adults, no_of_children, no_of_child, no_of_infants,
    family_count, rooms_need, cab_type, cab_capacity, destination_category, season_category,
    package_duration_days, hotel_cost, vehicle_cost, additional_costs, subtotal, total_price,
    discount_amount, emi_enabled, is_public_visible, public_display_order
) VALUES 
(
    'COUPLE', 'Romantic Couple Package', 2, 0, 0, 0, 1, 1, 'Sedan', 4, 'Beach', 'Peak',
    5, 25000, 8000, 12000, 45000, 42000, 3000, true, true, 1
),
(
    'FAMILY_4', 'Happy Family (2+2)', 2, 2, 0, 0, 1, 2, 'SUV', 7, 'Hill Station', 'Normal',
    7, 40000, 15000, 23000, 78000, 73000, 5000, true, true, 2
),
(
    'LARGE_FAMILY', 'Big Family (2+4)', 2, 4, 0, 0, 1, 3, 'Tempo Traveller', 12, 'Adventure', 'Normal',
    10, 60000, 25000, 40000, 125000, 117000, 8000, true, true, 3
);

-- Insert sample EMI plans for COUPLE
INSERT INTO family_type_emi_plans (
    family_price_id, emi_months, monthly_amount, total_amount, processing_fee, total_interest,
    savings_vs_full_payment, effective_annual_rate, is_featured, marketing_label
) 
SELECT 
    fp.id,
    3,
    ROUND((fp.total_price * 1.05) / 3, 2),
    ROUND(fp.total_price * 1.05, 2),
    ROUND(fp.total_price * 0.025, 2),
    ROUND(fp.total_price * 0.05, 2),
    0.00,
    12.00,
    true,
    'Quick Pay'
FROM family_type_prices fp
WHERE fp.family_type_id = 'COUPLE';

-- Insert 6-month EMI plans for families
INSERT INTO family_type_emi_plans (
    family_price_id, emi_months, monthly_amount, total_amount, processing_fee, total_interest,
    savings_vs_full_payment, effective_annual_rate, is_featured, marketing_label
) 
SELECT 
    fp.id,
    6,
    ROUND((fp.total_price * 1.08) / 6, 2),
    ROUND(fp.total_price * 1.08, 2),
    ROUND(fp.total_price * 0.025, 2),
    ROUND(fp.total_price * 0.08, 2),
    0.00,
    12.00,
    true,
    'Best Value'
FROM family_type_prices fp
WHERE fp.family_type_id IN ('FAMILY_4', 'LARGE_FAMILY');

-- Insert 12-month EMI plans for families
INSERT INTO family_type_emi_plans (
    family_price_id, emi_months, monthly_amount, total_amount, processing_fee, total_interest,
    savings_vs_full_payment, effective_annual_rate, is_featured, marketing_label
) 
SELECT 
    fp.id,
    12,
    ROUND((fp.total_price * 1.12) / 12, 2),
    ROUND(fp.total_price * 1.12, 2),
    ROUND(fp.total_price * 0.025, 2),
    ROUND(fp.total_price * 0.12, 2),
    0.00,
    12.00,
    false,
    'Low Monthly'
FROM family_type_prices fp
WHERE fp.family_type_id IN ('FAMILY_4', 'LARGE_FAMILY'); 