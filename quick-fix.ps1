# Quick Fix for TripXplo CRM Deployment
param(
    [string]$ServerIP = "*************",
    [string]$ServerUser = "root"
)

Write-Host "🚀 TripXplo CRM - Quick Deployment Fix" -ForegroundColor Green
Write-Host "=====================================" -ForegroundColor Green

# Create SSH directory
$sshDir = Join-Path $env:USERPROFILE ".ssh"
if (-not (Test-Path $sshDir)) {
    New-Item -ItemType Directory -Path $sshDir -Force | Out-Null
    Write-Host "Created .ssh directory" -ForegroundColor Gray
}

# Generate SSH key if it doesn't exist
$keyPath = Join-Path $sshDir "tripxplo_fix"
$pubKeyPath = "$keyPath.pub"

if (-not (Test-Path $keyPath)) {
    Write-Host "🔐 Generating SSH key..." -ForegroundColor Yellow
    & ssh-keygen -t rsa -b 4096 -f $keyPath -N '""' -q
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ SSH key generated!" -ForegroundColor Green
    } else {
        Write-Host "❌ Failed to generate SSH key" -ForegroundColor Red
        exit 1
    }
}

# Setup SSH key authentication
Write-Host "🔑 Setting up SSH key authentication..." -ForegroundColor Yellow
Write-Host "You'll need to enter your server password ONCE:" -ForegroundColor Cyan

if (Test-Path $pubKeyPath) {
    $pubKeyContent = Get-Content $pubKeyPath -Raw
    $sshCommand = "mkdir -p ~/.ssh; echo '$pubKeyContent' >> ~/.ssh/authorized_keys; chmod 600 ~/.ssh/authorized_keys; chmod 700 ~/.ssh"
    
    # Copy public key to server
    ssh $ServerUser@$ServerIP $sshCommand
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ SSH key authentication enabled!" -ForegroundColor Green
    } else {
        Write-Host "❌ Failed to setup SSH key" -ForegroundColor Red
        exit 1
    }
}

# Test SSH connection
Write-Host "🧪 Testing SSH connection..." -ForegroundColor Yellow
ssh -i $keyPath -o StrictHostKeyChecking=no $ServerUser@$ServerIP "echo 'SSH connection successful!'"

if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ SSH connection failed" -ForegroundColor Red
    exit 1
}

Write-Host "✅ SSH connection working!" -ForegroundColor Green

# Create server fix script
Write-Host "🔧 Creating server fix script..." -ForegroundColor Yellow

$fixScript = @'
#!/bin/bash
set -e

echo "🔧 Fixing TripXplo CRM deployment..."

# Navigate to CRM directory
cd /var/www/crm
echo "Current directory: $(pwd)"
echo "Current contents:"
ls -la

# Create backup
BACKUP_DIR="/var/www/crm.backup.$(date +%Y%m%d_%H%M%S)"
cp -r /var/www/crm $BACKUP_DIR
echo "💾 Backup created: $BACKUP_DIR"

# Move files if nested directory exists
if [ -d "TripXplo-CRM" ]; then
    echo "📁 Moving files from TripXplo-CRM to root..."
    cp -r TripXplo-CRM/* .
    rm -rf TripXplo-CRM
    echo "✅ Files moved successfully"
else
    echo "ℹ️ No nested TripXplo-CRM directory found"
fi

# Set permissions
echo "🔐 Setting permissions..."
chown -R www-data:www-data /var/www/crm
chmod -R 755 /var/www/crm

# Check for index.html
if [ -f "index.html" ]; then
    echo "✅ index.html found in correct location"
else
    echo "❌ index.html not found"
    echo "Contents:"
    ls -la
fi

# Update Nginx config
echo "🌐 Updating Nginx configuration..."
cat > /etc/nginx/sites-available/crm.tripxplo.com << 'EOF'
server {
    listen 80;
    server_name crm.tripxplo.com www.crm.tripxplo.com;
    root /var/www/crm;
    index index.html;
    
    error_log /var/log/nginx/crm_error.log;
    access_log /var/log/nginx/crm_access.log;
    
    location / {
        try_files $uri $uri/ /index.html;
    }
    
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot|map)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
EOF

# Enable site and reload
ln -sf /etc/nginx/sites-available/crm.tripxplo.com /etc/nginx/sites-enabled/

echo "🧪 Testing Nginx config..."
if nginx -t; then
    systemctl reload nginx
    echo "✅ Nginx reloaded successfully"
else
    echo "❌ Nginx config error"
    exit 1
fi

echo "🧪 Testing site..."
sleep 2
curl -I http://crm.tripxplo.com | head -5

echo "✅ Fix completed!"
'@

# Save script to temp file
$scriptPath = "temp-fix.sh"
$fixScript | Out-File -FilePath $scriptPath -Encoding UTF8

# Upload script to server
Write-Host "📤 Uploading fix script..." -ForegroundColor Yellow
scp -i $keyPath -o StrictHostKeyChecking=no $scriptPath "${ServerUser}@${ServerIP}:/tmp/fix.sh"

if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Failed to upload script" -ForegroundColor Red
    exit 1
}

# Execute script on server
Write-Host "🚀 Executing fix on server..." -ForegroundColor Yellow
ssh -i $keyPath -o StrictHostKeyChecking=no $ServerUser@$ServerIP "chmod +x /tmp/fix.sh && /tmp/fix.sh"

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ Server fix completed!" -ForegroundColor Green
} else {
    Write-Host "❌ Server fix had issues" -ForegroundColor Yellow
}

# Clean up
Remove-Item $scriptPath -ErrorAction SilentlyContinue

# Test the website
Write-Host "🧪 Testing website..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "http://crm.tripxplo.com" -Method Head -TimeoutSec 10
    if ($response.StatusCode -eq 200) {
        Write-Host "🎉 SUCCESS! Website is working!" -ForegroundColor Green
    } else {
        Write-Host "⚠️ Website returned status: $($response.StatusCode)" -ForegroundColor Yellow
    }
} catch {
    Write-Host "⚠️ Could not test from here, but check: http://crm.tripxplo.com" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "🎉 Deployment fix complete!" -ForegroundColor Green
Write-Host "🌐 Test your site: https://crm.tripxplo.com" -ForegroundColor Cyan
Write-Host ""
Write-Host "SSH Key for GitHub Actions:" -ForegroundColor Yellow
Write-Host "File: $keyPath" -ForegroundColor Gray
Write-Host "Add this as SSH_PRIVATE_KEY secret in GitHub:" -ForegroundColor White
Write-Host "=" * 50 -ForegroundColor Yellow
Get-Content $keyPath
Write-Host "=" * 50 -ForegroundColor Yellow 