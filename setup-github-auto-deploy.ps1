# Setup GitHub Actions Auto-Deployment for TripXplo CRM
# This script sets up automated deployment to /var/www/crm/

Write-Host "🚀 Setting up GitHub Actions Auto-Deployment" -ForegroundColor Green
Write-Host "=============================================" -ForegroundColor Green

$SERVER_IP = "*************"
$SERVER_USER = "root"
$DEPLOYMENT_KEY = "github_actions_deploy"

# Step 1: Create SSH keys for GitHub Actions
Write-Host ""
Write-Host "🔐 Step 1: Creating SSH keys for GitHub Actions..." -ForegroundColor Yellow

$sshDir = "$env:USERPROFILE\.ssh"
$privateKey = "$sshDir\$DEPLOYMENT_KEY"
$publicKey = "$privateKey.pub"

# Create .ssh directory if it doesn't exist
if (-not (Test-Path $sshDir)) {
    New-Item -ItemType Directory -Path $sshDir -Force | Out-Null
}

# Generate SSH key pair for deployment
if (-not (Test-Path $privateKey)) {
    Write-Host "Generating SSH key pair for GitHub Actions..." -ForegroundColor Gray
    & ssh-keygen -t rsa -b 4096 -f $privateKey -N '""' -C "github-actions-tripxplo-crm" -q
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ SSH key pair generated successfully!" -ForegroundColor Green
    } else {
        Write-Host "❌ Failed to generate SSH key pair" -ForegroundColor Red
        exit 1
    }
} else {
    Write-Host "✅ SSH key pair already exists" -ForegroundColor Green
}

# Step 2: Setup passwordless SSH access
Write-Host ""
Write-Host "🔑 Step 2: Setting up passwordless SSH access..." -ForegroundColor Yellow
Write-Host "You'll need to enter your server password to enable automated deployment:" -ForegroundColor Cyan

if (Test-Path $publicKey) {
    $publicKeyContent = Get-Content $publicKey -Raw
    $sshCommand = "mkdir -p ~/.ssh && echo '$publicKeyContent' >> ~/.ssh/authorized_keys && chmod 600 ~/.ssh/authorized_keys && chmod 700 ~/.ssh"
    
    # Copy public key to server
    & ssh $SERVER_USER@$SERVER_IP $sshCommand
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ SSH key installed on server!" -ForegroundColor Green
    } else {
        Write-Host "❌ Failed to install SSH key on server" -ForegroundColor Red
        Write-Host "💡 You may need to manually copy the key" -ForegroundColor Yellow
        exit 1
    }
} else {
    Write-Host "❌ Public key file not found" -ForegroundColor Red
    exit 1
}

# Step 3: Test SSH connection
Write-Host ""
Write-Host "🧪 Step 3: Testing SSH connection..." -ForegroundColor Yellow

& ssh -i $privateKey -o StrictHostKeyChecking=no $SERVER_USER@$SERVER_IP "echo 'SSH connection successful for GitHub Actions!'"

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ SSH connection test successful!" -ForegroundColor Green
} else {
    Write-Host "❌ SSH connection test failed" -ForegroundColor Red
    exit 1
}

# Step 4: Create/update deployment directories on server
Write-Host ""
Write-Host "📁 Step 4: Setting up deployment directories..." -ForegroundColor Yellow

$setupCommands = @"
# Create necessary directories
mkdir -p /var/www/crm
mkdir -p /var/log/nginx

# Set proper ownership
chown -R www-data:www-data /var/www/crm

# Ensure nginx is running
systemctl enable nginx
systemctl start nginx

echo "Server directories ready for deployment"
"@

& ssh -i $privateKey -o StrictHostKeyChecking=no $SERVER_USER@$SERVER_IP $setupCommands

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ Server directories configured!" -ForegroundColor Green
} else {
    Write-Host "❌ Failed to configure server directories" -ForegroundColor Red
}

# Step 5: Display GitHub Secrets Configuration
Write-Host ""
Write-Host "🔐 Step 5: GitHub Repository Secrets Configuration" -ForegroundColor Yellow
Write-Host ""
Write-Host "Go to your GitHub repository and add these secrets:" -ForegroundColor Cyan
Write-Host "https://github.com/YOUR_USERNAME/TripXplo-CRM/settings/secrets/actions" -ForegroundColor Blue
Write-Host ""

Write-Host "=========================================" -ForegroundColor Magenta
Write-Host "REQUIRED GITHUB SECRETS" -ForegroundColor Magenta
Write-Host "=========================================" -ForegroundColor Magenta
Write-Host ""

# Server configuration secrets
Write-Host "📡 SERVER CONFIGURATION:" -ForegroundColor Yellow
Write-Host ""
Write-Host "SECRET NAME: SERVER_IP" -ForegroundColor White
Write-Host "VALUE: $SERVER_IP" -ForegroundColor Gray
Write-Host ""

Write-Host "SECRET NAME: SSH_PRIVATE_KEY" -ForegroundColor White
Write-Host "VALUE: (Copy the private key below)" -ForegroundColor Gray
Write-Host ""
Write-Host "--- START SSH PRIVATE KEY ---" -ForegroundColor Red
Get-Content $privateKey
Write-Host "--- END SSH PRIVATE KEY ---" -ForegroundColor Red
Write-Host ""

# Environment secrets
Write-Host "🌍 ENVIRONMENT VARIABLES:" -ForegroundColor Yellow
Write-Host ""
$envSecrets = @(
    "VITE_SUPABASE_URL_CRM",
    "VITE_SUPABASE_ANON_KEY_CRM", 
    "VITE_SUPABASE_URL_QUOTE",
    "VITE_SUPABASE_ANON_KEY_QUOTE",
    "VITE_SUPABASE_URL",
    "VITE_SUPABASE_ANON_KEY",
    "EMAIL_USER",
    "EMAIL_PASS"
)

foreach ($secret in $envSecrets) {
    Write-Host "SECRET NAME: $secret" -ForegroundColor White
    Write-Host "VALUE: [Your actual $secret value]" -ForegroundColor Gray
    Write-Host ""
}

Write-Host "=========================================" -ForegroundColor Magenta
Write-Host ""

# Step 6: Instructions for next steps
Write-Host "📝 Next Steps:" -ForegroundColor Green
Write-Host ""
Write-Host "1. 📋 Copy the SSH private key above to GitHub Secrets" -ForegroundColor White
Write-Host "2. 🔑 Add all environment variable secrets listed above" -ForegroundColor White
Write-Host "3. 🧪 Test deployment by pushing code:" -ForegroundColor White
Write-Host "   git add ." -ForegroundColor Gray
Write-Host "   git commit -m 'Test auto-deployment'" -ForegroundColor Gray
Write-Host "   git push origin master" -ForegroundColor Gray
Write-Host ""
Write-Host "4. 🌐 Check deployment at: https://crm.tripxplo.com" -ForegroundColor White
Write-Host ""

# Step 7: Save configuration info
$configInfo = @"
TripXplo CRM Auto-Deployment Configuration
==========================================
Generated: $(Get-Date)

Server IP: $SERVER_IP
SSH Private Key: $privateKey
SSH Public Key: $publicKey
Deployment Target: /var/www/crm/

GitHub Repository: https://github.com/YOUR_USERNAME/TripXplo-CRM
GitHub Actions: https://github.com/YOUR_USERNAME/TripXplo-CRM/actions
Live Site: https://crm.tripxplo.com

Required GitHub Secrets:
- SERVER_IP: $SERVER_IP
- SSH_PRIVATE_KEY: [Content of $privateKey]
- VITE_SUPABASE_URL_CRM: [Your CRM database URL]
- VITE_SUPABASE_ANON_KEY_CRM: [Your CRM anon key]
- VITE_SUPABASE_URL_QUOTE: [Your quote database URL] 
- VITE_SUPABASE_ANON_KEY_QUOTE: [Your quote anon key]
- VITE_SUPABASE_URL: [Your main Supabase URL]
- VITE_SUPABASE_ANON_KEY: [Your main anon key]
- EMAIL_USER: [Your email user]
- EMAIL_PASS: [Your email password]
"@

$configInfo | Out-File -FilePath "github-actions-config.txt" -Encoding UTF8

Write-Host "📄 Configuration saved to: github-actions-config.txt" -ForegroundColor Green
Write-Host ""
Write-Host "🎉 Auto-deployment setup complete!" -ForegroundColor Green
Write-Host "🔗 Add the secrets above to GitHub and test deployment!" -ForegroundColor Cyan 