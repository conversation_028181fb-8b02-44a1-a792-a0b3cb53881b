import React, { useState, useEffect } from 'react';
import { 
  CreditCard, 
  DollarSign, 
  AlertCircle, 
  Clock, 
  Search,
  Filter,
  Download,
  Eye,
  Settings,
  Bell,
  TrendingUp,
  Package,
  Users
} from 'lucide-react';
import FamilyEMIPackages from './FamilyEMIPackages';
import {
  fetchVisitedCustomers,
  fetchEMITransactions,
  shouldUseDirectDatabase,
  getApiBaseUrl,
  debugDatabaseStatus,
  type VisitedCustomer
} from '../utils/databaseService';
import { insertAllSampleData, clearAllTestData } from '../utils/insertTestData';

interface PrepaidEMITransaction {
  id: string;
  booking_reference: string;
  customer_name: string;
  customer_phone: string;
  customer_email?: string;
  package_name: string;
  advance_payment_amount: number;
  advance_payment_status: 'pending' | 'completed' | 'failed';
  advance_payment_date?: string;
  total_emi_amount: number;
  monthly_emi_amount: number;
  remaining_emi_months: number;
  next_emi_due_date: string;
  total_paid_amount: number;
  pending_amount: number;
  payment_status: 'active' | 'completed' | 'defaulted' | 'cancelled';
  auto_debit_enabled: boolean;
  payment_method: string;
  created_at: string;
}

interface EMISettings {
  default_advance_percent: number;
  default_discount_percent: number;
  default_processing_fee: number;
  reminder_days_before: number;
  auto_debit_enabled: boolean;
  late_fee_percent: number;
  grace_period_days: number;
}

const PrepaidEMIManagement: React.FC = () => {
  const [transactions, setTransactions] = useState<PrepaidEMITransaction[]>([]);
  const [filteredTransactions, setFilteredTransactions] = useState<PrepaidEMITransaction[]>([]);
  const [visitedCustomers, setVisitedCustomers] = useState<VisitedCustomer[]>([]);
  const [filteredCustomers, setFilteredCustomers] = useState<VisitedCustomer[]>([]);
  const [loading, setLoading] = useState(true);
  const [customersLoading, setCustomersLoading] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [customerSearchQuery, setCustomerSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [dueDateFilter, setDueDateFilter] = useState('all');
  const [selectedTransaction, setSelectedTransaction] = useState<PrepaidEMITransaction | null>(null);
  const [showSettings, setShowSettings] = useState(false);
  const [activeTab, setActiveTab] = useState('transactions');
  const [showDebugPanel, setShowDebugPanel] = useState(false);
  const [emiSettings, setEmiSettings] = useState<EMISettings>({
    default_advance_percent: 20,
    default_discount_percent: 2.5,
    default_processing_fee: 1.0,
    reminder_days_before: 3,
    auto_debit_enabled: true,
    late_fee_percent: 2.0,
    grace_period_days: 5
  });

  useEffect(() => {
    console.log('🚀 PrepaidEMIManagement component mounted');
    console.log('📊 Configuration:', {
      hostname: window.location.hostname,
      shouldUseDirectDB: shouldUseDirectDatabase(),
      apiBaseUrl: getApiBaseUrl()
    });

    loadTransactions();
    loadEMISettings();
  }, []);

  useEffect(() => {
    if (activeTab === 'visited-customers' && visitedCustomers.length === 0) {
      loadVisitedCustomers();
    }
  }, [activeTab]);

  useEffect(() => {
    let filtered = visitedCustomers;

    if (customerSearchQuery) {
      filtered = filtered.filter(
        customer =>
          customer.customer_name.toLowerCase().includes(customerSearchQuery.toLowerCase()) ||
          customer.customer_email.toLowerCase().includes(customerSearchQuery.toLowerCase()) ||
          customer.customer_phone.includes(customerSearchQuery) ||
          customer.destination.toLowerCase().includes(customerSearchQuery.toLowerCase())
      );
    }

    setFilteredCustomers(filtered);
  }, [visitedCustomers, customerSearchQuery]);

  useEffect(() => {
    let filtered = transactions;

    if (searchQuery) {
      filtered = filtered.filter(
        t =>
          t.booking_reference.toLowerCase().includes(searchQuery.toLowerCase()) ||
          t.customer_name.toLowerCase().includes(searchQuery.toLowerCase()) ||
          t.customer_phone.includes(searchQuery) ||
          (t.customer_email && t.customer_email.toLowerCase().includes(searchQuery.toLowerCase())) ||
          t.package_name.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    if (statusFilter !== 'all') {
      filtered = filtered.filter(t => t.payment_status === statusFilter);
    }

    if (dueDateFilter !== 'all') {
      const today = new Date();
      const nextWeek = new Date(today.getTime() + 7 * 24 * 60 * 60 * 1000);
      
      filtered = filtered.filter(t => {
        const dueDate = new Date(t.next_emi_due_date);
        switch (dueDateFilter) {
          case 'overdue':
            return dueDate < today;
          case 'due_this_week':
            return dueDate >= today && dueDate <= nextWeek;
          case 'advance_pending':
            return t.advance_payment_status === 'pending';
          default:
            return true;
        }
      });
    }

    setFilteredTransactions(filtered);
  }, [transactions, searchQuery, statusFilter, dueDateFilter]);

  const loadTransactions = async () => {
    try {
      setLoading(true);

      if (shouldUseDirectDatabase()) {
        // Use direct database calls for production
        const emiTransactions = await fetchEMITransactions();
        
        // Convert EMITransaction to PrepaidEMITransaction
        const transactions: PrepaidEMITransaction[] = emiTransactions.map(transaction => ({
          id: transaction.id,
          booking_reference: transaction.booking_reference || `TXP-${transaction.id.slice(0, 8)}`,
          customer_name: transaction.customer_name || 'N/A',
          customer_phone: transaction.customer_phone || 'N/A',
          customer_email: transaction.customer_email,
          package_name: transaction.package_name || 'N/A',
          advance_payment_amount: transaction.advance_payment_amount,
          advance_payment_status: transaction.advance_payment_status as 'pending' | 'completed' | 'failed',
          advance_payment_date: transaction.advance_payment_date,
          total_emi_amount: transaction.total_emi_amount,
          monthly_emi_amount: transaction.monthly_emi_amount,
          remaining_emi_months: transaction.remaining_emi_months,
          next_emi_due_date: transaction.next_emi_due_date || new Date().toISOString(),
          total_paid_amount: transaction.total_paid_amount,
          pending_amount: transaction.pending_amount,
          payment_status: transaction.payment_status as 'active' | 'completed' | 'defaulted' | 'cancelled',
          auto_debit_enabled: transaction.auto_debit_enabled,
          payment_method: transaction.payment_method,
          created_at: transaction.created_at
        }));
        
        setTransactions(transactions);
      } else {
        // Use API server for local development
        const apiUrl = `${getApiBaseUrl()}/emi-transactions`;

        console.log('🌐 Fetching EMI transactions from API:', apiUrl);

        const response = await fetch(apiUrl, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        });

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();

        if (!data.success) {
          throw new Error(data.error || 'Failed to fetch transactions');
        }

        console.log('✅ Successfully loaded EMI transactions via API:', data.transactions?.length || 0);
        setTransactions(data.transactions || []);
      }

    } catch (error) {
      console.error('❌ Error loading transactions:', error);

      // Set empty array instead of mock data - let the user know there's an issue
      setTransactions([]);

      // Show user-friendly error message
      alert('Failed to load EMI transactions. Please check your connection and try again.');
    } finally {
      setLoading(false);
    }
  };

  const loadVisitedCustomers = async () => {
    try {
      setCustomersLoading(true);

      if (shouldUseDirectDatabase()) {
        // Use direct database calls for production
        console.log('🌐 Using direct database access for visited customers...');
        const customers = await fetchVisitedCustomers();
        setVisitedCustomers(customers);
        console.log('✅ Successfully loaded visited customers via direct database:', customers.length);
      } else {
        // Use API server for local development
        const apiUrl = `${getApiBaseUrl()}/visited-customers`;

        console.log('🌐 Fetching visited customers from API:', apiUrl);

        const response = await fetch(apiUrl, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        });

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();

        if (!data.success) {
          throw new Error(data.error || 'Failed to fetch visited customers');
        }

        console.log('✅ Successfully loaded visited customers via API:', data.data?.length || 0);
        setVisitedCustomers(data.data || []);
      }

    } catch (error) {
      console.error('❌ Error loading visited customers:', error);

      // Set empty array and show error message
      setVisitedCustomers([]);

      // Show user-friendly error message
      alert('Failed to load visited customers. Please check your connection and try again.');
    } finally {
      setCustomersLoading(false);
    }
  };

  const loadEMISettings = async () => {
    try {
      // API call to fetch EMI settings
    } catch (error) {
      console.error('Error loading EMI settings:', error);
    }
  };

  const updateEMISettings = async (newSettings: EMISettings) => {
    try {
      setEmiSettings(newSettings);
      setShowSettings(false);
    } catch (error) {
      console.error('Error updating EMI settings:', error);
    }
  };

  const sendPaymentReminder = async (_transactionId: string) => {
    try {
      alert('Payment reminder sent successfully!');
    } catch (error) {
      console.error('Error sending reminder:', error);
    }
  };

  const processPayment = async (_transactionId: string, _amount: number) => {
    try {
      alert('Payment processed successfully!');
      loadTransactions();
    } catch (error) {
      console.error('Error processing payment:', error);
    }
  };

  const handleInsertTestData = async () => {
    try {
      await insertAllSampleData();
      alert('Test data inserted successfully!');
      loadTransactions();
      loadVisitedCustomers();
    } catch (error) {
      console.error('Error inserting test data:', error);
      alert('Failed to insert test data. Check console for details.');
    }
  };

  const handleClearTestData = async () => {
    try {
      await clearAllTestData();
      alert('Test data cleared successfully!');
      loadTransactions();
      loadVisitedCustomers();
    } catch (error) {
      console.error('Error clearing test data:', error);
      alert('Failed to clear test data. Check console for details.');
    }
  };

  const handleDebugDatabase = async () => {
    await debugDatabaseStatus();
    alert('Database status logged to console. Check browser console for details.');
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'text-green-600 bg-green-100';
      case 'active': return 'text-blue-600 bg-blue-100';
      case 'pending': return 'text-yellow-600 bg-yellow-100';
      case 'defaulted': return 'text-red-600 bg-red-100';
      case 'cancelled': return 'text-gray-600 bg-gray-100';
      case 'failed': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const formatCurrency = (amount: number) => {
    return `₹${amount.toLocaleString()}`;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-IN');
  };

  const isOverdue = (dueDate: string) => {
    return new Date(dueDate) < new Date();
  };

  const stats = {
    totalActive: transactions.filter(t => t.payment_status === 'active').length,
    totalPending: transactions.filter(t => t.advance_payment_status === 'pending').length,
    totalOverdue: transactions.filter(t => isOverdue(t.next_emi_due_date)).length,
    totalRevenue: transactions.reduce((sum, t) => sum + t.total_paid_amount, 0),
    pendingRevenue: transactions.reduce((sum, t) => sum + t.pending_amount, 0)
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto p-6">
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-2xl font-bold text-gray-800">Prepaid EMI Management</h1>
          <p className="text-gray-600">Manage prepaid EMI plans, payments, and customer communications</p>
        </div>
        <div className="flex gap-3">
          <button
            onClick={() => setShowDebugPanel(!showDebugPanel)}
            className="px-4 py-2 bg-yellow-100 text-yellow-700 rounded-lg hover:bg-yellow-200 transition-colors flex items-center gap-2"
          >
            <Settings className="w-4 h-4" />
            Debug
          </button>
          <button
            onClick={() => setShowSettings(true)}
            className="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors flex items-center gap-2"
          >
            <Settings className="w-4 h-4" />
            Settings
          </button>
          <button className="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-dark transition-colors flex items-center gap-2">
            <Download className="w-4 h-4" />
            Export Report
          </button>
        </div>
      </div>

      {/* Debug Panel */}
      {showDebugPanel && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6 mb-8">
          <h3 className="text-lg font-semibold text-yellow-800 mb-4">Debug Panel</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <button
              onClick={handleDebugDatabase}
              className="px-4 py-2 bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200 transition-colors"
            >
              Check Database Status
            </button>
            <button
              onClick={handleInsertTestData}
              className="px-4 py-2 bg-green-100 text-green-700 rounded-lg hover:bg-green-200 transition-colors"
            >
              Insert Test Data
            </button>
            <button
              onClick={handleClearTestData}
              className="px-4 py-2 bg-red-100 text-red-700 rounded-lg hover:bg-red-200 transition-colors"
            >
              Clear Test Data
            </button>
            <button
              onClick={() => {
                console.log('Current environment:', {
                  hostname: window.location.hostname,
                  shouldUseDirectDB: shouldUseDirectDatabase(),
                  apiBaseUrl: getApiBaseUrl()
                });
                alert('Environment info logged to console');
              }}
              className="px-4 py-2 bg-purple-100 text-purple-700 rounded-lg hover:bg-purple-200 transition-colors"
            >
              Check Environment
            </button>
            <button
              onClick={() => {
                // Test Family EMI Packages data loading
                console.log('🧪 Testing Family EMI Packages data loading...');
                const testQuery = async () => {
                  try {
                    const { createClient } = await import('@supabase/supabase-js');
                    const quoteConfig = await import('../config/env').then(m => m.getQuoteSupabaseConfig());
                    const quoteSupabase = createClient(quoteConfig.url, quoteConfig.anonKey);
                    
                    // Test basic query
                    const { data, error } = await quoteSupabase
                      .from('family_type_prices')
                      .select('id, family_type_name, emi_enabled, is_public_visible')
                      .limit(3);
                    
                    console.log('🧪 Family EMI Packages test result:', { data, error });
                    alert(`Test completed. Found ${data?.length || 0} packages. Check console for details.`);
                  } catch (err) {
                    console.error('🧪 Test failed:', err);
                    alert('Test failed. Check console for error.');
                  }
                };
                testQuery();
              }}
              className="px-4 py-2 bg-indigo-100 text-indigo-700 rounded-lg hover:bg-indigo-200 transition-colors"
            >
              Test Family EMI Data
            </button>
          </div>
          <div className="mt-4 text-sm text-yellow-700">
            <p><strong>Current Mode:</strong> {shouldUseDirectDatabase() ? 'Direct Database' : 'API Server'}</p>
            <p><strong>Hostname:</strong> {window.location.hostname}</p>
            <p><strong>Transactions:</strong> {transactions.length} loaded</p>
            <p><strong>Customers:</strong> {visitedCustomers.length} loaded</p>
          </div>
        </div>
      )}

      {/* Tab Navigation */}
      <div className="border-b border-gray-200 mb-8">
        <nav className="-mb-px flex space-x-8">
          <button
            onClick={() => setActiveTab('transactions')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'transactions'
                ? 'border-primary text-primary'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <div className="flex items-center gap-2">
              <CreditCard className="w-4 h-4" />
              EMI Transactions
            </div>
          </button>
          <button
            onClick={() => setActiveTab('packages')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'packages'
                ? 'border-primary text-primary'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <div className="flex items-center gap-2">
              <Package className="w-4 h-4" />
              Available Family EMI Packages
            </div>
          </button>
          <button
            onClick={() => setActiveTab('visited-customers')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'visited-customers'
                ? 'border-primary text-primary'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <div className="flex items-center gap-2">
              <Users className="w-4 h-4" />
              Visited Customers
            </div>
          </button>
        </nav>
      </div>

      {activeTab === 'transactions' && (
        <>
          <div className="grid grid-cols-1 md:grid-cols-5 gap-6 mb-8">
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Active EMIs</p>
              <p className="text-2xl font-bold text-blue-600">{stats.totalActive}</p>
            </div>
            <TrendingUp className="w-8 h-8 text-blue-600" />
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Pending Advance</p>
              <p className="text-2xl font-bold text-yellow-600">{stats.totalPending}</p>
            </div>
            <Clock className="w-8 h-8 text-yellow-600" />
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Overdue</p>
              <p className="text-2xl font-bold text-red-600">{stats.totalOverdue}</p>
            </div>
            <AlertCircle className="w-8 h-8 text-red-600" />
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Revenue Collected</p>
              <p className="text-2xl font-bold text-green-600">{formatCurrency(stats.totalRevenue)}</p>
            </div>
            <DollarSign className="w-8 h-8 text-green-600" />
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Pending Revenue</p>
              <p className="text-2xl font-bold text-orange-600">{formatCurrency(stats.pendingRevenue)}</p>
            </div>
            <CreditCard className="w-8 h-8 text-orange-600" />
          </div>
        </div>
      </div>

      <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200 mb-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              placeholder="Search by booking ref, customer, phone, email..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
            />
          </div>

          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
          >
            <option value="all">All Status</option>
            <option value="active">Active</option>
            <option value="completed">Completed</option>
            <option value="defaulted">Defaulted</option>
            <option value="cancelled">Cancelled</option>
          </select>

          <select
            value={dueDateFilter}
            onChange={(e) => setDueDateFilter(e.target.value)}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
          >
            <option value="all">All Due Dates</option>
            <option value="overdue">Overdue</option>
            <option value="due_this_week">Due This Week</option>
            <option value="advance_pending">Advance Pending</option>
          </select>

          <div className="flex items-center text-sm text-gray-600">
            <Filter className="w-4 h-4 mr-2" />
            Showing {filteredTransactions.length} of {transactions.length} transactions
          </div>
        </div>
      </div>

      <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Booking Details
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Customer
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Advance Payment
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  EMI Details
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredTransactions.map((transaction) => (
                <tr key={transaction.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div className="text-sm font-medium text-gray-900">
                        {transaction.booking_reference}
                      </div>
                      <div className="text-sm text-gray-500">
                        {transaction.package_name}
                      </div>
                    </div>
                  </td>
                  
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div className="text-sm font-medium text-gray-900">
                        {transaction.customer_name}
                      </div>
                      <div className="text-sm text-gray-500">
                        {transaction.customer_phone}
                      </div>
                    </div>
                  </td>

                  <td className="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div className="text-sm font-medium text-gray-900">
                        {formatCurrency(transaction.advance_payment_amount)}
                      </div>
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(transaction.advance_payment_status)}`}>
                        {transaction.advance_payment_status}
                      </span>
                    </div>
                  </td>

                  <td className="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div className="text-sm font-medium text-gray-900">
                        {formatCurrency(transaction.monthly_emi_amount)}/month
                      </div>
                      <div className="text-sm text-gray-500">
                        {transaction.remaining_emi_months} months left
                      </div>
                      <div className={`text-sm ${isOverdue(transaction.next_emi_due_date) ? 'text-red-600 font-medium' : 'text-gray-500'}`}>
                        Next: {formatDate(transaction.next_emi_due_date)}
                      </div>
                    </div>
                  </td>

                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(transaction.payment_status)}`}>
                      {transaction.payment_status}
                    </span>
                    {transaction.auto_debit_enabled && (
                      <div className="text-xs text-green-600 mt-1">Auto-debit enabled</div>
                    )}
                  </td>

                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex space-x-2">
                      <button
                        onClick={() => setSelectedTransaction(transaction)}
                        className="text-primary hover:text-primary-dark"
                      >
                        <Eye className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => sendPaymentReminder(transaction.id)}
                        className="text-blue-600 hover:text-blue-800"
                      >
                        <Bell className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => processPayment(transaction.id, transaction.monthly_emi_amount)}
                        className="text-green-600 hover:text-green-800"
                      >
                        <CreditCard className="w-4 h-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
        </>
      )}

      {activeTab === 'packages' && (
        <FamilyEMIPackages />
      )}

      {activeTab === 'visited-customers' && (
        <>
          {/* Search Bar for Customers */}
          <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200 mb-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <input
                  type="text"
                  placeholder="Search by name, email, phone, or destination..."
                  value={customerSearchQuery}
                  onChange={(e) => setCustomerSearchQuery(e.target.value)}
                  className="pl-10 w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                />
              </div>
              <div className="flex items-center text-sm text-gray-600">
                <Users className="w-4 h-4 mr-2" />
                Showing {filteredCustomers.length} of {visitedCustomers.length} customers
              </div>
            </div>
          </div>

          {/* Customers Table */}
          {customersLoading ? (
            <div className="flex items-center justify-center h-64">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            </div>
          ) : (
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Customer Details
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Contact Information
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Trip Details
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Cost & EMI
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Status
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {filteredCustomers.length === 0 ? (
                      <tr>
                        <td colSpan={5} className="px-6 py-12 text-center text-gray-500">
                          {visitedCustomers.length === 0 ? 'No customer data available' : 'No customers match your search criteria'}
                        </td>
                      </tr>
                    ) : (
                      filteredCustomers.map((customer) => (
                        <tr key={customer.id} className="hover:bg-gray-50">
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div>
                              <div className="text-sm font-medium text-gray-900">
                                {customer.customer_name}
                              </div>
                              <div className="text-sm text-gray-500">
                                ID: {customer.id.substring(0, 8)}...
                              </div>
                            </div>
                          </td>

                          <td className="px-6 py-4 whitespace-nowrap">
                            <div>
                              <div className="text-sm font-medium text-gray-900">
                                {customer.customer_email}
                              </div>
                              <div className="text-sm text-gray-500">
                                {customer.customer_phone}
                              </div>
                            </div>
                          </td>

                          <td className="px-6 py-4 whitespace-nowrap">
                            <div>
                              <div className="text-sm font-medium text-gray-900">
                                {customer.destination}
                              </div>
                              <div className="text-sm text-gray-500">
                                {customer.travel_date ? formatDate(customer.travel_date) : 'Date not specified'}
                              </div>
                            </div>
                          </td>

                          <td className="px-6 py-4 whitespace-nowrap">
                            <div>
                              <div className="text-sm font-medium text-gray-900">
                                {customer.estimated_total_cost ? formatCurrency(customer.estimated_total_cost) : 'N/A'}
                              </div>
                              <div className="text-sm text-gray-500">
                                {customer.selected_emi_months > 0 && customer.monthly_emi_amount > 0
                                  ? `${customer.selected_emi_months} months @ ${formatCurrency(customer.monthly_emi_amount)}/mo`
                                  : 'EMI not selected'
                                }
                              </div>
                            </div>
                          </td>

                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(customer.quote_status || 'generated')}`}>
                              {customer.quote_status || 'generated'}
                            </span>
                            <div className="text-xs text-gray-500 mt-1">
                              {formatDate(customer.created_at)}
                            </div>
                          </td>
                        </tr>
                      ))
                    )}
                  </tbody>
                </table>
              </div>
            </div>
          )}
        </>
      )}

      {selectedTransaction && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-xl font-bold text-gray-800">Transaction Details</h2>
                <button
                  onClick={() => setSelectedTransaction(null)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  ×
                </button>
              </div>

              <div className="grid grid-cols-2 gap-6">
                <div>
                  <h3 className="font-semibold text-gray-800 mb-3">Booking Information</h3>
                  <div className="space-y-2 text-sm">
                    <div><span className="text-gray-600">Reference:</span> {selectedTransaction.booking_reference}</div>
                    <div><span className="text-gray-600">Package:</span> {selectedTransaction.package_name}</div>
                    <div><span className="text-gray-600">Created:</span> {formatDate(selectedTransaction.created_at)}</div>
                  </div>
                </div>

                <div>
                  <h3 className="font-semibold text-gray-800 mb-3">Customer Information</h3>
                  <div className="space-y-2 text-sm">
                    <div><span className="text-gray-600">Name:</span> {selectedTransaction.customer_name}</div>
                    <div><span className="text-gray-600">Phone:</span> {selectedTransaction.customer_phone}</div>
                    {selectedTransaction.customer_email && (
                      <div><span className="text-gray-600">Email:</span> {selectedTransaction.customer_email}</div>
                    )}
                    <div><span className="text-gray-600">Payment Method:</span> {selectedTransaction.payment_method}</div>
                  </div>
                </div>

                <div>
                  <h3 className="font-semibold text-gray-800 mb-3">Payment Details</h3>
                  <div className="space-y-2 text-sm">
                    <div><span className="text-gray-600">Advance Payment:</span> {formatCurrency(selectedTransaction.advance_payment_amount)}</div>
                    <div><span className="text-gray-600">Monthly EMI:</span> {formatCurrency(selectedTransaction.monthly_emi_amount)}</div>
                    <div><span className="text-gray-600">Total EMI Amount:</span> {formatCurrency(selectedTransaction.total_emi_amount)}</div>
                  </div>
                </div>

                <div>
                  <h3 className="font-semibold text-gray-800 mb-3">Payment Status</h3>
                  <div className="space-y-2 text-sm">
                    <div><span className="text-gray-600">Total Paid:</span> {formatCurrency(selectedTransaction.total_paid_amount)}</div>
                    <div><span className="text-gray-600">Pending:</span> {formatCurrency(selectedTransaction.pending_amount)}</div>
                    <div><span className="text-gray-600">Next Due:</span> {formatDate(selectedTransaction.next_emi_due_date)}</div>
                  </div>
                </div>
              </div>

              <div className="mt-6 flex justify-end space-x-3">
                <button
                  onClick={() => sendPaymentReminder(selectedTransaction.id)}
                  className="px-4 py-2 bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200 transition-colors"
                >
                  Send Reminder
                </button>
                <button
                  onClick={() => processPayment(selectedTransaction.id, selectedTransaction.monthly_emi_amount)}
                  className="px-4 py-2 bg-green-100 text-green-700 rounded-lg hover:bg-green-200 transition-colors"
                >
                  Process Payment
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {showSettings && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg max-w-md w-full">
            <div className="p-6">
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-xl font-bold text-gray-800">EMI Settings</h2>
                <button
                  onClick={() => setShowSettings(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  ×
                </button>
              </div>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Default Advance Payment (%)
                  </label>
                  <input
                    type="number"
                    value={emiSettings.default_advance_percent}
                    onChange={(e) => setEmiSettings({...emiSettings, default_advance_percent: Number(e.target.value)})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Default Discount (%)
                  </label>
                  <input
                    type="number"
                    value={emiSettings.default_discount_percent}
                    onChange={(e) => setEmiSettings({...emiSettings, default_discount_percent: Number(e.target.value)})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Processing Fee (%)
                  </label>
                  <input
                    type="number"
                    value={emiSettings.default_processing_fee}
                    onChange={(e) => setEmiSettings({...emiSettings, default_processing_fee: Number(e.target.value)})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Reminder Days Before Due Date
                  </label>
                  <input
                    type="number"
                    value={emiSettings.reminder_days_before}
                    onChange={(e) => setEmiSettings({...emiSettings, reminder_days_before: Number(e.target.value)})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Late Fee (%)
                  </label>
                  <input
                    type="number"
                    value={emiSettings.late_fee_percent}
                    onChange={(e) => setEmiSettings({...emiSettings, late_fee_percent: Number(e.target.value)})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Grace Period (Days)
                  </label>
                  <input
                    type="number"
                    value={emiSettings.grace_period_days}
                    onChange={(e) => setEmiSettings({...emiSettings, grace_period_days: Number(e.target.value)})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                  />
                </div>

                <div className="flex items-center">
                  <input
                    type="checkbox"
                    checked={emiSettings.auto_debit_enabled}
                    onChange={(e) => setEmiSettings({...emiSettings, auto_debit_enabled: e.target.checked})}
                    className="mr-3"
                  />
                  <label className="text-sm font-medium text-gray-700">
                    Enable Auto-debit by Default
                  </label>
                </div>
              </div>

              <div className="mt-6 flex justify-end space-x-3">
                <button
                  onClick={() => setShowSettings(false)}
                  className="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
                >
                  Cancel
                </button>
                <button
                  onClick={() => updateEMISettings(emiSettings)}
                  className="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-dark transition-colors"
                >
                  Save Settings
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default PrepaidEMIManagement; 