# Deploy Family Site with API Server to Linode (Windows Version)
# This script will deploy both the website and API server to family.tripxplo.com

Write-Host "🚀 Deploying Family Site with API Server..." -ForegroundColor Green
Write-Host "=============================================" -ForegroundColor Green

# Configuration
$SERVER_IP = "*************"
$SERVER_USER = "root"

# Check if family-tripxplo-production directory exists
if (-not (Test-Path "family-tripxplo-production")) {
    Write-Host "❌ Error: family-tripxplo-production directory not found!" -ForegroundColor Red
    exit 1
}

Write-Host "📁 Found production files. Proceeding with deployment..." -ForegroundColor Yellow

# Check if we have SSH access
Write-Host "🔍 Checking SSH connectivity..." -ForegroundColor Cyan
try {
    $testConnection = ssh "${SERVER_USER}@${SERVER_IP}" "echo 'Connection test successful'"
    Write-Host "✅ SSH connection successful" -ForegroundColor Green
} catch {
    Write-Host "❌ SSH connection failed. Please ensure:" -ForegroundColor Red
    Write-Host "  1. SSH is installed (try: winget install Microsoft.OpenSSH.Beta)" -ForegroundColor Yellow
    Write-Host "  2. You have SSH key access to the server" -ForegroundColor Yellow
    Write-Host "  3. Server IP is correct: $SERVER_IP" -ForegroundColor Yellow
    exit 1
}

# Step 1: Upload all files including API
Write-Host "📤 Step 1: Uploading website and API files to server..." -ForegroundColor Cyan

# Create temporary directory on server
ssh "${SERVER_USER}@${SERVER_IP}" "mkdir -p /tmp/family-new"

# Upload files using SCP
Write-Host "Uploading files..." -ForegroundColor Gray
scp -r "family-tripxplo-production/*" "${SERVER_USER}@${SERVER_IP}:/tmp/family-new/"

if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Error: Failed to upload files to server!" -ForegroundColor Red
    exit 1
}

Write-Host "✅ Files uploaded successfully!" -ForegroundColor Green

# Step 2: Execute deployment commands one by one
Write-Host "⚙️ Step 2: Configuring server..." -ForegroundColor Cyan

Write-Host "Creating backup..." -ForegroundColor Gray
ssh "${SERVER_USER}@${SERVER_IP}" "if [ -d '/var/www/family' ]; then cp -r /var/www/family /var/www/family.backup.`$(date +%Y%m%d_%H%M%S); fi"

Write-Host "Deploying website files..." -ForegroundColor Gray
ssh "${SERVER_USER}@${SERVER_IP}" "rm -rf /var/www/family/* && mkdir -p /var/www/family && cp -r /tmp/family-new/* /var/www/family/"

Write-Host "Setting permissions..." -ForegroundColor Gray
ssh "${SERVER_USER}@${SERVER_IP}" "chown -R www-data:www-data /var/www/family && chmod -R 755 /var/www/family"

Write-Host "Installing Node.js if needed..." -ForegroundColor Gray
ssh "${SERVER_USER}@${SERVER_IP}" "if ! command -v node &> /dev/null; then curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash - && apt-get install -y nodejs; fi"

Write-Host "Installing PM2 if needed..." -ForegroundColor Gray
ssh "${SERVER_USER}@${SERVER_IP}" "if ! command -v pm2 &> /dev/null; then npm install -g pm2; fi"

Write-Host "Installing API dependencies..." -ForegroundColor Gray
ssh "${SERVER_USER}@${SERVER_IP}" "cd /var/www/family/api && npm install"

Write-Host "Starting API server..." -ForegroundColor Gray
ssh "${SERVER_USER}@${SERVER_IP}" "cd /var/www/family/api && pm2 delete family-api 2>/dev/null || true && pm2 start server.js --name family-api && pm2 save"

Write-Host "Configuring Nginx..." -ForegroundColor Gray

# Create Nginx configuration using here-string
$nginxConfig = @'
server {
    listen 80;
    server_name family.tripxplo.com www.family.tripxplo.com;
    
    root /var/www/family;
    index index.html index.htm;
    
    # Serve static files
    location / {
        try_files $uri $uri/ =404;
    }
    
    # Proxy API requests to Node.js server
    location /api/ {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
    
    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;
}
'@

# Write nginx config to temporary file
$nginxConfig | Out-File -FilePath "nginx-config.tmp" -Encoding UTF8

# Upload and apply nginx config
scp "nginx-config.tmp" "${SERVER_USER}@${SERVER_IP}:/tmp/nginx-family.conf"
ssh "${SERVER_USER}@${SERVER_IP}" "cp /tmp/nginx-family.conf /etc/nginx/sites-available/family.tripxplo.com"
ssh "${SERVER_USER}@${SERVER_IP}" "ln -sf /etc/nginx/sites-available/family.tripxplo.com /etc/nginx/sites-enabled/ && rm -f /etc/nginx/sites-enabled/default"

# Test and reload nginx
Write-Host "Testing Nginx configuration..." -ForegroundColor Gray
ssh "${SERVER_USER}@${SERVER_IP}" "nginx -t && systemctl reload nginx"

Write-Host "Setting up SSL certificate..." -ForegroundColor Gray
ssh "${SERVER_USER}@${SERVER_IP}" "if ! command -v certbot &> /dev/null; then apt update && apt install -y certbot python3-certbot-nginx; fi"
ssh "${SERVER_USER}@${SERVER_IP}" "certbot --nginx -d family.tripxplo.com -d www.family.tripxplo.com --non-interactive --agree-tos --email <EMAIL> --redirect"

Write-Host "Cleaning up..." -ForegroundColor Gray
ssh "${SERVER_USER}@${SERVER_IP}" "rm -rf /tmp/family-new /tmp/nginx-family.conf"
Remove-Item "nginx-config.tmp" -ErrorAction SilentlyContinue

if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Error: Some deployment steps failed!" -ForegroundColor Red
    Write-Host "Please check the server manually or run the status check script." -ForegroundColor Yellow
} else {
    Write-Host "✅ Deployment completed successfully!" -ForegroundColor Green
}

# Test the deployment
Write-Host "🧪 Testing deployment..." -ForegroundColor Cyan
Write-Host "Testing website..." -ForegroundColor Gray
ssh "${SERVER_USER}@${SERVER_IP}" "curl -I https://family.tripxplo.com"

Write-Host "Testing API..." -ForegroundColor Gray
ssh "${SERVER_USER}@${SERVER_IP}" "curl -I https://family.tripxplo.com/api/submit-contact-details"

Write-Host "Checking PM2 status..." -ForegroundColor Gray
ssh "${SERVER_USER}@${SERVER_IP}" "pm2 status"

Write-Host "" -ForegroundColor Green
Write-Host "🎉 Deployment completed!" -ForegroundColor Green
Write-Host "🌐 Website: https://family.tripxplo.com" -ForegroundColor Green
Write-Host "🔗 API: https://family.tripxplo.com/api/" -ForegroundColor Green
Write-Host "" -ForegroundColor Green
Write-Host "📝 Next steps:" -ForegroundColor Yellow
Write-Host "1. Test the website at https://family.tripxplo.com" -ForegroundColor White
Write-Host "2. Test the contact form submission" -ForegroundColor White
Write-Host "3. Check API server status with: .\check-api-status.ps1" -ForegroundColor White
Write-Host "" -ForegroundColor Green

pause
