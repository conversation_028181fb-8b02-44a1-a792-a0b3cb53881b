# Deploy Family Site with API Server to Linode
# This script will deploy both the website and API server to family.tripxplo.com

Write-Host "🚀 Deploying Family Site with API Server..." -ForegroundColor Green
Write-Host "=============================================" -ForegroundColor Green

# Configuration
$SERVER_IP = "*************"
$SERVER_USER = "root"

# Check if family-tripxplo-production directory exists
if (-not (Test-Path "family-tripxplo-production")) {
    Write-Host "❌ Error: family-tripxplo-production directory not found!" -ForegroundColor Red
    exit 1
}

Write-Host "📁 Found production files. Proceeding with deployment..." -ForegroundColor Yellow

# Step 1: Upload all files including API
Write-Host "📤 Step 1: Uploading website and API files to server..." -ForegroundColor Cyan
$scpCommand = "scp -r family-tripxplo-production/* ${SERVER_USER}@${SERVER_IP}:/tmp/family-new/"
Write-Host "Running: $scpCommand" -ForegroundColor Gray
Invoke-Expression $scpCommand

if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Error: Failed to upload files to server!" -ForegroundColor Red
    exit 1
}

Write-Host "✅ Files uploaded successfully!" -ForegroundColor Green

# Step 2: Deploy and configure on server
Write-Host "⚙️ Step 2: Configuring server..." -ForegroundColor Cyan

# Create SSH commands for server configuration
$sshCommands = @"
#!/bin/bash
set -e

echo "🔧 Starting server configuration..."

# Create backup of existing files
if [ -d '/var/www/family' ]; then
    echo "💾 Creating backup..."
    cp -r /var/www/family /var/www/family.backup.`$(date +%Y%m%d_%H%M%S)
fi

# Clear existing files and copy new ones
echo "📁 Deploying website files..."
rm -rf /var/www/family/*
mkdir -p /var/www/family
cp -r /tmp/family-new/* /var/www/family/

# Set permissions for website files
echo "🔐 Setting website permissions..."
chown -R www-data:www-data /var/www/family
chmod -R 755 /var/www/family
find /var/www/family -name "*.html" -exec chmod 644 {} \;
find /var/www/family -name "*.css" -exec chmod 644 {} \;
find /var/www/family -name "*.js" -exec chmod 644 {} \;

# Install Node.js if not present
if ! command -v node &> /dev/null; then
    echo "📦 Installing Node.js..."
    curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
    apt-get install -y nodejs
fi

# Install PM2 if not present
if ! command -v pm2 &> /dev/null; then
    echo "📦 Installing PM2..."
    npm install -g pm2
fi

# Setup API server
echo "🔧 Setting up API server..."
cd /var/www/family/api

# Install dependencies
echo "📦 Installing API dependencies..."
npm install

# Stop existing API server if running
pm2 delete family-api 2>/dev/null || true

# Start API server with PM2
echo "🚀 Starting API server..."
pm2 start server.js --name family-api
pm2 save
pm2 startup

# Configure Nginx to proxy API requests
echo "🌐 Configuring Nginx..."

# Create Nginx configuration for family.tripxplo.com
cat > /etc/nginx/sites-available/family.tripxplo.com << 'EOF'
server {
    listen 80;
    server_name family.tripxplo.com www.family.tripxplo.com;
    
    root /var/www/family;
    index index.html index.htm;
    
    # Serve static files
    location / {
        try_files \$uri \$uri/ =404;
    }
    
    # Proxy API requests to Node.js server
    location /api/ {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;
    }
    
    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;
}
EOF

# Enable the site
ln -sf /etc/nginx/sites-available/family.tripxplo.com /etc/nginx/sites-enabled/
rm -f /etc/nginx/sites-enabled/default

# Test and reload Nginx
nginx -t && systemctl reload nginx

echo "🔒 Setting up SSL certificate..."
# Install Certbot if not present
if ! command -v certbot &> /dev/null; then
    apt update
    apt install -y certbot python3-certbot-nginx
fi

# Get SSL certificate
certbot --nginx -d family.tripxplo.com -d www.family.tripxplo.com --non-interactive --agree-tos --email <EMAIL> --redirect

# Clean up temporary files
rm -rf /tmp/family-new

echo "✅ Deployment completed successfully!"
echo "🌐 Website: https://family.tripxplo.com"
echo "🔗 API: https://family.tripxplo.com/api/"

# Test the deployment
echo "🧪 Testing deployment..."
curl -I https://family.tripxplo.com
curl -I https://family.tripxplo.com/api/submit-contact-details

echo "📊 PM2 Status:"
pm2 status
"@

# Write SSH commands to temporary file
$sshCommands | Out-File -FilePath "deploy-api-commands.sh" -Encoding UTF8

# Execute SSH commands
Write-Host "🔧 Executing deployment commands on server..." -ForegroundColor Cyan
Write-Host "Running deployment commands on server..." -ForegroundColor Gray

# Use Get-Content to read the file and pipe it to SSH
Get-Content "deploy-api-commands.sh" | ssh "${SERVER_USER}@${SERVER_IP}" "bash -s"

if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Error: Deployment failed!" -ForegroundColor Red
    exit 1
}

# Clean up temporary file
Remove-Item "deploy-api-commands.sh" -ErrorAction SilentlyContinue

Write-Host "" -ForegroundColor Green
Write-Host "🎉 Deployment completed successfully!" -ForegroundColor Green
Write-Host "🌐 Website: https://family.tripxplo.com" -ForegroundColor Green
Write-Host "🔗 API: https://family.tripxplo.com/api/" -ForegroundColor Green
Write-Host "" -ForegroundColor Green
Write-Host "📝 Next steps:" -ForegroundColor Yellow
Write-Host "1. Test the website at https://family.tripxplo.com" -ForegroundColor White
Write-Host "2. Test the contact form submission" -ForegroundColor White
Write-Host "3. Check API server status: ssh root@************* 'pm2 status'" -ForegroundColor White
Write-Host "" -ForegroundColor Green

pause
