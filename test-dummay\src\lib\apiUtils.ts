/**
 * API Utilities
 * 
 * This file contains utility functions for API requests, including retry logic,
 * timeout handling, and other common API-related functionality.
 */

/**
 * Retry a function with exponential backoff
 * @param fn Function to retry
 * @param maxRetries Maximum number of retries
 * @param initialDelay Initial delay in ms
 * @param maxDelay Maximum delay in ms
 * @returns Promise with the result of the function
 */
export async function retryWithBackoff<T>(
    fn: () => Promise<T>,
    maxRetries: number = 3,
    initialDelay: number = 300,
    maxDelay: number = 10000
  ): Promise<T> {
    let retries = 0;
    let delay = initialDelay;
  
    const execute = async (): Promise<T> => {
      try {
        return await fn();
      } catch (error) {
        if (retries >= maxRetries) {
          console.error(`[retryWithBackoff] Max retries (${maxRetries}) reached, giving up.`);
          throw error;
        }
  
        retries++;
        // Exponential backoff with jitter
        delay = Math.min(delay * 2, maxDelay) * (0.9 + Math.random() * 0.2);
        console.log(`[retryWithBackoff] Retry ${retries}/${maxRetries} after ${Math.round(delay)}ms`);
        
        return new Promise<T>((resolve, reject) => {
          setTimeout(() => {
            execute().then(resolve).catch(reject);
          }, delay);
        });
      }
    };
  
    return execute();
  }
  
  /**
   * Execute a function with a timeout
   * @param fn Function to execute
   * @param timeoutMs Timeout in milliseconds
   * @param fallbackFn Optional fallback function to execute if the timeout is reached
   * @returns Promise with the result of the function or fallback
   */
  export async function withTimeout<T>(
    fn: () => Promise<T>,
    timeoutMs: number,
    fallbackFn?: () => Promise<T>
  ): Promise<T> {
    // Create a promise that resolves after the timeout
    const timeoutPromise = new Promise<never>((_, reject) => {
      setTimeout(() => {
        reject(new Error(`Operation timed out after ${timeoutMs}ms`));
      }, timeoutMs);
    });

    try {
      // Race the function against the timeout
      return await Promise.race([fn(), timeoutPromise]);
    } catch (error) {
      console.error(`[withTimeout] Operation timed out after ${timeoutMs}ms:`, error);

      // If a fallback function is provided, execute it
      if (fallbackFn) {
        console.log('[withTimeout] Executing fallback function');
        try {
          return await fallbackFn();
        } catch (fallbackError) {
          console.error('[withTimeout] Fallback function also failed:', fallbackError);
          throw error; // Throw original error
        }
      }

      throw error;
    }
  }
  
  /**
   * Execute a function with retry and timeout
   * @param fn Function to execute
   * @param options Configuration options
   * @returns Promise with the result of the function
   */
  export async function executeWithRetryAndTimeout<T>(
    fn: () => Promise<T>,
    options: {
      maxRetries?: number;
      initialDelay?: number;
      maxDelay?: number;
      timeoutMs?: number;
      fallbackFn?: () => Promise<T>;
    } = {}
  ): Promise<T> {
    const {
      maxRetries = 3,
      initialDelay = 300,
      maxDelay = 10000,
      timeoutMs = 15000,
      fallbackFn
    } = options;
  
    // Combine retry and timeout logic
    return withTimeout(
      () => retryWithBackoff(fn, maxRetries, initialDelay, maxDelay),
      timeoutMs,
      fallbackFn
    );
  }