<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EMI Fix Test - TripXplo</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .package-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            background: #f9f9f9;
        }
        .emi-info {
            background: #e7f3ff;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 EMI Fix Test - Family Package Pricing</h1>
        <p>This test verifies that EMI calculations use the correct family-specific prices from the database instead of fallback values.</p>
        
        <div class="test-result info">
            <strong>Test Status:</strong> <span id="testStatus">Ready to test</span>
        </div>
        
        <button onclick="runEMITest()">🚀 Run EMI Test</button>
        <button onclick="clearResults()">🧹 Clear Results</button>
    </div>

    <div class="test-container">
        <h2>📊 Test Results</h2>
        <div id="testResults"></div>
    </div>

    <div class="test-container">
        <h2>🔍 Debug Information</h2>
        <div id="debugInfo"></div>
    </div>

    <script src="js/config.js"></script>
    <script src="js/databaseService.js"></script>
    <script>
        let testResults = [];
        
        async function runEMITest() {
            const statusEl = document.getElementById('testStatus');
            const resultsEl = document.getElementById('testResults');
            const debugEl = document.getElementById('debugInfo');
            
            statusEl.textContent = 'Running tests...';
            resultsEl.innerHTML = '';
            debugEl.innerHTML = '';
            testResults = [];
            
            try {
                // Initialize database service
                const dbService = new DatabaseService();
                
                // Test 1: Search for family packages
                addTestResult('info', 'Test 1: Searching for family packages...');
                
                const searchParams = {
                    destination: 'Kashmir',
                    adults: 2,
                    children: 1,
                    infants: 0
                };
                
                const searchResult = await dbService.searchPackages(searchParams);
                
                if (searchResult.success && searchResult.packages.length > 0) {
                    addTestResult('success', `✅ Found ${searchResult.packages.length} packages`);
                    
                    // Test 2: Check EMI calculations for each package
                    for (let i = 0; i < Math.min(3, searchResult.packages.length); i++) {
                        const pkg = searchResult.packages[i];
                        await testPackageEMI(pkg, i + 1);
                    }
                } else {
                    addTestResult('error', '❌ No packages found or search failed');
                    addDebugInfo('Search Result', searchResult);
                }
                
                statusEl.textContent = 'Tests completed';
                
            } catch (error) {
                addTestResult('error', `❌ Test failed: ${error.message}`);
                addDebugInfo('Error Details', error);
                statusEl.textContent = 'Tests failed';
            }
        }
        
        async function testPackageEMI(pkg, testNum) {
            addTestResult('info', `Test ${testNum + 1}: Testing EMI for package ${pkg.id || 'unknown'}`);
            
            // Check if package has valid price
            const totalPrice = pkg.total_price || pkg.subtotal || pkg.total_cost;
            
            if (!totalPrice) {
                addTestResult('error', `❌ Package ${pkg.id} has no valid price`);
                addDebugInfo(`Package ${pkg.id} Data`, pkg);
                return;
            }
            
            addTestResult('success', `✅ Package ${pkg.id} has valid price: ₹${totalPrice.toLocaleString()}`);
            
            // Check EMI options
            const emiOptions = pkg.emi_options || [];
            
            if (emiOptions.length === 0) {
                addTestResult('error', `❌ Package ${pkg.id} has no EMI options`);
                return;
            }
            
            addTestResult('success', `✅ Package ${pkg.id} has ${emiOptions.length} EMI options`);
            
            // Test each EMI option
            emiOptions.forEach((emi, index) => {
                const monthlyAmount = emi.monthly_amount || 0;
                const totalAmount = emi.total_amount || 0;
                const months = emi.months || emi.emi_months || 6;
                
                // Check if EMI calculation seems reasonable
                const expectedMonthly = Math.round(totalPrice / months);
                const isReasonable = Math.abs(monthlyAmount - expectedMonthly) < (expectedMonthly * 0.5); // Within 50%
                
                if (isReasonable) {
                    addTestResult('success', `✅ EMI ${index + 1}: ₹${monthlyAmount.toLocaleString()}/month for ${months} months (Total: ₹${totalAmount.toLocaleString()})`);
                } else {
                    addTestResult('error', `❌ EMI ${index + 1}: Suspicious calculation - ₹${monthlyAmount.toLocaleString()}/month for ${months} months`);
                }
            });
            
            // Add package card display
            displayPackageCard(pkg);
        }
        
        function displayPackageCard(pkg) {
            const resultsEl = document.getElementById('testResults');
            const cardHtml = `
                <div class="package-card">
                    <h3>📦 ${pkg.title || pkg.quote_name || 'Package'}</h3>
                    <p><strong>Family Type:</strong> ${pkg.family_type || 'Unknown'}</p>
                    <p><strong>Total Price:</strong> ₹${(pkg.total_price || pkg.subtotal || 0).toLocaleString()}</p>
                    <div class="emi-info">
                        <h4>💳 EMI Options:</h4>
                        ${(pkg.emi_options || []).map(emi => `
                            <p>• ${emi.months || emi.emi_months || 6} months: ₹${(emi.monthly_amount || 0).toLocaleString()}/month</p>
                        `).join('')}
                    </div>
                </div>
            `;
            resultsEl.innerHTML += cardHtml;
        }
        
        function addTestResult(type, message) {
            const resultsEl = document.getElementById('testResults');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${type}`;
            resultDiv.innerHTML = message;
            resultsEl.appendChild(resultDiv);
            
            testResults.push({ type, message, timestamp: new Date() });
        }
        
        function addDebugInfo(title, data) {
            const debugEl = document.getElementById('debugInfo');
            const debugDiv = document.createElement('div');
            debugDiv.innerHTML = `
                <h4>${title}:</h4>
                <pre>${JSON.stringify(data, null, 2)}</pre>
            `;
            debugEl.appendChild(debugDiv);
        }
        
        function clearResults() {
            document.getElementById('testResults').innerHTML = '';
            document.getElementById('debugInfo').innerHTML = '';
            document.getElementById('testStatus').textContent = 'Ready to test';
            testResults = [];
        }
        
        // Auto-run test on page load for quick verification
        window.addEventListener('load', () => {
            setTimeout(() => {
                console.log('🧪 EMI Fix Test page loaded. Click "Run EMI Test" to verify the fix.');
            }, 1000);
        });
    </script>
</body>
</html>
