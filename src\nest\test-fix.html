<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Database Fix - TripXplo Family EMI</title>
    <style>
        body {
            font-family: 'Inter', sans-serif;
            margin: 0;
            padding: 20px;
            background: #f8f9fa;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }
        .test-header {
            text-align: center;
            margin-bottom: 30px;
        }
        .test-btn {
            padding: 12px 24px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            margin: 10px;
        }
        .test-results {
            margin-top: 20px;
            padding: 20px;
            background: #f8f9ff;
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }
        .test-results pre {
            background: #fff;
            padding: 15px;
            border-radius: 6px;
            overflow-x: auto;
            font-size: 0.9rem;
            border: 1px solid #e9ecef;
            max-height: 400px;
            overflow-y: auto;
        }
        .status-success { color: #28a745; }
        .status-error { color: #dc3545; }
        .status-loading { color: #ffc107; }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1>🔧 Database Fix Test</h1>
            <p>Testing the fix for "Assignment to constant variable" error</p>
        </div>

        <div style="text-align: center;">
            <button class="test-btn" onclick="testPackageSearch()">Test Package Search (Goa)</button>
            <button class="test-btn" onclick="testPackageSearch('Kashmir')">Test Package Search (Kashmir)</button>
            <button class="test-btn" onclick="testDirectQuery()">Test Direct Database Query</button>
        </div>

        <div id="testResults" class="test-results" style="display: none;">
            <h3>Test Results</h3>
            <pre id="resultOutput"></pre>
        </div>
    </div>

    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
    <script src="js/config.js"></script>
    <script src="js/databaseService.js"></script>
    <script>
        function showResults(title, data, status = 'success') {
            const resultsDiv = document.getElementById('testResults');
            const resultOutput = document.getElementById('resultOutput');
            
            resultsDiv.style.display = 'block';
            
            const timestamp = new Date().toLocaleTimeString();
            const statusClass = status === 'success' ? 'status-success' : status === 'error' ? 'status-error' : 'status-loading';
            
            resultOutput.innerHTML = `<span class="${statusClass}">[${timestamp}] ${title}</span>\n` + 
                                   JSON.stringify(data, null, 2);
        }

        async function testPackageSearch(destination = 'Goa') {
            try {
                showResults(`Testing Package Search for ${destination}...`, { status: 'loading' }, 'loading');
                
                console.log(`🔍 Testing package search for ${destination}...`);
                
                // Test the searchPackages function that was causing the error
                const result = await databaseService.searchPackages(destination, 2, 1, 0);
                
                const summary = {
                    success: result.success,
                    error: result.error,
                    packages_found: result.packages?.length || 0,
                    matched_family_type: result.matched_family_type?.family_type,
                    destination_searched: destination,
                    first_package: result.packages?.[0] ? {
                        id: result.packages[0].id,
                        title: result.packages[0].title,
                        destination: result.packages[0].destination,
                        total_price: result.packages[0].total_price,
                        family_type: result.packages[0].family_type
                    } : null
                };
                
                showResults(`Package Search Test for ${destination}`, summary, result.success ? 'success' : 'error');
                
                if (result.success) {
                    console.log('✅ Package search test passed!');
                } else {
                    console.error('❌ Package search test failed:', result.error);
                }
                
            } catch (error) {
                console.error('❌ Test failed with error:', error);
                showResults(`Package Search Test Error for ${destination}`, { 
                    error: error.message,
                    stack: error.stack 
                }, 'error');
            }
        }

        async function testDirectQuery() {
            try {
                showResults('Testing Direct Database Query...', { status: 'loading' }, 'loading');
                
                // Test direct database access
                const { data, error } = await databaseService.quoteDB
                    .from('family_type_prices')
                    .select('id, family_type_id, family_type_name, total_price, quote_id')
                    .eq('is_public_visible', true)
                    .limit(3);
                
                const result = {
                    success: !error,
                    error: error?.message,
                    data_count: data?.length || 0,
                    sample_data: data?.slice(0, 2) || []
                };
                
                showResults('Direct Database Query Test', result, result.success ? 'success' : 'error');
                
            } catch (error) {
                showResults('Direct Database Query Error', { error: error.message }, 'error');
            }
        }

        // Auto-run test on page load
        window.addEventListener('load', () => {
            console.log('🔧 Database fix test page loaded');
            
            // Wait a bit for everything to initialize
            setTimeout(() => {
                console.log('🚀 Running automatic test...');
                testPackageSearch();
            }, 2000);
        });
    </script>
</body>
</html>
