-- CORRECTED Enhanced Database Schema for Family Type Prices with EMI & Public Website Support
-- Database: Quote Generation Database
-- Tables: family_type_prices + emi_plans + public_family_quotes
-- FIX: Changed quote_id from VARCHAR(255) to UUID to match quotes table

-- =============================================================================
-- MAIN TABLE: Family Type Prices (Enhanced & Fixed)
-- =============================================================================
CREATE TABLE family_type_prices (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    quote_id UUID NOT NULL, -- FIXED: Changed from VARCHAR(255) to UUID
    family_type_id VARCHAR(50) NOT NULL, -- Reference to family_type.family_id
    family_type_name VARCHAR(255) NOT NULL, -- Family type name for display
    
    -- Family Composition
    no_of_adults INTEGER NOT NULL,
    no_of_children INTEGER DEFAULT 0, -- 6-12 years
    no_of_child INTEGER DEFAULT 0, -- Below 5 years
    no_of_infants INTEGER DEFAULT 0, -- Below 2 years
    family_count INTEGER NOT NULL,
    
    -- Room and Vehicle Info
    rooms_need INTEGER NOT NULL,
    cab_type VARCHAR(255),
    cab_capacity INTEGER,
    
    -- Calculated Costs
    hotel_cost DECIMAL(10,2) NOT NULL,
    vehicle_cost DECIMAL(10,2) NOT NULL,
    additional_costs DECIMAL(10,2) NOT NULL,
    basic_costs DECIMAL(10,2) DEFAULT 0,
    addon_costs DECIMAL(10,2) DEFAULT 0,
    optional_costs DECIMAL(10,2) DEFAULT 0,
    
    -- Final Pricing
    subtotal DECIMAL(10,2) NOT NULL,
    discount_amount DECIMAL(10,2) DEFAULT 0,
    commission_amount DECIMAL(10,2) DEFAULT 0,
    total_price DECIMAL(10,2) NOT NULL,
    
    -- EMI Support Fields (NEW)
    emi_enabled BOOLEAN DEFAULT true,
    min_emi_months INTEGER DEFAULT 3,
    max_emi_months INTEGER DEFAULT 24,
    emi_processing_fee_percent DECIMAL(5,2) DEFAULT 2.5,
    emi_interest_rate_percent DECIMAL(5,2) DEFAULT 12.0,
    
    -- Public Website Support (NEW)
    is_public_visible BOOLEAN DEFAULT true,
    destination_category VARCHAR(100), -- Beach, Hill, Adventure, etc.
    season_category VARCHAR(50), -- Peak, Off-peak, Normal
    package_duration_days INTEGER,
    public_display_order INTEGER DEFAULT 0,
    
    -- Room Calculation Details
    extra_adults INTEGER DEFAULT 0,
    children_charged INTEGER DEFAULT 0,
    infants_free INTEGER DEFAULT 0,
    room_type VARCHAR(255),
    
    -- Metadata
    baseline_quote_data JSONB, -- Store baseline quote info
    quote_mapping_data JSONB, -- Store quote mapping used
    calculation_notes TEXT[], -- Store calculation notes
    
    -- Public Website Metadata (NEW)
    public_metadata JSONB DEFAULT '{}', -- For website customization
    seo_keywords TEXT[], -- For search optimization
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT fk_quote_id FOREIGN KEY (quote_id) REFERENCES quotes(id) ON DELETE CASCADE,
    CONSTRAINT unique_quote_family UNIQUE (quote_id, family_type_id),
    CONSTRAINT valid_emi_months CHECK (min_emi_months <= max_emi_months),
    CONSTRAINT valid_emi_rates CHECK (emi_interest_rate_percent >= 0 AND emi_processing_fee_percent >= 0)
);

-- =============================================================================
-- EMI PLANS TABLE
-- =============================================================================
CREATE TABLE family_type_emi_plans (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    family_price_id UUID NOT NULL, -- Reference to family_type_prices
    
    -- EMI Configuration
    emi_months INTEGER NOT NULL,
    monthly_amount DECIMAL(10,2) NOT NULL,
    total_amount DECIMAL(10,2) NOT NULL, -- Including interest & fees
    processing_fee DECIMAL(10,2) NOT NULL,
    total_interest DECIMAL(10,2) NOT NULL,
    
    -- EMI Details
    first_payment_amount DECIMAL(10,2), -- Might be different (down payment)
    subsequent_payment_amount DECIMAL(10,2),
    final_payment_amount DECIMAL(10,2), -- Might be different (remaining balance)
    
    -- Customer Savings Display
    savings_vs_full_payment DECIMAL(10,2) DEFAULT 0, -- If any promotional savings
    effective_annual_rate DECIMAL(5,2), -- APR for transparency
    
    -- Marketing & Display
    is_featured BOOLEAN DEFAULT false, -- Highlight popular EMI options
    marketing_label VARCHAR(100), -- "Most Popular", "Best Value", etc.
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT fk_family_price_id FOREIGN KEY (family_price_id) REFERENCES family_type_prices(id) ON DELETE CASCADE,
    CONSTRAINT valid_emi_months CHECK (emi_months > 0 AND emi_months <= 60),
    CONSTRAINT valid_amounts CHECK (monthly_amount > 0 AND total_amount > 0)
);

-- =============================================================================
-- PUBLIC WEBSITE QUOTES TABLE
-- =============================================================================
CREATE TABLE public_family_quotes (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    
    -- Customer Input
    customer_email VARCHAR(255),
    customer_phone VARCHAR(20),
    customer_name VARCHAR(255),
    
    -- Trip Details
    destination VARCHAR(255) NOT NULL,
    travel_date DATE,
    duration_days INTEGER,
    
    -- Family Composition (Customer Input)
    no_of_adults INTEGER NOT NULL,
    no_of_children INTEGER DEFAULT 0,
    no_of_child INTEGER DEFAULT 0,
    no_of_infants INTEGER DEFAULT 0,
    
    -- Matched Results
    matched_family_type_id VARCHAR(50), -- Best match from family_type
    matched_price_id UUID, -- Reference to family_type_prices
    estimated_total_cost DECIMAL(10,2),
    
    -- Selected EMI Plan
    selected_emi_plan_id UUID, -- Reference to family_type_emi_plans
    selected_emi_months INTEGER,
    monthly_emi_amount DECIMAL(10,2),
    
    -- Lead Status
    quote_status VARCHAR(50) DEFAULT 'generated', -- generated, contacted, converted, expired
    follow_up_date DATE,
    notes TEXT,
    
    -- Session & Tracking
    session_id VARCHAR(255), -- For tracking user journey
    utm_source VARCHAR(100), -- Marketing tracking
    utm_medium VARCHAR(100),
    utm_campaign VARCHAR(100),
    referrer_url TEXT,
    
    -- Communication Log
    emails_sent INTEGER DEFAULT 0,
    last_email_sent TIMESTAMP WITH TIME ZONE,
    whatsapp_sent BOOLEAN DEFAULT false,
    sms_sent BOOLEAN DEFAULT false,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE DEFAULT (NOW() + INTERVAL '30 days'),
    
    -- Constraints
    CONSTRAINT fk_matched_price_id FOREIGN KEY (matched_price_id) REFERENCES family_type_prices(id),
    CONSTRAINT fk_selected_emi_plan_id FOREIGN KEY (selected_emi_plan_id) REFERENCES family_type_emi_plans(id),
    CONSTRAINT valid_family_composition CHECK (no_of_adults > 0)
);

-- =============================================================================
-- INDEXES FOR PERFORMANCE
-- =============================================================================

-- Family Type Prices Indexes
CREATE INDEX idx_family_type_prices_quote_id ON family_type_prices(quote_id);
CREATE INDEX idx_family_type_prices_family_type_id ON family_type_prices(family_type_id);
CREATE INDEX idx_family_type_prices_public_visible ON family_type_prices(is_public_visible) WHERE is_public_visible = true;
CREATE INDEX idx_family_type_prices_destination ON family_type_prices(destination_category);
CREATE INDEX idx_family_type_prices_created_at ON family_type_prices(created_at);

-- EMI Plans Indexes
CREATE INDEX idx_emi_plans_family_price_id ON family_type_emi_plans(family_price_id);
CREATE INDEX idx_emi_plans_months ON family_type_emi_plans(emi_months);
CREATE INDEX idx_emi_plans_featured ON family_type_emi_plans(is_featured) WHERE is_featured = true;

-- Public Quotes Indexes
CREATE INDEX idx_public_quotes_destination ON public_family_quotes(destination);
CREATE INDEX idx_public_quotes_status ON public_family_quotes(quote_status);
CREATE INDEX idx_public_quotes_created_at ON public_family_quotes(created_at);
CREATE INDEX idx_public_quotes_email ON public_family_quotes(customer_email);
CREATE INDEX idx_public_quotes_session ON public_family_quotes(session_id);
CREATE INDEX idx_public_quotes_expires_at ON public_family_quotes(expires_at);

-- =============================================================================
-- FUNCTIONS & TRIGGERS
-- =============================================================================

-- Function to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers for updated_at
CREATE TRIGGER update_family_type_prices_updated_at
    BEFORE UPDATE ON family_type_prices
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_emi_plans_updated_at
    BEFORE UPDATE ON family_type_emi_plans
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_public_quotes_updated_at
    BEFORE UPDATE ON public_family_quotes
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Function to calculate EMI amounts
CREATE OR REPLACE FUNCTION calculate_emi_amount(
    principal DECIMAL(10,2),
    annual_rate DECIMAL(5,2),
    months INTEGER,
    processing_fee_percent DECIMAL(5,2) DEFAULT 2.5
)
RETURNS TABLE (
    monthly_amount DECIMAL(10,2),
    total_amount DECIMAL(10,2),
    total_interest DECIMAL(10,2),
    processing_fee DECIMAL(10,2)
) AS $$
DECLARE
    monthly_rate DECIMAL(10,6);
    emi_amount DECIMAL(10,2);
    total_payment DECIMAL(10,2);
    interest_amount DECIMAL(10,2);
    fee_amount DECIMAL(10,2);
BEGIN
    -- Calculate monthly interest rate
    monthly_rate := annual_rate / (12 * 100);
    
    -- Calculate processing fee
    fee_amount := principal * processing_fee_percent / 100;
    
    -- Calculate EMI using standard formula: P * r * (1+r)^n / ((1+r)^n - 1)
    IF monthly_rate = 0 THEN
        emi_amount := principal / months;
    ELSE
        emi_amount := principal * monthly_rate * POWER(1 + monthly_rate, months) / (POWER(1 + monthly_rate, months) - 1);
    END IF;
    
    -- Round to 2 decimal places
    emi_amount := ROUND(emi_amount, 2);
    
    -- Calculate totals
    total_payment := (emi_amount * months) + fee_amount;
    interest_amount := total_payment - principal - fee_amount;
    
    RETURN QUERY SELECT emi_amount, total_payment, interest_amount, fee_amount;
END;
$$ language plpgsql;

-- =============================================================================
-- SAMPLE DATA INSERTION FUNCTION
-- =============================================================================

CREATE OR REPLACE FUNCTION generate_sample_emi_plans(family_price_id UUID)
RETURNS VOID AS $$
DECLARE
    price_record RECORD;
    emi_months_array INTEGER[] := ARRAY[3, 6, 9, 12, 18, 24];
    months INTEGER;
    emi_calc RECORD;
BEGIN
    -- Get the family price record
    SELECT * INTO price_record FROM family_type_prices WHERE id = family_price_id;
    
    IF NOT FOUND THEN
        RAISE EXCEPTION 'Family price record not found for ID: %', family_price_id;
    END IF;
    
    -- Generate EMI plans for different months
    FOREACH months IN ARRAY emi_months_array
    LOOP
        -- Skip if months exceed max allowed
        IF months <= price_record.max_emi_months THEN
            -- Calculate EMI
            SELECT * INTO emi_calc FROM calculate_emi_amount(
                price_record.total_price,
                price_record.emi_interest_rate_percent,
                months,
                price_record.emi_processing_fee_percent
            );
            
            -- Insert EMI plan
            INSERT INTO family_type_emi_plans (
                family_price_id,
                emi_months,
                monthly_amount,
                total_amount,
                processing_fee,
                total_interest,
                first_payment_amount,
                subsequent_payment_amount,
                final_payment_amount,
                effective_annual_rate,
                is_featured,
                marketing_label
            ) VALUES (
                family_price_id,
                months,
                emi_calc.monthly_amount,
                emi_calc.total_amount,
                emi_calc.processing_fee,
                emi_calc.total_interest,
                emi_calc.monthly_amount, -- Same for now
                emi_calc.monthly_amount,
                emi_calc.monthly_amount,
                price_record.emi_interest_rate_percent,
                CASE 
                    WHEN months = 12 THEN true -- Mark 12 months as featured
                    ELSE false 
                END,
                CASE 
                    WHEN months = 3 THEN 'Quick Pay'
                    WHEN months = 6 THEN 'Popular'
                    WHEN months = 12 THEN 'Most Popular'
                    WHEN months = 24 THEN 'Low EMI'
                    ELSE NULL
                END
            );
        END IF;
    END LOOP;
END;
$$ language plpgsql;

-- =============================================================================
-- RLS (ROW LEVEL SECURITY) SETUP
-- =============================================================================

-- Enable RLS on all tables
ALTER TABLE family_type_prices ENABLE ROW LEVEL SECURITY;
ALTER TABLE family_type_emi_plans ENABLE ROW LEVEL SECURITY;
ALTER TABLE public_family_quotes ENABLE ROW LEVEL SECURITY;

-- Create RLS policies (Allow all operations for now - customize as needed)

-- Family Type Prices policies
CREATE POLICY "family_type_prices_select_all" ON family_type_prices FOR SELECT USING (true);
CREATE POLICY "family_type_prices_insert_all" ON family_type_prices FOR INSERT WITH CHECK (true);
CREATE POLICY "family_type_prices_update_all" ON family_type_prices FOR UPDATE USING (true);
CREATE POLICY "family_type_prices_delete_all" ON family_type_prices FOR DELETE USING (true);

-- EMI Plans policies
CREATE POLICY "emi_plans_select_all" ON family_type_emi_plans FOR SELECT USING (true);
CREATE POLICY "emi_plans_insert_all" ON family_type_emi_plans FOR INSERT WITH CHECK (true);
CREATE POLICY "emi_plans_update_all" ON family_type_emi_plans FOR UPDATE USING (true);
CREATE POLICY "emi_plans_delete_all" ON family_type_emi_plans FOR DELETE USING (true);

-- Public Family Quotes policies
CREATE POLICY "public_quotes_select_all" ON public_family_quotes FOR SELECT USING (true);
CREATE POLICY "public_quotes_insert_all" ON public_family_quotes FOR INSERT WITH CHECK (true);
CREATE POLICY "public_quotes_update_all" ON public_family_quotes FOR UPDATE USING (true);
CREATE POLICY "public_quotes_delete_all" ON public_family_quotes FOR DELETE USING (true);

-- =============================================================================
-- GRANT PERMISSIONS
-- =============================================================================

-- Grant permissions to anon and authenticated users
GRANT USAGE ON SCHEMA public TO anon, authenticated;
GRANT ALL ON family_type_prices TO anon, authenticated;
GRANT ALL ON family_type_emi_plans TO anon, authenticated;
GRANT ALL ON public_family_quotes TO anon, authenticated;
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO anon, authenticated;

-- =============================================================================
-- COMMENTS & DOCUMENTATION
-- =============================================================================

COMMENT ON TABLE family_type_prices IS 'Enhanced family type prices with EMI and public website support';
COMMENT ON TABLE family_type_emi_plans IS 'EMI payment plans for family type packages';
COMMENT ON TABLE public_family_quotes IS 'Customer quotes generated from public website (family.tripxplo.com)';

COMMENT ON COLUMN family_type_prices.quote_id IS 'UUID reference to quotes.id (FIXED from VARCHAR to UUID)';
COMMENT ON COLUMN family_type_prices.emi_enabled IS 'Whether EMI options are available for this package';
COMMENT ON COLUMN family_type_prices.is_public_visible IS 'Whether this package appears on public website';
COMMENT ON COLUMN family_type_prices.destination_category IS 'Category for filtering on public website';
COMMENT ON COLUMN family_type_prices.public_metadata IS 'Additional data for public website customization';

COMMENT ON COLUMN family_type_emi_plans.is_featured IS 'Highlight this EMI option on website';
COMMENT ON COLUMN family_type_emi_plans.marketing_label IS 'Display label like "Most Popular", "Best Value"';

COMMENT ON COLUMN public_family_quotes.quote_status IS 'Lead status: generated, contacted, converted, expired';
COMMENT ON COLUMN public_family_quotes.session_id IS 'Track user journey across website';
COMMENT ON COLUMN public_family_quotes.utm_source IS 'Marketing attribution tracking';

-- =============================================================================
-- VERIFICATION & TESTING
-- =============================================================================

-- Verify the foreign key constraint works
SELECT 
    'VERIFICATION: Foreign key constraint check' as test_type,
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM information_schema.table_constraints 
            WHERE constraint_name = 'fk_quote_id' 
            AND table_name = 'family_type_prices'
        ) THEN '✅ Foreign key constraint created successfully'
        ELSE '❌ Foreign key constraint failed'
    END as result;

-- Show table structure
SELECT 
    'TABLE STRUCTURE' as info_type,
    table_name,
    column_name,
    data_type,
    is_nullable
FROM information_schema.columns 
WHERE table_name IN ('family_type_prices', 'family_type_emi_plans', 'public_family_quotes')
AND table_schema = 'public'
ORDER BY table_name, ordinal_position;

-- =============================================================================
-- USAGE EXAMPLES
-- =============================================================================

/*
-- Example 1: Insert family type price with correct UUID
INSERT INTO family_type_prices (
    quote_id, 
    family_type_id, 
    family_type_name,
    no_of_adults,
    family_count,
    rooms_need,
    hotel_cost,
    vehicle_cost,
    additional_costs,
    subtotal,
    total_price,
    destination_category
) VALUES (
    (SELECT id FROM quotes LIMIT 1), -- Use actual quote UUID
    'DFD',
    'Dynamic Family Duo+',
    4,
    4,
    1,
    15000.00,
    8500.00,
    2000.00,
    25500.00,
    25500.00,
    'Beach'
) RETURNING id;

-- Example 2: Generate EMI plans for a family type price
-- SELECT generate_sample_emi_plans('<family_price_id_from_above>');

-- Example 3: Get all EMI options for a destination
SELECT 
    ftp.family_type_name,
    ftp.total_price as base_price,
    emi.emi_months,
    emi.monthly_amount,
    emi.total_amount,
    emi.marketing_label,
    emi.is_featured
FROM family_type_prices ftp
JOIN family_type_emi_plans emi ON ftp.id = emi.family_price_id
WHERE ftp.destination_category = 'Beach' 
AND ftp.is_public_visible = true
ORDER BY emi.emi_months;

-- Example 4: Track public website lead
INSERT INTO public_family_quotes (
    destination, 
    no_of_adults, 
    no_of_children, 
    customer_email, 
    session_id, 
    utm_source
) VALUES (
    'Andaman', 
    2, 
    1, 
    '<EMAIL>', 
    'sess_123', 
    'google'
);
*/ 