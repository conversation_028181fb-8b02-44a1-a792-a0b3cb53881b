# Fix Production API Server for family.tripxplo.com
# PowerShell script to deploy the Node.js API server to fix the 405 error

Write-Host "🔧 Fixing Production API Server for family.tripxplo.com" -ForegroundColor Cyan
Write-Host "==================================================" -ForegroundColor Cyan

# Server configuration
$SERVER_IP = "*************"
$SERVER_USER = "root"
$DOMAIN = "family.tripxplo.com"

Write-Host "📋 Server Details:" -ForegroundColor Yellow
Write-Host "   IP: $SERVER_IP" -ForegroundColor Gray
Write-Host "   Domain: $DOMAIN" -ForegroundColor Gray
Write-Host ""

# Check if production files exist
if (-not (Test-Path "family-tripxplo-production/api")) {
    Write-Host "❌ Error: family-tripxplo-production/api directory not found!" -ForegroundColor Red
    Write-Host "Please ensure the production files are prepared first." -ForegroundColor Red
    exit 1
}

# Step 1: Upload API files
Write-Host "📤 Step 1: Uploading API server files..." -ForegroundColor Yellow
try {
    scp -r family-tripxplo-production/api/* "${SERVER_USER}@${SERVER_IP}:/var/www/family/api/"
    Write-Host "✅ API files uploaded successfully!" -ForegroundColor Green
} catch {
    Write-Host "❌ Failed to upload API files!" -ForegroundColor Red
    Write-Host "Error: $_" -ForegroundColor Red
    exit 1
}

# Step 2: Configure server environment
Write-Host ""
Write-Host "🔧 Step 2: Configuring server environment..." -ForegroundColor Yellow

$serverSetupScript = @'
echo "📦 Installing Node.js and PM2..."

# Install Node.js if not present
if ! command -v node &> /dev/null; then
    curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
    apt-get install -y nodejs
    echo "✅ Node.js installed"
else
    echo "ℹ️ Node.js already installed: $(node --version)"
fi

# Install PM2 if not present
if ! command -v pm2 &> /dev/null; then
    npm install -g pm2
    echo "✅ PM2 installed"
else
    echo "ℹ️ PM2 already installed: $(pm2 --version)"
fi

# Navigate to API directory
cd /var/www/family/api

# Install dependencies
echo "📦 Installing API dependencies..."
npm install

# Create environment file
echo "⚙️ Creating environment configuration..."
cat > .env << 'ENV_EOF'
NODE_ENV=production
PORT=3000
CORS_ORIGIN=https://family.tripxplo.com

# Database Configuration
CRM_DB_URL=https://tlfwcnikdlwoliqzavxj.supabase.co
CRM_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRsZndjbmlrZGx3b2xpcXphdnhqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzQ1MjU5NzQsImV4cCI6MjA1MDEwMTk3NH0.Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8

QUOTE_DB_URL=https://lkqbrlrmrsnbtkoryazq.supabase.co
QUOTE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImxrcWJybHJtcnNuYnRrb3J5YXpxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzQ1MjU5NzQsImV4cCI6MjA1MDEwMTk3NH0.Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8
ENV_EOF

# Stop existing API server if running
pm2 delete family-api 2>/dev/null || true

# Start API server
echo "🚀 Starting API server..."
pm2 start server.js --name family-api
pm2 save
pm2 startup

echo "✅ API server started successfully!"
'@

try {
    ssh "${SERVER_USER}@${SERVER_IP}" $serverSetupScript
    Write-Host "✅ Server environment configured successfully!" -ForegroundColor Green
} catch {
    Write-Host "❌ Failed to configure server environment!" -ForegroundColor Red
    Write-Host "Error: $_" -ForegroundColor Red
    exit 1
}

# Step 3: Configure Nginx
Write-Host ""
Write-Host "🌐 Step 3: Configuring Nginx for API proxy..." -ForegroundColor Yellow

$nginxConfigScript = @'
# Create Nginx configuration
cat > /etc/nginx/sites-available/family.tripxplo.com << "NGINX_EOF"
server {
    listen 80;
    server_name family.tripxplo.com www.family.tripxplo.com;
    
    root /var/www/family;
    index index.html index.htm;
    
    # Enable gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/css application/javascript application/json image/svg+xml text/plain text/xml;
    
    # Cache static assets
    location ~* \.(css|js|png|jpg|jpeg|gif|svg|ico)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # Proxy API requests to Node.js server
    location /api/ {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # CORS headers for API
        add_header Access-Control-Allow-Origin "https://family.tripxplo.com" always;
        add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS" always;
        add_header Access-Control-Allow-Headers "Content-Type, Authorization" always;
        add_header Access-Control-Allow-Credentials "true" always;
        
        # Handle preflight requests
        if ($request_method = OPTIONS) {
            add_header Access-Control-Allow-Origin "https://family.tripxplo.com";
            add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS";
            add_header Access-Control-Allow-Headers "Content-Type, Authorization";
            add_header Access-Control-Allow-Credentials "true";
            add_header Content-Length 0;
            add_header Content-Type text/plain;
            return 200;
        }
    }
    
    # Main application
    location / {
        try_files $uri $uri/ /index.html;
    }
}
NGINX_EOF

# Enable the site
ln -sf /etc/nginx/sites-available/family.tripxplo.com /etc/nginx/sites-enabled/

# Test and reload Nginx
nginx -t && {
    systemctl reload nginx
    echo "✅ Nginx configured and reloaded successfully!"
} || {
    echo "❌ Nginx configuration error!"
    exit 1
}
'@

try {
    ssh "${SERVER_USER}@${SERVER_IP}" $nginxConfigScript
    Write-Host "✅ Nginx configured successfully!" -ForegroundColor Green
} catch {
    Write-Host "❌ Failed to configure Nginx!" -ForegroundColor Red
    Write-Host "Error: $_" -ForegroundColor Red
    exit 1
}

# Step 4: Final verification
Write-Host ""
Write-Host "🔍 Step 4: Verifying deployment..." -ForegroundColor Yellow

$verificationScript = @'
echo "📊 API Server Status:"
pm2 status family-api

echo ""
echo "🌐 Nginx Status:"
systemctl status nginx --no-pager -l | head -3

echo ""
echo "🧪 Testing API endpoint:"
curl -s -o /dev/null -w "%{http_code}" http://localhost:3000/api/submit-contact-details -X POST -H "Content-Type: application/json" -d "{\"test\":\"data\"}"
'@

try {
    ssh "${SERVER_USER}@${SERVER_IP}" $verificationScript
} catch {
    Write-Host "⚠️ Verification step had issues, but deployment may still be successful" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "🎉 Deployment Complete!" -ForegroundColor Green
Write-Host "==================================================" -ForegroundColor Cyan
Write-Host "✅ API server is now running on the production server" -ForegroundColor Green
Write-Host "✅ Nginx is configured to proxy /api/ requests" -ForegroundColor Green
Write-Host "✅ CORS is configured for family.tripxplo.com" -ForegroundColor Green
Write-Host ""
Write-Host "🧪 Test the fix:" -ForegroundColor Yellow
Write-Host "   1. Visit https://family.tripxplo.com" -ForegroundColor Gray
Write-Host "   2. Try submitting the contact form" -ForegroundColor Gray
Write-Host "   3. Check browser console for any remaining errors" -ForegroundColor Gray
Write-Host ""
Write-Host "📋 If issues persist:" -ForegroundColor Yellow
Write-Host "   - Check PM2 logs: ssh ${SERVER_USER}@${SERVER_IP} 'pm2 logs family-api'" -ForegroundColor Gray
Write-Host "   - Check Nginx logs: ssh ${SERVER_USER}@${SERVER_IP} 'tail -f /var/log/nginx/error.log'" -ForegroundColor Gray
