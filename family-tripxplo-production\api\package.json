{"name": "family-emi-api", "version": "1.0.0", "description": "Backend API for TripXplo Family EMI Website", "type": "module", "main": "server.js", "scripts": {"start": "node server.js", "dev": "node --watch server.js", "pm2:start": "pm2 start server.js --name family-api", "pm2:stop": "pm2 stop family-api", "pm2:restart": "pm2 restart family-api", "pm2:delete": "pm2 delete family-api"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "@supabase/supabase-js": "^2.49.4", "dotenv": "^16.3.1", "bcrypt": "^5.1.1"}, "engines": {"node": ">=18.0.0"}}