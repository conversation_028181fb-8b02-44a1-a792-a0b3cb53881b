<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EMI Simple Calculation Test - TripXplo</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .calculation-example {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
            border-left: 4px solid #007bff;
        }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
        }
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }
        .comparison-table th, .comparison-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        .comparison-table th {
            background-color: #f2f2f2;
        }
        .highlight {
            background-color: #fff3cd;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧮 EMI Simple Calculation Test</h1>
        <p>This test verifies that EMI calculations use simple division without interest rates or processing fees.</p>
        
        <div class="test-result info">
            <strong>Expected Behavior:</strong>
            <ul>
                <li>Monthly Amount = Total Price ÷ Number of Months</li>
                <li>Total Amount = Same as Database Total Price</li>
                <li>Processing Fee = ₹0 (Prepaid EMI)</li>
                <li>Interest = ₹0 (Simple Division)</li>
            </ul>
        </div>
        
        <button onclick="runSimpleEMITest()">🚀 Test Simple EMI Calculation</button>
        <button onclick="clearResults()">🧹 Clear Results</button>
    </div>

    <div class="test-container">
        <h2>📊 Test Results</h2>
        <div id="testResults"></div>
    </div>

    <div class="test-container">
        <h2>🔍 Calculation Examples</h2>
        <div id="calculationExamples"></div>
    </div>

    <script src="js/config.js"></script>
    <script src="js/databaseService.js"></script>
    <script>
        function runSimpleEMITest() {
            const resultsEl = document.getElementById('testResults');
            const examplesEl = document.getElementById('calculationExamples');
            
            resultsEl.innerHTML = '';
            examplesEl.innerHTML = '';
            
            // Test different package prices
            const testPrices = [
                { price: 30000, description: "₹30,000 Package" },
                { price: 45000, description: "₹45,000 Package" },
                { price: 60000, description: "₹60,000 Package" },
                { price: 75000, description: "₹75,000 Package" }
            ];
            
            testPrices.forEach((testCase, index) => {
                testEMICalculation(testCase.price, testCase.description, index + 1);
            });
        }
        
        function testEMICalculation(totalPrice, description, testNum) {
            const resultsEl = document.getElementById('testResults');
            const examplesEl = document.getElementById('calculationExamples');
            
            // Test EMI calculation for 3, 6, and 12 months
            const months = [3, 6, 12];
            
            addTestResult('info', `Test ${testNum}: ${description}`);
            
            // Create calculation table
            let tableHTML = `
                <div class="calculation-example">
                    <h4>${description} - EMI Breakdown</h4>
                    <table class="comparison-table">
                        <thead>
                            <tr>
                                <th>Months</th>
                                <th>Monthly Amount</th>
                                <th>Total Amount</th>
                                <th>Processing Fee</th>
                                <th>Calculation</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>
            `;
            
            months.forEach(month => {
                const expectedMonthly = Math.round(totalPrice / month);
                const expectedTotal = totalPrice; // Should be same as original price
                const expectedProcessingFee = 0; // Should be 0 for prepaid EMI
                
                // Test the calculation
                const isMonthlyCorrect = expectedMonthly === Math.round(totalPrice / month);
                const isTotalCorrect = expectedTotal === totalPrice;
                const isProcessingFeeCorrect = expectedProcessingFee === 0;
                
                const allCorrect = isMonthlyCorrect && isTotalCorrect && isProcessingFeeCorrect;
                const statusClass = allCorrect ? 'success' : 'error';
                const statusText = allCorrect ? '✅ Correct' : '❌ Error';
                
                tableHTML += `
                    <tr class="${allCorrect ? '' : 'highlight'}">
                        <td>${month}</td>
                        <td>₹${expectedMonthly.toLocaleString()}</td>
                        <td>₹${expectedTotal.toLocaleString()}</td>
                        <td>₹${expectedProcessingFee}</td>
                        <td>₹${totalPrice.toLocaleString()} ÷ ${month} = ₹${expectedMonthly.toLocaleString()}</td>
                        <td class="${statusClass}">${statusText}</td>
                    </tr>
                `;
                
                // Add individual test results
                if (allCorrect) {
                    addTestResult('success', `✅ ${month} months: ₹${expectedMonthly.toLocaleString()}/month, Total: ₹${expectedTotal.toLocaleString()}`);
                } else {
                    addTestResult('error', `❌ ${month} months: Calculation error detected`);
                }
            });
            
            tableHTML += `
                        </tbody>
                    </table>
                </div>
            `;
            
            examplesEl.innerHTML += tableHTML;
            
            // Test summary
            addTestResult('info', `📋 ${description} test completed`);
        }
        
        function addTestResult(type, message) {
            const resultsEl = document.getElementById('testResults');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${type}`;
            resultDiv.innerHTML = message;
            resultsEl.appendChild(resultDiv);
        }
        
        function clearResults() {
            document.getElementById('testResults').innerHTML = '';
            document.getElementById('calculationExamples').innerHTML = '';
        }
        
        // Show expected vs actual comparison
        function showComparisonExample() {
            const examplesEl = document.getElementById('calculationExamples');
            
            examplesEl.innerHTML = `
                <div class="calculation-example">
                    <h4>🔄 Before vs After Comparison</h4>
                    <table class="comparison-table">
                        <thead>
                            <tr>
                                <th>Package Price</th>
                                <th>Months</th>
                                <th>OLD (With Interest)</th>
                                <th>NEW (Simple Division)</th>
                                <th>Difference</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td rowspan="3">₹45,000</td>
                                <td>3</td>
                                <td>₹15,750 + fees</td>
                                <td>₹15,000</td>
                                <td class="success">-₹750/month</td>
                            </tr>
                            <tr>
                                <td>6</td>
                                <td>₹7,875 + fees</td>
                                <td>₹7,500</td>
                                <td class="success">-₹375/month</td>
                            </tr>
                            <tr>
                                <td>12</td>
                                <td>₹4,125 + fees</td>
                                <td>₹3,750</td>
                                <td class="success">-₹375/month</td>
                            </tr>
                        </tbody>
                    </table>
                    <p><strong>Key Benefits:</strong></p>
                    <ul>
                        <li>✅ Total amount always equals database price</li>
                        <li>✅ No hidden interest charges</li>
                        <li>✅ No processing fees for prepaid EMI</li>
                        <li>✅ Transparent pricing for customers</li>
                    </ul>
                </div>
            `;
        }
        
        // Auto-show comparison on page load
        window.addEventListener('load', () => {
            showComparisonExample();
            setTimeout(() => {
                console.log('🧮 EMI Simple Calculation Test page loaded. Click "Test Simple EMI Calculation" to verify the fix.');
            }, 1000);
        });
    </script>
</body>
</html>
