#!/bin/bash
set -e

echo "🔧 Fixing TripXplo CRM deployment..."

# Navigate to CRM directory
cd /var/www/crm
echo "Current directory: $(pwd)"
echo "Current contents:"
ls -la

# Create backup
BACKUP_DIR="/var/www/crm.backup.$(date +%Y%m%d_%H%M%S)"
cp -r /var/www/crm $BACKUP_DIR
echo "💾 Backup created: $BACKUP_DIR"

# Move files from nested directory if it exists
if [ -d "TripXplo-CRM" ]; then
    echo "📁 Moving files from TripXplo-CRM to root..."
    cp -r TripXplo-CRM/* .
    rm -rf TripXplo-CRM
    echo "✅ Files moved successfully"
else
    echo "ℹ️ No nested TripXplo-CRM directory found"
fi

# Set permissions
echo "🔐 Setting permissions..."
chown -R www-data:www-data /var/www/crm
chmod -R 755 /var/www/crm

# Check for index.html
if [ -f "index.html" ]; then
    echo "✅ index.html found in correct location"
else
    echo "❌ index.html not found"
    echo "Contents:"
    ls -la
fi

# Update Nginx config
echo "🌐 Updating Nginx configuration..."
cat > /etc/nginx/sites-available/crm.tripxplo.com << 'EOF'
server {
    listen 80;
    server_name crm.tripxplo.com www.crm.tripxplo.com;
    root /var/www/crm;
    index index.html;
    
    error_log /var/log/nginx/crm_error.log;
    access_log /var/log/nginx/crm_access.log;
    
    location / {
        try_files $uri $uri/ /index.html;
    }
    
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot|map)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # CORS headers for Supabase
    add_header Access-Control-Allow-Origin "*" always;
    add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS" always;
    add_header Access-Control-Allow-Headers "DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization" always;
}
EOF

# Enable site and reload
ln -sf /etc/nginx/sites-available/crm.tripxplo.com /etc/nginx/sites-enabled/

echo "🧪 Testing Nginx config..."
if nginx -t; then
    systemctl reload nginx
    echo "✅ Nginx reloaded successfully"
else
    echo "❌ Nginx config error"
    nginx -t 2>&1
    exit 1
fi

echo "🧪 Testing site..."
sleep 2
HTTP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" http://crm.tripxplo.com || echo "000")
echo "HTTP Status: $HTTP_STATUS"

if [ "$HTTP_STATUS" = "200" ]; then
    echo "🎉 Site is working! http://crm.tripxplo.com"
else
    echo "⚠️ Site may need a moment. Status: $HTTP_STATUS"
fi

echo ""
echo "📊 Final structure:"
ls -la /var/www/crm/ | head -10

echo ""
echo "✅ TripXplo CRM deployment fix completed!"
echo "🌐 Visit: http://crm.tripxplo.com" 