-- Create quote_mappings table in TripXplo Quote Database
-- Run this script in your Quote Supabase SQL Editor: https://lkqbrlrmrsnbtkoryazq.supabase.co

-- First, check if the table already exists
SELECT 
    CASE 
        WHEN EXISTS (
            SELECT FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_name = 'quote_mappings'
        ) THEN 'Table already exists'
        ELSE 'Table does not exist - will create it'
    END as status;

-- Create the table if it doesn't exist
CREATE TABLE IF NOT EXISTS quote_mappings (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    quote_id UUID NOT NULL,
    quote_name TEXT NOT NULL,
    customer_name TEXT NOT NULL,
    destination TEXT NOT NULL,
    
    -- JSONB fields for flexible data storage
    hotel_mappings JSONB DEFAULT '[]'::jsonb,
    vehicle_mappings JSONB DEFAULT '[]'::jsonb,
    additional_costs JSONB DEFAULT '{}'::jsonb,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Unique constraint to prevent duplicate mappings for same quote
    UNIQUE(quote_id)
);

-- Create indexes for faster lookups
CREATE INDEX IF NOT EXISTS idx_quote_mappings_quote_id ON quote_mappings(quote_id);
CREATE INDEX IF NOT EXISTS idx_quote_mappings_destination ON quote_mappings(destination);
CREATE INDEX IF NOT EXISTS idx_quote_mappings_created_at ON quote_mappings(created_at);

-- Enable RLS (Row Level Security)
ALTER TABLE quote_mappings ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist (to avoid conflicts)
DROP POLICY IF EXISTS "Users can view all quote mappings" ON quote_mappings;
DROP POLICY IF EXISTS "Users can insert quote mappings" ON quote_mappings;
DROP POLICY IF EXISTS "Users can update quote mappings" ON quote_mappings;
DROP POLICY IF EXISTS "Users can delete quote mappings" ON quote_mappings;

-- Create RLS policies (allow all operations for now)
CREATE POLICY "Users can view all quote mappings" ON quote_mappings
    FOR SELECT USING (true);

CREATE POLICY "Users can insert quote mappings" ON quote_mappings
    FOR INSERT WITH CHECK (true);

CREATE POLICY "Users can update quote mappings" ON quote_mappings
    FOR UPDATE USING (true);

CREATE POLICY "Users can delete quote mappings" ON quote_mappings
    FOR DELETE USING (true);

-- Grant permissions to anon and authenticated users
GRANT USAGE ON SCHEMA public TO anon, authenticated;
GRANT ALL ON quote_mappings TO anon, authenticated;
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO anon, authenticated;

-- Add comments for documentation
COMMENT ON TABLE quote_mappings IS 'Enhanced quote data for family type pricing calculations';
COMMENT ON COLUMN quote_mappings.hotel_mappings IS 'Array of hotel cost mappings with extra adult, children, infant costs';
COMMENT ON COLUMN quote_mappings.vehicle_mappings IS 'Array of vehicle cost mappings with pricing models and multipliers';
COMMENT ON COLUMN quote_mappings.additional_costs IS 'Additional cost mappings for meals, ferry, activities, guide, parking/toll';

-- Test the table creation
INSERT INTO quote_mappings (
    quote_id, 
    quote_name, 
    customer_name, 
    destination,
    hotel_mappings,
    vehicle_mappings,
    additional_costs
) VALUES (
    gen_random_uuid(),
    'Test Quote',
    'Test Customer',
    'Test Destination',
    '[]'::jsonb,
    '[{"vehicle_type": "Sedan", "pricing_type": "multiplier", "base_cost": 0, "cost_multiplier": 1.0, "max_capacity": 4, "is_active": true}]'::jsonb,
    '{"meal_cost_per_person": 0, "ferry_cost": 0, "activity_cost_per_person": 0, "guide_cost_per_day": 0, "parking_toll_multiplier": 1.0}'::jsonb
) ON CONFLICT (quote_id) DO NOTHING;

-- Verify the table was created and test data inserted
SELECT 
    'quote_mappings table created successfully!' as message,
    COUNT(*) as test_records
FROM quote_mappings;

-- Clean up test data
DELETE FROM quote_mappings WHERE quote_name = 'Test Quote';

-- Final verification
SELECT 
    table_name,
    CASE 
        WHEN table_name IS NOT NULL THEN 'SUCCESS: Table exists and is ready to use'
        ELSE 'ERROR: Table creation failed'
    END as result
FROM information_schema.tables 
WHERE table_name = 'quote_mappings' 
AND table_schema = 'public';

-- Show RLS policies
SELECT 
    policyname,
    cmd,
    'Policy created' as status
FROM pg_policies 
WHERE tablename = 'quote_mappings'
ORDER BY cmd;
