<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Package Display - TripXplo Family EMI</title>
    <style>
        body {
            font-family: 'Inter', sans-serif;
            margin: 0;
            padding: 20px;
            background: #f8f9fa;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }
        .test-header {
            text-align: center;
            margin-bottom: 30px;
        }
        .package-card {
            border: 1px solid #e9ecef;
            border-radius: 12px;
            padding: 20px;
            margin: 15px 0;
            background: white;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        .package-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 10px;
        }
        .package-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 15px 0;
        }
        .detail-item {
            padding: 10px;
            background: #f8f9ff;
            border-radius: 6px;
            border-left: 3px solid #667eea;
        }
        .detail-label {
            font-weight: 600;
            color: #667eea;
            font-size: 0.9rem;
        }
        .detail-value {
            color: #333;
            margin-top: 5px;
        }
        .inclusions-list {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-top: 10px;
        }
        .inclusion-tag {
            background: #e8f5e8;
            color: #28a745;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.85rem;
        }
        .test-btn {
            padding: 12px 24px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            margin: 10px;
        }
        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }
        .error {
            background: #ffe6e6;
            color: #d63384;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1>📦 Package Display Test</h1>
            <p>Testing enhanced package formatting with real database data</p>
        </div>

        <div style="text-align: center; margin-bottom: 30px;">
            <button class="test-btn" onclick="testPackageDisplay('Andaman')">🏝️ Test Andaman Packages</button>
            <button class="test-btn" onclick="testPackageDisplay('Goa')">🏖️ Test Goa Packages</button>
            <button class="test-btn" onclick="testPackageDisplay('Kashmir')">🏔️ Test Kashmir Packages</button>
        </div>

        <div id="packagesContainer">
            <div class="loading">Click a button above to test package display</div>
        </div>
    </div>

    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
    <script src="js/config.js"></script>
    <script src="js/databaseService.js"></script>
    <script>
        async function testPackageDisplay(destination) {
            const container = document.getElementById('packagesContainer');
            container.innerHTML = '<div class="loading">🔍 Searching packages...</div>';

            try {
                const searchParams = {
                    destination: destination,
                    adults: 2,
                    child: 1,
                    children: 0,
                    infants: 0
                };

                console.log(`🧪 Testing package display for ${destination}...`);
                
                const result = await databaseService.searchPackages(searchParams);
                
                if (result.success && result.packages && result.packages.length > 0) {
                    displayPackages(result.packages, destination);
                } else {
                    container.innerHTML = `<div class="error">❌ No packages found for ${destination}</div>`;
                }
                
            } catch (error) {
                console.error('❌ Test failed:', error);
                container.innerHTML = `<div class="error">❌ Error: ${error.message}</div>`;
            }
        }

        function displayPackages(packages, destination) {
            const container = document.getElementById('packagesContainer');
            
            let html = `<h2>📍 Packages for ${destination} (${packages.length} found)</h2>`;
            
            packages.forEach((pkg, index) => {
                html += `
                    <div class="package-card">
                        <div class="package-title">${pkg.title}</div>
                        
                        <div class="package-details">
                            <div class="detail-item">
                                <div class="detail-label">Destination</div>
                                <div class="detail-value">${pkg.destination || 'Not specified'}</div>
                            </div>
                            
                            <div class="detail-item">
                                <div class="detail-label">Duration</div>
                                <div class="detail-value">${pkg.duration_days}N/${pkg.duration_days + 1}D</div>
                            </div>
                            
                            <div class="detail-item">
                                <div class="detail-label">Total Price</div>
                                <div class="detail-value">₹${pkg.total_price?.toLocaleString()}</div>
                            </div>
                            
                            <div class="detail-item">
                                <div class="detail-label">Family Type</div>
                                <div class="detail-value">${pkg.family_type}</div>
                            </div>
                            
                            ${pkg.hotels_list && pkg.hotels_list.length > 0 ? `
                            <div class="detail-item" style="grid-column: 1 / -1;">
                                <div class="detail-label">Hotels (${pkg.hotels_list.length})</div>
                                <div class="detail-value">
                                    ${pkg.hotels_list.map(hotel => {
                                        const nights = hotel.nights || hotel.stay_nights || 1;
                                        const hotelName = hotel.hotel_name || 'Hotel Included';
                                        const mealPlan = hotel.meal_plan || 'Breakfast included';
                                        return `<div style="padding: 4px 0; color: #666; border-left: 3px solid #667eea; padding-left: 8px; margin: 4px 0;">${nights}N - ${hotelName} (${mealPlan})</div>`;
                                    }).join('')}
                                </div>
                            </div>
                            ` : pkg.hotel_name ? `
                            <div class="detail-item">
                                <div class="detail-label">Hotel</div>
                                <div class="detail-value">${pkg.hotel_name} (${pkg.hotel_category || 'Standard'})</div>
                            </div>
                            ` : ''}
                            
                            ${pkg.nights ? `
                            <div class="detail-item">
                                <div class="detail-label">Accommodation</div>
                                <div class="detail-value">${pkg.nights}N - ${pkg.hotel_name || 'Hotel'}</div>
                            </div>
                            ` : ''}
                            
                            <div class="detail-item">
                                <div class="detail-label">Category</div>
                                <div class="detail-value">${pkg.category}</div>
                            </div>
                            
                            <div class="detail-item">
                                <div class="detail-label">Data Source</div>
                                <div class="detail-value">${pkg.created_from || 'Unknown'}</div>
                            </div>
                        </div>
                        
                        <div class="detail-item">
                            <div class="detail-label">Inclusions</div>
                            <div class="inclusions-list">
                                ${pkg.inclusions.map(inc => `<span class="inclusion-tag">${inc}</span>`).join('')}
                            </div>
                        </div>
                        
                        ${pkg.offer_badge ? `
                        <div style="margin-top: 15px;">
                            <span style="background: #ff6b6b; color: white; padding: 6px 12px; border-radius: 20px; font-size: 0.9rem; font-weight: 600;">
                                ${pkg.offer_badge}
                            </span>
                        </div>
                        ` : ''}
                    </div>
                `;
            });
            
            container.innerHTML = html;
        }

        // Auto-test on page load
        window.addEventListener('load', () => {
            console.log('🧪 Package display test page loaded');
            
            // Auto-test Andaman packages
            setTimeout(() => {
                console.log('🚀 Running automatic test for Andaman...');
                testPackageDisplay('Andaman');
            }, 2000);
        });
    </script>
</body>
</html>
