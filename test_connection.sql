-- Test Database Connection
-- Run this first to verify your Supabase connection works

-- Check current database
SELECT current_database();

-- Check if we can create a simple table
CREATE TABLE test_table (
    id SERIAL PRIMARY KEY,
    name VARCHAR(50),
    created_at TIMESTAMP DEFAULT NOW()
);

-- Insert test data
INSERT INTO test_table (name) VALUES ('Test Connection');

-- Query test data
SELECT * FROM test_table;

-- Clean up
DROP TABLE test_table;

-- Success message
SELECT 'Connection test successful! Ready to create family_type_prices tables.' as status; 