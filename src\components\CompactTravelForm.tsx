import React, { useState, useEffect } from 'react';
import { createPub<PERSON>L<PERSON>, LeadData } from '../lib/leadService';
import PhoneInput from 'react-phone-number-input';
import 'react-phone-number-input/style.css';
import Calendar from 'react-calendar';
import 'react-calendar/dist/Calendar.css';

// Type definition for holiday object
interface Holiday {
  date: string;
  name: string;
}

// Custom Indian holidays list (2024-2026) - reliable and always works
const INDIAN_HOLIDAYS: Record<number, Holiday[]> = {
  2024: [
    { date: '2024-01-15', name: '<PERSON><PERSON>' },
    { date: '2024-01-26', name: 'Republic Day' },
    { date: '2024-03-08', name: '<PERSON><PERSON>' },
    { date: '2024-03-25', name: '<PERSON><PERSON> (Regional)' },
    { date: '2024-04-11', name: '<PERSON><PERSON> <PERSON><PERSON>' },
    { date: '2024-04-14', name: '<PERSON><PERSON><PERSON><PERSON>' },
    { date: '2024-04-17', name: '<PERSON>' },
    { date: '2024-05-01', name: 'Labour Day' },
    { date: '2024-05-23', name: '<PERSON>' },
    { date: '2024-06-17', name: '<PERSON><PERSON>' },
    { date: '2024-08-15', name: '<PERSON> Day' },
    { date: '2024-08-26', name: '<PERSON>mashtami' },
    { date: '2024-09-07', name: 'G<PERSON>sh <PERSON><PERSON><PERSON>' },
    { date: '2024-10-02', name: '<PERSON> <PERSON><PERSON>' },
    { date: '2024-10-12', name: '<PERSON>ssehra' },
    { date: '2024-11-01', name: 'Diwali' },
    { date: '2024-11-15', name: 'Guru Nanak Jayanti' },
    { date: '2024-12-25', name: 'Christmas' }
  ],
  2025: [
    { date: '2025-01-14', name: 'Makar Sankranti' },
    { date: '2025-01-26', name: 'Republic Day' },
    { date: '2025-03-14', name: 'Holi' },
    { date: '2025-03-31', name: 'Eid al-Fitr' },
    { date: '2025-04-06', name: 'Ram Navami' },
    { date: '2025-04-13', name: 'Baisakhi' },
    { date: '2025-05-01', name: 'Labour Day' },
    { date: '2025-05-12', name: 'Buddha Purnima' },
    { date: '2025-06-07', name: 'Eid al-Adha' },
    { date: '2025-08-15', name: 'Independence Day' },
    { date: '2025-08-16', name: 'Janmashtami' },
    { date: '2025-08-27', name: 'Ganesh Chaturthi' },
    { date: '2025-10-02', name: 'Gandhi Jayanti' },
    { date: '2025-10-21', name: 'Dussehra' },
    { date: '2025-11-05', name: 'Guru Nanak Jayanti' },
    { date: '2025-11-20', name: 'Diwali' },
    { date: '2025-12-25', name: 'Christmas' }
  ],
  2026: [
    { date: '2026-01-14', name: 'Makar Sankranti' },
    { date: '2026-01-26', name: 'Republic Day' },
    { date: '2026-03-03', name: 'Holi' },
    { date: '2026-03-20', name: 'Eid al-Fitr' },
    { date: '2026-03-25', name: 'Ram Navami' },
    { date: '2026-04-13', name: 'Baisakhi' },
    { date: '2026-05-01', name: 'Labour Day' },
    { date: '2026-05-01', name: 'Buddha Purnima' },
    { date: '2026-05-27', name: 'Eid al-Adha' },
    { date: '2026-08-15', name: 'Independence Day' },
    { date: '2026-09-05', name: 'Janmashtami' },
    { date: '2026-09-17', name: 'Ganesh Chaturthi' },
    { date: '2026-10-02', name: 'Gandhi Jayanti' },
    { date: '2026-10-10', name: 'Dussehra' },
    { date: '2026-11-08', name: 'Diwali' },
    { date: '2026-11-24', name: 'Guru Nanak Jayanti' },
    { date: '2026-12-25', name: 'Christmas' }
  ]
};

// Get Indian holidays for a specific year
const getIndianHolidays = (year: number): Holiday[] => {
  return INDIAN_HOLIDAYS[year] || [];
};

// Import assets
import tripxploLogo from '../quotes/assets/tripxplo-logo-crop.png';
import optionIcon from '../quotes/assets/option-icon.png';
import hotelIcon from '../quotes/assets/hotel-icon.png';
import starIcon from '../quotes/assets/star-icon.png';

// Featured destinations with Domestic first, then International
const FEATURED_DESTINATIONS = [
  // Domestic destinations first
  { name: 'Goa', tag: 'POPULAR', color: 'bg-purple-100 text-purple-800', isDomestic: true },
  { name: 'Kashmir', tag: 'HONEYMOON', color: 'bg-pink-100 text-pink-800', isDomestic: true },
  { name: 'Manali', tag:'HONEYMOON', color: 'bg-green-100 text-green-800', isDomestic: true },
  { name: 'Ooty', tag: 'BUDGET', color: 'bg-orange-100 text-orange-800', isDomestic: true },
  { name: 'Munnar', tag: 'TRENDING', color: 'bg-blue-100 text-blue-800', isDomestic: true },
  { name: 'Andaman', tag: 'IN SEASON', color: 'bg-green-100 text-green-800', isDomestic: true },
  { name: 'Kodaikanal', tag: 'IN SEASON', color: 'bg-orange-100 text-orange-800', isDomestic: true },
  { name: 'Coorg', tag: 'BUDGET', color: 'bg-blue-100 text-blue-800', isDomestic: true },
  { name: 'Alleppey', tag: 'BACKWATERS', color: 'bg-green-100 text-green-800', isDomestic: true },
  { name: 'Kochi', tag: 'BUDGET', color: 'bg-blue-100 text-blue-800', isDomestic: true },
  { name: 'Shimla', tag: 'BUDGET', color: 'bg-orange-100 text-orange-800', isDomestic: true },
  { name: 'Yelagiri', tag: '', color: '', isDomestic: true },
  { name: 'Wayanad', tag: '', color: '', isDomestic: true },
  { name: 'Meghalaya', tag: '', color: '', isDomestic: true },
  { name: 'Darjeeling', tag: '', color: '', isDomestic: true },
  { name: 'Sikkim', tag: '', color: '', isDomestic: true },
  { name: 'Delhi', tag: '', color: '', isDomestic: true },
  { name: 'Agra', tag: '', color: '', isDomestic: true },
  { name: 'Pondicherry', tag: '', color: '', isDomestic: true },
  { name: 'Madurai', tag: '', color: '', isDomestic: true },
  { name: 'Rameswaram', tag: '', color: '', isDomestic: true },
  { name: 'Ladakh', tag: '', color: '', isDomestic: true },
  { name: 'Mysore', tag: '', color: '', isDomestic: true },
  // International destinations second
  { name: 'Bali', tag: 'HONEYMOON', color: 'bg-green-100 text-green-800', isDomestic: false },
  { name: 'Maldives', tag: 'HONEYMOON', color: 'bg-pink-100 text-pink-800', isDomestic: false },
  { name: 'Europe', tag: 'IN SEASON', color: 'bg-blue-100 text-blue-800', isDomestic: false },
  { name: 'Thailand', tag: 'BUDGET', color: 'bg-orange-100 text-orange-800', isDomestic: false },
  { name: 'Singapore', tag: 'FAMILY', color: 'bg-red-100 text-red-800', isDomestic: false },
  { name: 'Abu Dhabi', tag: 'POPULAR', color: 'bg-purple-100 text-purple-800', isDomestic: false },
  { name: 'Vietnam', tag: '', color: '', isDomestic: false },
  { name: 'Dubai', tag: '', color: '', isDomestic: false },
  { name: 'Australia', tag: '', color: '', isDomestic: false },
];

// Autocomplete options for people count and nights
const PEOPLE_OPTIONS = Array.from({ length: 15 }, (_, i) => ({
  value: i + 1,
  label: `${i + 1} ${i === 0 ? 'Person' : 'People'}`
}));

const NIGHTS_OPTIONS = Array.from({ length: 30 }, (_, i) => ({
  value: i + 1,
  label: `${i + 1}N/${i + 2}D`
}));

// Departure cities for autocomplete
const DEPARTURE_CITIES = [
  'Mumbai', 'Delhi', 'Bangalore', 'Chennai', 'Kolkata', 'Hyderabad', 'Pune', 'Ahmedabad',
  'Surat', 'Jaipur', 'Lucknow', 'Kanpur', 'Nagpur', 'Indore', 'Bhopal', 'Visakhapatnam',
  'Vadodara', 'Ludhiana', 'Rajkot', 'Agra', 'Nashik', 'Faridabad',
  'Patiala', 'Ghaziabad', 'Coimbatore', 'Madurai', 'Jalandhar', 'Chandigarh',
  'Mysore', 'Bareilly', 'Guwahati', 'Aligarh', 'Tiruchirappalli', 'Bhubaneswar', 'Salem',
  'Mira-Bhayandar', 'Thiruvananthapuram', 'Bhiwandi', 'Saharanpur', 'Gorakhpur', 'Guntur',
  'Bikaner', 'Amravati', 'Noida', 'Jamshedpur', 'Bhilai Nagar', 'Warangal', 'Cuttack',
  'Kochi', 'Bhavnagar', 'Dehradun', 'Durgapur', 'Asansol', 'Nanded Waghala',
  'Kolhapur', 'Ajmer', 'Gulbarga', 'Jamnagar', 'Ujjain', 'Loni', 'Siliguri', 'Jhansi',
  'Ulhasnagar', 'Nellore', 'Jammu', 'Sangli-Miraj & Kupwad', 'Belgaum', 'Mangalore',
  'Ambattur', 'Tirunelveli', 'Malegaon', 'Gaya', 'Jalgaon', 'Udaipur', 'Maheshtala'
];

interface TravelFormData {
  name: string;
  travel_date: string;
  destination: string;
  people_count: number;
  children: number;
  infants: number;
  nights: number;
  departure_city: string;
  mobile: string;
  email: string;
  requests: string;
}

const isIndianHoliday = (date: Date): boolean => {
  if (!date) return false;
  
  // Use local date to avoid timezone issues
  const year = date.getFullYear();
  const month = date.getMonth() + 1; // getMonth() returns 0-11, we need 1-12
  const day = date.getDate();
  
  const holidays = getIndianHolidays(year);
  
  // Format date as YYYY-MM-DD to match our holiday data
  const dateString = `${year}-${String(month).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
  
  return holidays.some((holiday: Holiday) => holiday.date === dateString);
};

const getHolidayName = (date: Date): string => {
  if (!date) return '';
  const year = date.getFullYear();
  const holidays = getIndianHolidays(year);
  
  // Format date as YYYY-MM-DD to match our holiday data
  const dateString = `${year}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
  
  const holiday = holidays.find((holiday: Holiday) => holiday.date === dateString);
  return holiday ? holiday.name : 'Holiday';
};

// Helper function for string date input (for form validation)
const isIndianHolidayString = (dateString: string): boolean => {
  if (!dateString) return false;
  return isIndianHoliday(new Date(dateString));
};

const getHolidayNameString = (dateString: string): string => {
  if (!dateString) return '';
  return getHolidayName(new Date(dateString));
};

const CompactTravelForm: React.FC = () => {
  const [formData, setFormData] = useState<TravelFormData>({
    name: '',
    travel_date: '',
    destination: '',
    people_count: 2,
    children: 0,
    infants: 0,
    nights: 3,
    departure_city: '',
    mobile: '',
    email: '',
    requests: ''
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitted, setSubmitted] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [requestsExpanded, setRequestsExpanded] = useState(false);
  const [aboutExpanded, setAboutExpanded] = useState(false);
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [showDestinationPicker, setShowDestinationPicker] = useState(false);
  const [showPeoplePicker, setShowPeoplePicker] = useState(false);
  const [showNightsPicker, setShowNightsPicker] = useState(false);
  const [showChildrenPicker, setShowChildrenPicker] = useState(false);
  const [showInfantsPicker, setShowInfantsPicker] = useState(false);
  const [showDeparturePicker, setShowDeparturePicker] = useState(false);

  // Update page title and favicon
  useEffect(() => {
    document.title = 'TripXplo - Plan Your Dream Vacation | Travel Inquiry Form';
    
    // Create favicon if it doesn't exist
    const existingFavicon = document.querySelector('link[rel="icon"]') as HTMLLinkElement;
    if (existingFavicon) {
      existingFavicon.href = 'data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><text y=".9em" font-size="90">✈️</text></svg>';
    } else {
      const favicon = document.createElement('link');
      favicon.rel = 'icon';
      favicon.href = 'data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><text y=".9em" font-size="90">✈️</text></svg>';
      document.head.appendChild(favicon);
    }
  }, []);

  // Close pickers when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as HTMLElement;
      if (!target.closest('.picker-container')) {
        setShowDatePicker(false);
        setShowDestinationPicker(false);
        setShowPeoplePicker(false);
        setShowNightsPicker(false);
        setShowChildrenPicker(false);
        setShowInfantsPicker(false);
        setShowDeparturePicker(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;
    
    if (type === 'number') {
      const numValue = value === '' ? 0 : parseInt(value, 10);
      setFormData(prev => ({
        ...prev,
        [name]: isNaN(numValue) ? 0 : numValue
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: value
      }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    
    // Validate required fields
    if (!formData.name) {
      setError('Please enter your name');
      return;
    }
    if (!formData.destination) {
      setError('Please select a destination');
      return;
    }
    if (!formData.mobile) {
      setError('Please enter your mobile number');
      return;
    }
    if (!formData.travel_date) {
      setError('Please select a travel date');
      return;
    }

    setIsSubmitting(true);
    
    try {
      // Convert to LeadData format with minimal required fields
      const leadData: LeadData = {
        customer_name: formData.name,
        email: formData.email || '',
        phone: formData.mobile || '',
        destination: formData.destination || '',
        departure_city: formData.departure_city || '',
        travel_date: formData.travel_date || '',
        adults: formData.people_count || 1,
        children: formData.children || 0,
        infants: formData.infants || 0,
        nights: formData.nights || 1,
        budget_range: '',
        lead_source: 'customer_form',
        priority: 'medium',
        notes: formData.requests || '',
        special_requests: formData.requests || '',
        status: 'NEW LEAD',
      };

      // Use public lead creation function (no authentication required)
      const result = await createPublicLead(leadData);
      
      if (result.success) {
        setSubmitted(true);
      } else {
        // Show specific error from database
        const errorMessage = result.error?.message || result.error?.details || 'Failed to submit inquiry. Please try again.';
        setError(`Submission failed: ${errorMessage}`);
        console.error('Database error details:', result.error);
      }
    } catch (err) {
      setError('An error occurred. Please try again.');
      console.error('Form submission error:', err);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Enhanced Autocomplete Dropdown with search capability
  const AutocompleteDropdown = ({ 
    options, 
    value, 
    onChange, 
    placeholder, 
    isOpen, 
    onToggle,
    formatValue,
    searchTerm,
    onSearchChange,
    allowCustom = false
  }: {
    options: Array<{value: number | string, label: string}>;
    value: number | string;
    onChange: (value: number | string) => void;
    placeholder: string;
    isOpen: boolean;
    onToggle: () => void;
    formatValue?: (value: number | string) => string;
    searchTerm?: string;
    onSearchChange?: (term: string) => void;
    allowCustom?: boolean;
  }) => {
    const [localSearch, setLocalSearch] = useState('');
    const searchValue = searchTerm !== undefined ? searchTerm : localSearch;
    
    const filteredOptions = options.filter(option =>
      option.label.toLowerCase().includes(searchValue.toLowerCase())
    );

    return (
      <div className="relative picker-container">
        <div 
          onClick={onToggle}
          className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 cursor-pointer flex items-center justify-between bg-white"
        >
          <span className="text-gray-900">
            {formatValue ? formatValue(value) : `${value} ${placeholder}`}
          </span>
          <svg 
            className={`w-5 h-5 text-gray-400 transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`} 
            fill="none" 
            stroke="currentColor" 
            viewBox="0 0 24 24"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7"></path>
          </svg>
        </div>
        
        {isOpen && (
          <div className="absolute z-50 w-full mt-2 bg-white border border-gray-300 rounded-xl shadow-lg max-h-80 overflow-hidden">
            <div className="p-3 border-b border-gray-200">
              <input
                type="text"
                placeholder={`Search ${placeholder.toLowerCase()}...`}
                value={searchValue}
                onChange={(e) => {
                  const newValue = e.target.value;
                  if (onSearchChange) {
                    onSearchChange(newValue);
                  } else {
                    setLocalSearch(newValue);
                  }
                }}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                autoFocus
              />
            </div>
            <div className="max-h-60 overflow-auto">
              {allowCustom && searchValue && !filteredOptions.some(opt => opt.label.toLowerCase() === searchValue.toLowerCase()) && (
                <div
                  onClick={() => {
                    onChange(searchValue);
                    onToggle();
                  }}
                  className="flex items-center p-3 hover:bg-blue-50 cursor-pointer transition-colors border-b border-gray-100"
                >
                  <span className="text-blue-600 font-medium">
                    Add "{searchValue}"
                  </span>
                </div>
              )}
              {filteredOptions.map((option) => (
                <div
                  key={option.value}
                  onClick={() => {
                    onChange(option.value);
                    onToggle();
                  }}
                  className="flex items-center p-3 hover:bg-gray-50 cursor-pointer transition-colors"
                >
                  <span className="text-gray-800 font-medium">
                    {option.label}
                  </span>
                </div>
              ))}
              {filteredOptions.length === 0 && !allowCustom && (
                <div className="p-3 text-gray-500 text-center">
                  No matches found
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    );
  };

  // Holiday calendar tile content - show holiday name inside calendar
  const tileContent = ({ date }: { date: Date }) => {
    if (isIndianHoliday(date)) {
      const holidayName = getHolidayName(date);
      // Smart truncation for better readability
      let displayName = holidayName;
      if (holidayName.length > 8) {
        // For longer names, take first 5-6 characters + ".."
        displayName = holidayName.substring(0, 5) + '..';
      }
      
      return (
        <div className="flex flex-col justify-between h-full w-full">
          <div></div> {/* Spacer to push text to bottom */}
          <div className="text-xs font-bold leading-none text-left w-full px-1" 
               style={{ fontSize: '8px', lineHeight: '1' }}>
            {displayName}
          </div>
        </div>
      );
    }
    return null;
  };

  // Holiday calendar tile class name
  const tileClassName = ({ date }: { date: Date }) => {
    if (isIndianHoliday(date)) {
      return 'holiday-tile';
    }
    return '';
  };

  // State for search terms
  const [destinationSearch, setDestinationSearch] = useState('');
  const [departureSearch, setDepartureSearch] = useState('');

  if (submitted) {
    return (
              <div className="min-h-screen flex items-center justify-center p-4" style={{ background: 'linear-gradient(135deg, #f0fdf4 0%, #ffffff 50%, #ecfdf5 100%)' }}>
        <div className="bg-white rounded-2xl shadow-2xl p-10 max-w-md w-full text-center border border-gray-100">
          <div className="mb-8">
            <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-emerald-100 mb-4">
              <svg className="h-8 w-8 text-emerald-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
              </svg>
            </div>
            <img 
              src={tripxploLogo} 
              alt="TripXplo Logo" 
              className="h-12 mx-auto mb-4"
            />
          </div>
          <h3 className="text-2xl font-bold text-gray-900 mb-3" style={{ color: '#15ae8b' }}>Thank You!</h3>
          <p className="text-gray-600 mb-6 leading-relaxed">
            Your travel inquiry has been submitted successfully. Our travel experts will contact you within 
            <span className="font-semibold" style={{ color: '#15ae8b' }}> 24 hours </span>
            with personalized recommendations.
          </p>
          <div className="bg-blue-50 rounded-lg p-4 mb-6">
            <p className="text-sm text-gray-700">
              📱 Keep your phone handy - we'll call you soon!
            </p>
          </div>
          <div className="text-sm text-gray-500 border-t pt-4">
            <p className="font-medium">TripXplo by Tripmilestone (P) Ltd.</p>
            <p className="text-xs mt-1">Making Travel Dreams Come True</p>
          </div>
        </div>
      </div>
    );
  }

  return (
          <div className="min-h-screen py-6 px-4" style={{ background: 'linear-gradient(135deg, #f0fdf4 0%, #ffffff 50%, #ecfdf5 100%)' }}>
      <div className="max-w-2xl mx-auto">
        {/* Header with Logo */}
        <div className="text-center mb-6">
          <div className="bg-white rounded-2xl p-6 shadow-xl border border-gray-100 mb-4" style={{ boxShadow: '0 10px 15px -5px rgba(37, 150, 190, 0.08), 0 4px 6px -2px rgba(37, 150, 190, 0.03)' }}>
            <img 
              src={tripxploLogo} 
              alt="TripXplo Logo" 
              className="h-20 mx-auto mb-0"
            />
            <div className="text-base font-semibold text-[#15ae8b] tracking-wide" style={{marginTop: 0}}>Book Smart, Travel Easy!</div>
            <h1 className="text-3xl font-bold mb-2" style={{ color: '#15ae8b' }}>Travel Inquiry Form</h1>
            <p className="text-gray-600 text-base mb-3">
              Share your travel preferences and get <span className="font-semibold" style={{ color: '#15ae8b' }}>personalized recommendations</span> within 24 hours
            </p>
          </div>
        </div>

        {/* Main Form */}
        <div className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden mb-4">
          <div className="p-5 border-b border-gray-200" style={{ background: 'linear-gradient(135deg, #f0fdf4 0%, #ecfdf5 100%)' }}>
            <h2 className="text-xl font-bold" style={{ color: '#15ae8b' }}>✈️ Travel Details</h2>
            <p className="text-gray-600 mt-1 text-sm">Tell us about your dream vacation and we'll create the perfect package for you</p>
          </div>
          
          <form onSubmit={handleSubmit} className="p-5 space-y-4">
            {error && (
              <div className="bg-red-50 border-l-4 border-red-400 text-red-700 px-4 py-3 rounded-lg">
                <div className="flex">
                  <svg className="h-5 w-5 text-red-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                  </svg>
                  {error}
                </div>
              </div>
            )}

            {/* Name Field */}
            <div>
              <label className="block text-sm font-semibold text-gray-700 mb-2">
                👤 Your Name *
              </label>
              <input
                type="text"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                placeholder="Enter your full name"
                className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                required
              />
            </div>

            {/* Travel Date with Modern Calendar */}
            <div>
              <label className="block text-sm font-semibold text-gray-700 mb-2">
                📅 Travel Date
                {formData.travel_date && isIndianHolidayString(formData.travel_date) && (
                  <span className="ml-2 px-2 py-1 bg-orange-100 text-orange-800 text-xs rounded-full">
                    🎉 {getHolidayNameString(formData.travel_date)}
                  </span>
                )}
              </label>
              
              <div className="relative picker-container">
                <div 
                  onClick={() => setShowDatePicker(!showDatePicker)}
                  className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 cursor-pointer flex items-center justify-between bg-white"
                >
                  <div className="flex items-center">
                    <svg className="w-5 h-5 text-gray-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                    </svg>
                    <span className={`${formData.travel_date ? 'text-gray-900' : 'text-gray-500'}`}>
                      {formData.travel_date ? new Date(formData.travel_date).toLocaleDateString('en-IN', { 
                        weekday: 'short',
                        year: 'numeric', 
                        month: 'short', 
                        day: 'numeric' 
                      }) : 'Select travel date'}
                    </span>
                  </div>
                  <svg 
                    className={`w-5 h-5 text-gray-400 transition-transform duration-200 ${showDatePicker ? 'rotate-180' : ''}`} 
                    fill="none" 
                    stroke="currentColor" 
                    viewBox="0 0 24 24"
                  >
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7"></path>
                  </svg>
                </div>
                
                {showDatePicker && (
                  <div className="absolute z-50 mt-2 bg-white border border-gray-300 rounded-xl shadow-lg">
                    <Calendar
                      onChange={(date) => {
                        if (date instanceof Date) {
                          setFormData(prev => ({ 
                            ...prev, 
                            travel_date: date.toISOString().split('T')[0] 
                          }));
                          setShowDatePicker(false);
                        }
                      }}
                      value={formData.travel_date ? new Date(formData.travel_date) : null}
                      minDate={new Date()}
                      tileContent={tileContent}
                      tileClassName={tileClassName}
                      className="modern-calendar"
                    />
                  </div>
                )}
              </div>
              
              {formData.travel_date && isIndianHolidayString(formData.travel_date) && (
                <p className="text-xs text-orange-600 mt-1">
                  ✨ Great choice! This is a popular travel date during {getHolidayNameString(formData.travel_date)}.
                </p>
              )}
              
                             <style>
                 {`
                   .modern-calendar {
                     border: none;
                     border-radius: 12px;
                     font-family: inherit;
                     box-shadow: 0 10px 25px rgba(0,0,0,0.1);
                   }
                   .modern-calendar .react-calendar__navigation {
                     background: #f8fafc;
                     border-radius: 12px 12px 0 0;
                     margin-bottom: 0;
                     padding: 4px 0;
                   }
                   .modern-calendar .react-calendar__navigation button {
                     color: #374151;
                     font-weight: 600;
                     border-radius: 6px;
                   }
                   .modern-calendar .react-calendar__navigation button:hover {
                     background: #e0f2fe;
                   }
                   .modern-calendar .react-calendar__month-view__weekdays__weekday {
                     color: #15ae8b;
                     font-weight: 700;
                     text-decoration: none;
                   }
                   .modern-calendar .react-calendar__tile {
                     border: none;
                     background: none;
                     color: #374151;
                     height: 50px;
                     display: flex;
                     flex-direction: column;
                     align-items: center;
                     justify-content: flex-start;
                     padding: 2px;
                     position: relative;
                     border-radius: 8px;
                     transition: all 0.2s ease-in-out;
                   }
                   .modern-calendar .react-calendar__tile:hover {
                     background: #e0f2fe;
                     transform: scale(1.05);
                   }
                   .modern-calendar .react-calendar__tile--active {
                     background: #15ae8b !important;
                     color: white !important;
                     transform: scale(1.1);
                   }
                   .modern-calendar .react-calendar__tile--now {
                     background: #e0f2fe;
                     border: 2px solid #93c5fd;
                   }
                   .holiday-tile {
                     background: #e0f2fe !important;
                     color: #1e7ba2 !important;
                     font-weight: 700;
                     border: 2px solid #15ae8b;
                   }
                   .holiday-tile:hover {
                     background: #cceafc !important;
                     border-color: #1e7ba2;
                   }
                   .holiday-tile .react-calendar__tile__content {
                     height: 100%;
                     display: flex;
                     flex-direction: column;
                     align-items: center;
                     justify-content: space-between;
                   }
                 `}
               </style>
            </div>

            {/* Destination Picker with Search */}
            <div>
              <label className="block text-sm font-semibold text-gray-700 mb-2">
                🏖️ What's <span style={{ color: '#15ae8b' }} className="italic">your pick</span> for your next vacation? *
              </label>
              
              <div className="relative picker-container">
                <div 
                  onClick={() => setShowDestinationPicker(!showDestinationPicker)}
                  className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 cursor-pointer flex items-center justify-between bg-white"
                >
                  <div className="flex items-center">
                    <svg className="w-5 h-5 text-gray-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                    <span className={`${formData.destination ? 'text-gray-900' : 'text-gray-500'}`}>
                      {formData.destination || 'Pick your destination'}
                    </span>
                  </div>
                  <svg 
                    className={`w-5 h-5 text-gray-400 transition-transform duration-200 ${showDestinationPicker ? 'rotate-180' : ''}`} 
                    fill="none" 
                    stroke="currentColor" 
                    viewBox="0 0 24 24"
                  >
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7"></path>
                  </svg>
                </div>
                
                {showDestinationPicker && (
                  <div className="absolute z-50 w-full mt-2 bg-white border border-gray-300 rounded-xl shadow-lg max-h-80 overflow-hidden">
                    <div className="p-3 border-b border-gray-200">
                      <input
                        type="text"
                        placeholder="Search destinations..."
                        value={destinationSearch}
                        onChange={(e) => setDestinationSearch(e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                        autoFocus
                      />
                    </div>
                    <div className="max-h-60 overflow-auto">
                      {/* Add custom destination option */}
                      {destinationSearch && !FEATURED_DESTINATIONS.some(dest => 
                        dest.name.toLowerCase() === destinationSearch.toLowerCase()
                      ) && (
                        <div
                          onClick={() => {
                            setFormData(prev => ({ ...prev, destination: destinationSearch }));
                            setShowDestinationPicker(false);
                            setDestinationSearch('');
                          }}
                          className="flex items-center p-3 hover:bg-blue-50 cursor-pointer transition-colors border-b border-gray-100"
                        >
                          <span className="text-blue-600 font-medium">
                            Add "{destinationSearch}"
                          </span>
                        </div>
                      )}
                      
                      {/* Domestic Section */}
                      {FEATURED_DESTINATIONS.filter(dest => 
                        dest.isDomestic && 
                        dest.name.toLowerCase().includes(destinationSearch.toLowerCase())
                      ).length > 0 && (
                        <div className="mb-4">
                          <h4 className="text-sm font-bold text-blue-600 mb-2 px-3 py-2 bg-blue-50">🇮🇳 DOMESTIC DESTINATIONS</h4>
                          <div className="space-y-1">
                            {FEATURED_DESTINATIONS.filter(dest => 
                              dest.isDomestic && 
                              dest.name.toLowerCase().includes(destinationSearch.toLowerCase())
                            ).map((dest, index) => (
                              <div
                                key={`domestic-${index}`}
                                onClick={() => {
                                  setFormData(prev => ({ ...prev, destination: dest.name }));
                                  setShowDestinationPicker(false);
                                  setDestinationSearch('');
                                }}
                                className="flex items-center justify-between p-3 hover:bg-gray-50 cursor-pointer transition-colors group"
                              >
                                <span className="text-gray-800 font-medium group-hover:text-gray-900">
                                  {dest.name}
                                </span>
                                {dest.tag && (
                                  <span className={`px-2 py-1 text-xs font-medium rounded-full ${dest.color}`}>
                                    {dest.tag}
                                  </span>
                                )}
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                      
                      {/* International Section */}
                      {FEATURED_DESTINATIONS.filter(dest => 
                        !dest.isDomestic && 
                        dest.name.toLowerCase().includes(destinationSearch.toLowerCase())
                      ).length > 0 && (
                        <div className="border-t pt-4">
                          <h4 className="text-sm font-bold text-green-600 mb-2 px-3 py-2 bg-green-50">🌍 INTERNATIONAL DESTINATIONS</h4>
                          <div className="space-y-1">
                            {FEATURED_DESTINATIONS.filter(dest => 
                              !dest.isDomestic && 
                              dest.name.toLowerCase().includes(destinationSearch.toLowerCase())
                            ).map((dest, index) => (
                              <div
                                key={`international-${index}`}
                                onClick={() => {
                                  setFormData(prev => ({ ...prev, destination: dest.name }));
                                  setShowDestinationPicker(false);
                                  setDestinationSearch('');
                                }}
                                className="flex items-center justify-between p-3 hover:bg-gray-50 cursor-pointer transition-colors group"
                              >
                                <span className="text-gray-800 font-medium group-hover:text-gray-900">
                                  {dest.name}
                                </span>
                                {dest.tag && (
                                  <span className={`px-2 py-1 text-xs font-medium rounded-full ${dest.color}`}>
                                    {dest.tag}
                                  </span>
                                )}
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                      
                      {FEATURED_DESTINATIONS.filter(dest => 
                        dest.name.toLowerCase().includes(destinationSearch.toLowerCase())
                      ).length === 0 && !destinationSearch && (
                        <div className="p-3 text-gray-500 text-center">
                          Start typing to search destinations...
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>
              
              <p className="text-xs text-gray-500 mt-1">
                💡 Type to search or pick from popular destinations with special offers
              </p>
            </div>

            {/* People Count, Children, Infants and Nights */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-2">
                  👥 No. of People (Adults)
                </label>
                <AutocompleteDropdown
                  options={PEOPLE_OPTIONS}
                  value={formData.people_count}
                  onChange={(value) => setFormData(prev => ({ ...prev, people_count: value as number }))}
                  placeholder="Adults"
                  isOpen={showPeoplePicker}
                  onToggle={() => setShowPeoplePicker(!showPeoplePicker)}
                />
              </div>
              
              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-2">
                  🌙 Duration
                </label>
                <AutocompleteDropdown
                  options={NIGHTS_OPTIONS}
                  value={formData.nights}
                  onChange={(value) => setFormData(prev => ({ ...prev, nights: value as number }))}
                  placeholder="Nights"
                  isOpen={showNightsPicker}
                  onToggle={() => setShowNightsPicker(!showNightsPicker)}
                  formatValue={(value) => `${value}N/${Number(value) + 1}D`}
                />
              </div>
            </div>

            {/* Children and Infants */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-2">
                  👶 Children (2-12 years)
                </label>
                <AutocompleteDropdown
                  options={Array.from({ length: 10 }, (_, i) => ({
                    value: i,
                    label: i === 0 ? 'No Children' : `${i} ${i === 1 ? 'Child' : 'Children'}`
                  }))}
                  value={formData.children}
                  onChange={(value) => setFormData(prev => ({ ...prev, children: value as number }))}
                  placeholder="Children"
                  isOpen={showChildrenPicker}
                  onToggle={() => setShowChildrenPicker(!showChildrenPicker)}
                  formatValue={(value) => value === 0 ? 'No Children' : `${value} ${value === 1 ? 'Child' : 'Children'}`}
                />
              </div>
              
              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-2">
                  🍼 Infants (Under 2 years)
                </label>
                <AutocompleteDropdown
                  options={Array.from({ length: 5 }, (_, i) => ({
                    value: i,
                    label: i === 0 ? 'No Infants' : `${i} ${i === 1 ? 'Infant' : 'Infants'}`
                  }))}
                  value={formData.infants}
                  onChange={(value) => setFormData(prev => ({ ...prev, infants: value as number }))}
                  placeholder="Infants"
                  isOpen={showInfantsPicker}
                  onToggle={() => setShowInfantsPicker(!showInfantsPicker)}
                  formatValue={(value) => value === 0 ? 'No Infants' : `${value} ${value === 1 ? 'Infant' : 'Infants'}`}
                />
              </div>
            </div>

            {/* Departure City with Search */}
            <div>
              <label className="block text-sm font-semibold text-gray-700 mb-2">
                🛫 Departure City
              </label>
                              <AutocompleteDropdown
                 options={DEPARTURE_CITIES.map(city => ({ value: city, label: city }))}
                 value={formData.departure_city}
                 onChange={(value) => setFormData(prev => ({ ...prev, departure_city: value as string }))}
                 placeholder="departure city"
                 isOpen={showDeparturePicker}
                 onToggle={() => setShowDeparturePicker(!showDeparturePicker)}
                 searchTerm={departureSearch}
                 onSearchChange={setDepartureSearch}
                 allowCustom={true}
                 formatValue={(value) => (value as string) || 'Select departure city'}
               />
            </div>

            {/* Contact Information */}
                          <div className="bg-green-50 rounded-xl p-6 space-y-4">
              <h3 className="text-lg font-semibold" style={{ color: '#15ae8b' }}>📞 Contact Information</h3>
              
              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-2">
                  📱 Mobile No *
                </label>
                <div className="phone-input-container">
                  <PhoneInput
                    international
                    defaultCountry="IN"
                    value={formData.mobile}
                    onChange={(value) => setFormData(prev => ({ ...prev, mobile: value || '' }))}
                    className="w-full"
                    style={{
                      '--PhoneInputCountryFlag-borderColor': 'transparent',
                      '--PhoneInputCountrySelectArrow-color': '#6b7280',
                    } as React.CSSProperties}
                  />
                  <style>
                    {`
                      .phone-input-container .PhoneInputInput {
                        width: 100%;
                        padding: 12px 16px;
                        border: 1px solid #d1d5db;
                        border-radius: 12px;
                        outline: none;
                        transition: all 0.2s;
                        font-size: 16px;
                      }
                      .phone-input-container .PhoneInputInput:focus {
                        border-color: #3b82f6;
                        box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5);
                      }
                      .phone-input-container .PhoneInputCountrySelect {
                        margin-right: 8px;
                      }
                    `}
                  </style>
                </div>
              </div>

              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-2">
                  📧 Email ID
                </label>
                <input
                  type="email"
                  name="email"
                  value={formData.email}
                  onChange={handleInputChange}
                  placeholder="<EMAIL>"
                  className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                />
              </div>
            </div>

            {/* Requests - Collapsible */}
            <div className="border border-gray-200 rounded-xl overflow-hidden">
              <button
                type="button"
                onClick={() => setRequestsExpanded(!requestsExpanded)}
                className="w-full px-6 py-4 text-left flex justify-between items-center bg-gray-50 hover:bg-gray-100 transition-colors"
              >
                <div>
                  <span className="text-sm font-semibold text-gray-700">💭 Special Requests</span>
                  <p className="text-xs text-gray-500">Any preferences or special requirements</p>
                </div>
                <svg 
                  className={`w-5 h-5 transition-transform duration-200 ${requestsExpanded ? 'rotate-180' : ''}`} 
                  fill="none" 
                  stroke="#15ae8b" 
                  viewBox="0 0 24 24"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7"></path>
                </svg>
              </button>
              {requestsExpanded && (
                <div className="p-6 border-t border-gray-200 bg-white">
                  <textarea
                    name="requests"
                    value={formData.requests}
                    onChange={handleInputChange}
                    rows={4}
                    placeholder="Tell us about any special preferences, dietary requirements, accessibility needs, or specific activities you'd like to include..."
                    className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 resize-none"
                  />
                </div>
              )}
            </div>

            {/* Submit Button */}
            <div className="pt-4">
              <button
                type="submit"
                disabled={isSubmitting}
                className={`w-full py-4 px-6 rounded-xl text-white font-bold text-lg transition-all duration-300 transform hover:scale-105 focus:outline-none focus:ring-4 focus:ring-offset-2 ${
                  isSubmitting 
                    ? 'bg-gray-400 cursor-not-allowed transform-none' 
                    : 'shadow-lg hover:shadow-xl'
                }`}
                style={{ 
                  backgroundColor: isSubmitting ? '#9CA3AF' : '#15ae8b',
                                      boxShadow: isSubmitting ? 'none' : '0 10px 25px -5px rgba(21, 174, 139, 0.4), 0 4px 6px -2px rgba(21, 174, 139, 0.05)'
                }}
              >
                {isSubmitting ? (
                  <div className="flex items-center justify-center">
                    <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Submitting Your Request...
                  </div>
                ) : (
                  '🚀 Get My Personalized Package'
                )}
              </button>
              <p className="text-center text-xs text-gray-500 mt-3">
                🔒 Your information is secure and will only be used to contact you about your travel inquiry
              </p>
            </div>
          </form>
        </div>

        {/* Features Section */}
        <div className="mt-6 bg-white rounded-2xl shadow-lg border border-gray-100 p-5">
          <div className="text-center mb-6">
            <h3 className="text-xl font-bold mb-2" style={{ color: '#15ae8b' }}>Why Choose TripXplo?</h3>
            <p className="text-gray-600 text-sm">Your trusted travel partner since inception</p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                          <div className="text-center p-6 rounded-xl border-2 border-green-200 hover:border-green-300 transition-colors" style={{ background: 'linear-gradient(135deg, #f0fdf4 0%, #ffffff 100%)' }}>
              <img src={optionIcon} alt="Transparent Pricing" className="w-10 h-10 mx-auto mb-4" />
              <h4 className="font-bold text-gray-800 mb-2" style={{ color: '#15ae8b' }}>Transparent Pricing</h4>
              <p className="text-sm text-gray-600">No Hidden Costs</p>
            </div>
            <div className="text-center p-6 rounded-xl border-2 border-green-200 hover:border-green-300 transition-colors" style={{ background: 'linear-gradient(135deg, #f0fdf4 0%, #ffffff 100%)' }}>
              <img src={hotelIcon} alt="Customize" className="w-10 h-10 mx-auto mb-4" />
              <h4 className="font-bold text-gray-800 mb-2" style={{ color: '#15ae8b' }}>100+ Hotels</h4>
              <p className="text-sm text-gray-600">Customize Your Stay</p>
            </div>
            <div className="text-center p-6 rounded-xl border-2 border-yellow-200 hover:border-yellow-300 transition-colors" style={{ background: 'linear-gradient(135deg, #fffef7 0%, #ffffff 100%)' }}>
              <img src={starIcon} alt="Star Rating" className="w-10 h-10 mx-auto mb-4" />
              <h4 className="font-bold text-gray-800 mb-2" style={{ color: '#15ae8b' }}>4.9★ Rating</h4>
              <p className="text-sm text-gray-600">100+ Happy Reviews</p>
            </div>
          </div>
        </div>

        {/* About Us - Collapsible */}
        <div className="mt-6 bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden">
          <button
            type="button"
            onClick={() => setAboutExpanded(!aboutExpanded)}
            className="w-full px-8 py-6 text-left flex justify-between items-center hover:bg-blue-50 transition-colors"
          >
            <div>
              <span className="text-lg font-bold" style={{ color: '#15ae8b' }}>🏢 About TripXplo</span>
              <p className="text-sm text-gray-600 mt-1">Learn about our journey and achievements</p>
            </div>
            <svg 
              className={`w-5 h-5 transition-transform duration-200 ${aboutExpanded ? 'rotate-180' : ''}`} 
              fill="none" 
              stroke="#15ae8b" 
              viewBox="0 0 24 24"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7"></path>
            </svg>
          </button>
          {aboutExpanded && (
            <div className="bg-gradient-to-br from-green-50 to-white border-t border-gray-100">
              <div className="p-8">
                <div className="space-y-6 text-gray-700 leading-relaxed">
                  <div className="bg-white rounded-xl p-6 border-l-4 shadow-sm" style={{ borderColor: '#15ae8b' }}>
                    <h4 className="font-bold text-gray-800 mb-3 flex items-center">
                      <span className="w-2 h-2 rounded-full mr-3" style={{ backgroundColor: '#15ae8b' }}></span>
                      Our Vision
                    </h4>
                    <p>
                      <span className="font-semibold" style={{ color: '#15ae8b' }}>TripXplo's</span> user-friendly web app offers travellers an 
                      <span className="font-semibold text-green-600"> all-in-one solution</span> to plan and book their 
                      <span className="font-semibold text-orange-600"> dream vacations</span>. Our innovative web app simplifies the entire travel experience, 
                      from destination selection to receiving <span className="font-semibold" style={{ color: '#15ae8b' }}>personalized trip recommendations</span>, all
                      while prioritizing <span className="font-semibold text-red-600">traveller safety and security</span>.
                    </p>
                  </div>
                  
                  <div className="bg-white rounded-xl p-6 border-l-4 border-green-500 shadow-sm">
                    <h4 className="font-bold text-gray-800 mb-3 flex items-center">
                      <span className="w-2 h-2 bg-green-500 rounded-full mr-3"></span>
                      Government Recognition
                    </h4>
                    <p>
                      <span className="font-semibold text-gray-800">Tripmilestone Tours Pvt Limited</span>, the parent company of TripXplo, 
                      is <span className="font-semibold text-green-600">officially recognized</span> by 
                      <span className="font-semibold text-blue-600"> Startup India</span> and the 
                      <span className="font-semibold text-purple-600">Department for Promotion of Industry and Internal Trade (DPIIT)</span> under
                      the Ministry of Commerce & Industry.
                    </p>
                  </div>
                  
                  <div className="bg-white rounded-xl p-6 border-l-4 border-orange-500 shadow-sm">
                    <h4 className="font-bold text-gray-800 mb-3 flex items-center">
                      <span className="w-2 h-2 bg-orange-500 rounded-full mr-3"></span>
                      Official Launch
                    </h4>
                    <p>
                      <span className="font-semibold" style={{ color: '#15ae8b' }}>TripXplo Online Platform</span> was 
                      <span className="font-semibold text-orange-600"> officially launched</span> by 
                      <span className="font-semibold text-green-600">StartupTN</span>, 
                      <span className="font-semibold text-blue-600">Government of Tamil Nadu</span>.
                    </p>
                  </div>
                  
                  <div className="text-center pt-4">
                    <div className="inline-flex items-center space-x-4 bg-white px-6 py-3 rounded-full shadow-md border border-gray-200">
                      <span className="text-sm font-medium text-gray-600">🏆 Startup India Recognized</span>
                      <span className="w-1 h-1 bg-gray-400 rounded-full"></span>
                      <span className="text-sm font-medium text-gray-600">🚀 Government Launched</span>
                      <span className="w-1 h-1 bg-gray-400 rounded-full"></span>
                      <span className="text-sm font-medium text-gray-600">⭐ 4.9 Rated</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Updated Footer with Contact Information */}
        <div className="text-center mt-6 p-5 bg-white rounded-2xl shadow-lg border border-gray-100">
          <div className="space-y-4">
                              <p className="text-lg font-semibold" style={{ color: '#15ae8b' }}>
              🤝 We'll be happy to assist you!
            </p>
            <p className="text-sm text-gray-600">
              Share your travel dreams with us and get ready for an unforgettable journey
            </p>
            
            {/* Contact Links */}
            <div className="bg-gradient-to-r from-green-50 to-green-100 rounded-xl p-6 border border-green-200">
              <h3 className="text-lg font-bold mb-4" style={{ color: '#15ae8b' }}>📞 Contact Us</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                <div className="flex items-center justify-center space-x-2">
                  <svg className="w-4 h-4" style={{ color: '#25D366' }} fill="currentColor" viewBox="0 0 24 24">
                    <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488z"/>
                  </svg>
                  <a 
                    href="https://wa.me/919442424492" 
                    target="_blank" 
                    rel="noopener noreferrer" 
                    className="text-gray-700 hover:text-green-600 font-medium transition-colors"
                  >
                    WhatsApp: 9442424492
                  </a>
                </div>
                <div className="flex items-center justify-center space-x-2">
                                      <svg className="w-4 h-4" style={{ color: '#15ae8b' }} fill="currentColor" viewBox="0 0 20 20">
                    <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"></path>
                    <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"></path>
                  </svg>
                  <a 
                    href="mailto:<EMAIL>" 
                    className="text-gray-700 hover:text-blue-600 font-medium transition-colors"
                  >
                    <EMAIL>
                  </a>
                </div>
                <div className="flex items-center justify-center space-x-2">
                  <svg className="w-4 h-4" style={{ color: '#E4405F' }} fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
                  </svg>
                  <a 
                    href="https://www.instagram.com/mytripxplo/" 
                    target="_blank" 
                    rel="noopener noreferrer" 
                    className="text-gray-700 hover:text-pink-600 font-medium transition-colors"
                  >
                    @mytripxplo
                  </a>
                </div>
              </div>
            </div>
            
            <div className="pt-2 border-t border-gray-200">
              <p className="font-bold text-gray-800">
                ~ <span style={{ color: '#15ae8b' }}>TripXplo</span> by <span className="text-gray-600">Tripmilestone (P) Ltd.</span>
              </p>
              <p className="text-xs text-gray-500 mt-1">Making Travel Dreams Come True ✈️</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CompactTravelForm; 