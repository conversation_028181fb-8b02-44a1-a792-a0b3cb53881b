-- SQL migration script to create notes table for multiple notes per lead
-- This replaces the single notes column approach with a normalized notes table

-- Create the notes table for storing multiple notes per lead
CREATE TABLE IF NOT EXISTS public.notes (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    lead_id UUID NOT NULL,
    info TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Foreign key constraint to leads table
    CONSTRAINT fk_notes_lead_id 
        FOREIGN KEY (lead_id) 
        REFERENCES public.leads(id) 
        ON DELETE CASCADE
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_notes_lead_id ON public.notes(lead_id);
CREATE INDEX IF NOT EXISTS idx_notes_created_at ON public.notes(created_at);

-- Create a function to automatically update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_notes_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- <PERSON>reate trigger to automatically update updated_at on note updates
DROP TRIGGER IF EXISTS trigger_update_notes_updated_at ON public.notes;
CREATE TRIGGER trigger_update_notes_updated_at
    BEFORE UPDATE ON public.notes
    FOR EACH ROW
    EXECUTE FUNCTION update_notes_updated_at();

-- Enable Row Level Security (RLS) if needed
ALTER TABLE public.notes ENABLE ROW LEVEL SECURITY;

-- Create RLS policies (adjust based on your authentication setup)
-- Policy for authenticated users to manage notes
CREATE POLICY "Enable all operations for authenticated users" ON public.notes
    FOR ALL USING (auth.role() = 'authenticated');

-- Alternative: More restrictive policy based on user ownership
-- Uncomment and modify if you have user-based access control
-- CREATE POLICY "Users can manage notes for their leads" ON public.notes
--     FOR ALL USING (
--         lead_id IN (
--             SELECT id FROM public.leads 
--             WHERE assigned_to = auth.uid() OR created_by = auth.uid()
--         )
--     );

-- Optional: Migrate existing notes from leads.info column to notes table
-- Uncomment and run this section if you have existing notes to migrate
/*
INSERT INTO public.notes (lead_id, info, created_at, updated_at)
SELECT 
    id as lead_id,
    info,
    created_at,
    updated_at
FROM public.leads 
WHERE info IS NOT NULL AND info != '';

-- After successful migration, you can optionally clear the info column
-- UPDATE public.leads SET info = NULL WHERE info IS NOT NULL;
*/

-- Verify the table was created successfully
SELECT 
    'Notes table created successfully!' as message,
    COUNT(*) as existing_notes
FROM public.notes;
