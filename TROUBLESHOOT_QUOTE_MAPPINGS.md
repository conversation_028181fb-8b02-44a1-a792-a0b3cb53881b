# Quote Mappings Table Missing - Troubleshooting Guide

## Current Issue
You're getting the error: "Database table missing. Please contact administrator to set up the quote_mappings table."

## Quick Diagnosis Steps

### Step 1: Check Which Database You're Connected To
1. Go to the Quote Mapping tab
2. Click the **"Show Database"** button
3. This will show you exactly which Supabase instance you're connected to

**Expected Result:** Should show `https://lkqbrlrmrsnbtkoryazq.supabase.co`

### Step 2: Test Database Connection
1. Click the **"Test Database"** button
2. This will give you detailed information about what's working and what's not

## Most Likely Scenarios

### Scenario A: Table Doesn't Exist in Quote Database
**If the "Show Database" button shows the correct URL but table is missing:**

1. **Go to your Quote Supabase Dashboard:**
   - URL: https://lkqbrlrmrsnbtkoryazq.supabase.co
   - Navigate to SQL Editor

2. **Run the table creation script:**
   - Copy the entire content of `create-quote-mappings-table.sql`
   - Paste it in the SQL Editor
   - Execute the script

3. **Verify creation:**
   - The script will show "SUCCESS: Table exists and is ready to use"
   - Test the application again

### Scenario B: Connected to Wrong Database
**If the "Show Database" button shows a different URL:**

This means the environment configuration is pointing to the wrong database.

**Check your `.env` file:**
```
VITE_SUPABASE_URL_QUOTE=https://lkqbrlrmrsnbtkoryazq.supabase.co
VITE_SUPABASE_ANON_KEY_QUOTE=your-quote-anon-key
```

**If the URL is wrong:**
1. Update the `.env` file with the correct Quote database URL
2. Restart the development server (`npm run dev`)
3. Test again

### Scenario C: Authentication Issues
**If you get permission errors:**

1. **Check authentication:**
   - Click "Check Auth" button
   - Should show "Authentication verified"

2. **If authentication fails:**
   - Refresh the page
   - Clear browser cache
   - Try logging in again

### Scenario D: RLS Policy Issues
**If table exists but you get permission denied:**

Run the RLS policy check script in your Quote Supabase dashboard:
```sql
-- Check RLS policies
SELECT policyname, cmd FROM pg_policies WHERE tablename = 'quote_mappings';
```

If no policies exist, run the `create-quote-mappings-table.sql` script.

## Step-by-Step Solution

### 1. Immediate Fix (Most Common)
```sql
-- Run this in your Quote Supabase SQL Editor
-- URL: https://lkqbrlrmrsnbtkoryazq.supabase.co

CREATE TABLE IF NOT EXISTS quote_mappings (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    quote_id UUID NOT NULL,
    quote_name TEXT NOT NULL,
    customer_name TEXT NOT NULL,
    destination TEXT NOT NULL,
    hotel_mappings JSONB DEFAULT '[]'::jsonb,
    vehicle_mappings JSONB DEFAULT '[]'::jsonb,
    additional_costs JSONB DEFAULT '{}'::jsonb,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(quote_id)
);

-- Enable RLS and create policies
ALTER TABLE quote_mappings ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view all quote mappings" ON quote_mappings FOR SELECT USING (true);
CREATE POLICY "Users can insert quote mappings" ON quote_mappings FOR INSERT WITH CHECK (true);
CREATE POLICY "Users can update quote mappings" ON quote_mappings FOR UPDATE USING (true);
CREATE POLICY "Users can delete quote mappings" ON quote_mappings FOR DELETE USING (true);

-- Grant permissions
GRANT ALL ON quote_mappings TO anon, authenticated;
```

### 2. Test the Fix
1. Go back to the Quote Mapping tab
2. Click "Test Database" - should now show success
3. Try selecting a quote and saving mappings

### 3. If Still Not Working
1. Check browser console for detailed error messages
2. Verify you're in the correct Supabase project
3. Ensure the anon key in your `.env` file is correct for the Quote database

## Environment Configuration Check

Your `.env` file should have:
```
# Quote Database (where quote_mappings should be created)
VITE_SUPABASE_URL_QUOTE=https://lkqbrlrmrsnbtkoryazq.supabase.co
VITE_SUPABASE_ANON_KEY_QUOTE=your-quote-anon-key

# CRM Database (different from Quote)
VITE_SUPABASE_URL_CRM=https://tlfwcnikdlwoliqzavxj.supabase.co
VITE_SUPABASE_ANON_KEY_CRM=your-crm-anon-key
```

## Verification Commands

After creating the table, verify with these SQL commands:

```sql
-- Check table exists
SELECT table_name FROM information_schema.tables WHERE table_name = 'quote_mappings';

-- Check RLS policies
SELECT policyname, cmd FROM pg_policies WHERE tablename = 'quote_mappings';

-- Test basic access
SELECT COUNT(*) FROM quote_mappings;
```

## Support

If you continue to have issues:
1. Run the diagnostic buttons and note the exact error messages
2. Check which database URL is being used
3. Verify the table exists in that specific database
4. Contact support with the specific error messages and database URL
