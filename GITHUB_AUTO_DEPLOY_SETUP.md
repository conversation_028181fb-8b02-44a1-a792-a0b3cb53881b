# 🚀 Automatic GitHub Deployment Setup for crm.tripxplo.com

This guide will set up automatic deployment from GitHub to your production server at crm.tripxplo.com. Every time you push code to the `master` or `main` branch, it will automatically build and deploy to your server.

## 📋 Prerequisites

- ✅ GitHub repository with your TripXplo-CRM code
- ✅ Production server at *************
- ✅ Domain `crm.tripxplo.com` pointing to your server
- ✅ SSH access to your server

## 🔧 Step 1: Generate SSH Key for GitHub Actions

First, we need to create an SSH key that GitHub Actions will use to connect to your server.

### On your local machine (or server):

```bash
# Generate a new SSH key specifically for GitHub Actions
ssh-keygen -t rsa -b 4096 -f ~/.ssh/github_actions_key -N ""

# This creates two files:
# ~/.ssh/github_actions_key (private key - for GitHub secrets)
# ~/.ssh/github_actions_key.pub (public key - for server)
```

### Copy the public key to your server:

```bash
# Copy public key to server
ssh-copy-id -i ~/.ssh/github_actions_key.pub root@*************

# Or manually:
cat ~/.ssh/github_actions_key.pub | ssh root@************* "mkdir -p ~/.ssh && cat >> ~/.ssh/authorized_keys"
```

### Test the SSH connection:

```bash
# Test that the key works
ssh -i ~/.ssh/github_actions_key root@************* "echo 'SSH connection successful!'"
```

## 🔐 Step 2: Configure GitHub Secrets

Go to your GitHub repository and add these secrets:

### 2.1 Go to Repository Settings
1. Open your GitHub repository
2. Click on **Settings** tab
3. Click on **Secrets and variables** → **Actions**
4. Click **New repository secret**

### 2.2 Add Required Secrets

**SERVER_IP**
```
Name: SERVER_IP
Value: *************
```

**SSH_PRIVATE_KEY**
```bash
# Get your private key content
cat ~/.ssh/github_actions_key
```
```
Name: SSH_PRIVATE_KEY
Value: [Paste the entire private key content including -----BEGIN and -----END lines]
```

**Supabase Configuration Secrets**

Add all your Supabase credentials:

```
Name: VITE_SUPABASE_URL_CRM
Value: https://tlfwcnikdlwoliqzavxj.supabase.co

Name: VITE_SUPABASE_ANON_KEY_CRM  
Value: [Your CRM database anon key]

Name: VITE_SUPABASE_URL_QUOTE
Value: https://lkqbrlrmrsnbtkoryazq.supabase.co

Name: VITE_SUPABASE_ANON_KEY_QUOTE
Value: [Your Quote database anon key]

Name: VITE_SUPABASE_URL
Value: https://tlfwcnikdlwoliqzavxj.supabase.co

Name: VITE_SUPABASE_ANON_KEY
Value: [Your main Supabase anon key]
```

**EMAIL Configuration Secrets (NEW - REQUIRED)**

```
Name: EMAIL_USER
Value: [Your email username for sending quotes]

Name: EMAIL_PASS
Value: [Your email password or app-specific password]
```

## 🖥️ Step 3: Prepare Your Server

Ensure your server has the necessary software installed:

```bash
# SSH into your server
ssh root@*************

# Update system
apt update && apt upgrade -y

# Install Nginx (if not already installed)
apt install nginx -y
systemctl start nginx
systemctl enable nginx

# Install Certbot for SSL (if not already installed)
apt install certbot python3-certbot-nginx -y

# Create web directory
mkdir -p /var/www/crm
chown -R www-data:www-data /var/www/crm

# Allow HTTP and HTTPS through firewall
ufw allow 'Nginx Full'
ufw allow ssh
```

## 🌐 Step 4: DNS Configuration

Ensure your DNS is properly configured:

**Add these DNS records to your domain provider:**

| Type | Name | Value | TTL |
|------|------|-------|-----|
| A | crm | ************* | 300 |
| A | www.crm | ************* | 300 |

**Verify DNS propagation:**
```bash
# Check DNS resolution
nslookup crm.tripxplo.com
dig crm.tripxplo.com
```

## ✅ Step 5: Test the Setup

### 5.1 Push Your Code

The GitHub Actions workflow is already created in `.github/workflows/deploy.yml`. Now you can test it:

```bash
# Make a small change to test
echo "# Auto-deploy test" >> README.md

# Commit and push
git add .
git commit -m "🚀 Test automatic deployment"
git push origin master
```

### 5.2 Monitor the Deployment

1. Go to your GitHub repository
2. Click on **Actions** tab
3. You should see your deployment workflow running
4. Click on the workflow to see detailed logs

### 5.3 Verify the Website

After the deployment completes:
- Visit http://crm.tripxplo.com (should redirect to HTTPS)
- Visit https://crm.tripxplo.com
- Check that your CRM application loads correctly

## 🔧 How It Works

### Automatic Deployment Process:

1. **Trigger**: You push code to `master` or `main` branch
2. **Build**: GitHub Actions builds your React app using `npm run build`
3. **Deploy**: Built files are uploaded to `/var/www/crm` on your server
4. **Configure**: Nginx is automatically configured (first time only)
5. **SSL**: SSL certificate is automatically obtained (first time only)
6. **Verify**: Deployment is tested to ensure it's working

### What Gets Deployed:

- ✅ Latest code from your GitHub repository
- ✅ Production build with optimizations
- ✅ Environment variables from GitHub secrets
- ✅ Nginx configuration for SPA routing
- ✅ SSL certificate for HTTPS
- ✅ Security headers and CORS setup

## 🛡️ Security Features

### Built-in Security:

- ✅ SSH key-based authentication (no passwords)
- ✅ Environment variables stored as encrypted secrets
- ✅ Automatic HTTPS redirect
- ✅ Security headers (XSS protection, content type sniffing)
- ✅ CORS headers for Supabase integration

## 🔄 Benefits of This Setup

### No More Manual Deployment:

- ✅ **Push to deploy**: Just push your code, deployment happens automatically
- ✅ **Zero downtime**: Smart deployment process with backups
- ✅ **Always up-to-date**: Your production site always matches your latest code
- ✅ **Rollback capability**: Previous versions are automatically backed up
- ✅ **Build verification**: Code is built and tested before deployment

### Development Workflow:

```bash
# Your new workflow is simply:
git add .
git commit -m "Add new feature"
git push origin master

# That's it! No more:
# - Manual npm run build
# - Manual file uploads
# - Manual server configuration
# - Manual SSL setup
```

## 🚨 Troubleshooting

### If deployment fails:

1. **Check GitHub Actions logs** for specific error messages
2. **Verify SSH access** to your server manually
3. **Check DNS resolution** for crm.tripxplo.com
4. **Ensure secrets are correct** in GitHub repository settings

### Common issues:

#### SSH Connection Failed
```bash
# Test SSH connection manually
ssh -i ~/.ssh/github_actions_key root@*************
```

#### Build Failed
- Check that all environment variables are correctly set in GitHub secrets
- Verify the build works locally: `npm install && npm run build`

#### Missing Environment Variables
- Ensure all required secrets are added to GitHub:
  - `SERVER_IP`
  - `SSH_PRIVATE_KEY`
  - `VITE_SUPABASE_URL_CRM`
  - `VITE_SUPABASE_ANON_KEY_CRM`
  - `VITE_SUPABASE_URL_QUOTE`
  - `VITE_SUPABASE_ANON_KEY_QUOTE`
  - `EMAIL_USER` (NEW)
  - `EMAIL_PASS` (NEW)

#### Site Not Loading
- Check Nginx status: `systemctl status nginx`
- Check DNS: `nslookup crm.tripxplo.com`
- Check firewall: `ufw status`

#### SSL Certificate Issues
```bash
# Check SSL certificate status
ssh root@************* "certbot certificates"

# Manual SSL setup if needed
ssh root@************* "certbot --nginx -d crm.tripxplo.com -d www.crm.tripxplo.com"
```

## 📞 Support Commands

### Useful server commands:

```bash
# Check deployment status
ssh root@************* "ls -la /var/www/crm/"

# Check Nginx configuration
ssh root@************* "nginx -t"

# Check SSL certificate
ssh root@************* "certbot certificates"

# View deployment backups
ssh root@************* "ls -la /var/www/crm.backup.*"

# Check latest deployment logs
ssh root@************* "tail -f /var/log/nginx/access.log"
```

### Test deployment manually:

```bash
# Test if site is accessible
curl -I https://crm.tripxplo.com

# Test if application loads
curl -s https://crm.tripxplo.com | grep -i "tripxplo"
```

## 🎉 Success!

Once set up, your deployment workflow is:

1. **Code** → Write your code locally
2. **Commit** → `git commit -m "New feature"`
3. **Push** → `git push origin master`
4. **Deploy** → GitHub automatically deploys to crm.tripxplo.com
5. **Live** → Your changes are live!

**Your CRM will be automatically deployed to: https://crm.tripxplo.com**

---

## 🔄 Manual Override

If you ever need to deploy manually (emergency fixes), you can:

1. Go to GitHub Actions tab
2. Click on "Deploy to Production" workflow
3. Click "Run workflow"
4. Select the branch and click "Run workflow"

This gives you both automatic and manual deployment options! 🚀 

## 🔍 New Debugging Features

The updated workflow now includes:

- ✅ Better error messages and debugging information
- ✅ Verification of all required secrets
- ✅ Build output validation
- ✅ SSH connection testing
- ✅ Post-deployment verification
- ✅ Automatic cleanup and backups

Your deployment should now work reliably! 🎯 