{"name": "tripxplo-crm", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview --port=3002", "promo:dev": "next dev", "promo:build": "next build", "promo:start": "next start", "promo:lint": "next lint"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@emailjs/browser": "^4.4.1", "@fortawesome/fontawesome-free": "^7.0.0", "@headlessui/react": "^2.2.4", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^3.9.1", "@radix-ui/react-accordion": "1.2.2", "@radix-ui/react-alert-dialog": "1.1.4", "@radix-ui/react-aspect-ratio": "1.1.1", "@radix-ui/react-avatar": "1.1.2", "@radix-ui/react-checkbox": "1.1.3", "@radix-ui/react-collapsible": "1.1.2", "@radix-ui/react-context-menu": "2.2.4", "@radix-ui/react-dialog": "1.1.4", "@radix-ui/react-dropdown-menu": "2.1.4", "@radix-ui/react-hover-card": "1.1.4", "@radix-ui/react-label": "2.1.1", "@radix-ui/react-menubar": "1.1.4", "@radix-ui/react-navigation-menu": "1.2.3", "@radix-ui/react-popover": "1.1.4", "@radix-ui/react-progress": "1.1.1", "@radix-ui/react-radio-group": "1.2.2", "@radix-ui/react-scroll-area": "1.2.2", "@radix-ui/react-select": "2.1.4", "@radix-ui/react-separator": "1.1.1", "@radix-ui/react-slider": "1.2.2", "@radix-ui/react-slot": "1.1.1", "@radix-ui/react-switch": "^1.1.1", "@radix-ui/react-tabs": "1.1.2", "@radix-ui/react-toast": "1.2.4", "@radix-ui/react-toggle": "1.1.1", "@radix-ui/react-toggle-group": "1.1.1", "@radix-ui/react-tooltip": "1.1.6", "@supabase/supabase-js": "^2.50.0", "@types/uuid": "^10.0.0", "axios": "^1.9.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.4", "date-fns": "^4.1.0", "emailjs-com": "^3.2.0", "embla-carousel-react": "8.5.1", "html2canvas": "^1.4.1", "indian-holidays": "^2.0.1", "input-otp": "1.4.1", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "libphonenumber-js": "^1.12.10", "lucide-react": "^0.503.0", "next": "15.2.4", "next-themes": "^0.4.4", "react": "^19.0.0", "react-calendar": "^6.0.0", "react-day-picker": "^9.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.54.1", "react-hot-toast": "^2.5.2", "react-icons": "^5.5.0", "react-phone-number-input": "^3.4.12", "react-resizable-panels": "^2.1.7", "react-router-dom": "^7.5.1", "recharts": "2.15.0", "sonner": "^2.0.6", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0", "vaul": "^1.1.2"}, "devDependencies": {"@eslint/js": "^9.22.0", "@types/axios": "^0.9.36", "@types/node": "^20", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.7.0", "autoprefixer": "^10.4.20", "eslint": "^9.22.0", "eslint-config-next": "15.2.4", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "postcss": "^8.4.31", "tailwindcss": "^3.4.0", "typescript": "~5.7.2", "typescript-eslint": "^8.26.1", "vite": "^6.3.1"}}