import React, { useState, useEffect } from 'react';
import { fetchLeadById } from '../lib/leadService';
import { Lead } from './LeadCard';
import { Spinner } from './Spinner';

interface LeadDetailModalProps {
  leadId: string | null;
  onClose: () => void;
}

interface LeadDetailModalPropsWithLead extends LeadDetailModalProps {
  leads?: Lead[]; // Optional array of leads for fallback
}

const LeadDetailModal: React.FC<LeadDetailModalPropsWithLead> = ({ leadId, onClose, leads }) => {
  const [lead, setLead] = useState<Lead | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    console.log('[LeadDetailModal] useEffect triggered with leadId:', leadId);
    if (!leadId) {
      console.log('[LeadDetailModal] No leadId provided, clearing state');
      setLead(null);
      setError(null);
      return;
    }

    const fetchLead = async () => {
      setIsLoading(true);
      setError(null);

      try {
        // First try to find the lead in the provided leads array (for sample data)
        if (leads) {
          const foundLead = leads.find(l => l.id === leadId);
          if (foundLead) {
            console.log('[LeadDetailModal] Found lead in provided array:', foundLead);
            setLead(foundLead);
            setIsLoading(false);
            return;
          }
        }

        // If not found in array or no array provided, fetch from database
        console.log('[LeadDetailModal] Fetching lead from database:', leadId);
        const result = await fetchLeadById(leadId);

        if (result.success && result.data) {
          console.log('[LeadDetailModal] Successfully fetched lead:', result.data);
          setLead(result.data);
        } else {
          console.error('[LeadDetailModal] Failed to fetch lead:', result.error);
          setError(result.error?.message || 'Failed to fetch lead details');
        }
      } catch (err: any) {
        console.error('[LeadDetailModal] Exception:', err);
        setError(err.message || 'An unexpected error occurred');
      } finally {
        setIsLoading(false);
      }
    };

    fetchLead();
  }, [leadId, leads]);

  if (!leadId) {
    console.log('[LeadDetailModal] Rendering null - no leadId');
    return null;
  }

  console.log('[LeadDetailModal] Rendering modal for leadId:', leadId);

  const formatDate = (dateString?: string) => {
    if (!dateString) return 'Not set';

    try {
      const date = new Date(dateString);
      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
    } catch (e) {
      return 'Invalid date';
    }
  };

  const getPriorityClass = (priority?: string) => {
    switch(priority?.toLowerCase()) {
      case 'high':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      default:
        return 'bg-green-100 text-green-800 border-green-200';
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="sticky top-0 bg-white border-b border-gray-200 px-6 py-4 rounded-t-lg">
          <div className="flex justify-between items-center">
            <h2 className="text-2xl font-bold text-gray-800">Lead Details</h2>
            <button
              onClick={onClose}
              className="text-gray-500 hover:text-gray-700 focus:outline-none p-1 rounded-full hover:bg-gray-100 transition-colors"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="px-6 py-4">
          {isLoading && (
            <div className="flex flex-col items-center justify-center py-8">
              <Spinner size="lg" />
              <p className="text-gray-500 mt-4">Loading lead details...</p>
            </div>
          )}

          {error && (
            <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
              <h3 className="font-medium">Error Loading Lead</h3>
              <p className="text-sm mt-1">{error}</p>
            </div>
          )}

          {lead && !isLoading && !error && (
            <div className="space-y-6">
              {/* Customer Information */}
              <div className="bg-gray-50 rounded-lg p-4">
                <h3 className="text-lg font-semibold text-gray-800 mb-3 flex items-center">
                  <svg className="w-5 h-5 mr-2 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                  </svg>
                  Customer Information
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium text-gray-600">Name</label>
                    <p className="text-gray-800 font-medium">{lead.customer_name}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">Email</label>
                    <p className="text-gray-800">{lead.email}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">Phone</label>
                    <p className="text-gray-800">{lead.phone || 'Not provided'}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">Priority</label>
                    <span className={`inline-block px-2 py-1 text-xs font-medium rounded-full border ${getPriorityClass(lead.priority)}`}>
                      {lead.priority || 'Low'}
                    </span>
                  </div>
                </div>
              </div>

              {/* Travel Information */}
              <div className="bg-gray-50 rounded-lg p-4">
                <h3 className="text-lg font-semibold text-gray-800 mb-3 flex items-center">
                  <svg className="w-5 h-5 mr-2 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                  </svg>
                  Travel Information
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium text-gray-600">Destination</label>
                    <p className="text-gray-800">{lead.destination || 'Not specified'}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">Travel Date</label>
                    <p className="text-gray-800">{formatDate(lead.travel_date)}</p>
                  </div>
                </div>
              </div>

              {/* Lead Management */}
              <div className="bg-gray-50 rounded-lg p-4">
                <h3 className="text-lg font-semibold text-gray-800 mb-3 flex items-center">
                  <svg className="w-5 h-5 mr-2 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                  </svg>
                  Lead Management
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium text-gray-600">Status</label>
                    <p className="text-gray-800 font-medium">{lead.status}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">Lead Source</label>
                    <p className="text-gray-800">{lead.lead_source || 'Not specified'}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">Created Date</label>
                    <p className="text-gray-800">{formatDate(lead.created_at)}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">Last Updated</label>
                    <p className="text-gray-800">{formatDate(lead.updated_at)}</p>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="sticky bottom-0 bg-white border-t border-gray-200 px-6 py-4 rounded-b-lg">
          <div className="flex justify-end space-x-3">
            <button
              onClick={onClose}
              className="px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 transition-colors"
            >
              Close
            </button>
            {lead && (
              <button
                onClick={() => {
                  // TODO: Implement edit functionality
                  console.log('Edit lead:', lead.id);
                }}
                className="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-dark transition-colors"
              >
                Edit Lead
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default LeadDetailModal;