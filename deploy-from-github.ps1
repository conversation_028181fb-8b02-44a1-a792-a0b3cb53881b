# Deploy TripXplo CRM from GitHub to crm.tripxplo.com
# This script clones from GitHub and deploys to the server

Write-Host "🚀 Deploying TripXplo CRM from GitHub to crm.tripxplo.com..." -ForegroundColor Green
Write-Host "================================================================" -ForegroundColor Green

# Configuration
$GITHUB_USERNAME = "sathishshah"
$GITHUB_REPO = "TripXplo-CRM"
$SERVER_IP = "*************"
$SERVER_USER = "root"
$SUBDOMAIN = "crm.tripxplo.com"

# Get GitHub Personal Access Token
if (-not $env:GITHUB_PAT) {
    Write-Host "🔐 GitHub Personal Access Token Required" -ForegroundColor Cyan
    Write-Host "Please enter your GitHub Personal Access Token (PAT):" -ForegroundColor Yellow
    Write-Host "Note: The token will be hidden for security" -ForegroundColor Gray
    $secureToken = Read-Host "GitHub PAT" -AsSecureString
    $GITHUB_PAT = [Runtime.InteropServices.Marshal]::PtrToStringAuto([Runtime.InteropServices.Marshal]::SecureStringToBSTR($secureToken))
} else {
    $GITHUB_PAT = $env:GITHUB_PAT
    Write-Host "✅ Using GitHub PAT from environment variable" -ForegroundColor Green
}

# Validate GitHub credentials
Write-Host "🔍 Validating GitHub credentials..." -ForegroundColor Cyan
$authUrl = "https://$GITHUB_USERNAME`:$<EMAIL>/user"
try {
    $response = Invoke-RestMethod -Uri $authUrl -Method Get -ErrorAction Stop
    Write-Host "✅ GitHub authentication successful! Welcome, $($response.name)" -ForegroundColor Green
} catch {
    Write-Host "❌ GitHub authentication failed. Please check your PAT." -ForegroundColor Red
    Write-Host "Make sure your PAT has 'repo' permissions." -ForegroundColor Yellow
    exit 1
}

# Create deployment script for server
$deploymentScript = @"
#!/bin/bash
set -e

echo "🚀 Starting GitHub deployment for TripXplo CRM..."

# Configuration
GITHUB_USERNAME="$GITHUB_USERNAME"
GITHUB_REPO="$GITHUB_REPO"
GITHUB_PAT="$GITHUB_PAT"
DOMAIN="$SUBDOMAIN"
WEB_DIR="/var/www/crm"
TEMP_DIR="/tmp/crm-github-deploy"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
CYAN='\033[0;36m'
NC='\033[0m'

echo -e "\$CYAN📁 Setting up deployment directories...\$NC"

# Clean up any previous deployment
rm -rf \$TEMP_DIR
mkdir -p \$TEMP_DIR
cd \$TEMP_DIR

echo -e "\$CYAN📥 Cloning repository from GitHub...\$NC"

# Clone the repository using PAT
git clone https://\$GITHUB_USERNAME:\$<EMAIL>/\$GITHUB_USERNAME/\$GITHUB_REPO.git .

if [ \$? -ne 0 ]; then
    echo -e "\$RED❌ Failed to clone repository\$NC"
    exit 1
fi

echo -e "\$GREEN✅ Repository cloned successfully\$NC"

echo -e "\$CYAN📦 Installing dependencies...\$NC"

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo -e "\$YELLOW📦 Installing Node.js...\$NC"
    curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
    apt-get install -y nodejs
fi

# Check if npm is available
if ! command -v npm &> /dev/null; then
    echo -e "\$RED❌ npm not found\$NC"
    exit 1
fi

echo -e "\$CYAN📦 Installing project dependencies...\$NC"
npm install

if [ \$? -ne 0 ]; then
    echo -e "\$RED❌ Failed to install dependencies\$NC"
    exit 1
fi

echo -e "\$CYAN🔨 Building production application...\$NC"
npm run build

if [ \$? -ne 0 ]; then
    echo -e "\$RED❌ Build failed\$NC"
    exit 1
fi

echo -e "\$GREEN✅ Build completed successfully\$NC"

echo -e "\$CYAN📁 Deploying to web directory...\$NC"

# Create web directory if it doesn't exist
mkdir -p \$WEB_DIR

# Backup current deployment (if exists)
if [ -d "\$WEB_DIR" ] && [ "\$(ls -A \$WEB_DIR)" ]; then
    echo -e "\$YELLOW💾 Backing up current deployment...\$NC"
    cp -r \$WEB_DIR \$WEB_DIR.backup.\$(date +%Y%m%d_%H%M%S)
fi

# Clear web directory
rm -rf \$WEB_DIR/*

# Copy built files
cp -r dist/* \$WEB_DIR/

# Set proper permissions
chown -R www-data:www-data \$WEB_DIR
chmod -R 755 \$WEB_DIR

echo -e "\$GREEN✅ Files deployed successfully\$NC"

echo -e "\$CYAN🌐 Configuring Nginx...\$NC"

# Create Nginx configuration
cat > /etc/nginx/sites-available/\$DOMAIN << 'EOF'
server {
    listen 80;
    server_name crm.tripxplo.com www.crm.tripxplo.com;
    
    root /var/www/crm;
    index index.html index.htm;
    
    location / {
        try_files \$uri \$uri/ /index.html;
    }
    
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot|map)\$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header Access-Control-Allow-Origin "*";
    }
    
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Access-Control-Allow-Origin "*" always;
    add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS" always;
    add_header Access-Control-Allow-Headers "DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization" always;
}
EOF

# Enable the site
ln -sf /etc/nginx/sites-available/\$DOMAIN /etc/nginx/sites-enabled/

# Test Nginx configuration
echo -e "\$CYAN🧪 Testing Nginx configuration...\$NC"
nginx -t

if [ \$? -eq 0 ]; then
    echo -e "\$GREEN✅ Nginx configuration is valid\$NC"
    systemctl reload nginx
    echo -e "\$GREEN✅ Nginx reloaded successfully\$NC"
else
    echo -e "\$RED❌ Nginx configuration test failed\$NC"
    exit 1
fi

# Setup SSL certificate
echo -e "\$CYAN🔒 Setting up SSL certificate...\$NC"

# Check if Certbot is installed
if ! command -v certbot &> /dev/null; then
    echo -e "\$YELLOW📦 Installing Certbot...\$NC"
    apt update
    apt install -y certbot python3-certbot-nginx
fi

# Get SSL certificate
certbot --nginx -d \$DOMAIN -d www.\$DOMAIN --non-interactive --agree-tos --email <EMAIL> --redirect || {
    echo -e "\$YELLOW⚠️ SSL certificate setup failed. The site will be available over HTTP only.\$NC"
    echo -e "\$YELLOW Please ensure DNS is properly configured for \$DOMAIN\$NC"
}

# Clean up
echo -e "\$CYAN🧹 Cleaning up temporary files...\$NC"
cd /
rm -rf \$TEMP_DIR

echo ""
echo -e "\$GREEN🎉 GitHub deployment completed successfully!\$NC"
echo -e "\$CYAN🌐 HTTP: http://\$DOMAIN\$NC"
echo -e "\$CYAN🔒 HTTPS: https://\$DOMAIN (if SSL setup succeeded)\$NC"
echo ""

# Test the deployment
echo -e "\$CYAN🧪 Testing deployment...\$NC"
curl -I http://\$DOMAIN || echo -e "\$YELLOW⚠️ HTTP test failed - check DNS configuration\$NC"

echo ""
echo -e "\$GREEN📊 Deployment Summary:\$NC"
echo -e "Repository: https://github.com/\$GITHUB_USERNAME/\$GITHUB_REPO"
echo -e "Branch: main/master"
echo -e "Web Directory: \$WEB_DIR"
echo -e "Domain: \$DOMAIN"
echo -e "Status: \$(systemctl is-active nginx)"
"@

# Save deployment script to temporary file
$deploymentScript | Out-File -FilePath "github-deploy.sh" -Encoding UTF8

Write-Host "📤 Uploading deployment script to server..." -ForegroundColor Cyan

# Upload the deployment script to server
try {
    & scp "github-deploy.sh" "$SERVER_USER@$SERVER_IP`:/tmp/github-deploy.sh"
    Write-Host "✅ Deployment script uploaded successfully" -ForegroundColor Green
} catch {
    Write-Host "❌ Failed to upload deployment script to server" -ForegroundColor Red
    exit 1
}

# Execute the deployment script on server
Write-Host "🚀 Executing GitHub deployment on server..." -ForegroundColor Cyan
Write-Host "This may take a few minutes to clone, build, and deploy..." -ForegroundColor Yellow

try {
    & ssh "$SERVER_USER@$SERVER_IP" "chmod +x /tmp/github-deploy.sh && /tmp/github-deploy.sh"
    
    Write-Host ""
    Write-Host "🎉 GitHub deployment completed successfully!" -ForegroundColor Green
    Write-Host "🌐 Your CRM is now live at: https://crm.tripxplo.com" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "📝 What was deployed:" -ForegroundColor Yellow
    Write-Host "✅ Latest code from GitHub repository" -ForegroundColor White
    Write-Host "✅ Enhanced lead editing functionality" -ForegroundColor White
    Write-Host "✅ Material Design Kanban board" -ForegroundColor White
    Write-Host "✅ Priority management system" -ForegroundColor White
    Write-Host "✅ Real-time updates and activity logging" -ForegroundColor White
    Write-Host ""
    Write-Host "🔄 Future deployments:" -ForegroundColor Yellow
    Write-Host "Just push to GitHub and run this script again!" -ForegroundColor White
} catch {
    Write-Host "❌ GitHub deployment failed" -ForegroundColor Red
    Write-Host "Check the server logs for more details" -ForegroundColor Yellow
}

# Clean up local files
Remove-Item "github-deploy.sh" -ErrorAction SilentlyContinue

Write-Host ""
Write-Host "📋 Next steps:" -ForegroundColor Green
Write-Host "1. Test your CRM at https://crm.tripxplo.com" -ForegroundColor White
Write-Host "2. Verify all lead editing features work" -ForegroundColor White
Write-Host "3. For future updates: git push + run this script" -ForegroundColor White 