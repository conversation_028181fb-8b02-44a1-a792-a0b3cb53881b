<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>TripXplo Family - Your Family Adventure, Planned & Paid Your Way</title>
    <meta name="description" content="Experience hassle-free family vacations with TripXplo's Family Prepaid EMI Packages. Discover, relax, and create lasting memories with flexible payment options." />

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&family=Volkhov:wght@400;700&display=swap" rel="stylesheet">

    <!-- Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Custom Styles for Landing Page Look -->
    <style>
      /* Reset and base styles */
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: 'Inter', sans-serif;
        line-height: 1.6;
        color: #333;
        background: linear-gradient(135deg, #f0f9ff 0%, #ffffff 50%, #faf5ff 100%);
        min-height: 100vh;
      }

      /* Loading screen styles */
      .loading-container {
        min-height: 100vh;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        background: linear-gradient(135deg, #f0f9ff 0%, #ffffff 50%, #faf5ff 100%);
        padding: 2rem;
      }

      .loading-content {
        text-align: center;
        max-width: 500px;
        width: 100%;
      }

      .loading-logo {
        font-size: 3rem;
        font-weight: 800;
        margin-bottom: 2rem;
      }

      .loading-logo .trip {
        color: #2563eb;
      }

      .loading-logo .xplo {
        color: #7c3aed;
      }

      .loading-spinner {
        width: 3rem;
        height: 3rem;
        border: 4px solid #e5e7eb;
        border-top: 4px solid #2563eb;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin: 0 auto 2rem;
      }

      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }

      .loading-title {
        font-size: 1.5rem;
        font-weight: 600;
        color: #1f2937;
        margin-bottom: 1rem;
      }

      .loading-subtitle {
        font-size: 1rem;
        color: #6b7280;
        margin-bottom: 2rem;
      }

      .loading-message {
        background: linear-gradient(135deg, #dbeafe 0%, #e0e7ff 100%);
        border: 1px solid #bfdbfe;
        border-radius: 0.75rem;
        padding: 1.5rem;
        margin-top: 2rem;
        color: #1e40af;
      }

      .loading-message p {
        margin-bottom: 1rem;
      }

      .loading-message p:last-child {
        margin-bottom: 0;
      }

      .retry-button {
        background: linear-gradient(135deg, #2563eb 0%, #7c3aed 100%);
        color: white;
        border: none;
        padding: 0.75rem 2rem;
        border-radius: 0.5rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.2s;
        margin-top: 1rem;
      }

      .retry-button:hover {
        transform: translateY(-1px);
        box-shadow: 0 10px 25px rgba(37, 99, 235, 0.3);
      }

      /* Error screen styles */
      .error-container {
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
        background: linear-gradient(135deg, #fef2f2 0%, #ffffff 50%, #fef2f2 100%);
        padding: 2rem;
      }

      .error-content {
        background: white;
        border: 1px solid #fecaca;
        border-radius: 1rem;
        padding: 2rem;
        max-width: 500px;
        width: 100%;
        text-align: center;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
      }

      .error-icon {
        width: 4rem;
        height: 4rem;
        background: #fee2e2;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1.5rem;
        color: #dc2626;
        font-size: 1.5rem;
      }

      .error-title {
        font-size: 1.5rem;
        font-weight: 700;
        color: #dc2626;
        margin-bottom: 1rem;
      }

      .error-message {
        color: #6b7280;
        margin-bottom: 2rem;
        line-height: 1.6;
      }

      .error-button {
        background: #dc2626;
        color: white;
        border: none;
        padding: 0.75rem 2rem;
        border-radius: 0.5rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.2s;
      }

      .error-button:hover {
        background: #b91c1c;
        transform: translateY(-1px);
      }

      /* Responsive design */
      @media (max-width: 768px) {
        .loading-logo {
          font-size: 2.5rem;
        }

        .loading-title {
          font-size: 1.25rem;
        }

        .loading-container,
        .error-container {
          padding: 1rem;
        }
      }

      /* Hide the React root initially */
      #root {
        opacity: 0;
        transition: opacity 0.3s ease-in-out;
      }

      #root.loaded {
        opacity: 1;
      }
    </style>
  </head>
  <body>
    <!-- Loading Screen (shown while React loads) -->
    <div id="initial-loading" class="loading-container">
      <div class="loading-content">
        <div class="loading-logo">
          <span class="trip">TRIP</span><span class="xplo">XPLO</span>
        </div>
        <div class="loading-spinner"></div>
        <h1 class="loading-title">Your Family Adventure Awaits</h1>
        <p class="loading-subtitle">Loading your personalized travel experience...</p>
      </div>
    </div>

    <!-- React App Root -->
    <div id="root"></div>

    <!-- Loading and initialization script -->
    <script>
      // Show loading screen initially
      let loadingTime = 0;
      let loadingInterval;

      // Start loading timer
      function startLoadingTimer() {
        loadingInterval = setInterval(() => {
          loadingTime++;

          // Update loading message based on time
          const loadingContainer = document.getElementById('initial-loading');
          const subtitle = loadingContainer?.querySelector('.loading-subtitle');

          if (subtitle) {
            if (loadingTime === 5) {
              subtitle.textContent = 'Setting up your travel dashboard...';
            } else if (loadingTime === 10) {
              subtitle.textContent = 'Connecting to travel services...';
            } else if (loadingTime === 15) {
              // Show extended loading message
              const loadingContent = loadingContainer.querySelector('.loading-content');
              if (loadingContent && !loadingContent.querySelector('.loading-message')) {
                const message = document.createElement('div');
                message.className = 'loading-message';
                message.innerHTML = `
                  <p><strong>Still connecting...</strong></p>
                  <p>The travel database might be waking up from sleep mode. This usually takes a moment.</p>
                `;
                loadingContent.appendChild(message);
              }
            } else if (loadingTime === 30) {
              // Show retry option
              const loadingContent = loadingContainer.querySelector('.loading-content');
              const existingMessage = loadingContent?.querySelector('.loading-message');
              if (existingMessage) {
                existingMessage.innerHTML = `
                  <p><strong>Connection is taking longer than usual</strong></p>
                  <p>The database might be waking up from sleep mode.</p>
                  <button class="retry-button" onclick="window.location.reload()">
                    Retry Connection
                  </button>
                `;
              }
            }
          }
        }, 1000);
      }

      // Hide loading screen when React is ready
      function hideLoadingScreen() {
        clearInterval(loadingInterval);
        const loadingScreen = document.getElementById('initial-loading');
        const root = document.getElementById('root');

        if (loadingScreen && root) {
          loadingScreen.style.opacity = '0';
          root.classList.add('loaded');

          setTimeout(() => {
            loadingScreen.style.display = 'none';
          }, 300);
        }
      }

      // Start the loading timer
      startLoadingTimer();

      // Listen for React app to be ready
      window.addEventListener('load', () => {
        // Give React a moment to render
        setTimeout(() => {
          const rootContent = document.getElementById('root');
          if (rootContent && rootContent.children.length > 0) {
            hideLoadingScreen();
          } else {
            // If React hasn't rendered yet, wait a bit more
            setTimeout(hideLoadingScreen, 2000);
          }
        }, 1000);
      });

      // Fallback: hide loading screen after maximum wait time
      setTimeout(hideLoadingScreen, 45000);

      // Handle errors
      window.addEventListener('error', (event) => {
        console.error('Application error:', event.error);

        // Show error screen if React fails to load
        setTimeout(() => {
          const root = document.getElementById('root');
          const loadingScreen = document.getElementById('initial-loading');

          if (root && root.children.length === 0) {
            clearInterval(loadingInterval);

            if (loadingScreen) {
              loadingScreen.innerHTML = `
                <div class="error-container">
                  <div class="error-content">
                    <div class="error-icon">
                      <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <h2 class="error-title">Unable to Load Application</h2>
                    <div class="error-message">
                      <p>There was a problem loading the TripXplo application.</p>
                      <p>Please check your internet connection and try again.</p>
                    </div>
                    <button class="error-button" onclick="window.location.reload()">
                      Reload Application
                    </button>
                  </div>
                </div>
              `;
            }
          }
        }, 5000);
      });
    </script>

    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
