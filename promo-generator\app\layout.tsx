import type { Metadata } from 'next'
import './globals.css'
import { ThemeProvider } from '@/components/theme-provider'

export const metadata: Metadata = {
  title: 'Travel Instagram Generator',
  description: 'Create stunning travel posts for Instagram',
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
        <link 
          href="https://fonts.googleapis.com/css2?family=<PERSON>ush<PERSON>+Script&family=Pacifico&family=Dancing+Script:wght@400;500;600;700&family=Lobster&family=Satisfy&family=Caveat:wght@400;500;600;700&family=Kalam:wght@300;400;700&family=The+Nautigal:wght@400;700&family=Bad+Script&family=Nothing+You+Could+Do&family=Gochi+Hand&family=Patrick+Hand&family=Oswald:wght@200;300;400;500;600;700&family=Bebas+Neue&family=Anton&family=Archivo+Black&family=Passion+One&family=Alfa+Slab+One&family=Luckiest+Guy&family=Bangers&family=Righteous&family=Fredoka+One:wght@300;400;500;600&family=Paytone+One&family=Graduate&family=Playfair+Display:wght@400;500;600;700;800;900&family=Lora:wght@400;500;600;700&family=Merriweather:wght@300;400;700;900&family=Cormorant+Garamond:wght@300;400;500;600;700&family=Libre+Baskerville:wght@400;700&family=Abril+Fatface&family=Prata&family=Vidaloka&family=Yeseva+One&family=Cinzel:wght@400;500;600;700;800;900&family=Vollkorn:wght@400;500;600;700;800;900&family=Cardo:wght@400;700&family=Amatic+SC:wght@400;700&family=Permanent+Marker&family=Rock+Salt&family=Cinzel+Decorative:wght@400;500;600;700;800;900&family=Uncial+Antiqua&family=Special+Elite&family=Architects+Daughter&family=Shrikhand&family=Quicksand:wght@300;400;500;600;700&family=Comfortaa:wght@300;400;500;600;700&family=Nunito+Sans:wght@300;400;500;600;700;800&family=Rubik:wght@300;400;500;600;700;800&family=Poppins:wght@300;400;500;600;700;800&family=Raleway:wght@300;400;500;600;700;800&family=Source+Sans+Pro:wght@300;400;600;700&family=Lato:wght@300;400;700&family=Montserrat:wght@300;400;500;600;700;800&family=Open+Sans:wght@300;400;500;600;700;800&display=swap" 
          rel="stylesheet" 
        />
      </head>
      <body className="antialiased">
        <ThemeProvider
          attribute="class"
          defaultTheme="system"
          enableSystem
          disableTransitionOnChange
        >
          {children}
        </ThemeProvider>
      </body>
    </html>
  )
}
