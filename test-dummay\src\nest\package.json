{"name": "tripxplo-family-emi", "version": "1.0.0", "description": "TripXplo Family Prepaid EMI Website - Backend API and Frontend", "main": "api/server.js", "scripts": {"start": "node api/server.js", "dev": "nodemon api/server.js", "build": "npm run build:frontend", "build:frontend": "echo 'Frontend build complete'", "test": "echo 'No tests specified'", "deploy": "npm run build && pm2 restart family-emi-api", "serve": "node -e \"const http = require('http'); const fs = require('fs'); const path = require('path'); const server = http.createServer((req, res) => { let filePath = '.' + req.url; if (filePath === './') filePath = './index.html'; const extname = String(path.extname(filePath)).toLowerCase(); const mimeTypes = { '.html': 'text/html', '.js': 'text/javascript', '.css': 'text/css', '.json': 'application/json', '.png': 'image/png', '.jpg': 'image/jpg', '.gif': 'image/gif', '.svg': 'image/svg+xml', '.wav': 'audio/wav', '.mp4': 'video/mp4', '.woff': 'application/font-woff', '.ttf': 'application/font-ttf', '.eot': 'application/vnd.ms-fontobject', '.otf': 'application/font-otf', '.wasm': 'application/wasm' }; const contentType = mimeTypes[extname] || 'application/octet-stream'; fs.readFile(filePath, (error, content) => { if (error) { if(error.code == 'ENOENT') { res.writeHead(404); res.end('File not found'); } else { res.writeHead(500); res.end('Server error: '+error.code); } } else { res.writeHead(200, { 'Content-Type': contentType }); res.end(content, 'utf-8'); } }); }); server.listen(8080, () => console.log('Frontend server running at http://localhost:8080/'));\""}, "keywords": ["travel", "emi", "family", "vacation", "booking", "tripxplo"], "author": "TripXplo Team", "license": "MIT", "dependencies": {"@supabase/supabase-js": "^2.38.0", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "helmet": "^7.1.0", "morgan": "^1.10.0", "rate-limiter-flexible": "^3.0.8"}, "devDependencies": {"nodemon": "^3.0.1"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "https://github.com/tripxplo/family-emi.git"}, "bugs": {"url": "https://github.com/tripxplo/family-emi/issues"}, "homepage": "https://family.tripxplo.com"}