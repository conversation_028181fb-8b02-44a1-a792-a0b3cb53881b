# Linode Deployment Guide - family.tripxplo.com

## 1. **Prepare Files for Production**

### Files to Upload (Production Ready):
```
nest/
├── index.html              # Main application
├── style.css              # Main styles
├── styleguide.css         # Style guide
├── globals.css            # Global styles
├── js/
│   ├── config.js          # Database configuration
│   ├── databaseService.js # Database service
│   ├── apiService.js      # API service
│   └── packageCardGenerator.js # Card generator
├── img/                   # All image assets
└── api/
    └── server.js          # Optional API server
```

### Files to Exclude (Test Files):
- All test-*.html files
- debug-*.html files
- emi-test.html
- database-test.html
- quick-test.html
- TESTING_INSTRUCTIONS.md
- package.json (if not using Node.js server)

## 2. **Linode Server Setup**

### Option A: Static Website Hosting (Recommended)

1. **Create Linode Instance**:
   - Choose Ubuntu 22.04 LTS
   - Select appropriate plan (Nanode 1GB is sufficient)
   - Choose region closest to your users

2. **Install Nginx**:
```bash
sudo apt update
sudo apt install nginx
sudo systemctl start nginx
sudo systemctl enable nginx
```

3. **Configure Domain**:
   - Point family.tripxplo.com to your Linode IP
   - Update DNS A record: family.tripxplo.com → YOUR_LINODE_IP

### Option B: Object Storage (Alternative)

1. **Create Object Storage Bucket**:
   - Name: family-tripxplo
   - Region: Choose closest to users

2. **Enable Static Website**:
   - Set index.html as index document
   - Configure CORS for API access

## 3. **File Upload Methods**

### Method 1: SCP Upload (Recommended)
```bash
# Create production folder locally
mkdir family-tripxplo-production
cd family-tripxplo-production

# Copy production files
cp -r ../src/nest/index.html .
cp -r ../src/nest/style.css .
cp -r ../src/nest/styleguide.css .
cp -r ../src/nest/globals.css .
cp -r ../src/nest/js .
cp -r ../src/nest/img .

# Upload to Linode
scp -r * root@YOUR_LINODE_IP:/var/www/html/
```

### Method 2: Git Deployment
```bash
# On Linode server
cd /var/www/html
git clone YOUR_REPOSITORY_URL .
# Copy only production files to web root
```

### Method 3: SFTP Upload
- Use FileZilla, WinSCP, or similar
- Connect to YOUR_LINODE_IP
- Upload files to /var/www/html/

## 4. **Nginx Configuration**

### Create Site Configuration:
```bash
sudo nano /etc/nginx/sites-available/family.tripxplo.com
```

### Configuration Content:
```nginx
server {
    listen 80;
    server_name family.tripxplo.com www.family.tripxplo.com;
    root /var/www/html;
    index index.html;

    # Enable gzip compression
    gzip on;
    gzip_types text/css application/javascript image/svg+xml;

    # Cache static assets
    location ~* \.(css|js|png|jpg|jpeg|gif|svg|ico)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # Main application
    location / {
        try_files $uri $uri/ /index.html;
    }

    # API proxy (if using backend)
    location /api/ {
        proxy_pass http://localhost:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

### Enable Site:
```bash
sudo ln -s /etc/nginx/sites-available/family.tripxplo.com /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

## 5. **SSL Certificate Setup**

### Install Certbot:
```bash
sudo apt install certbot python3-certbot-nginx
```

### Get SSL Certificate:
```bash
sudo certbot --nginx -d family.tripxplo.com -d www.family.tripxplo.com
```

### Auto-renewal:
```bash
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

## 6. **Database Configuration**

### Update config.js for Production:
```javascript
const config = {
  supabase: {
    url: 'YOUR_SUPABASE_URL',
    key: 'YOUR_SUPABASE_ANON_KEY'
  },
  // Production settings
  environment: 'production',
  debug: false
};
```

## 7. **Performance Optimization**

### Enable Compression:
```nginx
# In nginx.conf
gzip on;
gzip_vary on;
gzip_min_length 1024;
gzip_types text/css application/javascript application/json image/svg+xml;
```

### CDN Setup (Optional):
- Use Cloudflare for global CDN
- Point family.tripxplo.com through Cloudflare
- Enable caching and optimization

## 8. **Monitoring & Maintenance**

### Log Monitoring:
```bash
# Nginx access logs
sudo tail -f /var/log/nginx/access.log

# Nginx error logs
sudo tail -f /var/log/nginx/error.log
```

### Health Check Script:
```bash
#!/bin/bash
# health-check.sh
curl -f http://family.tripxplo.com || echo "Site is down!"
```

## 9. **Deployment Commands**

### Quick Deployment Script:
```bash
#!/bin/bash
# deploy.sh

# Backup current version
sudo cp -r /var/www/html /var/www/html.backup.$(date +%Y%m%d)

# Upload new files
scp -r production/* root@YOUR_LINODE_IP:/var/www/html/

# Set permissions
sudo chown -R www-data:www-data /var/www/html
sudo chmod -R 755 /var/www/html

# Reload nginx
sudo systemctl reload nginx

echo "Deployment complete!"
```

## 10. **Testing Checklist**

After deployment, test:
- [ ] Site loads at family.tripxplo.com
- [ ] SSL certificate works (https://)
- [ ] Package search functionality
- [ ] Database connections
- [ ] Package card generation
- [ ] Mobile responsiveness
- [ ] All images load correctly
- [ ] EMI calculations work
- [ ] Family type selections work

## 11. **Troubleshooting**

### Common Issues:

1. **Site not loading**:
   - Check DNS propagation
   - Verify nginx configuration
   - Check firewall settings

2. **Database connection errors**:
   - Verify Supabase credentials
   - Check CORS settings
   - Ensure API keys are correct

3. **Images not loading**:
   - Check file permissions
   - Verify image paths
   - Ensure all images uploaded

### Debug Commands:
```bash
# Check nginx status
sudo systemctl status nginx

# Test nginx configuration
sudo nginx -t

# Check DNS resolution
nslookup family.tripxplo.com

# Check SSL certificate
openssl s_client -connect family.tripxplo.com:443
```

## 12. **Final Steps**

1. Upload production files to Linode
2. Configure nginx for family.tripxplo.com
3. Set up SSL certificate
4. Test all functionality
5. Monitor logs for any issues
6. Set up automated backups

Your TripXplo Family EMI site will be live at https://family.tripxplo.com!
