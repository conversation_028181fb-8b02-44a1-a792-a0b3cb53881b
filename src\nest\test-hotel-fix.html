<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Hotel Fix - TripXplo</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background: #e8f5e8; border-color: #28a745; }
        .error { background: #ffe6e6; border-color: #dc3545; }
        .info { background: #e6f3ff; border-color: #007bff; }
        .warning { background: #fff3cd; border-color: #ffc107; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; font-size: 0.9rem; }
        button { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; }
        .hotel-item { padding: 8px; margin: 4px 0; background: #f8f9ff; border-left: 3px solid #667eea; border-radius: 4px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Hotel Fix Test</h1>
        <p>Testing hotel data fetching and display functionality</p>
        
        <button onclick="runFullTest()">🚀 Run Full Test</button>
        <button onclick="testSpecificQuote()">🎯 Test Specific Quote</button>
        <button onclick="clearResults()">🗑️ Clear Results</button>
        
        <div id="results"></div>
    </div>

    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
    <script src="js/config.js"></script>
    <script src="js/databaseService.js"></script>
    <script>
        function addResult(title, content, type = 'info') {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `test-section ${type}`;
            
            let contentHtml = '';
            if (typeof content === 'object') {
                contentHtml = `<pre>${JSON.stringify(content, null, 2)}</pre>`;
            } else {
                contentHtml = `<p>${content}</p>`;
            }
            
            div.innerHTML = `<h3>${title}</h3>${contentHtml}`;
            results.appendChild(div);
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        async function runFullTest() {
            clearResults();
            
            // Step 1: Test database connection
            addResult('🔍 Step 1: Testing Database Connection', 'Checking connection...', 'info');
            
            try {
                const { data: testData, error: testError } = await databaseService.quoteDB
                    .from('quotes')
                    .select('id, destination, package_name')
                    .limit(1);

                if (testError) {
                    addResult('❌ Database Connection Failed', testError, 'error');
                    return;
                }
                
                addResult('✅ Database Connection Success', `Found ${testData.length} quotes`, 'success');
            } catch (error) {
                addResult('❌ Database Connection Error', error.message, 'error');
                return;
            }

            // Step 2: Check hotel_rows table
            addResult('🏨 Step 2: Checking hotel_rows Table', 'Fetching hotel data...', 'info');
            
            try {
                const { data: hotelData, error: hotelError } = await databaseService.quoteDB
                    .from('hotel_rows')
                    .select('quote_id, hotel_name, stay_nights, meal_plan')
                    .limit(10);

                if (hotelError) {
                    addResult('❌ hotel_rows Table Error', hotelError, 'error');
                    return;
                }
                
                if (hotelData.length === 0) {
                    addResult('⚠️ No Hotel Data Found', 'hotel_rows table is empty', 'warning');
                    return;
                }
                
                // Group by quote_id
                const groupedHotels = {};
                hotelData.forEach(hotel => {
                    if (!groupedHotels[hotel.quote_id]) {
                        groupedHotels[hotel.quote_id] = [];
                    }
                    groupedHotels[hotel.quote_id].push(hotel);
                });
                
                addResult('✅ Hotel Data Found', {
                    total_hotels: hotelData.length,
                    quotes_with_hotels: Object.keys(groupedHotels).length,
                    sample_data: hotelData.slice(0, 3)
                }, 'success');

                // Step 3: Test package search with hotel enhancement
                addResult('📦 Step 3: Testing Package Search', 'Searching for packages...', 'info');
                
                const searchParams = {
                    destination: 'Andaman',
                    adults: 2,
                    child: 1,
                    children: 0,
                    infants: 0
                };

                const result = await databaseService.searchPackages(searchParams);
                
                if (!result.success) {
                    addResult('❌ Package Search Failed', result.error, 'error');
                    return;
                }
                
                if (result.packages.length === 0) {
                    addResult('⚠️ No Packages Found', 'No packages found for search criteria', 'warning');
                    return;
                }
                
                const pkg = result.packages[0];
                
                addResult('✅ Package Search Success', {
                    packages_found: result.packages.length,
                    first_package: {
                        title: pkg.title,
                        quote_id: pkg.quote_id,
                        has_hotels_list: !!(pkg.hotels_list && pkg.hotels_list.length > 0),
                        hotels_count: pkg.hotels_list ? pkg.hotels_list.length : 0,
                        hotel_name: pkg.hotel_name,
                        inclusions_count: pkg.inclusions ? pkg.inclusions.length : 0
                    }
                }, 'success');

                // Step 4: Test hotel enhancement specifically
                if (pkg.quote_id) {
                    addResult('🏨 Step 4: Testing Hotel Enhancement', `Testing quote_id: ${pkg.quote_id}`, 'info');
                    
                    const { data: specificHotels, error: specificError } = await databaseService.quoteDB
                        .from('hotel_rows')
                        .select('hotel_name, stay_nights, meal_plan, room_type, price')
                        .eq('quote_id', pkg.quote_id);

                    if (specificError) {
                        addResult('❌ Hotel Enhancement Failed', specificError, 'error');
                    } else if (specificHotels.length === 0) {
                        addResult('⚠️ No Hotels for Quote', `No hotels found for quote_id: ${pkg.quote_id}`, 'warning');
                    } else {
                        addResult('✅ Hotel Enhancement Success', {
                            quote_id: pkg.quote_id,
                            hotels_found: specificHotels.length,
                            hotels: specificHotels.map(h => ({
                                name: h.hotel_name,
                                nights: h.stay_nights,
                                meal_plan: h.meal_plan
                            }))
                        }, 'success');
                        
                        // Display formatted hotels
                        let hotelDisplay = '<h4>Formatted Hotel Display:</h4>';
                        specificHotels.forEach(hotel => {
                            const nights = hotel.stay_nights || 1;
                            const hotelName = hotel.hotel_name || 'Hotel Included';
                            const mealPlan = hotel.meal_plan || 'Breakfast included';
                            hotelDisplay += `<div class="hotel-item">${nights}N - ${hotelName} (${mealPlan})</div>`;
                        });
                        
                        const displayDiv = document.createElement('div');
                        displayDiv.className = 'test-section success';
                        displayDiv.innerHTML = `<h3>🎨 Hotel Display Preview</h3>${hotelDisplay}`;
                        document.getElementById('results').appendChild(displayDiv);
                    }
                } else {
                    addResult('⚠️ No Quote ID', 'Package does not have quote_id for hotel enhancement', 'warning');
                }

            } catch (error) {
                addResult('❌ Test Failed', error.message, 'error');
            }
        }

        async function testSpecificQuote() {
            clearResults();
            
            try {
                // Get a quote that has hotels
                const { data: quotesWithHotels, error } = await databaseService.quoteDB
                    .from('hotel_rows')
                    .select('quote_id')
                    .limit(1);

                if (error || !quotesWithHotels.length) {
                    addResult('❌ No Quotes with Hotels', 'No quotes found with hotel data', 'error');
                    return;
                }

                const quoteId = quotesWithHotels[0].quote_id;
                
                addResult('🎯 Testing Specific Quote', `Quote ID: ${quoteId}`, 'info');

                // Get hotels for this quote
                const { data: hotels, error: hotelError } = await databaseService.quoteDB
                    .from('hotel_rows')
                    .select('*')
                    .eq('quote_id', quoteId);

                if (hotelError) {
                    addResult('❌ Hotel Fetch Failed', hotelError, 'error');
                    return;
                }

                addResult('✅ Hotels Retrieved', {
                    quote_id: quoteId,
                    hotels_count: hotels.length,
                    hotels: hotels
                }, 'success');

                // Test the enhancement function directly
                const mockPackageData = {
                    quote_id: quoteId,
                    destination: 'Test Destination',
                    total_price: 50000
                };

                const enhancedPackage = await databaseService.formatPackageForFrontendEnhanced(mockPackageData);
                
                addResult('🎁 Enhanced Package Result', {
                    has_hotels_list: !!(enhancedPackage.hotels_list && enhancedPackage.hotels_list.length > 0),
                    hotels_list: enhancedPackage.hotels_list,
                    inclusions: enhancedPackage.inclusions,
                    title: enhancedPackage.title
                }, enhancedPackage.hotels_list && enhancedPackage.hotels_list.length > 0 ? 'success' : 'warning');

            } catch (error) {
                addResult('❌ Specific Quote Test Failed', error.message, 'error');
            }
        }

        // Auto-run test on page load
        window.addEventListener('load', () => {
            setTimeout(() => {
                runFullTest();
            }, 1000);
        });
    </script>
</body>
</html>
