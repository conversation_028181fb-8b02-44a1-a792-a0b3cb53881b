#!/bin/bash

echo "=== Fixing HTTPS for family.tripxplo.com ==="

# Step 1: Check current configuration
echo "1. Checking current nginx configuration..."
if [ -f "/etc/nginx/sites-available/family.tripxplo.com" ]; then
    echo "Configuration file exists"
    cat /etc/nginx/sites-available/family.tripxplo.com
else
    echo "Configuration file does not exist!"
    exit 1
fi

echo ""
echo "2. Checking if site is enabled..."
if [ -L "/etc/nginx/sites-enabled/family.tripxplo.com" ]; then
    echo "Site is already enabled"
else
    echo "Enabling site..."
    ln -s /etc/nginx/sites-available/family.tripxplo.com /etc/nginx/sites-enabled/
fi

echo ""
echo "3. Checking for existing SSL certificates..."
if [ -d "/etc/letsencrypt/live/tripxplo.com" ]; then
    echo "Found existing SSL certificate for tripxplo.com"
    SSL_CERT="/etc/letsencrypt/live/tripxplo.com/fullchain.pem"
    SSL_KEY="/etc/letsencrypt/live/tripxplo.com/privkey.pem"
elif [ -d "/etc/letsencrypt/live/family.tripxplo.com" ]; then
    echo "Found existing SSL certificate for family.tripxplo.com"
    SSL_CERT="/etc/letsencrypt/live/family.tripxplo.com/fullchain.pem"
    SSL_KEY="/etc/letsencrypt/live/family.tripxplo.com/privkey.pem"
else
    echo "No existing SSL certificate found. Installing certbot..."
    apt update
    apt install certbot python3-certbot-nginx -y
    
    echo "Generating SSL certificate for family.tripxplo.com..."
    certbot --nginx -d family.tripxplo.com --non-interactive --agree-tos --email <EMAIL>
    
    if [ $? -eq 0 ]; then
        echo "SSL certificate generated successfully"
        SSL_CERT="/etc/letsencrypt/live/family.tripxplo.com/fullchain.pem"
        SSL_KEY="/etc/letsencrypt/live/family.tripxplo.com/privkey.pem"
    else
        echo "Failed to generate SSL certificate. Using manual configuration..."
        SSL_CERT="/etc/letsencrypt/live/tripxplo.com/fullchain.pem"
        SSL_KEY="/etc/letsencrypt/live/tripxplo.com/privkey.pem"
    fi
fi

echo ""
echo "4. Creating updated nginx configuration..."
cat > /etc/nginx/sites-available/family.tripxplo.com << 'EOF'
server {
    listen 80;
    server_name family.tripxplo.com www.family.tripxplo.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl;
    server_name family.tripxplo.com www.family.tripxplo.com;
    
    ssl_certificate SSL_CERT_PATH;
    ssl_certificate_key SSL_KEY_PATH;
    
    # SSL configuration
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    
    root /var/www/family;
    index index.html;
    
    # Enable gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/css application/javascript application/json image/svg+xml text/plain text/xml;
    
    # Cache static assets
    location ~* \.(css|js|png|jpg|jpeg|gif|svg|ico)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    location / {
        try_files $uri $uri/ =404;
    }
}
EOF

# Replace SSL certificate paths
sed -i "s|SSL_CERT_PATH|$SSL_CERT|g" /etc/nginx/sites-available/family.tripxplo.com
sed -i "s|SSL_KEY_PATH|$SSL_KEY|g" /etc/nginx/sites-available/family.tripxplo.com

echo ""
echo "5. Setting proper permissions..."
chown -R www-data:www-data /var/www/family
chmod -R 755 /var/www/family

echo ""
echo "6. Testing nginx configuration..."
nginx -t

if [ $? -eq 0 ]; then
    echo "Nginx configuration is valid"
    echo ""
    echo "7. Restarting nginx..."
    systemctl restart nginx
    systemctl status nginx --no-pager
    
    echo ""
    echo "=== Setup Complete ==="
    echo "HTTPS should now be working for family.tripxplo.com"
    echo "Test with: https://family.tripxplo.com"
else
    echo "Nginx configuration has errors. Please check the configuration."
    exit 1
fi
