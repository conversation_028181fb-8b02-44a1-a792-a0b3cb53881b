-- Database Schema for Family Type Prices Storage
-- Table: family_type_prices

CREATE TABLE family_type_prices (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    quote_id VARCHAR(255) NOT NULL, -- Reference to the baseline quote
    family_type_id VARCHAR(50) NOT NULL, -- Reference to family_type.family_id
    family_type_name VARCHAR(255) NOT NULL, -- Family type name for display
    
    -- Family Composition
    no_of_adults INTEGER NOT NULL,
    no_of_children INTEGER DEFAULT 0, -- 6-12 years
    no_of_child INTEGER DEFAULT 0, -- Below 5 years
    no_of_infants INTEGER DEFAULT 0, -- Below 2 years
    family_count INTEGER NOT NULL,
    
    -- Room and Vehicle Info
    rooms_need INTEGER NOT NULL,
    cab_type VARCHAR(255),
    cab_capacity INTEGER,
    
    -- Calculated Costs
    hotel_cost DECIMAL(10,2) NOT NULL,
    vehicle_cost DECIMAL(10,2) NOT NULL,
    additional_costs DECIMAL(10,2) NOT NULL,
    basic_costs DECIMAL(10,2) DEFAULT 0,
    addon_costs DECIMAL(10,2) DEFAULT 0,
    optional_costs DECIMAL(10,2) DEFAULT 0,
    
    -- Final Pricing
    subtotal DECIMAL(10,2) NOT NULL,
    discount_amount DECIMAL(10,2) DEFAULT 0,
    commission_amount DECIMAL(10,2) DEFAULT 0,
    total_price DECIMAL(10,2) NOT NULL,
    
    -- Room Calculation Details
    extra_adults INTEGER DEFAULT 0,
    children_charged INTEGER DEFAULT 0,
    infants_free INTEGER DEFAULT 0,
    room_type VARCHAR(255),
    
    -- Metadata
    baseline_quote_data JSONB, -- Store baseline quote info
    quote_mapping_data JSONB, -- Store quote mapping used
    calculation_notes TEXT[], -- Store calculation notes
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT fk_quote_id FOREIGN KEY (quote_id) REFERENCES quotes(id) ON DELETE CASCADE,
    CONSTRAINT unique_quote_family UNIQUE (quote_id, family_type_id)
);

-- Indexes for performance
CREATE INDEX idx_family_type_prices_quote_id ON family_type_prices(quote_id);
CREATE INDEX idx_family_type_prices_family_type_id ON family_type_prices(family_type_id);
CREATE INDEX idx_family_type_prices_created_at ON family_type_prices(created_at);

-- Comments
COMMENT ON TABLE family_type_prices IS 'Stores calculated family type prices for quotes';
COMMENT ON COLUMN family_type_prices.quote_id IS 'Reference to the baseline quote used for calculation';
COMMENT ON COLUMN family_type_prices.family_type_id IS 'Reference to family_type table';
COMMENT ON COLUMN family_type_prices.baseline_quote_data IS 'JSON data of the baseline quote used';
COMMENT ON COLUMN family_type_prices.quote_mapping_data IS 'JSON data of quote mapping used for calculation';
COMMENT ON COLUMN family_type_prices.calculation_notes IS 'Array of calculation notes and details';

-- Row Level Security (RLS) - Optional based on your auth setup
-- ALTER TABLE family_type_prices ENABLE ROW LEVEL SECURITY;

-- Sample RLS Policy
-- CREATE POLICY "Users can view their own family type prices" ON family_type_prices
--     FOR SELECT USING (auth.uid() = user_id); -- If you have user_id column

-- Function to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_family_type_prices_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Trigger to automatically update updated_at
CREATE TRIGGER update_family_type_prices_updated_at
    BEFORE UPDATE ON family_type_prices
    FOR EACH ROW
    EXECUTE FUNCTION update_family_type_prices_updated_at(); 