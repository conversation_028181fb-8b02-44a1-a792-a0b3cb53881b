import React, { ReactNode } from 'react';
import MainNavigation from './MainNavigation';

interface AppLayoutProps {
  children: ReactNode;
}

const AppLayout: React.FC<AppLayoutProps> = ({ children }) => {
  return (
    <div className="min-h-screen flex flex-col gradient-bg">
      <MainNavigation />
      <main className="flex-1 py-8 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto animate-fade-in">
          {children}
        </div>
      </main>
      <footer className="bg-white border-t border-gray-100 py-8 shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="flex items-center mb-4 md:mb-0">
              <span className="logo">
                <span className="trip">TRIP</span><span className="xplo">XPLO</span>
              </span>
              <span className="ml-3 text-sm text-gray-600">Travel CRM</span>
            </div>
            <div className="text-center md:text-right">
              <p className="text-sm text-gray-500">
                © {new Date().getFullYear()} TripXplo Family. All rights reserved.
              </p>
              <p className="text-xs text-gray-400 mt-1">
                Your Family Adventure, Planned & Paid Your Way
              </p>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default AppLayout; 