# Final TripXplo CRM Fix
Write-Host "🚀 TripXplo CRM - Final Server Fix" -ForegroundColor Green

$ServerIP = "*************"
$ServerUser = "root"
$keyPath = "$env:USERPROFILE\.ssh\tripxplo_fix"

# Create SSH directory
if (-not (Test-Path "$env:USERPROFILE\.ssh")) {
    New-Item -ItemType Directory -Path "$env:USERPROFILE\.ssh" -Force | Out-Null
}

# Generate SSH key if needed
if (-not (Test-Path $keyPath)) {
    Write-Host "🔐 Generating SSH key..." -ForegroundColor Yellow
    & ssh-keygen -t rsa -b 4096 -f $keyPath -N '""' -q
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ SSH key generated!" -ForegroundColor Green
    }
}

# Setup SSH key authentication
Write-Host "🔑 Setting up SSH access (enter server password once)..." -ForegroundColor Yellow
$pubKey = Get-Content "$keyPath.pub" -Raw
& ssh $ServerUser@$ServerIP "mkdir -p ~/.ssh; echo '$pubKey' >> ~/.ssh/authorized_keys; chmod 600 ~/.ssh/authorized_keys; chmod 700 ~/.ssh"

if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ SSH setup failed" -ForegroundColor Red
    exit 1
}

Write-Host "✅ SSH key authentication enabled!" -ForegroundColor Green

# Upload the fix script
Write-Host "📤 Uploading fix script..." -ForegroundColor Yellow
& scp -i $keyPath -o StrictHostKeyChecking=no "fix-server.sh" "${ServerUser}@${ServerIP}:/tmp/fix-server.sh"

if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Failed to upload script" -ForegroundColor Red
    exit 1
}

# Make script executable
Write-Host "🔧 Making script executable..." -ForegroundColor Yellow
& ssh -i $keyPath -o StrictHostKeyChecking=no $ServerUser@$ServerIP "chmod +x /tmp/fix-server.sh"

# Execute the fix
Write-Host "🚀 Executing fix on server..." -ForegroundColor Yellow
& ssh -i $keyPath -o StrictHostKeyChecking=no $ServerUser@$ServerIP "/tmp/fix-server.sh"

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ Server fix completed successfully!" -ForegroundColor Green
} else {
    Write-Host "❌ Server fix encountered issues" -ForegroundColor Red
}

# Test the website
Write-Host "🧪 Testing website..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "http://crm.tripxplo.com" -Method Head -TimeoutSec 10
    Write-Host "🎉 SUCCESS! Website working! Status: $($response.StatusCode)" -ForegroundColor Green
} catch {
    Write-Host "⚠️ Local test failed, but check: http://crm.tripxplo.com" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "🎉 Deployment fix complete!" -ForegroundColor Green
Write-Host "🌐 Visit: https://crm.tripxplo.com" -ForegroundColor Cyan
Write-Host ""
Write-Host "📋 For GitHub Actions, add this SSH key as SECRET:" -ForegroundColor Yellow
Write-Host "Secret name: SSH_PRIVATE_KEY" -ForegroundColor White
Write-Host "Secret value:" -ForegroundColor White
Write-Host "==============================" -ForegroundColor Gray
if (Test-Path $keyPath) {
    Get-Content $keyPath
}
Write-Host "==============================" -ForegroundColor Gray 