#!/bin/bash

echo "🚀 Re-deploying family.tripxplo.com..."
echo "====================================="

# Check if family-tripxplo-production directory exists
if [ ! -d "family-tripxplo-production" ]; then
    echo "❌ Error: family-tripxplo-production directory not found!"
    exit 1
fi

echo "📁 Found family-tripxplo-production directory"
echo "📋 Files to deploy:"
find family-tripxplo-production -type f | head -10
echo "... and more"

echo ""
echo "🔄 Uploading files to server..."

# Upload files using SCP
scp -r family-tripxplo-production/* root@*************:/var/www/family/ && {
    echo "✅ Files uploaded successfully!"
    
    echo ""
    echo "🔧 Configuring server..."
    
    # Connect to server and configure
    ssh root@************* '
        echo "📂 Checking uploaded files:"
        ls -la /var/www/family/ | head -5
        
        echo ""
        echo "🌐 Checking nginx configuration..."
        
        # Check if family subdomain is configured
        if [ ! -f /etc/nginx/sites-available/family.tripxplo.com ]; then
            echo "⚙️ Creating nginx configuration for family.tripxplo.com..."
            
            cat > /etc/nginx/sites-available/family.tripxplo.com << EOF
server {
    listen 80;
    server_name family.tripxplo.com;
    
    root /var/www/family;
    index index.html index.htm;
    
    location / {
        try_files \$uri \$uri/ =404;
    }
    
    # Handle static files
    location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
}
EOF
            
            # Enable the site
            ln -sf /etc/nginx/sites-available/family.tripxplo.com /etc/nginx/sites-enabled/
            echo "✅ Nginx configuration created and enabled"
        else
            echo "ℹ️ Nginx configuration already exists"
        fi
        
        echo ""
        echo "🔄 Testing nginx configuration..."
        nginx -t && {
            echo "✅ Nginx configuration is valid"
            echo "🔄 Reloading nginx..."
            systemctl reload nginx
            echo "✅ Nginx reloaded successfully"
        } || {
            echo "❌ Nginx configuration error!"
            exit 1
        }
        
        echo ""
        echo "📊 Final status check:"
        systemctl status nginx --no-pager -l | head -3
        
        echo ""
        echo "🎯 Testing local access:"
        curl -I http://localhost -H "Host: family.tripxplo.com" | head -1
        
    ' && {
        echo ""
        echo "🌍 Testing external access:"
        sleep 2
        curl -I http://family.tripxplo.com | head -1
        
        echo ""
        echo "🎉 Deployment completed successfully!"
        echo "🔗 Site should be available at: http://family.tripxplo.com"
    }
    
} || {
    echo "❌ File upload failed!"
    exit 1
}
