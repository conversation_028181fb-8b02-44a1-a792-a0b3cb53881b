<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Hotels - TripXplo</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .result { margin: 10px 0; padding: 10px; border: 1px solid #ccc; border-radius: 5px; }
        .error { background: #ffe6e6; }
        .success { background: #e6ffe6; }
        .info { background: #e6f3ff; }
        pre { background: #f5f5f5; padding: 10px; overflow-x: auto; }
        button { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; }
    </style>
</head>
<body>
    <h1>🔧 Debug Hotels Issue</h1>
    
    <button onclick="testDatabaseConnection()">Test Database Connection</button>
    <button onclick="testHotelRowsTable()">Test hotel_rows Table</button>
    <button onclick="testQuotesTable()">Test quotes Table</button>
    <button onclick="testPackageSearch()">Test Package Search</button>
    
    <div id="results"></div>

    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
    <script src="js/config.js"></script>
    <script src="js/databaseService.js"></script>
    <script>
        function addResult(title, content, type = 'info') {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `result ${type}`;
            div.innerHTML = `<h3>${title}</h3><pre>${JSON.stringify(content, null, 2)}</pre>`;
            results.appendChild(div);
        }

        async function testDatabaseConnection() {
            try {
                console.log('🔍 Testing database connection...');
                
                const { data, error } = await databaseService.quoteDB
                    .from('quotes')
                    .select('id')
                    .limit(1);

                if (error) {
                    addResult('❌ Database Connection Failed', error, 'error');
                } else {
                    addResult('✅ Database Connection Success', { 
                        message: 'Connected successfully',
                        sample_quote_id: data[0]?.id 
                    }, 'success');
                }
            } catch (error) {
                addResult('❌ Database Connection Error', error.message, 'error');
            }
        }

        async function testHotelRowsTable() {
            try {
                console.log('🏨 Testing hotel_rows table...');
                
                const { data, error } = await databaseService.quoteDB
                    .from('hotel_rows')
                    .select('*')
                    .limit(5);

                if (error) {
                    addResult('❌ hotel_rows Table Error', error, 'error');
                } else {
                    addResult('✅ hotel_rows Table Success', {
                        count: data.length,
                        sample_data: data
                    }, 'success');
                }
            } catch (error) {
                addResult('❌ hotel_rows Table Error', error.message, 'error');
            }
        }

        async function testQuotesTable() {
            try {
                console.log('📋 Testing quotes table...');
                
                const { data, error } = await databaseService.quoteDB
                    .from('quotes')
                    .select('id, destination, package_name, total_cost')
                    .limit(5);

                if (error) {
                    addResult('❌ quotes Table Error', error, 'error');
                } else {
                    addResult('✅ quotes Table Success', {
                        count: data.length,
                        sample_data: data
                    }, 'success');
                }
            } catch (error) {
                addResult('❌ quotes Table Error', error.message, 'error');
            }
        }

        async function testPackageSearch() {
            try {
                console.log('🔍 Testing package search...');
                
                const searchParams = {
                    destination: 'Andaman',
                    adults: 2,
                    child: 1,
                    children: 0,
                    infants: 0
                };

                const result = await databaseService.searchPackages(searchParams);
                
                if (result.success) {
                    const pkg = result.packages[0];
                    addResult('✅ Package Search Success', {
                        packages_found: result.packages.length,
                        first_package: {
                            title: pkg.title,
                            quote_id: pkg.quote_id,
                            hotels_list: pkg.hotels_list,
                            hotel_name: pkg.hotel_name,
                            inclusions: pkg.inclusions
                        }
                    }, 'success');
                } else {
                    addResult('❌ Package Search Failed', result, 'error');
                }
            } catch (error) {
                addResult('❌ Package Search Error', error.message, 'error');
            }
        }

        // Auto-run tests
        window.addEventListener('load', () => {
            setTimeout(async () => {
                await testDatabaseConnection();
                await testHotelRowsTable();
                await testQuotesTable();
                await testPackageSearch();
            }, 1000);
        });
    </script>
</body>
</html>
