# Dynamic API Configuration for TripXplo Family EMI

## 🎯 Overview

The contact form now works seamlessly on both **localhost** (for development) and **production** (https://family.tripxplo.com) without any manual configuration changes.

## 🔧 How It Works

### Automatic Environment Detection

The system automatically detects the environment and uses the appropriate API URL:

```javascript
const API_CONFIG = {
    getBaseUrl: () => {
        const hostname = window.location.hostname;
        const protocol = window.location.protocol;
        
        // Check if running on localhost
        if (hostname === 'localhost' || hostname === '127.0.0.1' || hostname.includes('localhost')) {
            return 'http://localhost:3001';
        }
        
        // Check if running on file:// protocol (local HTML file)
        if (protocol === 'file:') {
            return 'http://localhost:3001';
        }
        
        // Production environment
        return 'https://family.tripxplo.com';
    },
    
    getApiUrl: (endpoint) => {
        return `${API_CONFIG.getBaseUrl()}/api/${endpoint}`;
    }
};
```

### Environment Detection Logic

| Environment | Hostname | Protocol | API Base URL |
|-------------|----------|----------|--------------|
| **Local Development** | `localhost` or `127.0.0.1` | `http:` | `http://localhost:3001` |
| **Local HTML File** | Any | `file:` | `http://localhost:3001` |
| **Production** | `family.tripxplo.com` | `https:` | `https://family.tripxplo.com` |

## 🚀 Usage

### For Development (Localhost)

1. **Start the API server:**
   ```bash
   cd family-tripxplo-production/api
   npm start
   ```
   Server runs on: `http://localhost:3001`

2. **Open the website:**
   - Via Live Server: `http://localhost:5500/family-tripxplo-production/index.html`
   - Via file protocol: `file:///path/to/family-tripxplo-production/index.html`
   - Via local server: `http://localhost:3000/family-tripxplo-production/index.html`

3. **Contact form automatically uses:** `http://localhost:3001/api/submit-contact-details`

### For Production

1. **Website URL:** `https://family.tripxplo.com`
2. **Contact form automatically uses:** `https://family.tripxplo.com/api/submit-contact-details`

## 🔍 Testing

### Test Localhost Connection

1. **Open the test file:**
   ```
   file:///c:/TripXplo-CRM/test-localhost.html
   ```

2. **Fill in the test form and submit**

3. **Check the results:**
   - ✅ Success: API connection working
   - ❌ Error: Check troubleshooting steps

### Console Logging

The system logs environment detection for debugging:

```javascript
console.log('🌐 Environment detected:', {
    hostname: window.location.hostname,
    protocol: window.location.protocol,
    apiBaseUrl: API_CONFIG.getBaseUrl()
});
```

## 📋 API Endpoints Configured

Both endpoints now use dynamic URLs:

1. **Contact Form Submission:**
   - Localhost: `http://localhost:3001/api/submit-contact-details`
   - Production: `https://family.tripxplo.com/api/submit-contact-details`

2. **Payment Processing:**
   - Localhost: `http://localhost:3001/api/process-payment`
   - Production: `https://family.tripxplo.com/api/process-payment`

## ⚙️ Server Configuration

### Local Development Server

**File:** `family-tripxplo-production/api/.env`
```env
PORT=3001
CORS_ORIGIN=https://family.tripxplo.com,http://family.tripxplo.com,http://localhost:8000,http://localhost:3000,http://localhost:5500,http://127.0.0.1:5500,http://[::]:8000
```

### CORS Configuration

The server allows requests from:
- ✅ `https://family.tripxplo.com` (production)
- ✅ `http://localhost:*` (any localhost port)
- ✅ `http://127.0.0.1:*` (any local IP port)
- ✅ `file://` protocol (local HTML files)

## 🔧 Troubleshooting

### Common Issues

1. **"Connection Refused" Error:**
   ```bash
   # Start the local API server
   cd family-tripxplo-production/api
   npm start
   ```

2. **CORS Error:**
   - Check if localhost is in CORS_ORIGIN
   - Restart the server after .env changes

3. **Wrong API URL:**
   - Check browser console for environment detection logs
   - Verify hostname and protocol detection

### Debug Steps

1. **Check server status:**
   ```bash
   # Check if server is running on port 3001
   netstat -ano | findstr :3001
   ```

2. **Test API directly:**
   ```bash
   curl -X POST http://localhost:3001/api/submit-contact-details \
     -H "Content-Type: application/json" \
     -d '{"test":"data"}'
   ```

3. **Check browser console:**
   - Look for environment detection logs
   - Check for CORS or network errors

## 📝 Benefits

### ✅ Advantages

1. **No Manual Configuration:** Automatically detects environment
2. **Seamless Development:** Works on localhost without changes
3. **Production Ready:** Same code works in production
4. **Easy Testing:** Simple test file for verification
5. **Flexible Hosting:** Works with any localhost port or file protocol

### 🔄 Workflow

1. **Develop locally** with live API server
2. **Test contact form** on localhost
3. **Deploy to production** without code changes
4. **Contact form works** on both environments

## 🎉 Result

**The contact form now works perfectly on both:**
- ✅ **Localhost development** (with local API server)
- ✅ **Production website** (https://family.tripxplo.com)

**No more manual URL switching or environment-specific builds!**
