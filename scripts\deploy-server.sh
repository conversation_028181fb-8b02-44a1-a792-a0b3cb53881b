#!/bin/bash
set -e

echo "🚀 Starting TripXplo CRM deployment..."

# Configuration
DEPLOY_DIR="/tmp/crm-deploy"
WEB_DIR="/var/www/crm"
BACKUP_DIR="/var/www/crm.backup.$(date +%Y%m%d_%H%M%S)"

# Step 1: Prepare deployment directory
echo "📁 Preparing deployment..."
mkdir -p $DEPLOY_DIR
cd /tmp

# Check if dist.tar.gz exists
if [ ! -f "/tmp/dist.tar.gz" ]; then
    echo "❌ Error: /tmp/dist.tar.gz not found"
    exit 1
fi

# Step 2: Extract uploaded files
echo "📦 Extracting deployment files..."
tar -xzf /tmp/dist.tar.gz -C $DEPLOY_DIR

# Verify extraction
if [ ! -f "$DEPLOY_DIR/index.html" ]; then
    echo "❌ Error: index.html not found after extraction"
    ls -la $DEPLOY_DIR
    exit 1
fi

# Step 3: Create backup of existing deployment
echo "💾 Creating backup..."
if [ -d "$WEB_DIR" ] && [ "$(ls -A $WEB_DIR 2>/dev/null)" ]; then
  cp -r $WEB_DIR $BACKUP_DIR
  echo "✅ Backup created: $BACKUP_DIR"
  
  # Keep only the last 5 backups
  ls -dt /var/www/crm.backup.* 2>/dev/null | tail -n +6 | xargs rm -rf 2>/dev/null || true
fi

# Step 4: Deploy new files
echo "🔄 Deploying new files..."
mkdir -p $WEB_DIR
rm -rf $WEB_DIR/*
cp -r $DEPLOY_DIR/* $WEB_DIR/

# Step 5: Set proper permissions
echo "🔐 Setting file permissions..."
chown -R www-data:www-data $WEB_DIR
chmod -R 755 $WEB_DIR
find $WEB_DIR -name "*.html" -exec chmod 644 {} \;
find $WEB_DIR -name "*.css" -exec chmod 644 {} \;
find $WEB_DIR -name "*.js" -exec chmod 644 {} \;

# Step 6: Configure Nginx
echo "🌐 Configuring Nginx..."
echo "📝 Forcefully writing latest Nginx configuration to ensure consistency..."
cat > /etc/nginx/sites-available/crm.tripxplo.com << 'NGINXCONFIG'
# HTTP server block - redirect to HTTPS
server {
    listen 80;
    server_name crm.tripxplo.com www.crm.tripxplo.com;
    return 301 https://crm.tripxplo.com$request_uri;
}

# HTTPS server block - serve the application
server {
    listen 443 ssl http2;
    server_name crm.tripxplo.com www.crm.tripxplo.com;
    
    root /var/www/crm;
    index index.html;
    
    # SSL configuration
    ssl_certificate /etc/letsencrypt/live/crm.tripxplo.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/crm.tripxplo.com/privkey.pem;
    include /etc/letsencrypt/options-ssl-nginx.conf;
    ssl_dhparam /etc/letsencrypt/ssl-dhparams.pem;
    
    # Error and access logs
    error_log /var/log/nginx/crm_error.log;
    access_log /var/log/nginx/crm_access.log;
    
    # Handle React Router (SPA routing)
    location / {
        # Add CORS headers
        add_header Access-Control-Allow-Origin "*" always;
        add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS" always;
        add_header Access-Control-Allow-Headers "DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization" always;

        # Handle preflight OPTIONS requests
        if ($request_method = 'OPTIONS') {
            add_header Access-Control-Max-Age 1728000;
            add_header Content-Type "text/plain charset=UTF-8";
            add_header Content-Length 0;
            return 204;
        }

        try_files $uri $uri/ /index.html =404;
        add_header Cache-Control "no-cache, no-store, must-revalidate" always;
    }
    
    # Static assets with proper caching
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot|map)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header Access-Control-Allow-Origin "*";
    }
    
    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    
    # Block access to sensitive files
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }
}
NGINXCONFIG
  
# Enable the site (forcefully ensures link is correct)
ln -sf /etc/nginx/sites-available/crm.tripxplo.com /etc/nginx/sites-enabled/
  
echo "✅ Nginx configuration updated"

# Step 7: Test and reload Nginx
echo "🧪 Testing Nginx configuration..."
if nginx -t; then
  echo "✅ Nginx configuration is valid"
  systemctl reload nginx
  echo "✅ Nginx reloaded successfully"
else
  echo "❌ Nginx configuration test failed"
  nginx -t 2>&1
  exit 1
fi

# Step 8: Setup SSL certificate (first time only)
echo "🔒 Checking SSL certificate..."
if command -v certbot >/dev/null 2>&1; then
  if ! nginx -T 2>/dev/null | grep -q "ssl_certificate.*crm.tripxplo.com"; then
    echo "📜 Setting up SSL certificate..."
    certbot --nginx -d crm.tripxplo.com -d www.crm.tripxplo.com \
      --non-interactive --agree-tos --email <EMAIL> \
      --redirect || echo "⚠️ SSL setup failed, continuing without SSL"
  else
    echo "✅ SSL certificate already configured"
  fi
else
  echo "⚠️ Certbot not found, skipping SSL setup"
fi

# Step 9: Verify deployment
echo "🧪 Verifying deployment..."
if [ -f "$WEB_DIR/index.html" ]; then
  echo "✅ index.html found"
else
  echo "❌ index.html not found in deployment"
  ls -la $WEB_DIR
  exit 1
fi

# Check if static assets exist
ASSET_COUNT=$(find $WEB_DIR -name "*.js" -o -name "*.css" | wc -l)
echo "📊 Found $ASSET_COUNT static assets"

# Step 10: Cleanup
echo "🧹 Cleaning up temporary files..."
rm -rf $DEPLOY_DIR /tmp/dist.tar.gz

# Don't remove the deploy script until it's done
# rm -f /tmp/deploy-server.sh

# Step 11: Final status
echo ""
echo "✅ Deployment completed successfully!"
echo "🌐 Site is now live at:"
echo "   - http://crm.tripxplo.com"
echo "   - https://crm.tripxplo.com"
echo ""
echo "📊 Deployment Summary:"
echo "   - Backup created: $BACKUP_DIR"
echo "   - Files deployed: $(find $WEB_DIR -type f | wc -l)"
echo "   - Permissions set: ✅"
echo "   - Nginx configured: ✅"
echo "   - SSL certificate: $([ -f /etc/letsencrypt/live/crm.tripxplo.com/fullchain.pem ] && echo '✅' || echo '⚠️')"
echo ""
echo "🎉 TripXplo CRM deployment complete!" 