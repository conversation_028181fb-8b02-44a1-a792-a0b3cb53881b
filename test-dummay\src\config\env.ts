/**
 * Environment Configuration Manager
 * 
 * This file manages environment variables for different parts of the application.
 * It allows the CRM and Quote components to use different Supabase instances.
 */

// Define types for our environment configurations
interface SupabaseConfig {
  url: string;
  anonKey: string;
}

interface EmailConfig {
  user: string;
  pass: string;
}

interface AppConfig {
  crm: {
    supabase: SupabaseConfig;
  };
  quote: {
    supabase: SupabaseConfig;
    email: EmailConfig;
  };
}

// Load environment variables with fallbacks
const config: AppConfig = {
  crm: {
    supabase: {
      url: import.meta.env.VITE_SUPABASE_URL_CRM || 
           import.meta.env.VITE_SUPABASE_URL || 
           "https://tlfwcnikdlwoliqzavxj.supabase.co",
      anonKey: import.meta.env.VITE_SUPABASE_ANON_KEY_CRM || 
               import.meta.env.VITE_SUPABASE_ANON_KEY || 
               "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRsZndjbmlrZGx3b2xpcXphdnhqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDQ4NTcwMjgsImV4cCI6MjA2MDQzMzAyOH0.fCaJNbHL6VwKxTbt3vYl2F5O2gRoMFuUO1bhqEtSWpI"
    }
  },
  quote: {
    supabase: {
      url: import.meta.env.VITE_SUPABASE_URL_QUOTE ||
           import.meta.env.REACT_APP_SUPABASE_URL ||
           "https://lkqbrlrmrsnbtkoryazq.supabase.co", // Quote database URL
      anonKey: import.meta.env.VITE_SUPABASE_ANON_KEY_QUOTE ||
               import.meta.env.REACT_APP_SUPABASE_ANON_KEY ||
               "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImxrcWJybHJtcnNuYnRrb3J5YXpxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDU0MDA2ODYsImV4cCI6MjA2MDk3NjY4Nn0.0E4Z87L9j32k3jKa15n4LpmFsVx8YCJuwovi-mSw4SE" // Quote database key
    },
    email: {
      user: import.meta.env.EMAIL_USER || "<EMAIL>",
      pass: import.meta.env.EMAIL_PASS || "Arunkumar@58"
    }
  }
};

// Helper functions to get configurations
export const getCrmSupabaseConfig = (): SupabaseConfig => config.crm.supabase;
export const getQuoteSupabaseConfig = (): SupabaseConfig => config.quote.supabase;
export const getEmailConfig = (): EmailConfig => config.quote.email;

// Export the entire config for advanced usage
export default config;
