# Deploy CRM to crm.tripxplo.com subdomain on Linode
# This script will deploy the CRM application to a subdomain without affecting the main domain

Write-Host "🚀 Deploying CRM to crm.tripxplo.com subdomain..." -ForegroundColor Green
Write-Host "====================================================" -ForegroundColor Green

# Configuration
$SERVER_IP = "*************"
$SERVER_USER = "root"
$SUBDOMAIN = "crm.tripxplo.com"
$SSH_KEY_PATH = "$env:USERPROFILE\.ssh\tripxplo_crm_key"

# Check if SSH key exists
if (-not (Test-Path $SSH_KEY_PATH)) {
    Write-Host "❌ Error: SSH private key not found at $SSH_KEY_PATH" -ForegroundColor Red
    Write-Host "Please run the SSH key setup first: .\setup-ssh-key.ps1" -ForegroundColor Yellow
    exit 1
}

# Check if dist directory exists
if (-not (Test-Path "dist")) {
    Write-Host "❌ Error: dist directory not found! Please run 'npm run build' first." -ForegroundColor Red
    exit 1
}

Write-Host "📁 Found production build. Proceeding with deployment..." -ForegroundColor Yellow
Write-Host "🔐 Using SSH key authentication..." -ForegroundColor Green

# Step 1: Upload CRM files to server using SSH key
Write-Host "📤 Step 1: Uploading CRM files to server..." -ForegroundColor Cyan
$scpCommand = "scp -i `"$SSH_KEY_PATH`" -o StrictHostKeyChecking=no -r dist/* ${SERVER_USER}@${SERVER_IP}:/tmp/crm-deploy/"
Write-Host "Running: scp with SSH key authentication" -ForegroundColor Gray

# First create the temporary directory on server using SSH key
$sshCommand = "ssh -i `"$SSH_KEY_PATH`" -o StrictHostKeyChecking=no ${SERVER_USER}@${SERVER_IP}"
Invoke-Expression "$sshCommand `"mkdir -p /tmp/crm-deploy`""

# Upload files using SSH key
Invoke-Expression $scpCommand

if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Error: Failed to upload files to server!" -ForegroundColor Red
    Write-Host "💡 Tip: Make sure SSH key authentication is set up. Run: .\setup-ssh-key.ps1" -ForegroundColor Yellow
    exit 1
}

Write-Host "✅ Files uploaded successfully!" -ForegroundColor Green

# Step 2: Deploy and configure on server
Write-Host "⚙️ Step 2: Configuring CRM subdomain on server..." -ForegroundColor Cyan

# Create SSH commands for server configuration
$sshCommands = @"
#!/bin/bash
set -e

echo "🔧 Starting CRM subdomain configuration..."

# Create CRM directory structure
echo "📁 Creating CRM directory structure..."
mkdir -p /var/www/crm
mkdir -p /var/log/nginx

# Clear existing files and copy new ones
echo "📁 Deploying CRM files..."
rm -rf /var/www/crm/*
cp -r /tmp/crm-deploy/* /var/www/crm/

# Set permissions for CRM files
echo "🔐 Setting CRM permissions..."
chown -R www-data:www-data /var/www/crm
chmod -R 755 /var/www/crm
find /var/www/crm -name "*.html" -exec chmod 644 {} \;
find /var/www/crm -name "*.css" -exec chmod 644 {} \;
find /var/www/crm -name "*.js" -exec chmod 644 {} \;

# Configure Nginx for CRM subdomain
echo "🌐 Configuring Nginx for crm.tripxplo.com..."

# Create Nginx configuration for crm.tripxplo.com
cat > /etc/nginx/sites-available/crm.tripxplo.com << 'EOF'
server {
    listen 80;
    server_name crm.tripxplo.com www.crm.tripxplo.com;
    
    root /var/www/crm;
    index index.html index.htm;
    
    # Serve static files
    location / {
        try_files \$uri \$uri/ /index.html;
    }
    
    # Handle React Router (SPA)
    location ~ ^.+\..+$ {
        try_files \$uri =404;
    }
    
    # Cache static assets
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot|map)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header Access-Control-Allow-Origin "*";
    }
    
    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' https: data: blob: 'unsafe-inline' 'unsafe-eval'; connect-src 'self' https://*.supabase.co wss://*.supabase.co; img-src 'self' data: https:; font-src 'self' data:;" always;
    
    # CORS headers for API calls
    add_header Access-Control-Allow-Origin "*" always;
    add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS" always;
    add_header Access-Control-Allow-Headers "DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization" always;
    
    # Handle preflight OPTIONS requests
    if (\$request_method = 'OPTIONS') {
        add_header Access-Control-Allow-Origin "*";
        add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS";
        add_header Access-Control-Allow-Headers "DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization";
        add_header Access-Control-Max-Age 1728000;
        add_header Content-Type "text/plain charset=UTF-8";
        add_header Content-Length 0;
        return 204;
    }
}
EOF

# Enable the CRM site
echo "🔗 Enabling CRM site..."
ln -sf /etc/nginx/sites-available/crm.tripxplo.com /etc/nginx/sites-enabled/

# Test Nginx configuration
echo "🧪 Testing Nginx configuration..."
nginx -t

if [ \$? -eq 0 ]; then
    echo "✅ Nginx configuration is valid"
    systemctl reload nginx
    echo "✅ Nginx reloaded successfully"
else
    echo "❌ Nginx configuration test failed"
    exit 1
fi

# Check if Certbot is installed
if ! command -v certbot &> /dev/null; then
    echo "📦 Installing Certbot..."
    apt update
    apt install -y certbot python3-certbot-nginx
fi

# Get SSL certificate for CRM subdomain
echo "🔒 Setting up SSL certificate for crm.tripxplo.com..."
certbot --nginx -d crm.tripxplo.com -d www.crm.tripxplo.com --non-interactive --agree-tos --email <EMAIL> --redirect || {
    echo "⚠️ SSL certificate setup failed. The site will be available over HTTP only."
    echo "Please ensure DNS is properly configured for crm.tripxplo.com"
}

# Clean up temporary files
echo "🧹 Cleaning up temporary files..."
rm -rf /tmp/crm-deploy

echo ""
echo "✅ CRM deployment completed successfully!"
echo "🌐 HTTP: http://crm.tripxplo.com"
echo "🔒 HTTPS: https://crm.tripxplo.com (if SSL setup succeeded)"
echo ""

# Test the deployment
echo "🧪 Testing CRM deployment..."
echo "Testing HTTP connection..."
curl -I http://crm.tripxplo.com || echo "⚠️ HTTP test failed - check DNS configuration"

echo ""
echo "📊 Nginx Status:"
systemctl status nginx --no-pager -l

echo ""
echo "📋 Available sites:"
ls -la /etc/nginx/sites-enabled/
"@

# Write SSH commands to temporary file
$sshCommands | Out-File -FilePath "deploy-crm-commands.sh" -Encoding UTF8

# Execute SSH commands
Write-Host "🔧 Executing CRM deployment commands on server..." -ForegroundColor Cyan
Write-Host "Running deployment commands on server..." -ForegroundColor Gray

# Use Get-Content to read the file and pipe it to SSH with key authentication
Get-Content "deploy-crm-commands.sh" | Invoke-Expression "$sshCommand `"bash -s`""

if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Error: CRM deployment failed!" -ForegroundColor Red
    exit 1
}

# Clean up temporary file
Remove-Item "deploy-crm-commands.sh" -ErrorAction SilentlyContinue

Write-Host "" -ForegroundColor Green
Write-Host "🎉 CRM deployment completed successfully!" -ForegroundColor Green
Write-Host "🌐 CRM URL: https://crm.tripxplo.com" -ForegroundColor Green
Write-Host "" -ForegroundColor Green
Write-Host "📝 Next steps:" -ForegroundColor Yellow
Write-Host "1. Configure DNS: Add A record for 'crm' pointing to $SERVER_IP" -ForegroundColor White
Write-Host "2. Test the CRM at https://crm.tripxplo.com" -ForegroundColor White
Write-Host "3. Verify all CRM functionality works properly" -ForegroundColor White
Write-Host "" -ForegroundColor Green
Write-Host "⚠️ Important DNS Configuration:" -ForegroundColor Yellow
Write-Host "Add this A record to your DNS:" -ForegroundColor White
Write-Host "Type: A" -ForegroundColor White
Write-Host "Name: crm" -ForegroundColor White
Write-Host "Value: $SERVER_IP" -ForegroundColor White
Write-Host "TTL: 300" -ForegroundColor White
Write-Host "" -ForegroundColor Green

pause 