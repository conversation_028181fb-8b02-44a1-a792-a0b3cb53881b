import React, { useState, useEffect } from 'react';
import { 
  Calendar, 
  CreditCard, 
  DollarSign, 
  AlertCircle, 
  CheckCircle, 
  Clock, 
  Search,
  Filter,
  Download,
  Eye,
  Settings,
  Bell,
  TrendingUp,
  Package,
  Users,
  Star
} from 'lucide-react';
import FamilyEMIPackages from './FamilyEMIPackages';

interface PrepaidEMITransaction {
  id: string;
  booking_reference: string;
  customer_name: string;
  customer_phone: string;
  package_name: string;
  advance_payment_amount: number;
  advance_payment_status: 'pending' | 'completed' | 'failed';
  advance_payment_date?: string;
  total_emi_amount: number;
  monthly_emi_amount: number;
  remaining_emi_months: number;
  next_emi_due_date: string;
  total_paid_amount: number;
  pending_amount: number;
  payment_status: 'active' | 'completed' | 'defaulted' | 'cancelled';
  auto_debit_enabled: boolean;
  payment_method: string;
  created_at: string;
}

interface EMISettings {
  default_advance_percent: number;
  default_discount_percent: number;
  default_processing_fee: number;
  reminder_days_before: number;
  auto_debit_enabled: boolean;
  late_fee_percent: number;
  grace_period_days: number;
}

const PrepaidEMIManagement: React.FC = () => {
  const [transactions, setTransactions] = useState<PrepaidEMITransaction[]>([]);
  const [filteredTransactions, setFilteredTransactions] = useState<PrepaidEMITransaction[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [dueDateFilter, setDueDateFilter] = useState('all');
  const [selectedTransaction, setSelectedTransaction] = useState<PrepaidEMITransaction | null>(null);
  const [showSettings, setShowSettings] = useState(false);
  const [activeTab, setActiveTab] = useState('transactions');
  const [emiSettings, setEmiSettings] = useState<EMISettings>({
    default_advance_percent: 20,
    default_discount_percent: 2.5,
    default_processing_fee: 1.0,
    reminder_days_before: 3,
    auto_debit_enabled: true,
    late_fee_percent: 2.0,
    grace_period_days: 5
  });

  useEffect(() => {
    loadTransactions();
    loadEMISettings();
  }, []);

  useEffect(() => {
    let filtered = transactions;

    if (searchQuery) {
      filtered = filtered.filter(
        t => 
          t.booking_reference.toLowerCase().includes(searchQuery.toLowerCase()) ||
          t.customer_name.toLowerCase().includes(searchQuery.toLowerCase()) ||
          t.customer_phone.includes(searchQuery) ||
          t.package_name.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    if (statusFilter !== 'all') {
      filtered = filtered.filter(t => t.payment_status === statusFilter);
    }

    if (dueDateFilter !== 'all') {
      const today = new Date();
      const nextWeek = new Date(today.getTime() + 7 * 24 * 60 * 60 * 1000);
      
      filtered = filtered.filter(t => {
        const dueDate = new Date(t.next_emi_due_date);
        switch (dueDateFilter) {
          case 'overdue':
            return dueDate < today;
          case 'due_this_week':
            return dueDate >= today && dueDate <= nextWeek;
          case 'advance_pending':
            return t.advance_payment_status === 'pending';
          default:
            return true;
        }
      });
    }

    setFilteredTransactions(filtered);
  }, [transactions, searchQuery, statusFilter, dueDateFilter]);

  const loadTransactions = async () => {
    try {
      setLoading(true);
      
      const mockData: PrepaidEMITransaction[] = [
        {
          id: '1',
          booking_reference: 'TXP-001234',
          customer_name: 'Rajesh Kumar',
          customer_phone: '+91 98765 43210',
          package_name: 'Goa Beach Paradise - 5 Days',
          advance_payment_amount: 10000,
          advance_payment_status: 'completed',
          advance_payment_date: '2024-01-15',
          total_emi_amount: 40000,
          monthly_emi_amount: 8000,
          remaining_emi_months: 4,
          next_emi_due_date: '2024-02-15',
          total_paid_amount: 18000,
          pending_amount: 32000,
          payment_status: 'active',
          auto_debit_enabled: true,
          payment_method: 'UPI',
          created_at: '2024-01-10'
        },
        {
          id: '2',
          booking_reference: 'TXP-001235',
          customer_name: 'Priya Sharma',
          customer_phone: '+91 87654 32109',
          package_name: 'Kerala Backwaters - 7 Days',
          advance_payment_amount: 15000,
          advance_payment_status: 'pending',
          total_emi_amount: 60000,
          monthly_emi_amount: 10000,
          remaining_emi_months: 6,
          next_emi_due_date: '2024-02-10',
          total_paid_amount: 0,
          pending_amount: 75000,
          payment_status: 'active',
          auto_debit_enabled: false,
          payment_method: 'Bank Transfer',
          created_at: '2024-01-08'
        }
      ];
      
      setTransactions(mockData);
    } catch (error) {
      console.error('Error loading transactions:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadEMISettings = async () => {
    try {
      // API call to fetch EMI settings
    } catch (error) {
      console.error('Error loading EMI settings:', error);
    }
  };

  const updateEMISettings = async (newSettings: EMISettings) => {
    try {
      setEmiSettings(newSettings);
      setShowSettings(false);
    } catch (error) {
      console.error('Error updating EMI settings:', error);
    }
  };

  const sendPaymentReminder = async (transactionId: string) => {
    try {
      alert('Payment reminder sent successfully!');
    } catch (error) {
      console.error('Error sending reminder:', error);
    }
  };

  const processPayment = async (transactionId: string, amount: number) => {
    try {
      alert('Payment processed successfully!');
      loadTransactions();
    } catch (error) {
      console.error('Error processing payment:', error);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'text-green-600 bg-green-100';
      case 'active': return 'text-blue-600 bg-blue-100';
      case 'pending': return 'text-yellow-600 bg-yellow-100';
      case 'defaulted': return 'text-red-600 bg-red-100';
      case 'cancelled': return 'text-gray-600 bg-gray-100';
      case 'failed': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const formatCurrency = (amount: number) => {
    return `₹${amount.toLocaleString()}`;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-IN');
  };

  const isOverdue = (dueDate: string) => {
    return new Date(dueDate) < new Date();
  };

  const stats = {
    totalActive: transactions.filter(t => t.payment_status === 'active').length,
    totalPending: transactions.filter(t => t.advance_payment_status === 'pending').length,
    totalOverdue: transactions.filter(t => isOverdue(t.next_emi_due_date)).length,
    totalRevenue: transactions.reduce((sum, t) => sum + t.total_paid_amount, 0),
    pendingRevenue: transactions.reduce((sum, t) => sum + t.pending_amount, 0)
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto p-6">
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-2xl font-bold text-gray-800">Prepaid EMI Management</h1>
          <p className="text-gray-600">Manage prepaid EMI plans, payments, and customer communications</p>
        </div>
        <div className="flex gap-3">
          <button
            onClick={() => setShowSettings(true)}
            className="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors flex items-center gap-2"
          >
            <Settings className="w-4 h-4" />
            Settings
          </button>
          <button className="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-dark transition-colors flex items-center gap-2">
            <Download className="w-4 h-4" />
            Export Report
          </button>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="border-b border-gray-200 mb-8">
        <nav className="-mb-px flex space-x-8">
          <button
            onClick={() => setActiveTab('transactions')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'transactions'
                ? 'border-primary text-primary'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <div className="flex items-center gap-2">
              <CreditCard className="w-4 h-4" />
              EMI Transactions
            </div>
          </button>
          <button
            onClick={() => setActiveTab('packages')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'packages'
                ? 'border-primary text-primary'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <div className="flex items-center gap-2">
              <Package className="w-4 h-4" />
              Available Family EMI Packages
            </div>
          </button>
        </nav>
      </div>

      {activeTab === 'transactions' && (
        <>
          <div className="grid grid-cols-1 md:grid-cols-5 gap-6 mb-8">
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Active EMIs</p>
              <p className="text-2xl font-bold text-blue-600">{stats.totalActive}</p>
            </div>
            <TrendingUp className="w-8 h-8 text-blue-600" />
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Pending Advance</p>
              <p className="text-2xl font-bold text-yellow-600">{stats.totalPending}</p>
            </div>
            <Clock className="w-8 h-8 text-yellow-600" />
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Overdue</p>
              <p className="text-2xl font-bold text-red-600">{stats.totalOverdue}</p>
            </div>
            <AlertCircle className="w-8 h-8 text-red-600" />
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Revenue Collected</p>
              <p className="text-2xl font-bold text-green-600">{formatCurrency(stats.totalRevenue)}</p>
            </div>
            <DollarSign className="w-8 h-8 text-green-600" />
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Pending Revenue</p>
              <p className="text-2xl font-bold text-orange-600">{formatCurrency(stats.pendingRevenue)}</p>
            </div>
            <CreditCard className="w-8 h-8 text-orange-600" />
          </div>
        </div>
      </div>

      <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200 mb-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              placeholder="Search by booking ref, customer, phone..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
            />
          </div>

          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
          >
            <option value="all">All Status</option>
            <option value="active">Active</option>
            <option value="completed">Completed</option>
            <option value="defaulted">Defaulted</option>
            <option value="cancelled">Cancelled</option>
          </select>

          <select
            value={dueDateFilter}
            onChange={(e) => setDueDateFilter(e.target.value)}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
          >
            <option value="all">All Due Dates</option>
            <option value="overdue">Overdue</option>
            <option value="due_this_week">Due This Week</option>
            <option value="advance_pending">Advance Pending</option>
          </select>

          <div className="flex items-center text-sm text-gray-600">
            <Filter className="w-4 h-4 mr-2" />
            Showing {filteredTransactions.length} of {transactions.length} transactions
          </div>
        </div>
      </div>

      <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Booking Details
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Customer
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Advance Payment
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  EMI Details
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredTransactions.map((transaction) => (
                <tr key={transaction.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div className="text-sm font-medium text-gray-900">
                        {transaction.booking_reference}
                      </div>
                      <div className="text-sm text-gray-500">
                        {transaction.package_name}
                      </div>
                    </div>
                  </td>
                  
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div className="text-sm font-medium text-gray-900">
                        {transaction.customer_name}
                      </div>
                      <div className="text-sm text-gray-500">
                        {transaction.customer_phone}
                      </div>
                    </div>
                  </td>

                  <td className="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div className="text-sm font-medium text-gray-900">
                        {formatCurrency(transaction.advance_payment_amount)}
                      </div>
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(transaction.advance_payment_status)}`}>
                        {transaction.advance_payment_status}
                      </span>
                    </div>
                  </td>

                  <td className="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div className="text-sm font-medium text-gray-900">
                        {formatCurrency(transaction.monthly_emi_amount)}/month
                      </div>
                      <div className="text-sm text-gray-500">
                        {transaction.remaining_emi_months} months left
                      </div>
                      <div className={`text-sm ${isOverdue(transaction.next_emi_due_date) ? 'text-red-600 font-medium' : 'text-gray-500'}`}>
                        Next: {formatDate(transaction.next_emi_due_date)}
                      </div>
                    </div>
                  </td>

                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(transaction.payment_status)}`}>
                      {transaction.payment_status}
                    </span>
                    {transaction.auto_debit_enabled && (
                      <div className="text-xs text-green-600 mt-1">Auto-debit enabled</div>
                    )}
                  </td>

                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex space-x-2">
                      <button
                        onClick={() => setSelectedTransaction(transaction)}
                        className="text-primary hover:text-primary-dark"
                      >
                        <Eye className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => sendPaymentReminder(transaction.id)}
                        className="text-blue-600 hover:text-blue-800"
                      >
                        <Bell className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => processPayment(transaction.id, transaction.monthly_emi_amount)}
                        className="text-green-600 hover:text-green-800"
                      >
                        <CreditCard className="w-4 h-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
        </>
      )}

      {activeTab === 'packages' && (
        <FamilyEMIPackages />
      )}

      {selectedTransaction && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-xl font-bold text-gray-800">Transaction Details</h2>
                <button
                  onClick={() => setSelectedTransaction(null)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  ×
                </button>
              </div>

              <div className="grid grid-cols-2 gap-6">
                <div>
                  <h3 className="font-semibold text-gray-800 mb-3">Booking Information</h3>
                  <div className="space-y-2 text-sm">
                    <div><span className="text-gray-600">Reference:</span> {selectedTransaction.booking_reference}</div>
                    <div><span className="text-gray-600">Package:</span> {selectedTransaction.package_name}</div>
                    <div><span className="text-gray-600">Created:</span> {formatDate(selectedTransaction.created_at)}</div>
                  </div>
                </div>

                <div>
                  <h3 className="font-semibold text-gray-800 mb-3">Customer Information</h3>
                  <div className="space-y-2 text-sm">
                    <div><span className="text-gray-600">Name:</span> {selectedTransaction.customer_name}</div>
                    <div><span className="text-gray-600">Phone:</span> {selectedTransaction.customer_phone}</div>
                    <div><span className="text-gray-600">Payment Method:</span> {selectedTransaction.payment_method}</div>
                  </div>
                </div>

                <div>
                  <h3 className="font-semibold text-gray-800 mb-3">Payment Details</h3>
                  <div className="space-y-2 text-sm">
                    <div><span className="text-gray-600">Advance Payment:</span> {formatCurrency(selectedTransaction.advance_payment_amount)}</div>
                    <div><span className="text-gray-600">Monthly EMI:</span> {formatCurrency(selectedTransaction.monthly_emi_amount)}</div>
                    <div><span className="text-gray-600">Total EMI Amount:</span> {formatCurrency(selectedTransaction.total_emi_amount)}</div>
                  </div>
                </div>

                <div>
                  <h3 className="font-semibold text-gray-800 mb-3">Payment Status</h3>
                  <div className="space-y-2 text-sm">
                    <div><span className="text-gray-600">Total Paid:</span> {formatCurrency(selectedTransaction.total_paid_amount)}</div>
                    <div><span className="text-gray-600">Pending:</span> {formatCurrency(selectedTransaction.pending_amount)}</div>
                    <div><span className="text-gray-600">Next Due:</span> {formatDate(selectedTransaction.next_emi_due_date)}</div>
                  </div>
                </div>
              </div>

              <div className="mt-6 flex justify-end space-x-3">
                <button
                  onClick={() => sendPaymentReminder(selectedTransaction.id)}
                  className="px-4 py-2 bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200 transition-colors"
                >
                  Send Reminder
                </button>
                <button
                  onClick={() => processPayment(selectedTransaction.id, selectedTransaction.monthly_emi_amount)}
                  className="px-4 py-2 bg-green-100 text-green-700 rounded-lg hover:bg-green-200 transition-colors"
                >
                  Process Payment
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {showSettings && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg max-w-md w-full">
            <div className="p-6">
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-xl font-bold text-gray-800">EMI Settings</h2>
                <button
                  onClick={() => setShowSettings(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  ×
                </button>
              </div>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Default Advance Payment (%)
                  </label>
                  <input
                    type="number"
                    value={emiSettings.default_advance_percent}
                    onChange={(e) => setEmiSettings({...emiSettings, default_advance_percent: Number(e.target.value)})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Default Discount (%)
                  </label>
                  <input
                    type="number"
                    value={emiSettings.default_discount_percent}
                    onChange={(e) => setEmiSettings({...emiSettings, default_discount_percent: Number(e.target.value)})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Processing Fee (%)
                  </label>
                  <input
                    type="number"
                    value={emiSettings.default_processing_fee}
                    onChange={(e) => setEmiSettings({...emiSettings, default_processing_fee: Number(e.target.value)})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Reminder Days Before Due Date
                  </label>
                  <input
                    type="number"
                    value={emiSettings.reminder_days_before}
                    onChange={(e) => setEmiSettings({...emiSettings, reminder_days_before: Number(e.target.value)})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Late Fee (%)
                  </label>
                  <input
                    type="number"
                    value={emiSettings.late_fee_percent}
                    onChange={(e) => setEmiSettings({...emiSettings, late_fee_percent: Number(e.target.value)})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Grace Period (Days)
                  </label>
                  <input
                    type="number"
                    value={emiSettings.grace_period_days}
                    onChange={(e) => setEmiSettings({...emiSettings, grace_period_days: Number(e.target.value)})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                  />
                </div>

                <div className="flex items-center">
                  <input
                    type="checkbox"
                    checked={emiSettings.auto_debit_enabled}
                    onChange={(e) => setEmiSettings({...emiSettings, auto_debit_enabled: e.target.checked})}
                    className="mr-3"
                  />
                  <label className="text-sm font-medium text-gray-700">
                    Enable Auto-debit by Default
                  </label>
                </div>
              </div>

              <div className="mt-6 flex justify-end space-x-3">
                <button
                  onClick={() => setShowSettings(false)}
                  className="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
                >
                  Cancel
                </button>
                <button
                  onClick={() => updateEMISettings(emiSettings)}
                  className="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-dark transition-colors"
                >
                  Save Settings
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default PrepaidEMIManagement; 