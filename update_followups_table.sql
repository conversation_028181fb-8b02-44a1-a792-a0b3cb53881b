-- Add new columns with default values
ALTER TABLE followups ADD COLUMN IF NOT EXISTS hotel_status TEXT DEFAULT 'pending';
ALTER TABLE followups ADD COLUMN IF NOT EXISTS cab_status TEXT DEFAULT 'pending';
ALTER TABLE followups ADD COLUMN IF NOT EXISTS flight_train_status TEXT DEFAULT 'pending';
ALTER TABLE followups ADD COLUMN IF NOT EXISTS transportation_status TEXT DEFAULT 'pending';
ALTER TABLE followups ADD COLUMN IF NOT EXISTS hotel_details TEXT;
ALTER TABLE followups ADD COLUMN IF NOT EXISTS hotel_full_amount NUMERIC;
ALTER TABLE followups ADD COLUMN IF NOT EXISTS hotel_advance_amount NUMERIC;
ALTER TABLE followups ADD COLUMN IF NOT EXISTS cab_name TEXT;
ALTER TABLE followups ADD COLUMN IF NOT EXISTS cab_full_amount NUMERIC;
ALTER TABLE followups ADD COLUMN IF NOT EXISTS cab_advance_amount NUMERIC;
ALTER TABLE followups ADD COLUMN IF NOT EXISTS flight_train_amount NUMERIC;
ALTER TABLE followups ADD COLUMN IF NOT EXISTS transportation_amount NUMERIC;

-- Add comments for clarity on new columns and their expected values
COMMENT ON COLUMN followups.hotel_status IS 'Expected values: ''not completed'', ''pending'', ''completed''';
COMMENT ON COLUMN followups.cab_status IS 'Expected values: ''advance paid'', ''not completed'', ''pending'', ''completed''';
COMMENT ON COLUMN followups.flight_train_status IS 'Expected values: ''flight paid'', ''train paid'', ''flight not paid'', ''train not paid'', ''no flight/train'', ''pending''';
COMMENT ON COLUMN followups.transportation_status IS 'Expected values: ''ferry booked'', ''bus booked'', ''ferry not booked'', ''bus not booked'', ''pending'', ''no ferry/bus''';
COMMENT ON COLUMN followups.hotel_details IS 'Stores the name of the hotel.';
COMMENT ON COLUMN followups.hotel_full_amount IS 'Stores the total cost for the hotel booking.';
COMMENT ON COLUMN followups.hotel_advance_amount IS 'Stores the advance amount paid for the hotel booking.';
COMMENT ON COLUMN followups.cab_name IS 'Stores the name or details of the cab service.';
COMMENT ON COLUMN followups.cab_full_amount IS 'Stores the total cost for the cab service.';
COMMENT ON COLUMN followups.cab_advance_amount IS 'Stores the advance amount paid for the cab service.';
COMMENT ON COLUMN followups.flight_train_amount IS 'Stores the cost of the flight or train ticket.';
COMMENT ON COLUMN followups.transportation_amount IS 'Stores the cost of other transportation like ferry or bus.';

-- Add notes column
ALTER TABLE followups ADD COLUMN IF NOT EXISTS notes TEXT;
COMMENT ON COLUMN followups.notes IS 'Stores additional notes for the followup.';
