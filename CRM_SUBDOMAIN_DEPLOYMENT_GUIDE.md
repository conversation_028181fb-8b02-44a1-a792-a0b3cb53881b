# CRM Subdomain Deployment Guide - crm.tripxplo.com

## 🎯 Objective
Deploy the TripXplo CRM application to `crm.tripxplo.com` subdomain without affecting the main domain or existing services.

## 📋 Prerequisites
- [x] CRM application built (`npm run build` completed)
- [x] Linode server access (*************)
- [x] SSH access to the server
- [ ] DNS configuration for crm.tripxplo.com

## 🚀 Automated Deployment

### Option 1: Using the Deployment Script
```powershell
# Run the automated deployment script
.\deploy-crm-subdomain.ps1
```

### Option 2: Manual Deployment Steps

If the automated script fails, follow these manual steps:

#### Step 1: Upload Files to Server
```powershell
# Create temp directory on server
ssh root@************* "mkdir -p /tmp/crm-deploy"

# Upload built files
scp -r dist/* root@*************:/tmp/crm-deploy/
```

#### Step 2: Configure Server (SSH into server and run these commands)
```bash
# SSH into server
ssh root@*************

# Create CRM directory
mkdir -p /var/www/crm

# Copy files from temp to web directory
cp -r /tmp/crm-deploy/* /var/www/crm/

# Set proper permissions
chown -R www-data:www-data /var/www/crm
chmod -R 755 /var/www/crm

# Create Nginx configuration
cat > /etc/nginx/sites-available/crm.tripxplo.com << 'EOF'
server {
    listen 80;
    server_name crm.tripxplo.com www.crm.tripxplo.com;
    
    root /var/www/crm;
    index index.html index.htm;
    
    # Serve static files
    location / {
        try_files $uri $uri/ /index.html;
    }
    
    # Handle React Router (SPA)
    location ~ ^.+\..+$ {
        try_files $uri =404;
    }
    
    # Cache static assets
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot|map)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header Access-Control-Allow-Origin "*";
    }
    
    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' https: data: blob: 'unsafe-inline' 'unsafe-eval'; connect-src 'self' https://*.supabase.co wss://*.supabase.co; img-src 'self' data: https:; font-src 'self' data:;" always;
    
    # CORS headers for API calls
    add_header Access-Control-Allow-Origin "*" always;
    add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS" always;
    add_header Access-Control-Allow-Headers "DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization" always;
    
    # Handle preflight OPTIONS requests
    if ($request_method = 'OPTIONS') {
        add_header Access-Control-Allow-Origin "*";
        add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS";
        add_header Access-Control-Allow-Headers "DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization";
        add_header Access-Control-Max-Age 1728000;
        add_header Content-Type "text/plain charset=UTF-8";
        add_header Content-Length 0;
        return 204;
    }
}
EOF

# Enable the site
ln -sf /etc/nginx/sites-available/crm.tripxplo.com /etc/nginx/sites-enabled/

# Test and reload Nginx
nginx -t && systemctl reload nginx

# Clean up
rm -rf /tmp/crm-deploy
```

#### Step 3: SSL Certificate Setup
```bash
# Install Certbot (if not already installed)
apt update && apt install -y certbot python3-certbot-nginx

# Get SSL certificate (only after DNS is configured)
certbot --nginx -d crm.tripxplo.com -d www.crm.tripxplo.com --non-interactive --agree-tos --email <EMAIL> --redirect
```

## 🌐 DNS Configuration

### Required DNS Records
Add these A records to your DNS provider:

| Type | Name | Value | TTL |
|------|------|-------|-----|
| A | crm | ************* | 300 |
| A | www.crm | ************* | 300 |

### Where to Add DNS Records

#### Option 1: Domain Registrar (GoDaddy, Namecheap, etc.)
1. Login to your domain registrar
2. Find DNS management section
3. Add the A records above

#### Option 2: Cloudflare (Recommended for performance)
1. Add tripxplo.com to Cloudflare
2. Update nameservers at registrar
3. Add A records in Cloudflare dashboard
4. Enable proxy (orange cloud) for better performance

#### Option 3: Linode DNS Manager
1. Login to Linode Cloud Manager
2. Go to Domains section
3. Add/edit tripxplo.com domain
4. Add the A records

### DNS Verification Commands
```bash
# Check if DNS is propagated
nslookup crm.tripxplo.com

# Test from different DNS servers
dig crm.tripxplo.com @*******
dig crm.tripxplo.com @*******

# Online tools for checking
# https://dnschecker.org
# https://whatsmydns.net
```

## 🧪 Testing & Verification

### After DNS Configuration
1. **Wait for DNS Propagation** (5-60 minutes)
2. **Test HTTP Access:**
   ```bash
   curl -I http://crm.tripxplo.com
   ```
3. **Test in Browser:**
   - Navigate to http://crm.tripxplo.com
   - Verify CRM loads correctly
   - Test login functionality
   - Test all CRM features

### After SSL Setup
1. **Test HTTPS Access:**
   ```bash
   curl -I https://crm.tripxplo.com
   ```
2. **Verify SSL Certificate:**
   - Check for valid SSL in browser
   - No mixed content warnings
   - All features work over HTTPS

## 🔧 Troubleshooting

### Common Issues

#### DNS Not Resolving
- Wait longer for propagation (up to 24-48 hours)
- Check TTL values (lower = faster propagation)
- Clear local DNS cache: `ipconfig /flushdns` (Windows)

#### Site Not Loading
- Check Nginx configuration: `nginx -t`
- Check Nginx status: `systemctl status nginx`
- Check file permissions: `ls -la /var/www/crm/`
- Check Nginx logs: `tail -f /var/log/nginx/error.log`

#### SSL Certificate Issues
- Ensure DNS is working first
- Check if ports 80/443 are open
- Verify domain ownership
- Try manual certificate: `certbot certonly --webroot -w /var/www/crm -d crm.tripxplo.com`

#### CRM Application Issues
- Check browser console for JavaScript errors
- Verify Supabase configuration
- Check CORS settings
- Test API connections

### Debug Commands
```bash
# Check server status
systemctl status nginx
systemctl status certbot

# Check ports
netstat -tuln | grep :80
netstat -tuln | grep :443

# Check logs
tail -f /var/log/nginx/access.log
tail -f /var/log/nginx/error.log

# Test configuration
nginx -t
```

## 📊 Performance Optimization

### Enable Gzip Compression
Add to Nginx configuration:
```nginx
gzip on;
gzip_vary on;
gzip_min_length 1024;
gzip_types text/css application/javascript application/json image/svg+xml;
```

### CDN Setup (Optional)
- Use Cloudflare for global CDN
- Enable caching and minification
- Configure page rules for optimization

## 🔐 Security Considerations

### Implemented Security Features
- ✅ HTTPS enforcement
- ✅ Security headers (XSS, CSRF protection)
- ✅ Content Security Policy
- ✅ CORS configuration for Supabase
- ✅ File permission restrictions

### Additional Security (Optional)
- Rate limiting
- Fail2ban for SSH protection
- Regular security updates
- Backup strategy

## 📋 Deployment Checklist

### Pre-Deployment
- [ ] CRM application built successfully
- [ ] Server access verified
- [ ] Backup existing configurations

### During Deployment
- [ ] Files uploaded successfully
- [ ] Nginx configuration created
- [ ] Site enabled and tested
- [ ] DNS records configured
- [ ] SSL certificate obtained

### Post-Deployment
- [ ] DNS propagation verified
- [ ] HTTP access confirmed
- [ ] HTTPS access confirmed  
- [ ] CRM functionality tested
- [ ] Performance verified
- [ ] Monitoring set up

## 🎉 Success Indicators

When deployment is successful, you should see:
- ✅ `crm.tripxplo.com` resolves to `*************`
- ✅ HTTP site loads without errors
- ✅ HTTPS redirect works properly
- ✅ SSL certificate is valid
- ✅ CRM login page displays
- ✅ All CRM features work correctly
- ✅ No console errors in browser
- ✅ Supabase connections successful

## 📞 Support

If you encounter issues:
1. Check the troubleshooting section above
2. Review server logs for errors
3. Verify each step was completed correctly
4. Test with simple HTML file first if needed

---

**Note:** This deployment creates a completely separate subdomain that won't affect your main domain or any existing services. 