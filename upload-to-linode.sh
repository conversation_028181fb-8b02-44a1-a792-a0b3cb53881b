#!/bin/bash

# TripXplo Family EMI - Linode Upload Script
# Upload production files to family.tripxplo.com

echo "🚀 TripXplo Family EMI - Linode Deployment"
echo "=========================================="

# Configuration
LINODE_IP=""
LINODE_USER="root"
WEB_ROOT="/var/www/html"
PROD_DIR="family-tripxplo-production"

# Check if production directory exists
if [ ! -d "$PROD_DIR" ]; then
    echo "❌ Production directory not found!"
    echo "Please run ./prepare-production.sh first"
    exit 1
fi

# Get Linode IP if not set
if [ -z "$LINODE_IP" ]; then
    echo "📝 Please enter your Linode server IP address:"
    read -p "Linode IP: " LINODE_IP
fi

echo ""
echo "📋 Deployment Configuration:"
echo "   Server: $LINODE_IP"
echo "   User: $LINODE_USER"
echo "   Web Root: $WEB_ROOT"
echo "   Source: $PROD_DIR"
echo ""

# Confirm deployment
read -p "🤔 Continue with deployment? (y/N): " confirm
if [[ $confirm != [yY] ]]; then
    echo "❌ Deployment cancelled"
    exit 0
fi

echo ""
echo "🔄 Starting deployment..."

# Create backup of existing files
echo "💾 Creating backup of existing files..."
ssh $LINODE_USER@$LINODE_IP "
    if [ -d $WEB_ROOT ]; then
        sudo cp -r $WEB_ROOT ${WEB_ROOT}.backup.\$(date +%Y%m%d_%H%M%S)
        echo '✅ Backup created'
    fi
"

# Upload files
echo "📤 Uploading files to Linode..."
scp -r $PROD_DIR/* $LINODE_USER@$LINODE_IP:$WEB_ROOT/

# Set proper permissions
echo "🔐 Setting file permissions..."
ssh $LINODE_USER@$LINODE_IP "
    sudo chown -R www-data:www-data $WEB_ROOT
    sudo chmod -R 755 $WEB_ROOT
    sudo chmod 644 $WEB_ROOT/*.html
    sudo chmod 644 $WEB_ROOT/*.css
    sudo chmod 644 $WEB_ROOT/js/*.js
    echo '✅ Permissions set'
"

# Install and configure Nginx (if needed)
echo "🔧 Configuring Nginx..."
ssh $LINODE_USER@$LINODE_IP "
    # Install Nginx if not installed
    if ! command -v nginx &> /dev/null; then
        sudo apt update
        sudo apt install -y nginx
        sudo systemctl start nginx
        sudo systemctl enable nginx
        echo '✅ Nginx installed'
    fi

    # Create site configuration
    sudo tee /etc/nginx/sites-available/family.tripxplo.com > /dev/null << 'EOF'
server {
    listen 80;
    server_name family.tripxplo.com www.family.tripxplo.com;
    root $WEB_ROOT;
    index index.html;

    # Enable gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/css application/javascript application/json image/svg+xml text/plain text/xml;

    # Cache static assets
    location ~* \.(css|js|png|jpg|jpeg|gif|svg|ico)$ {
        expires 1y;
        add_header Cache-Control \"public, immutable\";
    }

    # Main application
    location / {
        try_files \$uri \$uri/ /index.html;
    }

    # Security headers
    add_header X-Content-Type-Options nosniff;
    add_header X-Frame-Options DENY;
    add_header X-XSS-Protection \"1; mode=block\";
}
EOF

    # Enable site
    sudo ln -sf /etc/nginx/sites-available/family.tripxplo.com /etc/nginx/sites-enabled/
    sudo rm -f /etc/nginx/sites-enabled/default
    
    # Test and reload Nginx
    sudo nginx -t && sudo systemctl reload nginx
    echo '✅ Nginx configured'
"

# Install SSL certificate
echo "🔒 Setting up SSL certificate..."
ssh $LINODE_USER@$LINODE_IP "
    # Install Certbot if not installed
    if ! command -v certbot &> /dev/null; then
        sudo apt install -y certbot python3-certbot-nginx
        echo '✅ Certbot installed'
    fi

    # Get SSL certificate
    sudo certbot --nginx -d family.tripxplo.com -d www.family.tripxplo.com --non-interactive --agree-tos --email <EMAIL>
    echo '✅ SSL certificate configured'
"

# Test deployment
echo "🧪 Testing deployment..."
if curl -f -s http://$LINODE_IP > /dev/null; then
    echo "✅ Server is responding"
else
    echo "⚠️ Server test failed - please check manually"
fi

echo ""
echo "🎉 Deployment completed successfully!"
echo ""
echo "📋 Next Steps:"
echo "1. Point family.tripxplo.com DNS to $LINODE_IP"
echo "2. Wait for DNS propagation (up to 24 hours)"
echo "3. Test the site at https://family.tripxplo.com"
echo "4. Monitor logs: sudo tail -f /var/log/nginx/access.log"
echo ""
echo "🔗 Your site will be available at:"
echo "   http://$LINODE_IP (immediate)"
echo "   https://family.tripxplo.com (after DNS propagation)"
echo ""
echo "📊 Deployment Summary:"
echo "   ✅ Files uploaded"
echo "   ✅ Permissions set"
echo "   ✅ Nginx configured"
echo "   ✅ SSL certificate installed"
echo "   ✅ Ready for production!"
