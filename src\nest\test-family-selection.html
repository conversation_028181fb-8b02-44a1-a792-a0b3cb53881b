<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Family Selection - TripXplo Family EMI</title>
    <style>
        body {
            font-family: 'Inter', sans-serif;
            margin: 0;
            padding: 20px;
            background: #f8f9fa;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }
        .test-header {
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            background: #f8f9ff;
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }
        .counter-group {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 15px 0;
            padding: 15px;
            background: white;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }
        .counter-info {
            flex: 1;
        }
        .counter-label {
            font-weight: 600;
            color: #333;
        }
        .counter-sublabel {
            font-size: 0.85rem;
            color: #666;
        }
        .counter-controls {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        .counter-btn {
            width: 40px;
            height: 40px;
            border: 2px solid #667eea;
            background: white;
            color: #667eea;
            border-radius: 50%;
            cursor: pointer;
            font-weight: bold;
            font-size: 1.2rem;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .counter-btn:hover {
            background: #667eea;
            color: white;
        }
        .counter-value {
            font-size: 1.2rem;
            font-weight: 600;
            min-width: 30px;
            text-align: center;
        }
        .result-display {
            margin-top: 20px;
            padding: 20px;
            background: #e8f5e8;
            border-radius: 8px;
            border-left: 4px solid #28a745;
        }
        .test-btn {
            padding: 12px 24px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            margin: 10px;
        }
        .family-type-result {
            font-size: 1.1rem;
            font-weight: 600;
            color: #333;
            margin: 10px 0;
        }
        .composition-result {
            font-size: 1rem;
            color: #666;
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1>🧪 Family Selection Test</h1>
            <p>Testing the corrected family type selection with proper field mapping</p>
        </div>

        <div class="test-section">
            <h3>Select Travelers</h3>
            
            <div class="counter-group">
                <div class="counter-info">
                    <div class="counter-label">Adults</div>
                    <div class="counter-sublabel">(12+ years) → no_of_adults</div>
                </div>
                <div class="counter-controls">
                    <button class="counter-btn" onclick="updateCounter('adults', -1)">-</button>
                    <span class="counter-value" id="adultsCount">3</span>
                    <button class="counter-btn" onclick="updateCounter('adults', 1)">+</button>
                </div>
            </div>

            <div class="counter-group">
                <div class="counter-info">
                    <div class="counter-label">Child below 5</div>
                    <div class="counter-sublabel">(2-5 years) → no_of_child</div>
                </div>
                <div class="counter-controls">
                    <button class="counter-btn" onclick="updateCounter('child', -1)">-</button>
                    <span class="counter-value" id="childCount">1</span>
                    <button class="counter-btn" onclick="updateCounter('child', 1)">+</button>
                </div>
            </div>

            <div class="counter-group">
                <div class="counter-info">
                    <div class="counter-label">Children</div>
                    <div class="counter-sublabel">(6-11 years) → no_of_children</div>
                </div>
                <div class="counter-controls">
                    <button class="counter-btn" onclick="updateCounter('children', -1)">-</button>
                    <span class="counter-value" id="childrenCount">0</span>
                    <button class="counter-btn" onclick="updateCounter('children', 1)">+</button>
                </div>
            </div>

            <div class="counter-group">
                <div class="counter-info">
                    <div class="counter-label">Infants</div>
                    <div class="counter-sublabel">(Under 2 years) → no_of_infants</div>
                </div>
                <div class="counter-controls">
                    <button class="counter-btn" onclick="updateCounter('infants', -1)">-</button>
                    <span class="counter-value" id="infantsCount">0</span>
                    <button class="counter-btn" onclick="updateCounter('infants', 1)">+</button>
                </div>
            </div>
        </div>

        <div class="result-display">
            <h3>🎯 Detected Family Type</h3>
            <div class="family-type-result" id="familyTypeResult">Cosmic Combo Duo - 3 Adults + 1 Child (Below 5 yrs)</div>
            <div class="composition-result" id="compositionResult">Composition: 3 Adults + 1 Child (2-5 yrs)</div>
            
            <div style="margin-top: 15px;">
                <strong>Database Fields:</strong><br>
                <span id="dbFieldsResult">no_of_adults: 3, no_of_child: 1, no_of_children: 0, no_of_infants: 0</span>
            </div>
        </div>

        <div style="text-align: center; margin-top: 20px;">
            <button class="test-btn" onclick="testFamilyTypeDetection()">🔍 Test Family Type Detection</button>
            <button class="test-btn" onclick="testPackageSearch()">📦 Test Package Search</button>
        </div>

        <div id="testResults" style="display: none; margin-top: 20px; padding: 20px; background: #fff3cd; border-radius: 8px;">
            <h3>Test Results</h3>
            <pre id="resultOutput"></pre>
        </div>
    </div>

    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
    <script src="js/config.js"></script>
    <script src="js/databaseService.js"></script>
    <script>
        // Test data
        let travelers = { adults: 3, child: 1, children: 0, infants: 0 };

        function updateCounter(type, change) {
            travelers[type] = Math.max(0, travelers[type] + change);
            if (type === 'adults') travelers[type] = Math.max(1, travelers[type]); // At least 1 adult

            document.getElementById(type + 'Count').textContent = travelers[type];
            updateDisplay();
        }

        function updateDisplay() {
            const { adults, child, children, infants } = travelers;
            
            // Update family type display
            let displayText = `${adults} Adult${adults > 1 ? 's' : ''}`;
            if (child > 0) displayText += ` + ${child} Child (2-5 yrs)`;
            if (children > 0) displayText += ` + ${children} Children (6-11 yrs)`;
            if (infants > 0) displayText += ` + ${infants} Infant${infants > 1 ? 's' : ''}`;

            document.getElementById('familyTypeResult').textContent = displayText;
            document.getElementById('compositionResult').textContent = `Composition: ${displayText}`;
            document.getElementById('dbFieldsResult').textContent = 
                `no_of_adults: ${adults}, no_of_child: ${child}, no_of_children: ${children}, no_of_infants: ${infants}`;
        }

        async function testFamilyTypeDetection() {
            try {
                const { adults, child, children, infants } = travelers;
                
                console.log('🧪 Testing family type detection with:', { adults, child, children, infants });
                
                const familyType = await databaseService.detectFamilyType(adults, child, children, infants);
                
                const result = {
                    input: { adults, child, children, infants },
                    detected_family_type: familyType,
                    success: true
                };
                
                showTestResults('Family Type Detection Test', result);
                
            } catch (error) {
                showTestResults('Family Type Detection Error', { error: error.message });
            }
        }

        async function testPackageSearch() {
            try {
                const { adults, child, children, infants } = travelers;
                
                const searchParams = {
                    destination: 'Goa',
                    adults,
                    child,
                    children,
                    infants
                };
                
                console.log('🧪 Testing package search with:', searchParams);
                
                const result = await databaseService.searchPackages(searchParams);
                
                const summary = {
                    search_params: searchParams,
                    success: result.success,
                    packages_found: result.packages?.length || 0,
                    matched_family_type: result.matched_family_type,
                    first_package: result.packages?.[0] ? {
                        id: result.packages[0].id,
                        title: result.packages[0].title,
                        family_type: result.packages[0].family_type
                    } : null
                };
                
                showTestResults('Package Search Test', summary);
                
            } catch (error) {
                showTestResults('Package Search Error', { error: error.message });
            }
        }

        function showTestResults(title, data) {
            const resultsDiv = document.getElementById('testResults');
            const resultOutput = document.getElementById('resultOutput');
            
            resultsDiv.style.display = 'block';
            
            const timestamp = new Date().toLocaleTimeString();
            resultOutput.innerHTML = `[${timestamp}] ${title}\n` + JSON.stringify(data, null, 2);
        }

        // Initialize display
        updateDisplay();
    </script>
</body>
</html>
