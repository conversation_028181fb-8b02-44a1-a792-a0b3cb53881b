-- =============================================================================
-- FAMILY EMI PACKAGES - DATABASE QUERIES
-- =============================================================================
-- These queries retrieve family EMI packages with their EMI plans from the database

-- =============================================================================
-- 1. GET ALL FAMILY EMI PACKAGES WITH EMI PLANS
-- =============================================================================
-- This query retrieves all family type packages that have EMI enabled
-- along with their associated EMI plans

SELECT 
    ftp.id,
    ftp.quote_id,
    ftp.family_type_id,
    ftp.family_type_name,
    ftp.no_of_adults,
    ftp.no_of_children,
    ftp.no_of_child,
    ftp.no_of_infants,
    ftp.family_count,
    ftp.rooms_need,
    ftp.cab_type,
    ftp.cab_capacity,
    ftp.destination_category,
    ftp.package_duration_days,
    ftp.hotel_cost,
    ftp.vehicle_cost,
    ftp.additional_costs,
    ftp.subtotal,
    ftp.discount_amount,
    ftp.total_price,
    ftp.emi_enabled,
    ftp.min_emi_months,
    ftp.max_emi_months,
    ftp.emi_processing_fee_percent,
    ftp.emi_interest_rate_percent,
    ftp.is_public_visible,
    ftp.season_category,
    ftp.public_display_order,
    ftp.created_at,
    ftp.updated_at,
    
    -- EMI Plans as JSON array
    COALESCE(
        json_agg(
            json_build_object(
                'id', emi.id,
                'emi_months', emi.emi_months,
                'monthly_amount', emi.monthly_amount,
                'total_amount', emi.total_amount,
                'processing_fee', emi.processing_fee,
                'total_interest', emi.total_interest,
                'first_payment_amount', emi.first_payment_amount,
                'subsequent_payment_amount', emi.subsequent_payment_amount,
                'final_payment_amount', emi.final_payment_amount,
                'savings_vs_full_payment', emi.savings_vs_full_payment,
                'effective_annual_rate', emi.effective_annual_rate,
                'is_featured', emi.is_featured,
                'marketing_label', emi.marketing_label,
                'created_at', emi.created_at
            ) ORDER BY emi.emi_months
        ) FILTER (WHERE emi.id IS NOT NULL),
        '[]'::json
    ) as emi_plans
    
FROM family_type_prices ftp
LEFT JOIN family_type_emi_plans emi ON ftp.id = emi.family_price_id
WHERE ftp.emi_enabled = true 
    AND ftp.is_public_visible = true
GROUP BY ftp.id, ftp.quote_id, ftp.family_type_id, ftp.family_type_name,
         ftp.no_of_adults, ftp.no_of_children, ftp.no_of_child, ftp.no_of_infants,
         ftp.family_count, ftp.rooms_need, ftp.cab_type, ftp.cab_capacity,
         ftp.destination_category, ftp.package_duration_days, ftp.hotel_cost,
         ftp.vehicle_cost, ftp.additional_costs, ftp.subtotal, ftp.discount_amount,
         ftp.total_price, ftp.emi_enabled, ftp.min_emi_months, ftp.max_emi_months,
         ftp.emi_processing_fee_percent, ftp.emi_interest_rate_percent,
         ftp.is_public_visible, ftp.season_category, ftp.public_display_order,
         ftp.created_at, ftp.updated_at
ORDER BY ftp.public_display_order ASC, ftp.created_at DESC;

-- =============================================================================
-- 2. GET FAMILY EMI PACKAGES BY DESTINATION
-- =============================================================================
-- Filter packages by destination category

SELECT 
    ftp.*,
    COUNT(emi.id) as emi_plans_count,
    MIN(emi.monthly_amount) as min_monthly_emi,
    MAX(emi.monthly_amount) as max_monthly_emi,
    
    -- Featured EMI plan details
    (SELECT json_build_object(
        'id', id,
        'emi_months', emi_months,
        'monthly_amount', monthly_amount,
        'marketing_label', marketing_label
    ) FROM family_type_emi_plans 
     WHERE family_price_id = ftp.id AND is_featured = true 
     LIMIT 1) as featured_emi_plan
    
FROM family_type_prices ftp
LEFT JOIN family_type_emi_plans emi ON ftp.id = emi.family_price_id
WHERE ftp.emi_enabled = true 
    AND ftp.is_public_visible = true
    AND ftp.destination_category = $1  -- Parameter: 'Beach', 'Hill Station', etc.
GROUP BY ftp.id
ORDER BY ftp.public_display_order ASC;

-- =============================================================================
-- 3. GET FAMILY EMI PACKAGES BY FAMILY TYPE
-- =============================================================================
-- Filter packages by family type ID

SELECT 
    ftp.*,
    json_agg(
        json_build_object(
            'id', emi.id,
            'emi_months', emi.emi_months,
            'monthly_amount', emi.monthly_amount,
            'total_amount', emi.total_amount,
            'is_featured', emi.is_featured,
            'marketing_label', emi.marketing_label
        ) ORDER BY emi.emi_months
    ) as emi_plans
    
FROM family_type_prices ftp
LEFT JOIN family_type_emi_plans emi ON ftp.id = emi.family_price_id
WHERE ftp.emi_enabled = true 
    AND ftp.is_public_visible = true
    AND ftp.family_type_id = $1  -- Parameter: 'COUPLE', 'FAMILY_4', etc.
GROUP BY ftp.id
ORDER BY ftp.total_price ASC;

-- =============================================================================
-- 4. GET FAMILY EMI PACKAGES BY PRICE RANGE
-- =============================================================================
-- Filter packages by price range

SELECT 
    ftp.*,
    json_agg(
        json_build_object(
            'emi_months', emi.emi_months,
            'monthly_amount', emi.monthly_amount,
            'is_featured', emi.is_featured
        ) ORDER BY emi.emi_months
    ) as emi_options
    
FROM family_type_prices ftp
LEFT JOIN family_type_emi_plans emi ON ftp.id = emi.family_price_id
WHERE ftp.emi_enabled = true 
    AND ftp.is_public_visible = true
    AND ftp.total_price BETWEEN $1 AND $2  -- Parameters: min_price, max_price
GROUP BY ftp.id
ORDER BY ftp.total_price ASC;

-- =============================================================================
-- 5. GET SINGLE FAMILY EMI PACKAGE WITH DETAILED EMI PLANS
-- =============================================================================
-- Get complete details for a specific package

SELECT 
    ftp.*,
    json_agg(
        json_build_object(
            'id', emi.id,
            'emi_months', emi.emi_months,
            'monthly_amount', emi.monthly_amount,
            'total_amount', emi.total_amount,
            'processing_fee', emi.processing_fee,
            'total_interest', emi.total_interest,
            'first_payment_amount', emi.first_payment_amount,
            'subsequent_payment_amount', emi.subsequent_payment_amount,
            'final_payment_amount', emi.final_payment_amount,
            'savings_vs_full_payment', emi.savings_vs_full_payment,
            'effective_annual_rate', emi.effective_annual_rate,
            'is_featured', emi.is_featured,
            'marketing_label', emi.marketing_label,
            'created_at', emi.created_at,
            'updated_at', emi.updated_at
        ) ORDER BY emi.emi_months
    ) as emi_plans
    
FROM family_type_prices ftp
LEFT JOIN family_type_emi_plans emi ON ftp.id = emi.family_price_id
WHERE ftp.id = $1  -- Parameter: package_id
GROUP BY ftp.id;

-- =============================================================================
-- 6. GET EMI PACKAGES STATISTICS
-- =============================================================================
-- Get summary statistics for dashboard

SELECT 
    COUNT(*) as total_packages,
    COUNT(CASE WHEN emi_enabled = true THEN 1 END) as emi_enabled_packages,
    COUNT(CASE WHEN is_public_visible = true THEN 1 END) as public_visible_packages,
    AVG(total_price) as avg_package_price,
    MIN(total_price) as min_package_price,
    MAX(total_price) as max_package_price,
    SUM(discount_amount) as total_discounts_offered,
    
    -- Destination breakdown
    json_object_agg(
        destination_category, 
        destination_count
    ) as destination_breakdown,
    
    -- Season breakdown
    json_object_agg(
        season_category,
        season_count
    ) as season_breakdown
    
FROM (
    SELECT 
        *,
        COUNT(*) OVER (PARTITION BY destination_category) as destination_count,
        COUNT(*) OVER (PARTITION BY season_category) as season_count
    FROM family_type_prices 
    WHERE emi_enabled = true AND is_public_visible = true
) stats;

-- =============================================================================
-- 7. SEARCH FAMILY EMI PACKAGES
-- =============================================================================
-- Search packages by text query

SELECT 
    ftp.*,
    COUNT(emi.id) as emi_plans_count,
    -- Similarity score for ranking
    GREATEST(
        similarity(ftp.family_type_name, $1),
        similarity(ftp.destination_category, $1),
        similarity(ftp.family_type_id, $1)
    ) as similarity_score
    
FROM family_type_prices ftp
LEFT JOIN family_type_emi_plans emi ON ftp.id = emi.family_price_id
WHERE ftp.emi_enabled = true 
    AND ftp.is_public_visible = true
    AND (
        ftp.family_type_name ILIKE '%' || $1 || '%' OR
        ftp.destination_category ILIKE '%' || $1 || '%' OR
        ftp.family_type_id ILIKE '%' || $1 || '%' OR
        ftp.season_category ILIKE '%' || $1 || '%'
    )
GROUP BY ftp.id
ORDER BY similarity_score DESC, ftp.public_display_order ASC;

-- =============================================================================
-- 8. GET FEATURED EMI PLANS ACROSS ALL PACKAGES
-- =============================================================================
-- Get all featured EMI plans for homepage display

SELECT 
    ftp.family_type_name,
    ftp.destination_category,
    ftp.total_price,
    ftp.discount_amount,
    ftp.package_duration_days,
    emi.emi_months,
    emi.monthly_amount,
    emi.marketing_label,
    emi.is_featured,
    emi.effective_annual_rate
    
FROM family_type_prices ftp
JOIN family_type_emi_plans emi ON ftp.id = emi.family_price_id
WHERE ftp.emi_enabled = true 
    AND ftp.is_public_visible = true
    AND emi.is_featured = true
ORDER BY ftp.public_display_order ASC, emi.emi_months ASC;

-- =============================================================================
-- SUPABASE IMPLEMENTATION EXAMPLE
-- =============================================================================
/*
// TypeScript/JavaScript code for Supabase integration

const loadFamilyEMIPackages = async () => {
  try {
    const { data, error } = await supabase
      .from('family_type_prices')
      .select(`
        *,
        emi_plans:family_type_emi_plans(*)
      `)
      .eq('emi_enabled', true)
      .eq('is_public_visible', true)
      .order('public_display_order', { ascending: true });

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error loading family EMI packages:', error);
    throw error;
  }
};

// Filter by destination
const loadPackagesByDestination = async (destination: string) => {
  const { data, error } = await supabase
    .from('family_type_prices')
    .select(`
      *,
      emi_plans:family_type_emi_plans(*)
    `)
    .eq('emi_enabled', true)
    .eq('is_public_visible', true)
    .eq('destination_category', destination)
    .order('public_display_order', { ascending: true });

  if (error) throw error;
  return data;
};

// Search packages
const searchPackages = async (searchQuery: string) => {
  const { data, error } = await supabase
    .from('family_type_prices')
    .select(`
      *,
      emi_plans:family_type_emi_plans(*)
    `)
    .eq('emi_enabled', true)
    .eq('is_public_visible', true)
    .or(`family_type_name.ilike.%${searchQuery}%,destination_category.ilike.%${searchQuery}%,family_type_id.ilike.%${searchQuery}%`)
    .order('public_display_order', { ascending: true });

  if (error) throw error;
  return data;
};
*/ 