<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quick Database Test</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; background: #f5f5f5; }
        .container { max-width: 600px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }
        .test-btn { background: #8B5CF6; color: white; border: none; padding: 10px 20px; margin: 5px; border-radius: 5px; cursor: pointer; }
        .result { background: #f8f9fa; border: 1px solid #dee2e6; padding: 15px; margin: 10px 0; border-radius: 4px; font-family: monospace; font-size: 12px; }
        .success { border-left: 4px solid #28a745; }
        .error { border-left: 4px solid #dc3545; }
        .loading { border-left: 4px solid #ffc107; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Quick Database Connection Test</h1>
        <p>Click the button below to test your live database connection:</p>
        
        <button class="test-btn" onclick="runQuickTest()">🧪 Test Live Database Connection</button>
        
        <div id="result" class="result">Click the button above to start testing...</div>
    </div>

    <!-- Include required scripts -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <script src="js/config.js"></script>
    <script src="js/databaseService.js"></script>

    <script>
        async function runQuickTest() {
            const resultDiv = document.getElementById('result');
            resultDiv.className = 'result loading';
            resultDiv.innerHTML = '🔄 Testing database connection...\n\n';

            try {
                // Test 1: Configuration
                resultDiv.innerHTML += '1. Checking configuration...\n';
                if (typeof CONFIG === 'undefined') {
                    throw new Error('CONFIG not loaded');
                }
                if (typeof databaseService === 'undefined') {
                    throw new Error('Database service not loaded');
                }
                resultDiv.innerHTML += '   ✅ Configuration loaded\n';
                resultDiv.innerHTML += '   ✅ Database service initialized\n\n';

                // Test 2: Family Types (CRM Database)
                resultDiv.innerHTML += '2. Testing CRM Database (Family Types)...\n';
                const familyTypesResult = await databaseService.getFamilyTypes();
                if (familyTypesResult.success) {
                    resultDiv.innerHTML += `   ✅ SUCCESS: Loaded ${familyTypesResult.data.length} family types\n`;
                    resultDiv.innerHTML += `   📊 Sample: ${familyTypesResult.data.slice(0, 3).map(ft => ft.family_type).join(', ')}\n\n`;
                } else {
                    throw new Error(`Family Types: ${familyTypesResult.error}`);
                }

                // Test 3: Destinations (Quote Database)
                resultDiv.innerHTML += '3. Testing Quote Database (Destinations)...\n';
                const destinationsResult = await databaseService.getDestinations();
                if (destinationsResult.success) {
                    resultDiv.innerHTML += `   ✅ SUCCESS: Loaded ${destinationsResult.data.length} destinations\n`;
                    resultDiv.innerHTML += `   🗺️ Sample: ${destinationsResult.data.slice(0, 5).map(d => d.destination).join(', ')}\n\n`;
                } else {
                    throw new Error(`Destinations: ${destinationsResult.error}`);
                }

                // Test 4: Package Search
                resultDiv.innerHTML += '4. Testing Package Search...\n';
                const searchResult = await databaseService.searchPackages({
                    destination: destinationsResult.data[0]?.destination || 'Kashmir',
                    adults: 2,
                    children: 0,
                    infants: 0
                });
                if (searchResult.success) {
                    resultDiv.innerHTML += `   ✅ SUCCESS: Found ${searchResult.packages.length} packages\n`;
                    resultDiv.innerHTML += `   👨‍👩‍👧‍👦 Detected Family Type: ${searchResult.matched_family_type.family_type}\n\n`;
                } else {
                    resultDiv.innerHTML += `   ⚠️ Package search: ${searchResult.error}\n\n`;
                }

                // Final Result
                resultDiv.className = 'result success';
                resultDiv.innerHTML += '🎉 ALL TESTS PASSED!\n\n';
                resultDiv.innerHTML += '✅ Your Family EMI website is ready with live database integration!\n';
                resultDiv.innerHTML += '✅ You can now deploy to family.tripxplo.com\n\n';
                resultDiv.innerHTML += '📋 Next Steps:\n';
                resultDiv.innerHTML += '1. Test the main website (index.html)\n';
                resultDiv.innerHTML += '2. Try searching for packages\n';
                resultDiv.innerHTML += '3. Test the quote request form\n';
                resultDiv.innerHTML += '4. Deploy to your server\n';

            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML += `\n❌ ERROR: ${error.message}\n\n`;
                resultDiv.innerHTML += '🔧 Troubleshooting:\n';
                resultDiv.innerHTML += '1. Check browser console for detailed errors\n';
                resultDiv.innerHTML += '2. Verify Supabase project status\n';
                resultDiv.innerHTML += '3. Ensure RLS policies allow public access\n';
                resultDiv.innerHTML += '4. Check if tables exist and have data\n';
                
                console.error('Database test error:', error);
            }
        }

        // Auto-run test on page load
        window.addEventListener('load', () => {
            setTimeout(() => {
                document.getElementById('result').innerHTML = 'Page loaded. Click the button to test database connection.';
            }, 1000);
        });
    </script>
</body>
</html>
