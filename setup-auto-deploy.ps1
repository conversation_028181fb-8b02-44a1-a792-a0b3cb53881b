# Setup Auto-Deploy for TripXplo CRM
# This script helps you set up automatic deployment from GitHub

Write-Host "🚀 TripXplo CRM - Auto Deploy Setup" -ForegroundColor Green
Write-Host "====================================" -ForegroundColor Green

$SERVER_IP = "*************"
$SERVER_USER = "root"
$KEY_NAME = "github_actions_key"

Write-Host ""
Write-Host "📋 This script will help you set up automatic deployment from GitHub to crm.tripxplo.com" -ForegroundColor Cyan
Write-Host ""

# Step 1: Generate SSH Key
Write-Host "🔐 Step 1: Generating SSH Key for GitHub Actions..." -ForegroundColor Yellow

$sshDir = "$env:USERPROFILE\.ssh"
$privateKey = "$sshDir\$KEY_NAME"
$publicKey = "$sshDir\$KEY_NAME.pub"

# Create .ssh directory if it doesn't exist
if (-not (Test-Path $sshDir)) {
    New-Item -ItemType Directory -Path $sshDir -Force | Out-Null
}

# Generate SSH key pair
Write-Host "Generating SSH key pair..." -ForegroundColor Gray
& ssh-keygen -t rsa -b 4096 -f $privateKey -N '""' -q

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ SSH key generated successfully!" -ForegroundColor Green
} else {
    Write-Host "❌ Failed to generate SSH key" -ForegroundColor Red
    exit 1
}

# Step 2: Copy public key to server
Write-Host ""
Write-Host "📤 Step 2: Copying public key to server..." -ForegroundColor Yellow
Write-Host "You may be prompted for your server password." -ForegroundColor Gray

# Use ssh-copy-id if available, otherwise manual method
if (Get-Command ssh-copy-id -ErrorAction SilentlyContinue) {
    Write-Host "Using ssh-copy-id..." -ForegroundColor Gray
    & ssh-copy-id -i $publicKey $SERVER_USER@$SERVER_IP
} else {
    Write-Host "Using manual key copy method..." -ForegroundColor Gray
    $publicKeyContent = Get-Content $publicKey -Raw
    & ssh $SERVER_USER@$SERVER_IP "mkdir -p ~/.ssh; echo '$publicKeyContent' >> ~/.ssh/authorized_keys; chmod 600 ~/.ssh/authorized_keys; chmod 700 ~/.ssh"
}

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ Public key copied to server!" -ForegroundColor Green
} else {
    Write-Host "❌ Failed to copy public key to server" -ForegroundColor Red
    Write-Host "💡 You may need to manually copy the key. See the setup guide." -ForegroundColor Yellow
}

# Step 3: Test SSH connection
Write-Host ""
Write-Host "🧪 Step 3: Testing SSH connection..." -ForegroundColor Yellow

Write-Host "ssh -i $privateKey -o StrictHostKeyChecking=no $SERVER_USER@$SERVER_IP 'echo SSH connection successful!'" -ForegroundColor Gray
& ssh -i $privateKey -o StrictHostKeyChecking=no $SERVER_USER@$SERVER_IP "echo 'SSH connection successful!'"

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ SSH connection test successful!" -ForegroundColor Green
} else {
    Write-Host "❌ SSH connection test failed" -ForegroundColor Red
    Write-Host "💡 Please check your server access and try manually" -ForegroundColor Yellow
}

# Step 4: Display secrets for GitHub
Write-Host ""
Write-Host "🔐 Step 4: GitHub Secrets Configuration" -ForegroundColor Yellow
Write-Host "Copy these values to your GitHub repository secrets:" -ForegroundColor Gray
Write-Host ""

Write-Host "=========================================" -ForegroundColor Cyan
Write-Host "GITHUB SECRETS TO ADD:" -ForegroundColor Cyan
Write-Host "=========================================" -ForegroundColor Cyan
Write-Host ""

Write-Host "SECRET NAME: SERVER_IP" -ForegroundColor Yellow
Write-Host "VALUE: $SERVER_IP" -ForegroundColor White
Write-Host ""

Write-Host "SECRET NAME: SSH_PRIVATE_KEY" -ForegroundColor Yellow
Write-Host "VALUE: (Copy the content below)" -ForegroundColor White
Write-Host "--- START OF PRIVATE KEY ---" -ForegroundColor Red
Get-Content $privateKey
Write-Host "--- END OF PRIVATE KEY ---" -ForegroundColor Red
Write-Host ""

Write-Host "ENVIRONMENT SECRETS (You need to add these with your actual values):" -ForegroundColor Yellow
Write-Host ""
$envSecrets = @(
    "VITE_SUPABASE_URL_CRM",
    "VITE_SUPABASE_ANON_KEY_CRM",
    "VITE_SUPABASE_URL_QUOTE", 
    "VITE_SUPABASE_ANON_KEY_QUOTE",
    "VITE_SUPABASE_URL",
    "VITE_SUPABASE_ANON_KEY"
)

foreach ($secret in $envSecrets) {
    Write-Host "SECRET NAME: $secret" -ForegroundColor Yellow
    Write-Host "VALUE: [Your actual $secret value]" -ForegroundColor White
    Write-Host ""
}

Write-Host "=========================================" -ForegroundColor Cyan
Write-Host ""

# Step 5: Instructions
Write-Host "📝 Next Steps:" -ForegroundColor Green
Write-Host ""
Write-Host "1. Go to your GitHub repository: https://github.com/your-username/TripXplo-CRM" -ForegroundColor White
Write-Host "2. Click Settings → Secrets and variables → Actions" -ForegroundColor White
Write-Host "3. Add each secret shown above" -ForegroundColor White
Write-Host "4. Push code to master branch to trigger automatic deployment" -ForegroundColor White
Write-Host ""

Write-Host "🚀 Test your setup:" -ForegroundColor Green
Write-Host "git add ." -ForegroundColor Gray
Write-Host "git commit -m 'Test auto-deploy'" -ForegroundColor Gray
Write-Host "git push origin master" -ForegroundColor Gray
Write-Host ""

Write-Host "🌐 Your site will be live at: https://crm.tripxplo.com" -ForegroundColor Cyan
Write-Host ""

Write-Host "📖 For detailed instructions, see: GITHUB_AUTO_DEPLOY_SETUP.md" -ForegroundColor Yellow

# Step 6: Save setup info to file
$setupInfo = @"
TripXplo CRM Auto-Deploy Setup
Generated on: $(Get-Date)

Server IP: $SERVER_IP
SSH Private Key: $privateKey
SSH Public Key: $publicKey

GitHub Secrets Required:
- SERVER_IP: $SERVER_IP
- SSH_PRIVATE_KEY: [Content of $privateKey]
- VITE_SUPABASE_URL_CRM: [Your CRM database URL]
- VITE_SUPABASE_ANON_KEY_CRM: [Your CRM anon key]
- VITE_SUPABASE_URL_QUOTE: [Your quote database URL]
- VITE_SUPABASE_ANON_KEY_QUOTE: [Your quote anon key]
- VITE_SUPABASE_URL: [Your main Supabase URL]
- VITE_SUPABASE_ANON_KEY: [Your main anon key]

Deployment URL: https://crm.tripxplo.com
"@

$setupInfo | Out-File -FilePath "auto-deploy-setup-info.txt" -Encoding UTF8

Write-Host ""
Write-Host "📄 Setup information saved to: auto-deploy-setup-info.txt" -ForegroundColor Green
Write-Host ""
Write-Host "🎉 Setup complete! Follow the next steps above to finish configuration." -ForegroundColor Green 