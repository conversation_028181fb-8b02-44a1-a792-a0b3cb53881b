declare module 'jspdf-autotable' {
  import { jsPDF } from 'jspdf';

  interface AutoTableSettings {
    head?: any[][];
    body?: any[][];
    startY?: number;
    margin?: { left?: number; right?: number };
    theme?: 'striped' | 'grid' | 'plain';
    styles?: {
      fontSize?: number;
      cellPadding?: number;
      font?: string;
      textColor?: number[];
      fillColor?: number[];
    };
    headStyles?: {
      fillColor?: number[];
      textColor?: number;
    };
    columnStyles?: {
      [key: number]: {
        cellWidth?: number;
      };
    };
  }

  interface AutoTableOutput {
    finalY: number;
    previous: AutoTableOutput;
  }

  interface JsPDFWithAutoTable extends jsPDF {
    autoTable: (options: AutoTableSettings) => AutoTableOutput;
  }

  function autoTable(options: AutoTableSettings): void;
}
