/**
 * Family EMI API Server
 * Backend API for family.tripxplo.com
 */

const express = require('express');
const cors = require('cors');
const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(cors({
  origin: process.env.CORS_ORIGIN || 'http://localhost:8080',
  credentials: true
}));
app.use(express.json());
app.use(express.static('public'));

// Database connections
const crmDB = createClient(
  process.env.CRM_DB_URL || 'https://tlfwcnikdlwoliqzavxj.supabase.co',
  process.env.CRM_ANON_KEY
);

const quoteDB = createClient(
  process.env.QUOTE_DB_URL || 'https://lkqbrlrmrsnbtkoryazq.supabase.co',
  process.env.QUOTE_ANON_KEY
);

// Utility Functions
const detectFamilyType = async (adults, children, infants) => {
  try {
    const { data: familyTypes, error } = await crmDB
      .from('family_type')
      .select('*');
    
    if (error) throw error;
    
    // Find exact match first
    let match = familyTypes.find(ft => 
      ft.no_of_adults === adults && 
      ft.no_of_children === children && 
      ft.no_of_infants === infants
    );
    
    // If no exact match, find closest match
    if (!match) {
      match = familyTypes.find(ft => 
        ft.no_of_adults === adults && 
        ft.no_of_children >= children && 
        ft.no_of_infants >= infants
      );
    }
    
    // Default to Stellar Duo if no match
    if (!match) {
      match = familyTypes.find(ft => ft.family_id === 'SD') || familyTypes[0];
    }
    
    return match;
  } catch (error) {
    console.error('Error detecting family type:', error);
    // Return default family type
    return {
      family_id: 'SD',
      family_type: 'Stellar Duo',
      no_of_adults: 2,
      no_of_children: 0,
      no_of_infants: 0,
      composition: '2 Adults'
    };
  }
};

const formatPackageForFrontend = (packageData) => {
  return {
    id: packageData.id,
    title: packageData.package_title || `${packageData.destination} Package`,
    destination: packageData.destination,
    duration_days: packageData.package_duration_days || 5,
    total_price: packageData.total_price,
    family_type: packageData.family_type_name,
    emi_options: packageData.family_type_emi_plans?.map(emi => ({
      id: emi.id,
      months: emi.emi_months,
      monthly_amount: emi.monthly_amount,
      total_amount: emi.total_amount,
      processing_fee: emi.processing_fee,
      label: emi.marketing_label || `${emi.emi_months} Months`,
      is_featured: emi.is_featured
    })) || [],
    inclusions: ['Flights', 'Hotels', 'Meals', 'Sightseeing'],
    images: [`/img/rectangle-14.png`], // Default image
    offer_badge: packageData.total_price > 40000 ? '15% OFF' : 'Best Value'
  };
};

// Enhanced helper function to format package details for frontend
async function formatPackageDetailsForFrontend(packageData) {
  // First, try to get additional data from quotes table if we have quote_id
  let enrichedPackageData = { ...packageData };

  if (packageData.quote_id && !packageData.destination) {
    try {
      const { data: quoteData, error: quoteError } = await quoteDB
        .from('quotes')
        .select('destination, package_name, total_cost, customer_name, family_type, no_of_persons, children, infants, extra_adults, trip_duration')
        .eq('id', packageData.quote_id)
        .single();

      if (!quoteError && quoteData) {
        enrichedPackageData = {
          ...packageData,
          destination: quoteData.destination,
          package_name: quoteData.package_name,
          quote_total_cost: quoteData.total_cost,
          quote_customer_name: quoteData.customer_name,
          quote_family_type: quoteData.family_type,
          quote_duration: quoteData.trip_duration
        };
        console.log('✅ Enriched package data with quote information');
      }
    } catch (error) {
      console.warn('⚠️ Could not enrich package data with quote information:', error);
    }
  }

  const basePackage = formatPackageForFrontend(enrichedPackageData);

  // Extract detailed information from Quote Generator database
  const hotelInfo = extractHotelDetails(enrichedPackageData);
  const mealPlanInfo = extractMealPlanDetails(enrichedPackageData);
  const detailedInclusions = extractDetailedInclusions(enrichedPackageData);
  const detailedExclusions = extractDetailedExclusions(enrichedPackageData);
  const packageDescription = generatePackageDescription(enrichedPackageData);
  const itinerary = generateDetailedItinerary(enrichedPackageData);

  return {
    ...basePackage,
    // Enhanced package information
    package_name: enrichedPackageData.package_name || enrichedPackageData.quote_name || basePackage.title,
    hotel_name: hotelInfo.name,
    hotel_category: hotelInfo.category,
    nights: enrichedPackageData.package_duration_days || enrichedPackageData.quote_duration || enrichedPackageData.duration_days || 5,
    meal_plan: mealPlanInfo.plan,
    meal_details: mealPlanInfo.details,

    // Use destination from enriched data
    destination: enrichedPackageData.destination || basePackage.destination,

    // Detailed descriptions
    description: packageDescription,
    itinerary: itinerary,

    // Comprehensive inclusions and exclusions
    inclusions: detailedInclusions,
    exclusions: detailedExclusions,

    // Additional package details
    ferry_included: isFerryIncluded(enrichedPackageData),
    guide_included: isGuideIncluded(enrichedPackageData),
    activities_included: getIncludedActivities(enrichedPackageData),

    // Pricing breakdown
    cost_breakdown: generateCostBreakdown(enrichedPackageData),

    // Package metadata
    data_source: enrichedPackageData.data_source || 'quote_generator',
    last_updated: enrichedPackageData.updated_at || enrichedPackageData.created_at,
    validity: getPackageValidity(enrichedPackageData)
  };
}

// Helper functions for package details extraction
function extractHotelDetails(packageData) {
  let hotelName = 'Hotel Included';
  let hotelCategory = '3-4 Star';

  // Try to get from quote_mappings hotel_mappings
  if (packageData.quote_mapping_data && packageData.quote_mapping_data.hotel_mappings) {
    const hotelMappings = packageData.quote_mapping_data.hotel_mappings;
    if (hotelMappings.length > 0) {
      hotelName = hotelMappings[0].hotel_name || hotelName;
    }
  }

  // Try to get from baseline_quote_data
  if (packageData.baseline_quote_data && packageData.baseline_quote_data.hotel_name) {
    hotelName = packageData.baseline_quote_data.hotel_name;
  }

  // Try to get from direct fields
  if (packageData.hotel_name) {
    hotelName = packageData.hotel_name;
  }

  // Determine hotel category based on price
  const totalPrice = packageData.total_price || 0;
  if (totalPrice > 80000) {
    hotelCategory = '4-5 Star Luxury';
  } else if (totalPrice > 50000) {
    hotelCategory = '3-4 Star Premium';
  } else if (totalPrice > 30000) {
    hotelCategory = '3 Star Standard';
  } else {
    hotelCategory = '2-3 Star Budget';
  }

  return {
    name: hotelName,
    category: hotelCategory
  };
}

function extractMealPlanDetails(packageData) {
  let mealPlan = 'Breakfast Included';
  let mealDetails = [];

  // Check additional costs for meal information
  if (packageData.additional_costs) {
    const mealCost = packageData.additional_costs.meal_cost_per_person || 0;
    if (mealCost > 0) {
      if (mealCost > 1500) {
        mealPlan = 'All Meals Included (MAP)';
        mealDetails = ['Daily Breakfast', 'Daily Lunch', 'Daily Dinner'];
      } else if (mealCost > 800) {
        mealPlan = 'Breakfast & Dinner (CP)';
        mealDetails = ['Daily Breakfast', 'Daily Dinner'];
      } else {
        mealPlan = 'Breakfast Included (EP)';
        mealDetails = ['Daily Breakfast'];
      }
    }
  }

  // Check quote mapping data
  if (packageData.quote_mapping_data && packageData.quote_mapping_data.additional_costs) {
    const costs = packageData.quote_mapping_data.additional_costs;
    const mealCost = costs.meal_cost_per_person || 0;
    if (mealCost > 0) {
      if (mealCost > 1500) {
        mealPlan = 'All Meals Included (MAP)';
        mealDetails = ['Daily Breakfast', 'Daily Lunch', 'Daily Dinner'];
      } else if (mealCost > 800) {
        mealPlan = 'Breakfast & Dinner (CP)';
        mealDetails = ['Daily Breakfast', 'Daily Dinner'];
      }
    }
  }

  return {
    plan: mealPlan,
    details: mealDetails
  };
}

function extractDetailedInclusions(packageData) {
  const inclusions = [];

  // Standard inclusions
  inclusions.push('Accommodation as per itinerary');

  // Transportation
  if (packageData.vehicle_cost > 0 || (packageData.quote_mapping_data && packageData.quote_mapping_data.vehicle_mappings)) {
    inclusions.push('Private vehicle for all transfers and sightseeing');
  } else {
    inclusions.push('Airport transfers');
  }

  // Meals based on meal plan
  const mealInfo = extractMealPlanDetails(packageData);
  if (mealInfo.details.length > 0) {
    inclusions.push(...mealInfo.details);
  } else {
    inclusions.push('Daily breakfast');
  }

  // Ferry if included
  if (isFerryIncluded(packageData)) {
    inclusions.push('Ferry tickets (adults and children)');
  }

  // Guide if included
  if (isGuideIncluded(packageData)) {
    inclusions.push('Professional tour guide');
  }

  // Activities
  const activities = getIncludedActivities(packageData);
  if (activities.length > 0) {
    inclusions.push(...activities);
  } else {
    inclusions.push('All sightseeing as per itinerary');
  }

  // Additional inclusions based on price
  const totalPrice = packageData.total_price || 0;
  if (totalPrice > 50000) {
    inclusions.push('Welcome drink on arrival');
  }
  if (totalPrice > 80000) {
    inclusions.push('Complimentary Wi-Fi');
    inclusions.push('24/7 customer support');
  }

  // Standard inclusions
  inclusions.push('All applicable taxes');

  return inclusions;
}

function extractDetailedExclusions(packageData) {
  const exclusions = [
    'Airfare (can be arranged separately)',
    'Personal expenses like laundry, telephone calls, tips, etc.',
    'Travel insurance',
    'Any meals not mentioned in inclusions',
    'Entry fees to monuments and parks',
    'Camera fees at monuments',
    'Medical expenses',
    'Any expenses arising due to unforeseen circumstances',
    'Anything not mentioned in inclusions'
  ];

  // Add specific exclusions based on package type
  if (!isFerryIncluded(packageData)) {
    exclusions.unshift('Ferry tickets');
  }

  if (!isGuideIncluded(packageData)) {
    exclusions.unshift('Tour guide charges');
  }

  return exclusions;
}

function isFerryIncluded(packageData) {
  if (packageData.additional_costs && packageData.additional_costs.ferry_cost > 0) {
    return true;
  }
  if (packageData.quote_mapping_data &&
      packageData.quote_mapping_data.additional_costs &&
      packageData.quote_mapping_data.additional_costs.ferry_cost > 0) {
    return true;
  }
  return false;
}

function isGuideIncluded(packageData) {
  if (packageData.additional_costs && packageData.additional_costs.guide_cost_per_day > 0) {
    return true;
  }
  if (packageData.quote_mapping_data &&
      packageData.quote_mapping_data.additional_costs &&
      packageData.quote_mapping_data.additional_costs.guide_cost_per_day > 0) {
    return true;
  }
  return packageData.total_price > 60000; // Include guide for premium packages
}

function getIncludedActivities(packageData) {
  const activities = [];

  if (packageData.additional_costs && packageData.additional_costs.activity_cost_per_person > 0) {
    // Determine activities based on destination
    const destination = (packageData.destination || '').toLowerCase();

    if (destination.includes('kashmir')) {
      activities.push('Shikara ride in Dal Lake', 'Gondola ride in Gulmarg');
    } else if (destination.includes('goa')) {
      activities.push('Water sports activities', 'Beach activities');
    } else if (destination.includes('manali')) {
      activities.push('Adventure activities', 'Local sightseeing');
    } else if (destination.includes('andaman')) {
      activities.push('Snorkeling', 'Island hopping');
    } else {
      activities.push('Local activities and experiences');
    }
  }

  return activities;
}

function generatePackageDescription(packageData) {
  const destination = packageData.destination || 'your destination';
  const nights = packageData.package_duration_days || packageData.duration_days || 5;
  const familyType = packageData.family_type_name || 'families';

  let description = `Experience the beauty and charm of ${destination} with our specially crafted ${nights}-night family package. `;
  description += `Perfect for ${familyType.toLowerCase()}, this package offers a perfect blend of comfort, adventure, and relaxation. `;

  // Add destination-specific description
  const dest = destination.toLowerCase();
  if (dest.includes('kashmir')) {
    description += 'Explore the paradise on earth with its pristine lakes, snow-capped mountains, and beautiful gardens. ';
  } else if (dest.includes('goa')) {
    description += 'Enjoy the sun, sand, and sea with beautiful beaches, vibrant nightlife, and Portuguese heritage. ';
  } else if (dest.includes('manali')) {
    description += 'Discover the hill station beauty with adventure activities, scenic landscapes, and pleasant weather. ';
  } else if (dest.includes('kerala')) {
    description += 'Experience God\'s own country with backwaters, spice plantations, and cultural heritage. ';
  }

  description += 'All arrangements are made to ensure a memorable and hassle-free vacation for your family.';

  return description;
}

function generateDetailedItinerary(packageData) {
  const destination = packageData.destination || 'Destination';
  const nights = packageData.package_duration_days || packageData.duration_days || 5;
  const itinerary = [];

  // Day 1 - Arrival
  itinerary.push({
    day: 1,
    title: `Arrival in ${destination}`,
    description: `Arrive at ${destination} airport/railway station. Meet and greet by our representative. Transfer to hotel and check-in. Rest of the day at leisure. Overnight stay at hotel.`
  });

  // Middle days - Sightseeing
  for (let day = 2; day <= nights; day++) {
    if (day === nights) break; // Skip last day for departure

    let dayTitle = '';
    let dayDescription = '';

    // Destination-specific itinerary
    const dest = destination.toLowerCase();
    if (dest.includes('kashmir')) {
      if (day === 2) {
        dayTitle = 'Srinagar Local Sightseeing';
        dayDescription = 'After breakfast, visit Mughal Gardens - Nishat Bagh, Shalimar Bagh, and Chashme Shahi. Enjoy Shikara ride in Dal Lake. Visit local markets. Overnight stay at hotel.';
      } else if (day === 3) {
        dayTitle = 'Srinagar to Gulmarg';
        dayDescription = 'After breakfast, drive to Gulmarg (2 hours). Enjoy Gondola ride (Phase 1 & 2). Experience snow activities. Return to Srinagar. Overnight stay at hotel.';
      } else {
        dayTitle = 'Pahalgam Excursion';
        dayDescription = 'After breakfast, full day excursion to Pahalgam. Visit Betaab Valley, Aru Valley, and Chandanwari. Return to Srinagar. Overnight stay at hotel.';
      }
    } else if (dest.includes('goa')) {
      if (day === 2) {
        dayTitle = 'North Goa Sightseeing';
        dayDescription = 'After breakfast, visit North Goa beaches - Calangute, Baga, Anjuna. Visit Fort Aguada. Enjoy water sports. Overnight stay at hotel.';
      } else if (day === 3) {
        dayTitle = 'South Goa Sightseeing';
        dayDescription = 'After breakfast, visit South Goa beaches - Colva, Benaulim. Visit Old Goa churches. Spice plantation tour. Overnight stay at hotel.';
      } else {
        dayTitle = 'Leisure Day';
        dayDescription = 'Day at leisure. Enjoy beach activities, shopping, or optional tours. Overnight stay at hotel.';
      }
    } else {
      dayTitle = `${destination} Sightseeing - Day ${day - 1}`;
      dayDescription = `After breakfast, proceed for sightseeing tour of ${destination}. Visit major attractions and local markets. Return to hotel. Overnight stay at hotel.`;
    }

    itinerary.push({
      day: day,
      title: dayTitle,
      description: dayDescription
    });
  }

  // Last day - Departure
  itinerary.push({
    day: nights + 1,
    title: 'Departure',
    description: `After breakfast, check-out from hotel. Transfer to airport/railway station for onward journey. Tour ends with sweet memories.`
  });

  return itinerary;
}

function generateCostBreakdown(packageData) {
  const breakdown = [];

  if (packageData.hotel_cost > 0) {
    breakdown.push({
      item: 'Accommodation',
      cost: packageData.hotel_cost,
      description: `${packageData.package_duration_days || 5} nights hotel stay`
    });
  }

  if (packageData.vehicle_cost > 0) {
    breakdown.push({
      item: 'Transportation',
      cost: packageData.vehicle_cost,
      description: 'Private vehicle for transfers and sightseeing'
    });
  }

  if (packageData.additional_costs) {
    const costs = packageData.additional_costs;

    if (costs.meal_cost_per_person > 0) {
      const familyCount = packageData.family_count || 4;
      breakdown.push({
        item: 'Meals',
        cost: costs.meal_cost_per_person * familyCount,
        description: `Meals for ${familyCount} persons`
      });
    }

    if (costs.ferry_cost > 0) {
      const ferryPersons = (packageData.no_of_adults || 2) + (packageData.no_of_children || 0) + (packageData.no_of_child || 0);
      breakdown.push({
        item: 'Ferry',
        cost: costs.ferry_cost * ferryPersons,
        description: `Ferry tickets for ${ferryPersons} persons (excluding infants)`
      });
    }

    if (costs.activity_cost_per_person > 0) {
      const familyCount = packageData.family_count || 4;
      breakdown.push({
        item: 'Activities',
        cost: costs.activity_cost_per_person * familyCount,
        description: `Activities for ${familyCount} persons`
      });
    }

    if (costs.guide_cost_per_day > 0) {
      const days = packageData.package_duration_days || 5;
      breakdown.push({
        item: 'Guide',
        cost: costs.guide_cost_per_day * days,
        description: `Professional guide for ${days} days`
      });
    }
  }

  // Add total
  const total = breakdown.reduce((sum, item) => sum + item.cost, 0);
  if (total > 0 && total !== packageData.total_price) {
    breakdown.push({
      item: 'Other charges',
      cost: (packageData.total_price || 0) - total,
      description: 'Taxes, service charges, and other fees'
    });
  }

  return breakdown;
}

function getPackageValidity(packageData) {
  if (packageData.travel_date) {
    return `Valid for travel: ${new Date(packageData.travel_date).toLocaleDateString()}`;
  }
  if (packageData.created_at) {
    const validUntil = new Date(packageData.created_at);
    validUntil.setMonth(validUntil.getMonth() + 6); // Valid for 6 months
    return `Valid until: ${validUntil.toLocaleDateString()}`;
  }
  return 'Valid for booking';
}

// API Routes

// Health check
app.get('/api/health', (req, res) => {
  res.json({ 
    success: true, 
    message: 'Family EMI API is running',
    timestamp: new Date().toISOString()
  });
});

// Get all family types
app.get('/api/family-types', async (req, res) => {
  try {
    const { data, error } = await crmDB
      .from('family_type')
      .select('*')
      .order('family_type');
    
    if (error) throw error;
    
    const formattedData = data.map(ft => ({
      ...ft,
      composition: `${ft.no_of_adults} Adult${ft.no_of_adults > 1 ? 's' : ''}${
        ft.no_of_children > 0 ? ` + ${ft.no_of_children} Child${ft.no_of_children > 1 ? 'ren' : ''}` : ''
      }${
        ft.no_of_infants > 0 ? ` + ${ft.no_of_infants} Infant${ft.no_of_infants > 1 ? 's' : ''}` : ''
      }`
    }));
    
    res.json({ success: true, data: formattedData });
  } catch (error) {
    console.error('Error fetching family types:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

// Get available destinations
app.get('/api/destinations', async (req, res) => {
  try {
    const { data, error } = await quoteDB
      .from('family_type_prices')
      .select('destination_category')
      .eq('is_public_visible', true)
      .not('destination_category', 'is', null);
    
    if (error) throw error;
    
    // Get unique destinations with counts
    const destinationMap = new Map();
    data.forEach(item => {
      if (item.destination) {
        const dest = item.destination;
        if (!destinationMap.has(dest)) {
          destinationMap.set(dest, {
            destination: dest,
            category: item.destination_category || 'General',
            packages_available: 0
          });
        }
        destinationMap.get(dest).packages_available++;
      }
    });
    
    const destinations = Array.from(destinationMap.values())
      .sort((a, b) => a.destination.localeCompare(b.destination));
    
    res.json({ success: true, data: destinations });
  } catch (error) {
    console.error('Error fetching destinations:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

// Search packages
app.post('/api/search-packages', async (req, res) => {
  try {
    const { destination, travel_date, adults, children, infants } = req.body;
    
    // Validate input
    if (!destination || !adults) {
      return res.status(400).json({ 
        success: false, 
        error: 'Destination and number of adults are required' 
      });
    }
    
    // Detect family type
    const familyType = await detectFamilyType(adults, children || 0, infants || 0);
    
    // Search packages
    const { data: packages, error } = await quoteDB
      .from('family_type_prices')
      .select(`
        *,
        family_type_emi_plans(*)
      `)
      .ilike('destination', `%${destination}%`)
      .eq('is_public_visible', true)
      .limit(10);
    
    if (error) throw error;
    
    // Format packages for frontend
    const formattedPackages = packages.map(formatPackageForFrontend);
    
    res.json({ 
      success: true, 
      matched_family_type: {
        ...familyType,
        composition: `${familyType.no_of_adults} Adult${familyType.no_of_adults > 1 ? 's' : ''}${
          familyType.no_of_children > 0 ? ` + ${familyType.no_of_children} Child${familyType.no_of_children > 1 ? 'ren' : ''}` : ''
        }${
          familyType.no_of_infants > 0 ? ` + ${familyType.no_of_infants} Infant${familyType.no_of_infants > 1 ? 's' : ''}` : ''
        }`
      },
      packages: formattedPackages,
      search_params: { destination, travel_date, adults, children, infants }
    });
  } catch (error) {
    console.error('Error searching packages:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

// Get package details with enhanced information
app.get('/api/packages/:id', async (req, res) => {
  try {
    const { id } = req.params;

    // First try to get from family_type_prices table
    let packageData = null;
    let dataSource = 'unknown';

    try {
      const { data: priceData, error: priceError } = await quoteDB
        .from('family_type_prices')
        .select(`
          *,
          family_type_emi_plans(*)
        `)
        .eq('id', id)
        .single();

      if (!priceError && priceData) {
        packageData = priceData;
        dataSource = 'family_type_prices';
      }
    } catch (priceError) {
      console.log('Package not found in family_type_prices, trying quote_mappings...');
    }

    // If not found, try quote_mappings table
    if (!packageData) {
      try {
        const { data: mappingData, error: mappingError } = await quoteDB
          .from('quote_mappings')
          .select('*')
          .eq('id', id)
          .single();

        if (!mappingError && mappingData) {
          packageData = mappingData;
          dataSource = 'quote_mappings';
        }
      } catch (mappingError) {
        console.log('Package not found in quote_mappings, trying quotes...');
      }
    }

    // If still not found, try quotes table
    if (!packageData) {
      try {
        const { data: quoteData, error: quoteError } = await quoteDB
          .from('quotes')
          .select('*')
          .eq('id', id)
          .single();

        if (!quoteError && quoteData) {
          packageData = quoteData;
          dataSource = 'quotes';
        }
      } catch (quoteError) {
        console.error('Package not found in any table');
      }
    }

    if (!packageData) {
      return res.status(404).json({ success: false, error: 'Package not found' });
    }

    // Add data source info
    packageData.data_source = dataSource;

    // Format package with enhanced details using the database service logic
    const formattedPackage = await formatPackageDetailsForFrontend(packageData);

    res.json({
      success: true,
      package: formattedPackage
    });
  } catch (error) {
    console.error('Error fetching package details:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

// Submit quote request
app.post('/api/quote-request', async (req, res) => {
  try {
    const {
      customer_email,
      customer_phone,
      customer_name,
      destination,
      travel_date,
      adults,
      children,
      infants,
      selected_package_id,
      selected_emi_plan_id,
      utm_source,
      session_id
    } = req.body;
    
    // Validate required fields
    if (!customer_email || !destination || !adults) {
      return res.status(400).json({ 
        success: false, 
        error: 'Email, destination, and number of adults are required' 
      });
    }
    
    // Insert into public_family_quotes table
    const { data, error } = await quoteDB
      .from('public_family_quotes')
      .insert({
        customer_email,
        customer_phone,
        customer_name,
        destination,
        travel_date,
        no_of_adults: adults,
        no_of_children: children || 0,
        no_of_infants: infants || 0,
        matched_price_id: selected_package_id,
        selected_emi_plan_id,
        utm_source: utm_source || 'direct',
        session_id: session_id || `sess_${Date.now()}`,
        lead_source: 'family_website'
      })
      .select()
      .single();
    
    if (error) throw error;
    
    res.json({ 
      success: true, 
      quote_id: data.id,
      message: 'Quote request submitted successfully. Our team will contact you soon!'
    });
  } catch (error) {
    console.error('Error submitting quote request:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error('Unhandled error:', err);
  res.status(500).json({ 
    success: false, 
    error: 'Internal server error' 
  });
});

// 404 handler
app.use((req, res) => {
  res.status(404).json({ 
    success: false, 
    error: 'Endpoint not found' 
  });
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 Family EMI API server running on port ${PORT}`);
  console.log(`📊 CRM Database: ${process.env.CRM_DB_URL?.substring(0, 30)}...`);
  console.log(`💰 Quote Database: ${process.env.QUOTE_DB_URL?.substring(0, 30)}...`);
});

module.exports = app;
