# 🔐 Add Missing GitHub Secrets

Your auto-deployment is failing because some required secrets are missing. Here's what you need to add:

## 📋 Required Secrets to Add

Go to your GitHub repository → **Settings** → **Secrets and variables** → **Actions** and add these missing secrets:

### 1. EMAIL_USER
```
Name: EMAIL_USER
Value: <EMAIL>
```
*This is the email address used for sending quotes from the application*

### 2. EMAIL_PASS
```
Name: EMAIL_PASS
Value: [Your email password or app-specific password]
```
*If using Gmail, create an App Password instead of your regular password*

## 🔍 How to Create Gmail App Password

1. Go to your Google Account settings
2. Security → 2-Step Verification (must be enabled)
3. App passwords → Select app "Mail" → Select device "Other" 
4. Enter "TripXplo CRM" as the name
5. Copy the 16-character password and use it as EMAIL_PASS

## ✅ Verify Your Secrets

After adding the missing secrets, ensure you have all of these in your GitHub repository secrets:

- ✅ `SERVER_IP` = *************
- ✅ `SSH_PRIVATE_KEY` = [Your SSH private key]
- ✅ `VITE_SUPABASE_URL_CRM` = https://tlfwcnikdlwoliqzavxj.supabase.co
- ✅ `VITE_SUPABASE_ANON_KEY_CRM` = [Your CRM anon key]
- ✅ `VITE_SUPABASE_URL_QUOTE` = https://lkqbrlrmrsnbtkoryazq.supabase.co
- ✅ `VITE_SUPABASE_ANON_KEY_QUOTE` = [Your Quote anon key]
- ✅ `VITE_SUPABASE_URL` = https://tlfwcnikdlwoliqzavxj.supabase.co
- ✅ `VITE_SUPABASE_ANON_KEY` = [Your main anon key]
- 🆕 `EMAIL_USER` = <EMAIL>
- 🆕 `EMAIL_PASS` = [Your Gmail app password]

## 🚀 Test the Deployment

Once you've added the missing secrets:

1. Make a small change to any file
2. Commit and push:
   ```bash
   git add .
   git commit -m "Test auto-deployment with fixed secrets"
   git push origin master
   ```
3. Go to GitHub → Actions tab to watch the deployment
4. Your site will be deployed to https://crm.tripxplo.com

## 🔧 What's Been Fixed

- ✅ Improved GitHub Actions workflow with better error handling
- ✅ Separated deployment script to avoid YAML syntax issues  
- ✅ Added missing environment variables for email functionality
- ✅ Enhanced debugging and validation steps
- ✅ Automatic SSL certificate setup
- ✅ Better backup and rollback capability

Your auto-deployment should now work perfectly! 🎉 