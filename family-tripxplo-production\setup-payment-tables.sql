-- TripXplo CRM Payment Tables Setup (Simplified)
-- Run this script in your Supabase CRM database

-- Step 1: Create prepaid_emi_transactions table (without <PERSON><PERSON> first)
CREATE TABLE IF NOT EXISTS prepaid_emi_transactions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  next_emi_due_date DATE,
  total_paid_amount DECIMAL(10,2),
  pending_amount DECIMAL(10,2),
  remaining_emi_months INTEGER,
  monthly_emi_amount DECIMAL(10,2),
  total_emi_amount DECIMAL(10,2),
  advance_payment_date DATE,
  advance_payment_amount DECIMAL(10,2),
  advance_payment_status VARCHAR(50),
  booking_reference VARCHAR(100) UNIQUE,
  customer_id UUID,
  emi_plan_id UUID,
  payment_status VARCHAR(50),
  auto_debit_enabled BOOLEAN DEFAULT false,
  payment_method VARCHAR(50),
  reminder_sent_count INTEGER DEFAULT 0,
  last_reminder_sent TIMESTAMP,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Step 2: Create emi_payment_history table (without foreign key constraint first)
CREATE TABLE IF NOT EXISTS emi_payment_history (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  transaction_id UUID,
  payment_amount DECIMAL(10,2),
  payment_date DATE,
  payment_method VARCHAR(50),
  payment_reference VARCHAR(100),
  emi_month_number INTEGER,
  payment_type VARCHAR(50),
  payment_status VARCHAR(50),
  gateway_reference VARCHAR(100),
  late_fee_amount DECIMAL(10,2) DEFAULT 0,
  discount_applied DECIMAL(10,2) DEFAULT 0,
  notes TEXT,
  created_at TIMESTAMP DEFAULT NOW()
);

-- Step 3: Create basic indexes
CREATE INDEX IF NOT EXISTS idx_prepaid_emi_booking_ref ON prepaid_emi_transactions(booking_reference);
CREATE INDEX IF NOT EXISTS idx_prepaid_emi_customer ON prepaid_emi_transactions(customer_id);
CREATE INDEX IF NOT EXISTS idx_payment_history_transaction ON emi_payment_history(transaction_id);

-- Step 4: Insert a test record to verify tables work
INSERT INTO prepaid_emi_transactions (
  booking_reference,
  customer_id,
  payment_status,
  total_paid_amount,
  pending_amount,
  monthly_emi_amount,
  total_emi_amount,
  remaining_emi_months,
  payment_method
) VALUES (
  'TEST_SETUP_' || extract(epoch from now())::text,
  gen_random_uuid(),
  'test',
  0,
  0,
  0,
  0,
  0,
  'setup_test'
) ON CONFLICT (booking_reference) DO NOTHING;

-- Step 5: Verify tables exist and are accessible
SELECT 'Tables created successfully!' as status,
       (SELECT COUNT(*) FROM prepaid_emi_transactions) as emi_transactions_count,
       (SELECT COUNT(*) FROM emi_payment_history) as payment_history_count;
