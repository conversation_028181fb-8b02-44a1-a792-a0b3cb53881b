# TripXplo Family EMI - Environment Configuration

# Server Configuration
NODE_ENV=development
PORT=3000
CORS_ORIGIN=http://localhost:8080

# Database Configuration - CRM Database
CRM_DB_URL=https://tlfwcnikdlwoliqzavxj.supabase.co
CRM_ANON_KEY=your-crm-database-anon-key-here

# Database Configuration - Quote Database  
QUOTE_DB_URL=https://lkqbrlrmrsnbtkoryazq.supabase.co
QUOTE_ANON_KEY=your-quote-database-anon-key-here

# Security
JWT_SECRET=your-jwt-secret-key-here
API_RATE_LIMIT=100

# Email Configuration (Optional)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# Analytics (Optional)
GOOGLE_ANALYTICS_ID=GA-XXXXXXXXX
FACEBOOK_PIXEL_ID=your-pixel-id

# Production Configuration (for family.tripxplo.com)
# NODE_ENV=production
# PORT=3000
# CORS_ORIGIN=https://family.tripxplo.com
# API_RATE_LIMIT=1000

# SSL Configuration (Production)
# SSL_CERT_PATH=/path/to/ssl/cert.pem
# SSL_KEY_PATH=/path/to/ssl/private.key

# Logging
LOG_LEVEL=info
LOG_FILE=logs/family-emi.log

# Cache Configuration
REDIS_URL=redis://localhost:6379
CACHE_TTL=300

# Monitoring
SENTRY_DSN=your-sentry-dsn-here
