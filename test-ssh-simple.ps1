# Test SSH Connection to Linode Server
Write-Host "Testing SSH Connection to Linode Server..." -ForegroundColor Green

$SERVER_IP = "*************"
$SERVER_USER = "root"

Write-Host "Server: $SERVER_USER@$SERVER_IP" -ForegroundColor Cyan

try {
    Write-Host "Testing connection..." -ForegroundColor Yellow
    $result = ssh "$SERVER_USER@$SERVER_IP" "echo 'SSH connection successful'"
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "SSH Connection Successful!" -ForegroundColor Green
        Write-Host "Server Response: $result" -ForegroundColor White
        Write-Host ""
        Write-Host "Ready to deploy! Run: .\deploy-with-api-windows.ps1" -ForegroundColor Green
    } else {
        Write-Host "SSH Connection Failed!" -ForegroundColor Red
    }
} catch {
    Write-Host "SSH Connection Error: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host ""
    Write-Host "Troubleshooting:" -ForegroundColor Yellow
    Write-Host "1. Install OpenSSH: winget install Microsoft.OpenSSH.Beta" -ForegroundColor White
    Write-Host "2. Check if you have SSH key access to the server" -ForegroundColor White
    Write-Host "3. Try manual connection: ssh root@*************" -ForegroundColor White
}

pause
