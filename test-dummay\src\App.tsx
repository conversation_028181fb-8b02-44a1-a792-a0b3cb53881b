import React, { useState, useEffect } from 'react';
import './index.css'; // Ensure CSS is loaded
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { AuthProvider, useAuth } from './contexts/AuthContext';
import Login from './pages/Login';
import Signup from './pages/Signup';
import ProtectedRoute from './components/ProtectedRoute';
import Dashboard from './pages/Dashboard';
import LeadEntry from './pages/LeadEntry';
import LeadsKanban from './pages/LeadsKanban';
import Quotes from './pages/Quotes';
import AppLayout from './components/AppLayout';
import PrepaidEMIManagement from './components/PrepaidEMIManagement';
import FamilyTypeManagement from './components/FamilyTypeManagement';
import PrepaidEmiCalculator from './quotes/Tabs/PrepaidEmiCalculator';
import FamilyBookingLanding from './pages/FamilyBookingLanding';

// Redirect root based on authentication state
function RootRedirect() {
  const { isAuthenticated, isLoading } = useAuth();
  const [error, setError] = useState<string | null>(null);
  const [loadingTime, setLoadingTime] = useState<number>(0);

  useEffect(() => {
    let intervalId: number;

    // Only start the timer if we're in the loading state
    if (isLoading) {
      // Create a loading timer that increments every second
      intervalId = window.setInterval(() => {
        setLoadingTime(prevTime => {
          const newTime = prevTime + 1;
          // After 15 seconds, show a friendly message
          if (newTime === 15) {
            setError('Still connecting to Supabase...');
          }
          // After 30 seconds, show a more detailed message
          if (newTime === 30) {
            setError('Connection is taking longer than usual. The database might be waking up from sleep mode.');
          }
          return newTime;
        });
      }, 1000) as unknown as number;
    } else {
      // Reset when not loading
      setLoadingTime(0);
      setError(null);
    }

    return () => {
      if (intervalId) clearInterval(intervalId);
    };
  }, [isLoading]);

  // Handler for manual retry
  const handleRetry = () => {
    setError(null);
    setLoadingTime(0);
    window.location.reload();
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 flex flex-col items-center justify-center p-4">
        <div className="text-center max-w-md w-full">
          {/* Logo */}
          <div className="mb-8">
            <span className="text-4xl font-bold">
              <span className="text-blue-600">TRIP</span>
              <span className="text-purple-600">XPLO</span>
            </span>
          </div>

          {/* Spinner */}
          <div className="mb-6">
            <div className="inline-block animate-spin rounded-full h-12 w-12 border-4 border-gray-200 border-t-blue-600"></div>
          </div>

          {/* Loading Text */}
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            {loadingTime > 15 ? 'Connecting to Travel Services' : 'Loading Your Dashboard'}
          </h2>
          <p className="text-gray-600 mb-4">
            {loadingTime > 5 ? `Please wait (${loadingTime}s)...` : 'Setting up your personalized experience...'}
          </p>

          {/* Error/Extended Loading Message */}
          {error && (
            <div className="mt-6 p-6 bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200 rounded-xl max-w-md">
              <div className="flex items-center mb-3">
                <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                  <i className="fas fa-info-circle text-blue-600"></i>
                </div>
                <h3 className="font-semibold text-blue-900">Connection Status</h3>
              </div>
              <p className="text-blue-800 text-sm mb-4">{error}</p>
              {loadingTime > 20 && (
                <button
                  onClick={handleRetry}
                  className="w-full bg-gradient-to-r from-blue-600 to-purple-600 text-white px-6 py-3 rounded-lg font-medium hover:from-blue-700 hover:to-purple-700 transition-all duration-200"
                >
                  <i className="fas fa-redo mr-2"></i>
                  Retry Connection
                </button>
              )}
            </div>
          )}
        </div>
      </div>
    );
  }

  return <Navigate to={isAuthenticated ? '/dashboard' : '/family-booking'} replace />;
}

// Fallback component for auth errors
function AuthErrorBoundary({ children }: { children: React.ReactNode }) {
  const [hasError, setHasError] = useState(false);

  useEffect(() => {
    // Handle unhandled errors related to auth
    const handler = (event: ErrorEvent) => {
      console.error('Global error caught:', event.error);
      if (event.error?.message?.includes('Supabase')) {
        setHasError(true);
      }
    };

    window.addEventListener('error', handler);
    return () => window.removeEventListener('error', handler);
  }, []);

  if (hasError) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-red-50 via-white to-red-50 flex items-center justify-center p-4">
        <div className="bg-white border border-red-200 rounded-2xl shadow-xl p-8 max-w-md w-full text-center">
          {/* Error Icon */}
          <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6">
            <i className="fas fa-exclamation-triangle text-2xl text-red-600"></i>
          </div>

          {/* Logo */}
          <div className="mb-4">
            <span className="text-2xl font-bold">
              <span className="text-blue-600">TRIP</span>
              <span className="text-purple-600">XPLO</span>
            </span>
          </div>

          <h2 className="text-xl font-bold text-red-800 mb-4">Service Connection Error</h2>
          <div className="text-gray-700 mb-6 space-y-2">
            <p>We're having trouble connecting to our travel services.</p>
            <p className="text-sm text-gray-600">
              This might be due to network connectivity or service configuration issues.
            </p>
          </div>

          <button
            className="w-full bg-gradient-to-r from-red-600 to-red-700 text-white px-6 py-3 rounded-lg font-medium hover:from-red-700 hover:to-red-800 transition-all duration-200"
            onClick={() => window.location.reload()}
          >
            <i className="fas fa-redo mr-2"></i>
            Reload Application
          </button>

          <p className="text-xs text-gray-500 mt-4">
            If the problem persists, please contact our support team.
          </p>
        </div>
      </div>
    );
  }

  return <>{children}</>;
}

function App() {
  return (
    <Router>
      <AuthErrorBoundary>
        <AuthProvider>
          <Routes>
            {/* Public Routes */}
            <Route path="/family-booking" element={<FamilyBookingLanding />} />
            <Route path="/login" element={<Login />} />
            <Route path="/signup" element={<Signup />} />
            <Route element={<ProtectedRoute />}>
              <Route
                path="/dashboard"
                element={
                  <AppLayout>
                    <Dashboard />
                  </AppLayout>
                }
              />
              <Route
                path="/leads"
                element={
                  <AppLayout>
                    <LeadsKanban />
                  </AppLayout>
                }
              />
              <Route
                path="/leads/new"
                element={
                  <AppLayout>
                    <LeadEntry />
                  </AppLayout>
                }
              />
              <Route
                path="/quotes"
                element={
                  <AppLayout>
                    <Quotes />
                  </AppLayout>
                }
              />
              <Route
                path="/quotes/new"
                element={
                  <AppLayout>
                    <Quotes />
                  </AppLayout>
                }
              />
              {/* EMI Management Routes */}
              <Route
                path="/emi/dashboard"
                element={
                  <AppLayout>
                    <PrepaidEMIManagement />
                  </AppLayout>
                }
              />
              <Route
                path="/emi/transactions"
                element={
                  <AppLayout>
                    <PrepaidEMIManagement />
                  </AppLayout>
                }
              />
              <Route
                path="/emi/calculator"
                element={
                  <AppLayout>
                    <PrepaidEmiCalculator />
                  </AppLayout>
                }
              />
              <Route
                path="/family-types"
                element={
                  <AppLayout>
                    <FamilyTypeManagement />
                  </AppLayout>
                }
              />
            </Route>
            <Route path="/" element={<RootRedirect />} />
          </Routes>
        </AuthProvider>
      </AuthErrorBoundary>
    </Router>
  );
}

export default App;
