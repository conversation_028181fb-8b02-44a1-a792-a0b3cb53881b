create table public.destination_quote (
  id uuid not null default extensions.uuid_generate_v4 (),
  package_id text null,
  package_name text null,
  customer_name text null,
  plan text null,
  destination text null,
  no_of_persons integer null default 0,
  extra_adults integer null default 0,
  children integer null default 0,
  infants integer null default 0,
  travel_date date null,
  validity_date date null,
  season text null,
  cab_type text null,
  cab_seating text null,
  quote_type text null,
  trip_duration text null,
  family_type text null,
  package_type text null,
  commission integer null default 5,
  custom_commission double precision null default 0,
  currency text null default 'INR'::text,
  discount_type text null default 'percentage'::text,
  discount_value double precision null default 0,
  discount_mode text null default 'manual'::text,
  selected_promo_code text null,
  is_draft boolean null default true,
  created_at timestamp with time zone null default now(),
  updated_at timestamp with time zone null default now(),
  user_id uuid null,
  total_cost numeric null,
  commission_rate numeric null,
  customer_phone character varying(20) null,
  customer_email character varying(255) null,
  itinerary text null,
  constraint destination_quote_pkey primary key (id),
  constraint destination_quote_user_id_fkey foreign KEY (user_id) references auth.users (id)
) TABLESPACE pg_default;

create index IF not exists idx_destination_quote_customer_phone on public.destination_quote using btree (customer_phone) TABLESPACE pg_default;

create index IF not exists idx_destination_quote_customer_email on public.destination_quote using btree (customer_email) TABLESPACE pg_default;

create index IF not exists idx_destination_quote_customer_name on public.destination_quote using btree (customer_name) TABLESPACE pg_default;

create index IF not exists idx_destination_quote_customer_lookup on public.destination_quote using btree (customer_phone, customer_name) TABLESPACE pg_default;

create index IF not exists idx_destination_quote_user_id on public.destination_quote using btree (user_id) TABLESPACE pg_default;

create trigger trigger_sync_customer_info_destination_quote BEFORE INSERT
or
update on destination_quote for EACH row
execute FUNCTION sync_customer_info ();
