<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Hotel Data Fetching - TripXplo Family EMI</title>
    <style>
        body {
            font-family: 'Inter', sans-serif;
            margin: 0;
            padding: 20px;
            background: #f8f9fa;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }
        .test-header {
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            background: #f8f9ff;
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }
        .hotel-card {
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            background: white;
        }
        .hotel-name {
            font-weight: 600;
            color: #333;
            font-size: 1.1rem;
        }
        .hotel-details {
            color: #666;
            margin-top: 5px;
        }
        .test-btn {
            padding: 12px 24px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            margin: 10px;
        }
        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }
        .error {
            background: #ffe6e6;
            color: #d63384;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        .success {
            background: #e8f5e8;
            color: #28a745;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        pre {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            overflow-x: auto;
            font-size: 0.9rem;
            border: 1px solid #e9ecef;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1>🏨 Hotel Data Fetching Test</h1>
            <p>Testing hotel data retrieval from hotel_rows table</p>
        </div>

        <div style="text-align: center; margin-bottom: 30px;">
            <button class="test-btn" onclick="testDirectHotelQuery()">🔍 Test Direct Hotel Query</button>
            <button class="test-btn" onclick="testQuoteWithHotels()">📋 Test Quote with Hotels</button>
            <button class="test-btn" onclick="testPackageEnhancement()">🎁 Test Package Enhancement</button>
        </div>

        <div id="testResults">
            <div class="loading">Click a button above to test hotel data fetching</div>
        </div>
    </div>

    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
    <script src="js/config.js"></script>
    <script src="js/databaseService.js"></script>
    <script>
        async function testDirectHotelQuery() {
            const container = document.getElementById('testResults');
            container.innerHTML = '<div class="loading">🔍 Testing direct hotel query...</div>';

            try {
                console.log('🧪 Testing direct hotel_rows query...');
                
                // Test direct query to hotel_rows table
                const { data: hotelRows, error } = await databaseService.quoteDB
                    .from('hotel_rows')
                    .select('quote_id, hotel_name, stay_nights, meal_plan, room_type, price')
                    .limit(10);

                if (error) throw error;

                let html = '<div class="test-section">';
                html += '<h3>🏨 Direct Hotel Rows Query Results</h3>';
                
                if (hotelRows && hotelRows.length > 0) {
                    html += `<div class="success">✅ Found ${hotelRows.length} hotel records</div>`;
                    
                    // Group by quote_id
                    const groupedByQuote = {};
                    hotelRows.forEach(hotel => {
                        if (!groupedByQuote[hotel.quote_id]) {
                            groupedByQuote[hotel.quote_id] = [];
                        }
                        groupedByQuote[hotel.quote_id].push(hotel);
                    });

                    html += '<h4>Hotels grouped by Quote ID:</h4>';
                    Object.keys(groupedByQuote).forEach(quoteId => {
                        const hotels = groupedByQuote[quoteId];
                        html += `<div class="hotel-card">`;
                        html += `<div style="font-weight: 600; color: #667eea;">Quote ID: ${quoteId} (${hotels.length} hotels)</div>`;
                        
                        hotels.forEach(hotel => {
                            const nights = hotel.stay_nights || 1;
                            const hotelName = hotel.hotel_name || 'Unknown Hotel';
                            const mealPlan = hotel.meal_plan || 'Breakfast included';
                            html += `<div class="hotel-details">${nights}N - ${hotelName} (${mealPlan})</div>`;
                        });
                        
                        html += '</div>';
                    });

                    html += '<h4>Raw Data Sample:</h4>';
                    html += `<pre>${JSON.stringify(hotelRows.slice(0, 3), null, 2)}</pre>`;
                } else {
                    html += '<div class="error">❌ No hotel records found</div>';
                }
                
                html += '</div>';
                container.innerHTML = html;

            } catch (error) {
                console.error('❌ Test failed:', error);
                container.innerHTML = `<div class="error">❌ Error: ${error.message}</div>`;
            }
        }

        async function testQuoteWithHotels() {
            const container = document.getElementById('testResults');
            container.innerHTML = '<div class="loading">📋 Testing quote with hotels...</div>';

            try {
                console.log('🧪 Testing quote with hotels...');
                
                // First get a quote that has hotels
                const { data: quotesWithHotels, error: quotesError } = await databaseService.quoteDB
                    .from('hotel_rows')
                    .select('quote_id')
                    .limit(1);

                if (quotesError) throw quotesError;

                if (quotesWithHotels && quotesWithHotels.length > 0) {
                    const quoteId = quotesWithHotels[0].quote_id;
                    
                    // Now get all hotels for this quote
                    const { data: hotelData, error: hotelError } = await databaseService.quoteDB
                        .from('hotel_rows')
                        .select('hotel_name, stay_nights, meal_plan, room_type, price')
                        .eq('quote_id', quoteId);

                    if (hotelError) throw hotelError;

                    let html = '<div class="test-section">';
                    html += `<h3>📋 Quote ${quoteId} Hotel Details</h3>`;
                    
                    if (hotelData && hotelData.length > 0) {
                        html += `<div class="success">✅ Found ${hotelData.length} hotels for quote ${quoteId}</div>`;
                        
                        let totalNights = 0;
                        hotelData.forEach(hotel => {
                            totalNights += hotel.stay_nights || 1;
                            const nights = hotel.stay_nights || 1;
                            const hotelName = hotel.hotel_name || 'Unknown Hotel';
                            const mealPlan = hotel.meal_plan || 'Breakfast included';
                            const price = hotel.price || 0;
                            
                            html += `<div class="hotel-card">`;
                            html += `<div class="hotel-name">${nights}N - ${hotelName} (${mealPlan})</div>`;
                            html += `<div class="hotel-details">Room Type: ${hotel.room_type || 'Standard'} | Price: ₹${price}</div>`;
                            html += `</div>`;
                        });

                        html += `<div style="margin-top: 15px; font-weight: 600;">Total Duration: ${totalNights}N/${totalNights + 1}D</div>`;
                        
                        html += '<h4>Raw Hotel Data:</h4>';
                        html += `<pre>${JSON.stringify(hotelData, null, 2)}</pre>`;
                    } else {
                        html += '<div class="error">❌ No hotels found for this quote</div>';
                    }
                    
                    html += '</div>';
                    container.innerHTML = html;
                } else {
                    container.innerHTML = '<div class="error">❌ No quotes with hotels found</div>';
                }

            } catch (error) {
                console.error('❌ Test failed:', error);
                container.innerHTML = `<div class="error">❌ Error: ${error.message}</div>`;
            }
        }

        async function testPackageEnhancement() {
            const container = document.getElementById('testResults');
            container.innerHTML = '<div class="loading">🎁 Testing package enhancement...</div>';

            try {
                console.log('🧪 Testing package enhancement with hotel data...');
                
                // Get a sample package with quote_id
                const searchParams = {
                    destination: 'Andaman',
                    adults: 2,
                    child: 1,
                    children: 0,
                    infants: 0
                };

                const result = await databaseService.searchPackages(searchParams);
                
                let html = '<div class="test-section">';
                html += '<h3>🎁 Package Enhancement Test</h3>';
                
                if (result.success && result.packages && result.packages.length > 0) {
                    const pkg = result.packages[0];
                    
                    html += `<div class="success">✅ Package enhanced successfully</div>`;
                    html += `<div><strong>Package:</strong> ${pkg.title}</div>`;
                    html += `<div><strong>Quote ID:</strong> ${pkg.quote_id || 'N/A'}</div>`;
                    
                    if (pkg.hotels_list && pkg.hotels_list.length > 0) {
                        html += `<div class="success">✅ Found ${pkg.hotels_list.length} hotels in package</div>`;
                        
                        html += '<h4>Hotels List:</h4>';
                        pkg.hotels_list.forEach(hotel => {
                            const nights = hotel.nights || hotel.stay_nights || 1;
                            const hotelName = hotel.hotel_name || 'Unknown Hotel';
                            const mealPlan = hotel.meal_plan || 'Breakfast included';
                            
                            html += `<div class="hotel-card">`;
                            html += `<div class="hotel-name">${nights}N - ${hotelName} (${mealPlan})</div>`;
                            html += `<div class="hotel-details">Room: ${hotel.room_type || 'Standard'} | Price: ₹${hotel.price || 0}</div>`;
                            html += `</div>`;
                        });
                        
                        html += '<h4>Package Inclusions:</h4>';
                        html += '<ul>';
                        pkg.inclusions.forEach(inclusion => {
                            html += `<li>${inclusion}</li>`;
                        });
                        html += '</ul>';
                    } else {
                        html += '<div class="error">❌ No hotels_list found in package</div>';
                        html += `<div>Hotel Name: ${pkg.hotel_name || 'N/A'}</div>`;
                    }
                    
                    html += '<h4>Full Package Data:</h4>';
                    html += `<pre>${JSON.stringify(pkg, null, 2)}</pre>`;
                } else {
                    html += '<div class="error">❌ No packages found or enhancement failed</div>';
                    html += `<div>Error: ${result.error || 'Unknown error'}</div>`;
                }
                
                html += '</div>';
                container.innerHTML = html;

            } catch (error) {
                console.error('❌ Test failed:', error);
                container.innerHTML = `<div class="error">❌ Error: ${error.message}</div>`;
            }
        }

        // Auto-run test on page load
        window.addEventListener('load', () => {
            console.log('🧪 Hotel data test page loaded');
            
            // Auto-test direct hotel query
            setTimeout(() => {
                console.log('🚀 Running automatic hotel data test...');
                testDirectHotelQuery();
            }, 2000);
        });
    </script>
</body>
</html>
