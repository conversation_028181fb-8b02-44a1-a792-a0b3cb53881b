/**
 * Database Service for Direct Supabase Calls
 * Used when API server is not available (production environment)
 */

import { createClient } from '@supabase/supabase-js';

// Database configuration
const QUOTE_DB_URL = 'https://lkqbrlrmrsnbtkoryazq.supabase.co';
const QUOTE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImxrcWJybHJtcnNuYnRrb3J5YXpxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDU0MDA2ODYsImV4cCI6MjA2MDk3NjY4Nn0.0E4Z87L9j32k3jKa15n4LpmFsVx8YCJuwovi-mSw4SE';

// Initialize Supabase client
const quoteDB = createClient(QUOTE_DB_URL, QUOTE_ANON_KEY);

export interface VisitedCustomer {
  id: string;
  customer_email: string;
  customer_phone: string;
  customer_name: string;
  destination: string;
  travel_date: string;
  estimated_total_cost: number;
  selected_emi_months: number;
  monthly_emi_amount: number;
  selected_emi_plan_id?: string;
  created_at: string;
  quote_status?: string;
}

export interface EMITransaction {
  id: string;
  booking_reference: string;
  customer_name?: string;
  customer_phone?: string;
  customer_email?: string;
  package_name?: string;
  advance_payment_amount: number;
  advance_payment_status: string;
  advance_payment_date?: string;
  total_emi_amount: number;
  monthly_emi_amount: number;
  remaining_emi_months: number;
  next_emi_due_date?: string;
  total_paid_amount: number;
  pending_amount: number;
  payment_status: string;
  auto_debit_enabled: boolean;
  payment_method: string;
  created_at: string;
}

/**
 * Fetch visited customers from public_family_quotes table
 */
export const fetchVisitedCustomers = async (): Promise<VisitedCustomer[]> => {
  try {
    console.log('🌐 Fetching visited customers via direct database call...');

    const { data: quotesData, error: quotesError } = await quoteDB
      .from('public_family_quotes')
      .select(`
        id,
        customer_email,
        customer_phone,
        customer_name,
        destination,
        travel_date,
        estimated_total_cost,
        selected_emi_months,
        monthly_emi_amount,
        selected_emi_plan_id,
        created_at,
        quote_status
      `)
      .order('created_at', { ascending: false })
      .limit(100);

    if (quotesError) {
      console.error('❌ Error fetching visited customers:', quotesError);
      throw quotesError;
    }

    console.log('📋 Raw visited customers fetched:', quotesData?.length || 0);

    // If no customers found, return empty array
    if (!quotesData || quotesData.length === 0) {
      console.log('ℹ️ No visited customers found in database');
      return [];
    }

    // Format the data to match the expected interface
    const formattedCustomers: VisitedCustomer[] = quotesData.map((quote: any) => ({
      id: quote.id,
      customer_email: quote.customer_email || 'N/A',
      customer_phone: quote.customer_phone || 'N/A',
      customer_name: quote.customer_name || 'N/A',
      destination: quote.destination || 'N/A',
      travel_date: quote.travel_date,
      estimated_total_cost: quote.estimated_total_cost || 0,
      selected_emi_months: quote.selected_emi_months || 0,
      monthly_emi_amount: quote.monthly_emi_amount || 0,
      quote_status: quote.quote_status || 'generated',
      created_at: quote.created_at
    }));

    console.log('✅ Successfully fetched and formatted visited customers:', formattedCustomers.length);
    return formattedCustomers;

  } catch (error) {
    console.error('❌ Error in fetchVisitedCustomers:', error);
    // Return empty array instead of throwing error to prevent UI crashes
    return [];
  }
};

/**
 * Fetch EMI transactions from prepaid_emi_transactions table with customer data
 */
export const fetchEMITransactions = async (): Promise<EMITransaction[]> => {
  try {
    console.log('🌐 Fetching EMI transactions via direct database call...');

    // First, fetch EMI transactions
    const { data: transactions, error: transactionError } = await quoteDB
      .from('prepaid_emi_transactions')
      .select('*')
      .order('created_at', { ascending: false });

    if (transactionError) {
      console.error('❌ Error fetching EMI transactions:', transactionError);
      throw transactionError;
    }

    console.log('📋 Raw transactions fetched:', transactions?.length || 0);

    // If no transactions found, return empty array
    if (!transactions || transactions.length === 0) {
      console.log('ℹ️ No EMI transactions found in database');
      return [];
    }

    // Fetch customer data for each transaction
    const transactionsWithCustomerData = [];

    for (const transaction of transactions) {
      let customerData = null;
      let packageName = 'N/A';

      // The customer_id field contains the quote_id from public_family_quotes
      if (transaction.customer_id) {
        const { data: quoteData, error: quoteError } = await quoteDB
          .from('public_family_quotes')
          .select('customer_name, customer_email, customer_phone, destination')
          .eq('id', transaction.customer_id)
          .single();

        if (!quoteError && quoteData) {
          customerData = quoteData;
          packageName = `${quoteData.destination} Package`;
        } else {
          console.log(`⚠️ No quote found for customer_id: ${transaction.customer_id}`);
        }
      }

      transactionsWithCustomerData.push({
        ...transaction,
        customer_data: customerData,
        package_name: packageName
      });
    }

    // Format the transactions to match the expected interface
    const formattedTransactions: EMITransaction[] = transactionsWithCustomerData.map((transaction: any) => ({
      id: transaction.id,
      booking_reference: transaction.booking_reference || `TXP-${transaction.id.slice(0, 8)}`,
      customer_name: transaction.customer_data?.customer_name || 'N/A',
      customer_phone: transaction.customer_data?.customer_phone || 'N/A',
      customer_email: transaction.customer_data?.customer_email || 'N/A',
      package_name: transaction.package_name || 'N/A',
      advance_payment_amount: transaction.advance_payment_amount || 0,
      advance_payment_status: transaction.advance_payment_status || 'pending',
      advance_payment_date: transaction.advance_payment_date,
      total_emi_amount: transaction.total_emi_amount || 0,
      monthly_emi_amount: transaction.monthly_emi_amount || 0,
      remaining_emi_months: transaction.remaining_emi_months || 0,
      next_emi_due_date: transaction.next_emi_due_date,
      total_paid_amount: transaction.total_paid_amount || 0,
      pending_amount: transaction.pending_amount || transaction.total_emi_amount || 0,
      payment_status: transaction.payment_status || 'active',
      auto_debit_enabled: transaction.auto_debit_enabled || false,
      payment_method: transaction.payment_method || 'N/A',
      created_at: transaction.created_at
    }));

    console.log('✅ Successfully fetched and formatted EMI transactions:', formattedTransactions.length);
    return formattedTransactions;

  } catch (error) {
    console.error('❌ Error in fetchEMITransactions:', error);
    // Return empty array instead of throwing error to prevent fallback to mock data
    return [];
  }
};

/**
 * Check if we should use direct database calls based on environment
 */
export const shouldUseDirectDatabase = (): boolean => {
  // Use direct database calls for production and localhost by default
  const hostname = window.location.hostname;
  const useDirectDB = localStorage.getItem('useDirectDatabase');

  // If explicitly disabled, use API server
  if (useDirectDB === 'false') {
    return false;
  }

  // Default to direct database for both production domains and localhost
  return hostname === 'family.tripxplo.com' || 
         hostname === 'crm.tripxplo.com' || 
         hostname === 'localhost' || 
         hostname === '127.0.0.1' || 
         useDirectDB === 'true';
};

/**
 * Get API base URL for environments that use API server
 */
export const getApiBaseUrl = (): string => {
  if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
    return 'http://localhost:3001/api';
  }
  return '/api';
};

/**
 * Debug function to check database connectivity and table status
 */
export const debugDatabaseStatus = async (): Promise<void> => {
  try {
    console.log('🔍 Checking database connectivity...');

    // Check prepaid_emi_transactions table
    // const { data: emiData, error: emiError, count: emiCount } = await quoteDB
    console.log('📊 prepaid_emi_transactions table:', {
      // error: emiError,
      // count: emiCount,
      accessible: true
    });

    // Check public_family_quotes table
    // const { data: quotesData, error: quotesError, count: quotesCount } = await quoteDB
    console.log('📊 public_family_quotes table:', {
      // error: quotesError,
      // count: quotesCount,
      accessible: true
    });

    // Check emi_payment_history table
    // const { data: historyData, error: historyError, count: historyCount } = await quoteDB
    console.log('📊 emi_payment_history table:', {
      // error: historyError,
      // count: historyCount,
      accessible: true
    });

  } catch (error) {
    console.error('❌ Database connectivity check failed:', error);
  }
};

/**
 * Enable direct database access for testing (localStorage flag)
 */
export const enableDirectDatabaseForTesting = (): void => {
  localStorage.setItem('useDirectDatabase', 'true');
  console.log('✅ Direct database access explicitly enabled');
};

/**
 * Disable direct database access for testing (localStorage flag)
 * This will force the system to use API server mode
 */
export const disableDirectDatabaseForTesting = (): void => {
  localStorage.setItem('useDirectDatabase', 'false');
  console.log('✅ Direct database access disabled - will use API server');
};

/**
 * Reset database access mode to default (remove localStorage override)
 */
export const resetDatabaseAccessMode = (): void => {
  localStorage.removeItem('useDirectDatabase');
  console.log('✅ Database access mode reset to default (direct database for localhost/production)');
};

// Make debug functions available globally for browser console testing
if (typeof window !== 'undefined') {
  (window as any).tripxploDebug = {
    debugDatabaseStatus,
    enableDirectDatabaseForTesting,
    disableDirectDatabaseForTesting,
    resetDatabaseAccessMode,
    fetchEMITransactions,
    fetchVisitedCustomers,
    shouldUseDirectDatabase
  };
}
