#!/bin/bash
set -e

echo "🚀 TripXplo CRM GitHub Deployment"
echo "================================="

# Configuration
GITHUB_USERNAME="sathishshah"
GITHUB_REPO="TripXplo-CRM"
DOMAIN="crm.tripxplo.com"
WEB_DIR="/var/www/crm"
TEMP_DIR="/tmp/crm-deploy"

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
CYAN='\033[0;36m'
NC='\033[0m'

# Get GitHub PAT from user
echo -e "${CYAN}🔐 Please enter your GitHub Personal Access Token:${NC}"
read -s GITHUB_PAT
echo ""

if [ -z "$GITHUB_PAT" ]; then
    echo -e "${RED}❌ GitHub PAT is required${NC}"
    exit 1
fi

echo -e "${CYAN}📁 Setting up deployment...${NC}"

# Clean up and create temp directory
rm -rf $TEMP_DIR
mkdir -p $TEMP_DIR
cd $TEMP_DIR

echo -e "${CYAN}📥 Cloning repository from GitHub...${NC}"

# Clone repository using PAT
git clone https://$GITHUB_USERNAME:$<EMAIL>/$GITHUB_USERNAME/$GITHUB_REPO.git .

if [ $? -ne 0 ]; then
    echo -e "${RED}❌ Failed to clone repository. Check your PAT and internet connection.${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Repository cloned successfully${NC}"

# Install Node.js if not present
if ! command -v node > /dev/null 2>&1; then
    echo -e "${YELLOW}📦 Installing Node.js...${NC}"
    curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
    apt-get install -y nodejs
fi

# Check Node.js version
NODE_VERSION=$(node --version)
echo -e "${GREEN}✅ Node.js version: $NODE_VERSION${NC}"

echo -e "${CYAN}📦 Installing project dependencies...${NC}"
npm install

if [ $? -ne 0 ]; then
    echo -e "${RED}❌ Failed to install dependencies${NC}"
    exit 1
fi

echo -e "${CYAN}🔨 Building production application...${NC}"
npm run build

if [ $? -ne 0 ]; then
    echo -e "${RED}❌ Build failed${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Build completed successfully${NC}"

echo -e "${CYAN}📁 Deploying to web directory...${NC}"

# Create web directory
mkdir -p $WEB_DIR

# Backup existing deployment if it exists
if [ -d "$WEB_DIR" ] && [ "$(ls -A $WEB_DIR 2>/dev/null)" ]; then
    BACKUP_DIR="$WEB_DIR.backup.$(date +%Y%m%d_%H%M%S)"
    echo -e "${YELLOW}💾 Backing up current deployment to $BACKUP_DIR...${NC}"
    cp -r $WEB_DIR $BACKUP_DIR
fi

# Deploy new files
echo -e "${CYAN}📋 Copying built files...${NC}"
rm -rf $WEB_DIR/*
cp -r dist/* $WEB_DIR/

# Set proper permissions
chown -R www-data:www-data $WEB_DIR 2>/dev/null || chown -R nginx:nginx $WEB_DIR 2>/dev/null || echo -e "${YELLOW}⚠️ Could not set web server ownership${NC}"
chmod -R 755 $WEB_DIR

echo -e "${GREEN}✅ Files deployed successfully${NC}"

echo -e "${CYAN}🌐 Configuring Nginx...${NC}"

# Create Nginx configuration
cat > /etc/nginx/sites-available/$DOMAIN << 'EOF'
server {
    listen 80;
    server_name crm.tripxplo.com www.crm.tripxplo.com;
    
    root /var/www/crm;
    index index.html index.htm;
    
    # Handle React Router (SPA routing)
    location / {
        try_files $uri $uri/ /index.html;
    }
    
    # Cache static assets
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot|map)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    
    # CORS headers for Supabase
    add_header Access-Control-Allow-Origin "*" always;
    add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS" always;
    add_header Access-Control-Allow-Headers "DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization" always;
    
    # Handle preflight OPTIONS requests
    if ($request_method = 'OPTIONS') {
        add_header Access-Control-Allow-Origin "*";
        add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS";
        add_header Access-Control-Allow-Headers "DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization";
        add_header Access-Control-Max-Age 1728000;
        add_header Content-Type "text/plain charset=UTF-8";
        add_header Content-Length 0;
        return 204;
    }
}
EOF

# Enable the site
ln -sf /etc/nginx/sites-available/$DOMAIN /etc/nginx/sites-enabled/

# Remove default site if it exists
rm -f /etc/nginx/sites-enabled/default

echo -e "${CYAN}🧪 Testing Nginx configuration...${NC}"
nginx -t

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Nginx configuration is valid${NC}"
    systemctl reload nginx
    echo -e "${GREEN}✅ Nginx reloaded successfully${NC}"
else
    echo -e "${RED}❌ Nginx configuration test failed${NC}"
    exit 1
fi

echo -e "${CYAN}🔒 Setting up SSL certificate...${NC}"

# Install Certbot if not present
if ! command -v certbot > /dev/null 2>&1; then
    echo -e "${YELLOW}📦 Installing Certbot...${NC}"
    apt update
    apt install -y certbot python3-certbot-nginx
fi

# Get SSL certificate
echo -e "${CYAN}🔐 Obtaining SSL certificate...${NC}"
certbot --nginx -d $DOMAIN -d www.$DOMAIN --non-interactive --agree-tos --email <EMAIL> --redirect

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ SSL certificate installed successfully${NC}"
else
    echo -e "${YELLOW}⚠️ SSL certificate setup failed. Site available over HTTP only.${NC}"
    echo -e "${YELLOW}Make sure DNS is properly configured for $DOMAIN${NC}"
fi

# Clean up temporary files
echo -e "${CYAN}🧹 Cleaning up...${NC}"
cd /
rm -rf $TEMP_DIR

echo ""
echo -e "${GREEN}🎉 Deployment completed successfully!${NC}"
echo ""
echo -e "${CYAN}📊 Deployment Summary:${NC}"
echo -e "Repository: https://github.com/$GITHUB_USERNAME/$GITHUB_REPO"
echo -e "Domain: $DOMAIN"
echo -e "Web Directory: $WEB_DIR"
echo -e "Nginx Status: $(systemctl is-active nginx)"
echo ""
echo -e "${GREEN}🌐 Your CRM is now live at:${NC}"
echo -e "${CYAN}🔗 https://$DOMAIN${NC}"
echo ""
echo -e "${YELLOW}✅ Features deployed:${NC}"
echo -e "• Enhanced lead editing functionality"
echo -e "• Material Design Trello-like Kanban board"
echo -e "• Priority management system"
echo -e "• Real-time updates and activity logging"
echo -e "• Smart auto-loading (once per session)"
echo ""
echo -e "${GREEN}🔄 For future deployments:${NC}"
echo -e "1. Push changes to GitHub"
echo -e "2. Run this script again on the server"
echo ""

# Test the deployment
echo -e "${CYAN}🧪 Testing deployment...${NC}"
HTTP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" http://$DOMAIN || echo "000")
if [ "$HTTP_STATUS" = "200" ] || [ "$HTTP_STATUS" = "301" ] || [ "$HTTP_STATUS" = "302" ]; then
    echo -e "${GREEN}✅ HTTP test passed (Status: $HTTP_STATUS)${NC}"
else
    echo -e "${YELLOW}⚠️ HTTP test failed (Status: $HTTP_STATUS)${NC}"
fi

echo -e "${GREEN}🎯 Deployment complete! Visit https://$DOMAIN to test your CRM.${NC}" 