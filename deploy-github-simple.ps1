# Simple GitHub Deployment Script for TripXplo CRM
Write-Host "🚀 GitHub Deployment for TripXplo CRM" -ForegroundColor Green
Write-Host "=====================================" -ForegroundColor Green

# Configuration
$GITHUB_USERNAME = "sathishshah"
$GITHUB_REPO = "TripXplo-CRM"
$SERVER_IP = "*************"
$SERVER_USER = "root"
$DOMAIN = "crm.tripxplo.com"

# Get GitHub PAT
Write-Host "🔐 Please enter your GitHub Personal Access Token:" -ForegroundColor Cyan
$secureToken = Read-Host "GitHub PAT" -AsSecureString
$GITHUB_PAT = [Runtime.InteropServices.Marshal]::PtrToStringAuto([Runtime.InteropServices.Marshal]::SecureStringToBSTR($secureToken))

# Validate GitHub credentials
Write-Host "🔍 Validating GitHub credentials..." -ForegroundColor Cyan
$headers = @{
    'Authorization' = "token $GITHUB_PAT"
    'User-Agent' = 'PowerShell'
}

try {
    $response = Invoke-RestMethod -Uri "https://api.github.com/user" -Headers $headers -Method Get
    Write-Host "✅ GitHub authentication successful! Welcome, $($response.name)" -ForegroundColor Green
} catch {
    Write-Host "❌ GitHub authentication failed. Please check your PAT." -ForegroundColor Red
    exit 1
}

# Create simple deployment script
$bashScript = @'
#!/bin/bash
set -e

echo "🚀 Starting GitHub deployment..."

# Configuration
GITHUB_USERNAME="REPLACE_USERNAME"
GITHUB_REPO="REPLACE_REPO"
GITHUB_PAT="REPLACE_PAT"
DOMAIN="REPLACE_DOMAIN"
WEB_DIR="/var/www/crm"
TEMP_DIR="/tmp/crm-deploy"

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
CYAN='\033[0;36m'
NC='\033[0m'

echo -e "${CYAN}📁 Setting up deployment...${NC}"

# Clean up and create temp directory
rm -rf $TEMP_DIR
mkdir -p $TEMP_DIR
cd $TEMP_DIR

echo -e "${CYAN}📥 Cloning repository...${NC}"

# Clone repository
git clone https://$GITHUB_USERNAME:$<EMAIL>/$GITHUB_USERNAME/$GITHUB_REPO.git .

if [ $? -ne 0 ]; then
    echo -e "${RED}❌ Failed to clone repository${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Repository cloned successfully${NC}"

# Install Node.js if needed
if ! command -v node > /dev/null; then
    echo -e "${YELLOW}📦 Installing Node.js...${NC}"
    curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
    apt-get install -y nodejs
fi

echo -e "${CYAN}📦 Installing dependencies...${NC}"
npm install

echo -e "${CYAN}🔨 Building application...${NC}"
npm run build

if [ $? -ne 0 ]; then
    echo -e "${RED}❌ Build failed${NC}"
    exit 1
fi

echo -e "${CYAN}📁 Deploying files...${NC}"

# Create web directory
mkdir -p $WEB_DIR

# Backup existing deployment
if [ -d "$WEB_DIR" ] && [ "$(ls -A $WEB_DIR)" ]; then
    echo -e "${YELLOW}💾 Backing up current deployment...${NC}"
    cp -r $WEB_DIR $WEB_DIR.backup.$(date +%Y%m%d_%H%M%S)
fi

# Deploy new files
rm -rf $WEB_DIR/*
cp -r dist/* $WEB_DIR/

# Set permissions
chown -R www-data:www-data $WEB_DIR
chmod -R 755 $WEB_DIR

echo -e "${CYAN}🌐 Configuring Nginx...${NC}"

# Create Nginx config
cat > /etc/nginx/sites-available/$DOMAIN << 'EOF'
server {
    listen 80;
    server_name crm.tripxplo.com www.crm.tripxplo.com;
    
    root /var/www/crm;
    index index.html;
    
    location / {
        try_files $uri $uri/ /index.html;
    }
    
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    add_header Access-Control-Allow-Origin "*" always;
    add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS" always;
    add_header Access-Control-Allow-Headers "DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization" always;
}
EOF

# Enable site
ln -sf /etc/nginx/sites-available/$DOMAIN /etc/nginx/sites-enabled/

# Test and reload Nginx
nginx -t && systemctl reload nginx

echo -e "${CYAN}🔒 Setting up SSL...${NC}"

# Install Certbot if needed
if ! command -v certbot > /dev/null; then
    apt update
    apt install -y certbot python3-certbot-nginx
fi

# Get SSL certificate
certbot --nginx -d $DOMAIN -d www.$DOMAIN --non-interactive --agree-tos --email <EMAIL> --redirect || echo -e "${YELLOW}⚠️ SSL setup failed${NC}"

# Clean up
cd /
rm -rf $TEMP_DIR

echo -e "${GREEN}🎉 Deployment completed!${NC}"
echo -e "${CYAN}🌐 Visit: https://$DOMAIN${NC}"
'@

# Replace placeholders in the script
$bashScript = $bashScript -replace "REPLACE_USERNAME", $GITHUB_USERNAME
$bashScript = $bashScript -replace "REPLACE_REPO", $GITHUB_REPO
$bashScript = $bashScript -replace "REPLACE_PAT", $GITHUB_PAT
$bashScript = $bashScript -replace "REPLACE_DOMAIN", $DOMAIN

# Save the script
$bashScript | Out-File -FilePath "deploy.sh" -Encoding UTF8

Write-Host "📤 Uploading deployment script..." -ForegroundColor Cyan

# Upload script to server
& scp "deploy.sh" "$SERVER_USER@$SERVER_IP`:/tmp/deploy.sh"

if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Failed to upload script" -ForegroundColor Red
    exit 1
}

Write-Host "✅ Script uploaded successfully" -ForegroundColor Green

# Execute deployment on server
Write-Host "🚀 Running deployment on server..." -ForegroundColor Cyan
Write-Host "This will take a few minutes..." -ForegroundColor Yellow

& ssh "$SERVER_USER@$SERVER_IP" "chmod +x /tmp/deploy.sh"
& ssh "$SERVER_USER@$SERVER_IP" "/tmp/deploy.sh"

if ($LASTEXITCODE -eq 0) {
    Write-Host ""
    Write-Host "🎉 Deployment completed successfully!" -ForegroundColor Green
    Write-Host "🌐 Your CRM is live at: https://crm.tripxplo.com" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "✅ What was deployed:" -ForegroundColor Yellow
    Write-Host "• Latest code from GitHub" -ForegroundColor White
    Write-Host "• Enhanced lead editing" -ForegroundColor White
    Write-Host "• Material Design Kanban" -ForegroundColor White
    Write-Host "• Priority management" -ForegroundColor White
    Write-Host "• Real-time updates" -ForegroundColor White
} else {
    Write-Host "❌ Deployment failed" -ForegroundColor Red
}

# Clean up
Remove-Item "deploy.sh" -ErrorAction SilentlyContinue

Write-Host ""
Write-Host "📋 Next steps:" -ForegroundColor Green
Write-Host "1. Test your CRM at https://crm.tripxplo.com" -ForegroundColor White
Write-Host "2. Verify lead editing works" -ForegroundColor White
Write-Host "3. For future updates: git push + run this script" -ForegroundColor White 