@echo off
echo Starting deployment of family.tripxplo.com...

REM Check if production directory exists
if not exist "family-tripxplo-production" (
    echo Error: family-tripxplo-production directory not found!
    pause
    exit /b 1
)

echo Found production files. Proceeding with deployment...

REM Step 1: Upload files using SCP
echo Step 1: Uploading files to server...
scp -r family-tripxplo-production/* root@*************:/tmp/family-new/

if %errorlevel% neq 0 (
    echo Error: Failed to upload files to server!
    pause
    exit /b 1
)

echo Files uploaded successfully!

REM Step 2: Execute deployment commands on server
echo Step 2: Executing deployment commands on server...

ssh root@************* "mkdir -p /tmp/family-new && echo 'Creating backup...' && if [ -d '/var/www/family' ]; then cp -r /var/www/family /var/www/family.backup.$(date +%%Y%%m%%d_%%H%%M%%S); fi && echo 'Clearing existing files...' && rm -rf /var/www/family/* && echo 'Copying new files...' && cp -r /tmp/family-new/* /var/www/family/ && echo 'Setting permissions...' && chown -R www-data:www-data /var/www/family && chmod -R 755 /var/www/family && echo 'Testing nginx...' && nginx -t && systemctl reload nginx && echo 'Cleaning up...' && rm -rf /tmp/family-new && echo 'Deployment completed!' && curl -I http://family.tripxplo.com"

if %errorlevel% neq 0 (
    echo Error: Deployment failed!
    pause
    exit /b 1
)

echo.
echo Deployment completed successfully!
echo Your site should now be available at: http://family.tripxplo.com
echo Please test the site to ensure everything is working correctly.
pause
