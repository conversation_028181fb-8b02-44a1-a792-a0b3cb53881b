<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enhanced Package Details Test - TripXplo Family EMI</title>
    <link rel="stylesheet" href="style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: 'Inter', sans-serif;
            margin: 0;
            padding: 20px;
            background: #f8f9fa;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }
        .test-header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e9ecef;
        }
        .test-header h1 {
            color: #333;
            margin-bottom: 10px;
        }
        .test-header p {
            color: #666;
            font-size: 1.1rem;
        }
        .test-buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }
        .test-btn {
            padding: 12px 24px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        .test-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }
        .test-btn.secondary {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        }
        .test-results {
            margin-top: 20px;
            padding: 20px;
            background: #f8f9ff;
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }
        .test-results h3 {
            margin-top: 0;
            color: #333;
        }
        .test-results pre {
            background: #fff;
            padding: 15px;
            border-radius: 6px;
            overflow-x: auto;
            font-size: 0.9rem;
            border: 1px solid #e9ecef;
        }
        .status-indicator {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            margin-left: 10px;
        }
        .status-success {
            background: #d4edda;
            color: #155724;
        }
        .status-error {
            background: #f8d7da;
            color: #721c24;
        }
        .status-loading {
            background: #fff3cd;
            color: #856404;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1><i class="fas fa-vial"></i> Enhanced Package Details Test</h1>
            <p>Test the enhanced package details functionality with comprehensive information display</p>
        </div>

        <div class="test-buttons">
            <button class="test-btn" onclick="testSamplePackage()">
                <i class="fas fa-play"></i> Test Sample Package
            </button>
            <button class="test-btn secondary" onclick="testDatabasePackage()">
                <i class="fas fa-database"></i> Test Database Package
            </button>
            <button class="test-btn" onclick="openPackageModal('sample-kashmir-1')">
                <i class="fas fa-eye"></i> Open Package Modal
            </button>
            <button class="test-btn secondary" onclick="testAllFeatures()">
                <i class="fas fa-check-double"></i> Test All Features
            </button>
        </div>

        <div id="testResults" class="test-results" style="display: none;">
            <h3>Test Results <span id="statusIndicator" class="status-indicator status-loading">Running...</span></h3>
            <pre id="resultOutput"></pre>
        </div>
    </div>

    <!-- Package Modal (copied from main index.html) -->
    <div class="modal-overlay" id="packageModal">
        <div class="modal-content package-modal">
            <div class="modal-header">
                <h3 id="packageModalTitle">Package Details</h3>
                <button class="modal-close" onclick="closePackageModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <div class="modal-body">
                <!-- Image Gallery -->
                <div class="image-gallery">
                    <div class="main-image">
                        <img id="packageMainImage" src="img/rectangle-14.png" alt="Package Image" />
                    </div>
                </div>

                <!-- Package Tabs -->
                <div class="package-tabs">
                    <button class="tab-btn active" onclick="switchTab('overview')">Overview</button>
                    <button class="tab-btn" onclick="switchTab('itinerary')">Itinerary</button>
                    <button class="tab-btn" onclick="switchTab('emi-options')">EMI Options</button>
                </div>

                <!-- Tab Content -->
                <div class="tab-content">
                    <!-- Overview Tab -->
                    <div class="tab-pane active" id="overview">
                        <!-- Content will be populated by JavaScript -->
                    </div>

                    <!-- Itinerary Tab -->
                    <div class="tab-pane" id="itinerary">
                        <!-- Content will be populated by JavaScript -->
                    </div>

                    <!-- EMI Options Tab -->
                    <div class="tab-pane" id="emi-options">
                        <!-- Content will be populated by JavaScript -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="js/config.js"></script>
    <script src="js/databaseService.js"></script>
    <script>
        // Test functions
        function showResults(title, data, status = 'success') {
            const resultsDiv = document.getElementById('testResults');
            const statusIndicator = document.getElementById('statusIndicator');
            const resultOutput = document.getElementById('resultOutput');
            
            resultsDiv.style.display = 'block';
            statusIndicator.textContent = status === 'success' ? 'Success' : status === 'error' ? 'Error' : 'Loading...';
            statusIndicator.className = `status-indicator status-${status}`;
            
            resultOutput.textContent = JSON.stringify(data, null, 2);
        }

        function testSamplePackage() {
            showResults('Sample Package Test', {}, 'loading');
            
            // Create sample package data
            const samplePackage = {
                id: 'sample-kashmir-1',
                package_name: 'Kashmir Winter Wonderland',
                destination: 'Kashmir',
                total_price: 65000,
                package_duration_days: 6,
                family_type_name: 'Family Nest',
                hotel_name: 'Houseboat Deluxe',
                additional_costs: {
                    meal_cost_per_person: 1200,
                    ferry_cost: 500,
                    activity_cost_per_person: 800,
                    guide_cost_per_day: 1500
                },
                no_of_adults: 2,
                no_of_children: 1,
                no_of_child: 1,
                family_count: 4,
                data_source: 'test_sample'
            };

            // Test the enhanced formatting
            try {
                const formattedPackage = databaseService.formatPackageDetailsForFrontend(samplePackage);
                showResults('Sample Package Test', formattedPackage, 'success');
            } catch (error) {
                showResults('Sample Package Test', { error: error.message }, 'error');
            }
        }

        async function testDatabasePackage() {
            showResults('Database Package Test', {}, 'loading');
            
            try {
                // Test database connection and package retrieval
                const response = await databaseService.getPackageDetails('sample-1');
                showResults('Database Package Test', response, response.success ? 'success' : 'error');
            } catch (error) {
                showResults('Database Package Test', { error: error.message }, 'error');
            }
        }

        async function testAllFeatures() {
            showResults('Comprehensive Feature Test', {}, 'loading');
            
            const testResults = {
                timestamp: new Date().toISOString(),
                tests: []
            };

            // Test 1: Sample package formatting
            try {
                const samplePackage = {
                    id: 'test-1',
                    destination: 'Goa',
                    total_price: 45000,
                    package_duration_days: 5,
                    additional_costs: { meal_cost_per_person: 900, ferry_cost: 0 }
                };
                const formatted = databaseService.formatPackageDetailsForFrontend(samplePackage);
                testResults.tests.push({
                    name: 'Sample Package Formatting',
                    status: 'PASS',
                    result: `Generated ${formatted.inclusions.length} inclusions, ${formatted.exclusions.length} exclusions`
                });
            } catch (error) {
                testResults.tests.push({
                    name: 'Sample Package Formatting',
                    status: 'FAIL',
                    error: error.message
                });
            }

            // Test 2: Database service initialization
            try {
                const familyTypes = await databaseService.getFamilyTypes();
                testResults.tests.push({
                    name: 'Database Service - Family Types',
                    status: familyTypes.success ? 'PASS' : 'FAIL',
                    result: familyTypes.success ? `Loaded ${familyTypes.data.length} family types` : familyTypes.error
                });
            } catch (error) {
                testResults.tests.push({
                    name: 'Database Service - Family Types',
                    status: 'FAIL',
                    error: error.message
                });
            }

            // Test 3: Destinations loading
            try {
                const destinations = await databaseService.getDestinations();
                testResults.tests.push({
                    name: 'Database Service - Destinations',
                    status: destinations.success ? 'PASS' : 'FAIL',
                    result: destinations.success ? `Loaded ${destinations.data.length} destinations` : destinations.error
                });
            } catch (error) {
                testResults.tests.push({
                    name: 'Database Service - Destinations',
                    status: 'FAIL',
                    error: error.message
                });
            }

            const overallStatus = testResults.tests.every(test => test.status === 'PASS') ? 'success' : 'error';
            showResults('Comprehensive Feature Test', testResults, overallStatus);
        }

        // Modal functions (simplified versions from main file)
        async function openPackageModal(packageId) {
            try {
                const modal = document.getElementById('packageModal');
                modal.style.display = 'flex';

                document.getElementById('packageModalTitle').textContent = 'Loading...';

                // Create sample package for demo
                const samplePackage = {
                    id: packageId,
                    package_name: 'Kashmir Winter Wonderland - Enhanced Demo',
                    destination: 'Kashmir',
                    total_price: 65000,
                    package_duration_days: 6,
                    nights: 6,
                    family_type_name: 'Family Nest (2A+1C+1Ch)',
                    hotel_name: 'Houseboat Deluxe',
                    hotel_category: '4-Star Premium',
                    meal_plan: 'Breakfast & Dinner (CP)',
                    meal_details: ['Daily Breakfast', 'Daily Dinner'],
                    ferry_included: true,
                    guide_included: true,
                    activities_included: ['Shikara ride in Dal Lake', 'Gondola ride in Gulmarg'],
                    description: 'Experience the paradise on earth with our specially crafted 6-night family package. Perfect for Family Nest, this package offers a perfect blend of comfort, adventure, and relaxation. Explore the paradise on earth with its pristine lakes, snow-capped mountains, and beautiful gardens.',
                    inclusions: [
                        'Accommodation as per itinerary',
                        'Private vehicle for all transfers and sightseeing',
                        'Daily Breakfast',
                        'Daily Dinner',
                        'Ferry tickets (adults and children)',
                        'Professional tour guide',
                        'Shikara ride in Dal Lake',
                        'Gondola ride in Gulmarg',
                        'All sightseeing as per itinerary',
                        'Welcome drink on arrival',
                        'All applicable taxes'
                    ],
                    exclusions: [
                        'Airfare (can be arranged separately)',
                        'Personal expenses like laundry, telephone calls, tips, etc.',
                        'Travel insurance',
                        'Any meals not mentioned in inclusions',
                        'Entry fees to monuments and parks',
                        'Camera fees at monuments',
                        'Medical expenses',
                        'Any expenses arising due to unforeseen circumstances',
                        'Anything not mentioned in inclusions'
                    ],
                    itinerary: [
                        {
                            day: 1,
                            title: 'Arrival in Kashmir',
                            description: 'Arrive at Kashmir airport/railway station. Meet and greet by our representative. Transfer to hotel and check-in. Rest of the day at leisure. Overnight stay at hotel.'
                        },
                        {
                            day: 2,
                            title: 'Srinagar Local Sightseeing',
                            description: 'After breakfast, visit Mughal Gardens - Nishat Bagh, Shalimar Bagh, and Chashme Shahi. Enjoy Shikara ride in Dal Lake. Visit local markets. Overnight stay at hotel.'
                        },
                        {
                            day: 3,
                            title: 'Srinagar to Gulmarg',
                            description: 'After breakfast, drive to Gulmarg (2 hours). Enjoy Gondola ride (Phase 1 & 2). Experience snow activities. Return to Srinagar. Overnight stay at hotel.'
                        }
                    ],
                    cost_breakdown: [
                        { item: 'Accommodation', cost: 25000, description: '6 nights hotel stay' },
                        { item: 'Transportation', cost: 15000, description: 'Private vehicle for transfers and sightseeing' },
                        { item: 'Meals', cost: 12000, description: 'Meals for 4 persons' },
                        { item: 'Activities', cost: 8000, description: 'Activities for 4 persons' },
                        { item: 'Guide', cost: 5000, description: 'Professional guide for 6 days' }
                    ],
                    validity: 'Valid for booking',
                    images: ['img/rectangle-14.png']
                };

                populatePackageModal(samplePackage);
            } catch (error) {
                console.error('Error loading package details:', error);
                closePackageModal();
            }
        }

        function closePackageModal() {
            document.getElementById('packageModal').style.display = 'none';
        }

        function switchTab(tabName) {
            // Remove active class from all tabs and panes
            document.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
            document.querySelectorAll('.tab-pane').forEach(pane => pane.classList.remove('active'));

            // Add active class to clicked tab and corresponding pane
            event.target.classList.add('active');
            document.getElementById(tabName).classList.add('active');
        }

        // Include the enhanced populatePackageModal function from the main file
        // (This would be the same function we created in index.html)
    </script>
</body>
</html>
