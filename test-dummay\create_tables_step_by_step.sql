-- STEP-BY-STEP TABLE CREATION
-- Run each section separately if needed

-- STEP 1: Create the main family_type_prices table
CREATE TABLE family_type_prices (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    quote_id UUID,
    family_type_id VARCHAR(50) NOT NULL,
    family_type_name VARCHAR(255) NOT NULL,
    no_of_adults INTEGER NOT NULL,
    no_of_children INTEGER DEFAULT 0,
    no_of_child INTEGER DEFAULT 0,
    no_of_infants INTEGER DEFAULT 0,
    family_count INTEGER NOT NULL,
    rooms_need INTEGER NOT NULL,
    cab_type VARCHAR(255),
    cab_capacity INTEGER,
    hotel_cost DECIMAL(10,2) NOT NULL DEFAULT 0,
    vehicle_cost DECIMAL(10,2) NOT NULL DEFAULT 0,
    additional_costs DECIMAL(10,2) NOT NULL DEFAULT 0,
    basic_costs DECIMAL(10,2) DEFAULT 0,
    addon_costs DECIMAL(10,2) DEFAULT 0,
    optional_costs DECIMAL(10,2) DEFAULT 0,
    subtotal DECIMAL(10,2) NOT NULL DEFAULT 0,
    discount_amount DECIMAL(10,2) DEFAULT 0,
    commission_amount DECIMAL(10,2) DEFAULT 0,
    total_price DECIMAL(10,2) NOT NULL DEFAULT 0,
    emi_enabled BOOLEAN DEFAULT true,
    min_emi_months INTEGER DEFAULT 3,
    max_emi_months INTEGER DEFAULT 24,
    emi_processing_fee_percent DECIMAL(5,2) DEFAULT 2.5,
    emi_interest_rate_percent DECIMAL(5,2) DEFAULT 12.0,
    is_public_visible BOOLEAN DEFAULT true,
    destination_category VARCHAR(100),
    season_category VARCHAR(50),
    package_duration_days INTEGER,
    public_display_order INTEGER DEFAULT 0,
    extra_adults INTEGER DEFAULT 0,
    children_charged INTEGER DEFAULT 0,
    infants_free INTEGER DEFAULT 0,
    room_type VARCHAR(255),
    baseline_quote_data JSONB,
    quote_mapping_data JSONB,
    calculation_notes TEXT[],
    public_metadata JSONB DEFAULT '{}',
    seo_keywords TEXT[],
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- STEP 2: Create the EMI plans table
CREATE TABLE family_type_emi_plans (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    family_price_id UUID NOT NULL,
    emi_months INTEGER NOT NULL,
    monthly_amount DECIMAL(10,2) NOT NULL,
    total_amount DECIMAL(10,2) NOT NULL,
    processing_fee DECIMAL(10,2) NOT NULL,
    total_interest DECIMAL(10,2) NOT NULL,
    first_payment_amount DECIMAL(10,2),
    subsequent_payment_amount DECIMAL(10,2),
    final_payment_amount DECIMAL(10,2),
    savings_vs_full_payment DECIMAL(10,2) DEFAULT 0,
    effective_annual_rate DECIMAL(5,2),
    is_featured BOOLEAN DEFAULT false,
    marketing_label VARCHAR(100),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    CONSTRAINT fk_family_price_id FOREIGN KEY (family_price_id) REFERENCES family_type_prices(id) ON DELETE CASCADE
);

-- STEP 3: Insert sample data
INSERT INTO family_type_prices (
    family_type_id, family_type_name, no_of_adults, no_of_children, 
    family_count, rooms_need, cab_type, destination_category, season_category,
    package_duration_days, hotel_cost, vehicle_cost, additional_costs, 
    subtotal, total_price, discount_amount, emi_enabled, is_public_visible
) VALUES 
('COUPLE', 'Romantic Couple Package', 2, 0, 1, 1, 'Sedan', 'Beach', 'Peak', 5, 25000, 8000, 12000, 45000, 42000, 3000, true, true),
('FAMILY_4', 'Happy Family (2+2)', 2, 2, 1, 2, 'SUV', 'Hill Station', 'Normal', 7, 40000, 15000, 23000, 78000, 73000, 5000, true, true),
('LARGE_FAMILY', 'Big Family (2+4)', 2, 4, 1, 3, 'Tempo Traveller', 'Adventure', 'Normal', 10, 60000, 25000, 40000, 125000, 117000, 8000, true, true);

-- STEP 4: Insert EMI plans
INSERT INTO family_type_emi_plans (family_price_id, emi_months, monthly_amount, total_amount, processing_fee, total_interest, effective_annual_rate, is_featured, marketing_label)
SELECT id, 3, 14700.00, 44100.00, 1050.00, 2100.00, 12.00, true, 'Quick Pay' FROM family_type_prices WHERE family_type_id = 'COUPLE';

INSERT INTO family_type_emi_plans (family_price_id, emi_months, monthly_amount, total_amount, processing_fee, total_interest, effective_annual_rate, is_featured, marketing_label)
SELECT id, 6, 13133.33, 78800.00, 1825.00, 5800.00, 12.00, true, 'Best Value' FROM family_type_prices WHERE family_type_id = 'FAMILY_4';

INSERT INTO family_type_emi_plans (family_price_id, emi_months, monthly_amount, total_amount, processing_fee, total_interest, effective_annual_rate, is_featured, marketing_label)
SELECT id, 12, 10920.00, 131040.00, 2925.00, 14040.00, 12.00, false, 'Low Monthly' FROM family_type_prices WHERE family_type_id = 'LARGE_FAMILY';

-- STEP 5: Verify data
SELECT 'Tables created successfully!' as status;
SELECT COUNT(*) as family_packages_count FROM family_type_prices;
SELECT COUNT(*) as emi_plans_count FROM family_type_emi_plans; 