import React, { useState, useEffect } from 'react';
import { fetchLeadById, updateLead } from '../lib/leadService';
import { Lead } from './LeadCard';
import { Spinner } from './Spinner';
import { LEAD_STATUSES } from '../constants/leadStatus';
import { toast } from 'react-hot-toast';

interface LeadDetailModalProps {
  leadId: string | null;
  onClose: () => void;
  onLeadUpdated?: (updatedLead: Lead) => void; // Callback to refresh the kanban board
}

interface LeadDetailModalPropsWithLead extends LeadDetailModalProps {
  leads?: Lead[]; // Optional array of leads for fallback
}

const LeadDetailModal: React.FC<LeadDetailModalPropsWithLead> = ({ 
  leadId, 
  onClose, 
  leads, 
  onLeadUpdated 
}) => {
  const [lead, setLead] = useState<Lead | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [isEditing, setIsEditing] = useState<boolean>(false);
  const [isSaving, setIsSaving] = useState<boolean>(false);
  const [editData, setEditData] = useState<Partial<Lead>>({});

  useEffect(() => {
    console.log('[LeadDetailModal] useEffect triggered with leadId:', leadId);
    if (!leadId) {
      console.log('[LeadDetailModal] No leadId provided, clearing state');
      setLead(null);
      setError(null);
      setIsEditing(false);
      return;
    }

    const fetchLead = async () => {
      setIsLoading(true);
      setError(null);

      try {
        // First try to find the lead in the provided leads array (for sample data)
        if (leads) {
          const foundLead = leads.find(l => l.id === leadId);
          if (foundLead) {
            console.log('[LeadDetailModal] Found lead in provided array:', foundLead);
            setLead(foundLead);
            setEditData(foundLead);
            setIsLoading(false);
            return;
          }
        }

        // If not found in array or no array provided, fetch from database
        console.log('[LeadDetailModal] Fetching lead from database:', leadId);
        const result = await fetchLeadById(leadId);

        if (result.success && result.data) {
          console.log('[LeadDetailModal] Successfully fetched lead:', result.data);
          setLead(result.data);
          setEditData(result.data);
        } else {
          console.error('[LeadDetailModal] Failed to fetch lead:', result.error);
          setError(result.error?.message || 'Failed to fetch lead details');
        }
      } catch (err: any) {
        console.error('[LeadDetailModal] Exception:', err);
        setError(err.message || 'An unexpected error occurred');
      } finally {
        setIsLoading(false);
      }
    };

    fetchLead();
  }, [leadId, leads]);

  const handleEdit = () => {
    setIsEditing(true);
    setEditData(lead || {});
  };

  const handleCancelEdit = () => {
    setIsEditing(false);
    setEditData(lead || {});
  };

  const handleSave = async () => {
    if (!lead?.id) return;

    setIsSaving(true);
    try {
      const result = await updateLead(lead.id, editData);
      
      if (result.success && result.data) {
        setLead(result.data);
        setEditData(result.data);
        setIsEditing(false);
        toast.success('Lead updated successfully!');
        
        // Notify parent component to refresh the kanban board
        if (onLeadUpdated) {
          onLeadUpdated(result.data);
        }
      } else {
        toast.error(result.error || 'Failed to update lead');
      }
    } catch (error: any) {
      console.error('Error saving lead:', error);
      toast.error('Failed to update lead');
    } finally {
      setIsSaving(false);
    }
  };

  const handleInputChange = (field: keyof Lead, value: any) => {
    setEditData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  if (!leadId) {
    console.log('[LeadDetailModal] Rendering null - no leadId');
    return null;
  }

  console.log('[LeadDetailModal] Rendering modal for leadId:', leadId);

  const formatDate = (dateString?: string) => {
    if (!dateString) return 'Not set';

    try {
      const date = new Date(dateString);
      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
    } catch (e) {
      return 'Invalid date';
    }
  };

  const formatDateForInput = (dateString?: string) => {
    if (!dateString) return '';
    try {
      const date = new Date(dateString);
      return date.toISOString().split('T')[0];
    } catch (e) {
      return '';
    }
  };

  const getPriorityClass = (priority?: string) => {
    switch(priority?.toLowerCase()) {
      case 'high':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      default:
        return 'bg-green-100 text-green-800 border-green-200';
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="sticky top-0 bg-white border-b border-gray-200 px-6 py-4 rounded-t-lg">
          <div className="flex justify-between items-center">
            <h2 className="text-2xl font-bold text-gray-800">
              {isEditing ? 'Edit Lead' : 'Lead Details'}
            </h2>
            <button
              onClick={onClose}
              className="text-gray-500 hover:text-gray-700 focus:outline-none p-1 rounded-full hover:bg-gray-100 transition-colors"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="px-6 py-4">
          {isLoading && (
            <div className="flex flex-col items-center justify-center py-8">
              <Spinner size="lg" />
              <p className="text-gray-500 mt-4">Loading lead details...</p>
            </div>
          )}

          {error && (
            <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
              <h3 className="font-medium">Error Loading Lead</h3>
              <p className="text-sm mt-1">{error}</p>
            </div>
          )}

          {lead && !isLoading && !error && (
            <div className="space-y-6">
              {/* Customer Information */}
              <div className="bg-gray-50 rounded-lg p-4">
                <h3 className="text-lg font-semibold text-gray-800 mb-3 flex items-center">
                  <svg className="w-5 h-5 mr-2 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                  </svg>
                  Customer Information
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium text-gray-600 block mb-1">Name</label>
                    {isEditing ? (
                      <input
                        type="text"
                        value={editData.customer_name || ''}
                        onChange={(e) => handleInputChange('customer_name', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                      />
                    ) : (
                      <p className="text-gray-800 font-medium">{lead.customer_name}</p>
                    )}
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600 block mb-1">Email</label>
                    {isEditing ? (
                      <input
                        type="email"
                        value={editData.email || ''}
                        onChange={(e) => handleInputChange('email', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                      />
                    ) : (
                      <p className="text-gray-800">{lead.email}</p>
                    )}
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600 block mb-1">Phone</label>
                    {isEditing ? (
                      <input
                        type="tel"
                        value={editData.phone || ''}
                        onChange={(e) => handleInputChange('phone', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                      />
                    ) : (
                      <p className="text-gray-800">{lead.phone || 'Not provided'}</p>
                    )}
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600 block mb-1">Priority</label>
                    {isEditing ? (
                      <select
                        value={editData.priority || 'low'}
                        onChange={(e) => handleInputChange('priority', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                      >
                        <option value="low">Low</option>
                        <option value="medium">Medium</option>
                        <option value="high">High</option>
                      </select>
                    ) : (
                      <span className={`inline-block px-2 py-1 text-xs font-medium rounded-full border ${getPriorityClass(lead.priority)}`}>
                        {lead.priority || 'Low'}
                      </span>
                    )}
                  </div>
                </div>
              </div>

              {/* Travel Information */}
              <div className="bg-gray-50 rounded-lg p-4">
                <h3 className="text-lg font-semibold text-gray-800 mb-3 flex items-center">
                  <svg className="w-5 h-5 mr-2 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                  </svg>
                  Travel Information
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium text-gray-600 block mb-1">Destination</label>
                    {isEditing ? (
                      <input
                        type="text"
                        value={editData.destination || ''}
                        onChange={(e) => handleInputChange('destination', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                      />
                    ) : (
                      <p className="text-gray-800">{lead.destination || 'Not specified'}</p>
                    )}
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600 block mb-1">Departure City</label>
                    {isEditing ? (
                      <input
                        type="text"
                        value={editData.departure_city || ''}
                        onChange={(e) => handleInputChange('departure_city', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                      />
                    ) : (
                      <p className="text-gray-800">{lead.departure_city || 'Not specified'}</p>
                    )}
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600 block mb-1">Travel Date</label>
                    {isEditing ? (
                      <input
                        type="date"
                        value={formatDateForInput(editData.travel_date)}
                        onChange={(e) => handleInputChange('travel_date', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                      />
                    ) : (
                      <p className="text-gray-800">{formatDate(lead.travel_date)}</p>
                    )}
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600 block mb-1">Nights</label>
                    {isEditing ? (
                      <input
                        type="number"
                        min="1"
                        value={editData.nights || 1}
                        onChange={(e) => handleInputChange('nights', parseInt(e.target.value) || 1)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                      />
                    ) : (
                      <p className="text-gray-800">{lead.nights || 1} nights</p>
                    )}
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
                  <div>
                    <label className="text-sm font-medium text-gray-600 block mb-1">Adults</label>
                    {isEditing ? (
                      <input
                        type="number"
                        min="1"
                        value={editData.adults || 1}
                        onChange={(e) => handleInputChange('adults', parseInt(e.target.value) || 1)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                      />
                    ) : (
                      <p className="text-gray-800">{lead.adults || 1}</p>
                    )}
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600 block mb-1">Children</label>
                    {isEditing ? (
                      <input
                        type="number"
                        min="0"
                        value={editData.children || 0}
                        onChange={(e) => handleInputChange('children', parseInt(e.target.value) || 0)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                      />
                    ) : (
                      <p className="text-gray-800">{lead.children || 0}</p>
                    )}
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600 block mb-1">Infants</label>
                    {isEditing ? (
                      <input
                        type="number"
                        min="0"
                        value={editData.infants || 0}
                        onChange={(e) => handleInputChange('infants', parseInt(e.target.value) || 0)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                      />
                    ) : (
                      <p className="text-gray-800">{lead.infants || 0}</p>
                    )}
                  </div>
                </div>
              </div>

              {/* Additional Information */}
              <div className="bg-gray-50 rounded-lg p-4">
                <h3 className="text-lg font-semibold text-gray-800 mb-3 flex items-center">
                  <svg className="w-5 h-5 mr-2 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  Additional Information
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium text-gray-600 block mb-1">Budget Range</label>
                    {isEditing ? (
                      <input
                        type="text"
                        value={editData.budget_range || ''}
                        onChange={(e) => handleInputChange('budget_range', e.target.value)}
                        placeholder="e.g., 50000-100000"
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                      />
                    ) : (
                      <p className="text-gray-800">{lead.budget_range || 'Not specified'}</p>
                    )}
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600 block mb-1">Lead Source</label>
                    {isEditing ? (
                      <select
                        value={editData.lead_source || 'website'}
                        onChange={(e) => handleInputChange('lead_source', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                      >
                        <option value="instagram">Instagram</option>
                        <option value="whatsapp">WhatsApp</option>
                        <option value="website">Website</option>
                        <option value="referral">Referral</option>
                        <option value="partner">Partner</option>
                        <option value="direct">Direct</option>
                        <option value="other">Other</option>
                      </select>
                    ) : (
                      <p className="text-gray-800">{lead.lead_source || 'Not specified'}</p>
                    )}
                  </div>
                </div>

                <div className="grid grid-cols-1 gap-4 mt-4">
                  <div>
                    <label className="text-sm font-medium text-gray-600 block mb-1">Notes</label>
                    {isEditing ? (
                      <textarea
                        value={editData.notes || ''}
                        onChange={(e) => handleInputChange('notes', e.target.value)}
                        rows={3}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                        placeholder="Any additional notes..."
                      />
                    ) : (
                      <p className="text-gray-800">{lead.notes || 'No notes'}</p>
                    )}
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600 block mb-1">Special Requests</label>
                    {isEditing ? (
                      <textarea
                        value={editData.special_requests || ''}
                        onChange={(e) => handleInputChange('special_requests', e.target.value)}
                        rows={3}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                        placeholder="Any special requirements..."
                      />
                    ) : (
                      <p className="text-gray-800">{lead.special_requests || 'No special requests'}</p>
                    )}
                  </div>
                </div>
              </div>

              {/* Lead Management */}
              <div className="bg-gray-50 rounded-lg p-4">
                <h3 className="text-lg font-semibold text-gray-800 mb-3 flex items-center">
                  <svg className="w-5 h-5 mr-2 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                  </svg>
                  Lead Management
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium text-gray-600 block mb-1">Status</label>
                    {isEditing ? (
                      <select
                        value={editData.status || LEAD_STATUSES[0]}
                        onChange={(e) => handleInputChange('status', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                      >
                        {LEAD_STATUSES.map((status) => (
                          <option key={status} value={status}>{status}</option>
                        ))}
                      </select>
                    ) : (
                      <p className="text-gray-800 font-medium">{lead.status}</p>
                    )}
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">Created Date</label>
                    <p className="text-gray-800">{formatDate(lead.created_at)}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">Last Updated</label>
                    <p className="text-gray-800">{formatDate(lead.updated_at)}</p>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="sticky bottom-0 bg-white border-t border-gray-200 px-6 py-4 rounded-b-lg">
          <div className="flex justify-end space-x-3">
            {isEditing ? (
              <>
                <button
                  onClick={handleCancelEdit}
                  disabled={isSaving}
                  className="px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 transition-colors disabled:opacity-50"
                >
                  Cancel
                </button>
                <button
                  onClick={handleSave}
                  disabled={isSaving}
                  className="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-dark transition-colors disabled:opacity-50 flex items-center space-x-2"
                >
                  {isSaving && <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>}
                  <span>{isSaving ? 'Saving...' : 'Save Changes'}</span>
                </button>
              </>
            ) : (
              <>
                <button
                  onClick={onClose}
                  className="px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 transition-colors"
                >
                  Close
                </button>
                {lead && (
                  <button
                    onClick={handleEdit}
                    className="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-dark transition-colors flex items-center space-x-2"
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                    </svg>
                    <span>Edit Lead</span>
                  </button>
                )}
              </>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default LeadDetailModal;