import React, { useState, useEffect } from 'react';
import { supabase } from '../lib/supabaseClient';
import { toast } from 'react-hot-toast';

interface CustomerQuote {
  quote_id: string;
  customer_name: string;
  customer_phone: string;
  customer_email: string;
  destination: string;
  total_cost: number;
  created_at: string;
  package_name: string;
}

interface CustomerSummary {
  customer_phone: string;
  customer_name: string;
  customer_email: string;
  total_quotes: number;
  total_value: number;
  latest_quote_date: string;
  first_quote_date: string;
  quote_ids: string[];
  destinations: string[];
}

interface CustomerQuoteManagementProps {
  onQuoteSelect?: (quoteId: string) => void;
}

const CustomerQuoteManagement: React.FC<CustomerQuoteManagementProps> = ({ onQuoteSelect }) => {
  const [customers, setCustomers] = useState<CustomerSummary[]>([]);
  const [customerQuotes, setCustomerQuotes] = useState<CustomerQuote[]>([]);
  const [selectedCustomerPhone, setSelectedCustomerPhone] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [showWhatsAppModal, setShowWhatsAppModal] = useState(false);
  const [whatsAppMessage, setWhatsAppMessage] = useState('');

  // Load customer summary on component mount
  useEffect(() => {
    loadCustomerSummary();
  }, []);

  const loadCustomerSummary = async () => {
    setIsLoading(true);
    try {
      const { data, error } = await supabase
        .from('customer_quote_summary')
        .select('*')
        .order('latest_quote_date', { ascending: false });

      if (error) throw error;
      setCustomers(data || []);
    } catch (error) {
      console.error('Error loading customer summary:', error);
      toast.error('Failed to load customer summary');
    } finally {
      setIsLoading(false);
    }
  };

  const loadCustomerQuotes = async (mobileNumber: string) => {
    setIsLoading(true);
    try {
      const { data, error } = await supabase
        .rpc('find_quotes_by_mobile', { mobile_input: mobileNumber });

      if (error) throw error;
      setCustomerQuotes(data || []);
      setSelectedCustomerPhone(mobileNumber);
    } catch (error) {
      console.error('Error loading customer quotes:', error);
      toast.error('Failed to load customer quotes');
    } finally {
      setIsLoading(false);
    }
  };

  // const searchCustomerByMobile = async (mobile: string) => {
  //   if (!mobile.trim()) {
  //     loadCustomerSummary();
  //     return;
  //   }

  //   setIsLoading(true);
  //   try {
  //     // Clean mobile number
  //     const cleanMobile = mobile.replace(/\D/g, '');
      
  //     const { data, error } = await supabase
  //       .from('customer_quote_summary')
  //       .select('*')
  //       .or(`customer_phone.ilike.%${cleanMobile}%,customer_name.ilike.%${mobile}%`)
  //       .order('latest_quote_date', { ascending: false });

  //     if (error) throw error;
  //     setCustomers(data || []);
  //   } catch (error) {
  //     console.error('Error searching customers:', error);
  //     toast.error('Failed to search customers');
  //   } finally {
  //     setIsLoading(false);
  //   }
  // };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const handleWhatsAppSend = (customer: CustomerSummary) => {
    const defaultMessage = `Hi ${customer.customer_name}! 

Thank you for your interest in our travel packages. Here's a summary of your quotes:

📍 Destinations: ${customer.destinations.slice(0, 3).join(', ')}${customer.destinations.length > 3 ? '...' : ''}
💰 Total Value: ${formatCurrency(customer.total_value)}
📅 Latest Quote: ${formatDate(customer.latest_quote_date)}

Would you like to discuss your travel plans? We're here to help make your trip memorable!

Best regards,
TripXplo Team`;

    setWhatsAppMessage(defaultMessage);
    setSelectedCustomerPhone(customer.customer_phone);
    setShowWhatsAppModal(true);
  };

  const sendWhatsApp = () => {
    if (!selectedCustomerPhone || !whatsAppMessage.trim()) {
      toast.error('Please enter a message');
      return;
    }

    const cleanNumber = selectedCustomerPhone.replace(/\D/g, '');
    const encodedMessage = encodeURIComponent(whatsAppMessage);
    
    // Try web WhatsApp first
    const webUrl = `https://web.whatsapp.com/send?phone=91${cleanNumber}&text=${encodedMessage}`;
    window.open(webUrl, '_blank');
    
    setShowWhatsAppModal(false);
    toast.success('WhatsApp opened successfully!');
  };

  const filteredCustomers = customers.filter(customer =>
    customer.customer_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    customer.customer_phone.includes(searchTerm) ||
    customer.destinations.some(dest => dest.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  return (
    <div className="max-w-7xl mx-auto p-6">
      <div className="bg-white rounded-lg shadow-lg">
        {/* Header */}
        <div className="p-6 border-b border-gray-200">
          <div className="flex justify-between items-center">
            <div>
              <h2 className="text-2xl font-bold text-gray-900">Customer Quote Management</h2>
              <p className="text-gray-600 mt-1">Manage quotes grouped by customer mobile number</p>
            </div>
            <button
              onClick={loadCustomerSummary}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              Refresh
            </button>
          </div>

          {/* Search */}
          <div className="mt-4">
            <input
              type="text"
              placeholder="Search by customer name, mobile number, or destination..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
        </div>

        <div className="flex">
          {/* Customer List */}
          <div className="w-1/2 border-r border-gray-200">
            <div className="p-4 bg-gray-50 border-b border-gray-200">
              <h3 className="text-lg font-semibold text-gray-800">
                Customers ({filteredCustomers.length})
              </h3>
            </div>
            
            <div className="overflow-y-auto max-h-96">
              {isLoading ? (
                <div className="p-8 text-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                  <p className="text-gray-500 mt-2">Loading customers...</p>
                </div>
              ) : filteredCustomers.length === 0 ? (
                <div className="p-8 text-center text-gray-500">
                  <p>No customers found</p>
                </div>
              ) : (
                filteredCustomers.map((customer) => (
                  <div
                    key={customer.customer_phone}
                    className={`p-4 border-b border-gray-100 cursor-pointer hover:bg-gray-50 ${
                      selectedCustomerPhone === customer.customer_phone ? 'bg-blue-50 border-blue-200' : ''
                    }`}
                    onClick={() => loadCustomerQuotes(customer.customer_phone)}
                  >
                    <div className="flex justify-between items-start">
                      <div className="flex-1">
                        <h4 className="font-semibold text-gray-900">{customer.customer_name}</h4>
                        <p className="text-sm text-gray-600">📱 {customer.customer_phone}</p>
                        {customer.customer_email && (
                          <p className="text-sm text-gray-600">✉️ {customer.customer_email}</p>
                        )}
                        <div className="mt-2">
                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                            {customer.total_quotes} quote{customer.total_quotes !== 1 ? 's' : ''}
                          </span>
                          <span className="ml-2 text-sm font-medium text-green-600">
                            {formatCurrency(customer.total_value)}
                          </span>
                        </div>
                        <p className="text-xs text-gray-500 mt-1">
                          Latest: {formatDate(customer.latest_quote_date)}
                        </p>
                      </div>
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleWhatsAppSend(customer);
                        }}
                        className="ml-2 p-2 text-green-600 hover:bg-green-100 rounded-full"
                        title="Send WhatsApp"
                      >
                        📱
                      </button>
                    </div>
                  </div>
                ))
              )}
            </div>
          </div>

          {/* Quote Details */}
          <div className="w-1/2">
            <div className="p-4 bg-gray-50 border-b border-gray-200">
              <h3 className="text-lg font-semibold text-gray-800">
                {selectedCustomerPhone ? `Quotes for ${selectedCustomerPhone}` : 'Select a customer'}
              </h3>
            </div>

            <div className="overflow-y-auto max-h-96">
              {selectedCustomerPhone ? (
                customerQuotes.length === 0 ? (
                  <div className="p-8 text-center text-gray-500">
                    <p>No quotes found for this customer</p>
                  </div>
                ) : (
                  customerQuotes.map((quote) => (
                    <div
                      key={quote.quote_id}
                      className="p-4 border-b border-gray-100 hover:bg-gray-50 cursor-pointer"
                      onClick={() => onQuoteSelect?.(quote.quote_id)}
                    >
                      <div className="flex justify-between items-start">
                        <div className="flex-1">
                          <h4 className="font-semibold text-gray-900">
                            {quote.package_name || 'Untitled Package'}
                          </h4>
                          <p className="text-sm text-gray-600">📍 {quote.destination}</p>
                          <p className="text-sm font-medium text-green-600 mt-1">
                            {formatCurrency(quote.total_cost)}
                          </p>
                          <p className="text-xs text-gray-500 mt-1">
                            Created: {formatDate(quote.created_at)}
                          </p>
                        </div>
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            onQuoteSelect?.(quote.quote_id);
                          }}
                          className="ml-2 px-3 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700"
                        >
                          Open
                        </button>
                      </div>
                    </div>
                  ))
                )
              ) : (
                <div className="p-8 text-center text-gray-500">
                  <p>Select a customer to view their quotes</p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* WhatsApp Modal */}
      {showWhatsAppModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold text-gray-900">Send WhatsApp Message</h3>
              <button
                onClick={() => setShowWhatsAppModal(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                ✕
              </button>
            </div>
            
            <div className="mb-4">
              <p className="text-sm text-gray-600 mb-2">To: {selectedCustomerPhone}</p>
              <textarea
                value={whatsAppMessage}
                onChange={(e) => setWhatsAppMessage(e.target.value)}
                rows={8}
                className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500"
                placeholder="Enter your message..."
              />
            </div>
            
            <div className="flex space-x-3">
              <button
                onClick={() => setShowWhatsAppModal(false)}
                className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                onClick={sendWhatsApp}
                className="flex-1 px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
              >
                Send WhatsApp
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default CustomerQuoteManagement; 