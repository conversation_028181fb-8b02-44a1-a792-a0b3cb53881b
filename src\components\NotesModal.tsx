import React, { useState, useEffect } from 'react';
import { X, Edit, Trash2, Save, Plus } from 'lucide-react';

export interface Note {
  id: string;
  lead_id: string;
  info: string;
  created_at: string;
  updated_at: string;
}

interface NotesModalProps {
  isOpen: boolean;
  onClose: () => void;
  leadId: string;
  leadName: string;
  notes: Note[];
  onSaveNote: (leadId: string, noteInfo: string, noteId?: string) => Promise<void>;
  onDeleteNote: (noteId: string) => Promise<void>;
  onLoadNotes: (leadId: string) => Promise<Note[]>;
}

const NotesModal: React.FC<NotesModalProps> = ({
  isOpen,
  onClose,
  leadId,
  leadName,
  notes,
  onSaveNote,
  onDeleteNote,
  onLoadNotes,
}) => {
  const [localNotes, setLocalNotes] = useState<Note[]>(notes);
  const [editingNoteId, setEditingNoteId] = useState<string | null>(null);
  const [newNoteText, setNewNoteText] = useState('');
  const [editNoteText, setEditNoteText] = useState('');
  const [isAddingNew, setIsAddingNew] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [isDeleting, setIsDeleting] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (isOpen) {
      loadNotes();
    }
  }, [isOpen, leadId]);

  const loadNotes = async () => {
    setIsLoading(true);
    try {
      const freshNotes = await onLoadNotes(leadId);
      setLocalNotes(freshNotes);
    } catch (error) {
      console.error('Error loading notes:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSaveNewNote = async () => {
    if (!newNoteText.trim()) return;

    setIsSaving(true);
    try {
      await onSaveNote(leadId, newNoteText.trim());
      setNewNoteText('');
      setIsAddingNew(false);
      await loadNotes(); // Refresh notes list
    } catch (error) {
      console.error('Error saving new note:', error);
    } finally {
      setIsSaving(false);
    }
  };

  const handleSaveEditNote = async (noteId: string) => {
    if (!editNoteText.trim()) return;

    setIsSaving(true);
    try {
      await onSaveNote(leadId, editNoteText.trim(), noteId);
      setEditingNoteId(null);
      setEditNoteText('');
      await loadNotes(); // Refresh notes list
    } catch (error) {
      console.error('Error updating note:', error);
    } finally {
      setIsSaving(false);
    }
  };

  const handleDeleteNote = async (noteId: string) => {
    if (!window.confirm('Are you sure you want to delete this note?')) return;

    setIsDeleting(noteId);
    try {
      await onDeleteNote(noteId);
      await loadNotes(); // Refresh notes list
    } catch (error) {
      console.error('Error deleting note:', error);
    } finally {
      setIsDeleting(null);
    }
  };

  const startEditingNote = (note: Note) => {
    setEditingNoteId(note.id);
    setEditNoteText(note.info);
  };

  const cancelEditing = () => {
    setEditingNoteId(null);
    setEditNoteText('');
    setIsAddingNew(false);
    setNewNoteText('');
  };

  const handleClose = () => {
    cancelEditing();
    onClose();
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-2xl mx-4 max-h-[80vh] flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">
            Notes for {leadName}
          </h3>
          <button
            onClick={handleClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto p-4">
          {isLoading ? (
            <div className="flex items-center justify-center py-8">
              <div className="text-gray-500">Loading notes...</div>
            </div>
          ) : (
            <>
              {/* Add New Note Section */}
              <div className="mb-6">
                <div className="flex items-center justify-between mb-3">
                  <h4 className="text-sm font-medium text-gray-700">Add New Note</h4>
                  {!isAddingNew && (
                    <button
                      onClick={() => setIsAddingNew(true)}
                      className="flex items-center gap-2 px-3 py-1.5 text-sm font-medium text-blue-600 bg-blue-50 rounded-md hover:bg-blue-100 transition-colors"
                    >
                      <Plus className="w-4 h-4" />
                      Add Note
                    </button>
                  )}
                </div>

                {isAddingNew && (
                  <div className="space-y-3">
                    <textarea
                      value={newNoteText}
                      onChange={(e) => setNewNoteText(e.target.value)}
                      placeholder="Enter your note here..."
                      className="w-full h-24 px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none"
                      autoFocus
                    />
                    <div className="flex gap-2">
                      <button
                        onClick={handleSaveNewNote}
                        disabled={isSaving || !newNoteText.trim()}
                        className="flex items-center gap-2 px-3 py-1.5 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 transition-colors disabled:opacity-50"
                      >
                        <Save className="w-4 h-4" />
                        {isSaving ? 'Saving...' : 'Save'}
                      </button>
                      <button
                        onClick={cancelEditing}
                        className="px-3 py-1.5 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors"
                      >
                        Cancel
                      </button>
                    </div>
                  </div>
                )}
              </div>

              {/* Existing Notes List */}
              <div>
                <h4 className="text-sm font-medium text-gray-700 mb-3">
                  Existing Notes ({localNotes.length})
                </h4>

                {localNotes.length === 0 ? (
                  <div className="text-center py-8 text-gray-500">
                    <div className="text-sm">No notes yet</div>
                    <div className="text-xs mt-1">Click "Add Note" to create your first note</div>
                  </div>
                ) : (
                  <div className="space-y-3">
                    {localNotes.map((note) => (
                      <div key={note.id} className="border border-gray-200 rounded-lg p-3">
                        {editingNoteId === note.id ? (
                          <div className="space-y-3">
                            <textarea
                              value={editNoteText}
                              onChange={(e) => setEditNoteText(e.target.value)}
                              className="w-full h-24 px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none"
                            />
                            <div className="flex gap-2">
                              <button
                                onClick={() => handleSaveEditNote(note.id)}
                                disabled={isSaving || !editNoteText.trim()}
                                className="flex items-center gap-2 px-3 py-1.5 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 transition-colors disabled:opacity-50"
                              >
                                <Save className="w-4 h-4" />
                                {isSaving ? 'Saving...' : 'Save'}
                              </button>
                              <button
                                onClick={cancelEditing}
                                className="px-3 py-1.5 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors"
                              >
                                Cancel
                              </button>
                            </div>
                          </div>
                        ) : (
                          <div>
                            <div className="text-sm text-gray-900 mb-2 whitespace-pre-wrap">
                              {note.info}
                            </div>
                            <div className="flex items-center justify-between">
                              <div className="text-xs text-gray-500">
                                Created: {formatDate(note.created_at)}
                                {note.updated_at !== note.created_at && (
                                  <span className="ml-2">
                                    • Updated: {formatDate(note.updated_at)}
                                  </span>
                                )}
                              </div>
                              <div className="flex gap-2">
                                <button
                                  onClick={() => startEditingNote(note)}
                                  className="flex items-center gap-1 px-2 py-1 text-xs font-medium text-blue-600 bg-blue-50 rounded hover:bg-blue-100 transition-colors"
                                >
                                  <Edit className="w-3 h-3" />
                                  Edit
                                </button>
                                <button
                                  onClick={() => handleDeleteNote(note.id)}
                                  disabled={isDeleting === note.id}
                                  className="flex items-center gap-1 px-2 py-1 text-xs font-medium text-red-600 bg-red-50 rounded hover:bg-red-100 transition-colors disabled:opacity-50"
                                >
                                  <Trash2 className="w-3 h-3" />
                                  {isDeleting === note.id ? 'Deleting...' : 'Delete'}
                                </button>
                              </div>
                            </div>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </>
          )}
        </div>

        {/* Footer */}
        <div className="flex items-center justify-end p-4 border-t border-gray-200">
          <button
            onClick={handleClose}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
};

export default NotesModal;
