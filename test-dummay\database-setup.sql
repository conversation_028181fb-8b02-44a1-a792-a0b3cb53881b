-- TripXplo Quote Database Setup Script
-- Run this script in your Quote Supabase database to create the required tables

-- Create quote_mappings table for storing enhanced quote data
CREATE TABLE IF NOT EXISTS quote_mappings (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    quote_id UUID NOT NULL REFERENCES quotes(id) ON DELETE CASCADE,
    quote_name TEXT NOT NULL,
    customer_name TEXT NOT NULL,
    destination TEXT NOT NULL,
    
    -- JSONB fields for flexible data storage
    hotel_mappings JSONB DEFAULT '[]'::jsonb,
    vehicle_mappings JSONB DEFAULT '[]'::jsonb,
    additional_costs JSONB DEFAULT '{}'::jsonb,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Unique constraint to prevent duplicate mappings for same quote
    UNIQUE(quote_id)
);

-- Create index for faster lookups
CREATE INDEX IF NOT EXISTS idx_quote_mappings_quote_id ON quote_mappings(quote_id);
CREATE INDEX IF NOT EXISTS idx_quote_mappings_destination ON quote_mappings(destination);

-- Enable RLS (Row Level Security)
ALTER TABLE quote_mappings ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
CREATE POLICY "Users can view all quote mappings" ON quote_mappings
    FOR SELECT USING (true);

CREATE POLICY "Users can insert quote mappings" ON quote_mappings
    FOR INSERT WITH CHECK (true);

CREATE POLICY "Users can update quote mappings" ON quote_mappings
    FOR UPDATE USING (true);

CREATE POLICY "Users can delete quote mappings" ON quote_mappings
    FOR DELETE USING (true);

-- Add comments for documentation
COMMENT ON TABLE quote_mappings IS 'Enhanced quote data for family type pricing calculations';
COMMENT ON COLUMN quote_mappings.hotel_mappings IS 'Array of hotel cost mappings with extra adult, children, infant costs';
COMMENT ON COLUMN quote_mappings.vehicle_mappings IS 'Array of vehicle cost mappings with pricing models and multipliers';
COMMENT ON COLUMN quote_mappings.additional_costs IS 'Additional cost mappings for meals, ferry, activities, guide, parking/toll';

-- Create a function to help with table creation (optional)
CREATE OR REPLACE FUNCTION create_quote_mappings_table_if_not_exists()
RETURNS void
LANGUAGE plpgsql
AS $$
BEGIN
    -- Check if table exists
    IF NOT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'quote_mappings'
    ) THEN
        -- Create the table
        CREATE TABLE quote_mappings (
            id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
            quote_id UUID NOT NULL REFERENCES quotes(id) ON DELETE CASCADE,
            quote_name TEXT NOT NULL,
            customer_name TEXT NOT NULL,
            destination TEXT NOT NULL,
            hotel_mappings JSONB DEFAULT '[]'::jsonb,
            vehicle_mappings JSONB DEFAULT '[]'::jsonb,
            additional_costs JSONB DEFAULT '{}'::jsonb,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            UNIQUE(quote_id)
        );
        
        -- Create indexes
        CREATE INDEX idx_quote_mappings_quote_id ON quote_mappings(quote_id);
        CREATE INDEX idx_quote_mappings_destination ON quote_mappings(destination);
        
        -- Enable RLS
        ALTER TABLE quote_mappings ENABLE ROW LEVEL SECURITY;
        
        -- Create policies
        CREATE POLICY "Users can view all quote mappings" ON quote_mappings FOR SELECT USING (true);
        CREATE POLICY "Users can insert quote mappings" ON quote_mappings FOR INSERT WITH CHECK (true);
        CREATE POLICY "Users can update quote mappings" ON quote_mappings FOR UPDATE USING (true);
        CREATE POLICY "Users can delete quote mappings" ON quote_mappings FOR DELETE USING (true);
    END IF;
END;
$$;

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO anon, authenticated;
GRANT ALL ON quote_mappings TO anon, authenticated;
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO anon, authenticated;
