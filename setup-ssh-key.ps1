# Setup SSH Key Authentication for TripXplo CRM Server
# This script will copy your public key to the server and test the connection

Write-Host "🔐 Setting up SSH Key Authentication for TripXplo CRM Server" -ForegroundColor Green
Write-Host "================================================================" -ForegroundColor Green

$SERVER_IP = "*************"
$SERVER_USER = "root"
$PUBLIC_KEY_PATH = "$env:USERPROFILE\.ssh\tripxplo_crm_key.pub"
$PRIVATE_KEY_PATH = "$env:USERPROFILE\.ssh\tripxplo_crm_key"

# Check if keys exist
if (-not (Test-Path $PUBLIC_KEY_PATH)) {
    Write-Host "❌ Public key not found at $PUBLIC_KEY_PATH" -ForegroundColor Red
    Write-Host "Please run the key generation commands first." -ForegroundColor Yellow
    exit 1
}

Write-Host "✅ SSH keys found" -ForegroundColor Green
Write-Host "📁 Public key: $PUBLIC_KEY_PATH" -ForegroundColor Gray
Write-Host "📁 Private key: $PRIVATE_KEY_PATH" -ForegroundColor Gray

# Display the public key
Write-Host "`n📋 Your public key (copy this to the server):" -ForegroundColor Cyan
Write-Host "================================================" -ForegroundColor Cyan
$publicKey = Get-Content $PUBLIC_KEY_PATH
Write-Host $publicKey -ForegroundColor Yellow
Write-Host "================================================" -ForegroundColor Cyan

Write-Host "`n🚀 Now you need to copy this key to the server. Choose an option:" -ForegroundColor Green
Write-Host "1. Automatic setup (requires server password)" -ForegroundColor White
Write-Host "2. Manual setup (copy-paste instructions)" -ForegroundColor White
Write-Host "3. Just show me the commands" -ForegroundColor White

$choice = Read-Host "`nEnter your choice (1, 2, or 3)"

switch ($choice) {
    "1" {
        Write-Host "`n🔄 Attempting automatic setup..." -ForegroundColor Cyan
        
        # Create the command to add the key to authorized_keys
        $sshCommand = @"
mkdir -p ~/.ssh && \
echo '$publicKey' >> ~/.ssh/authorized_keys && \
chmod 700 ~/.ssh && \
chmod 600 ~/.ssh/authorized_keys && \
echo 'SSH key added successfully!'
"@

        Write-Host "📤 Copying public key to server..." -ForegroundColor Yellow
        Write-Host "You will be prompted for the server password..." -ForegroundColor Gray
        
        # Execute the command on the server
        echo $sshCommand | ssh "${SERVER_USER}@${SERVER_IP}" "bash -s"
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Public key copied successfully!" -ForegroundColor Green
        } else {
            Write-Host "❌ Failed to copy public key. Try manual setup." -ForegroundColor Red
            exit 1
        }
    }
    
    "2" {
        Write-Host "`n📋 Manual Setup Instructions:" -ForegroundColor Cyan
        Write-Host "==============================" -ForegroundColor Cyan
        Write-Host "1. SSH into your server:" -ForegroundColor White
        Write-Host "   ssh root@$SERVER_IP" -ForegroundColor Gray
        Write-Host ""
        Write-Host "2. Create .ssh directory (if it doesn't exist):" -ForegroundColor White
        Write-Host "   mkdir -p ~/.ssh" -ForegroundColor Gray
        Write-Host ""
        Write-Host "3. Add your public key to authorized_keys:" -ForegroundColor White
        Write-Host "   echo '$publicKey' >> ~/.ssh/authorized_keys" -ForegroundColor Gray
        Write-Host ""
        Write-Host "4. Set proper permissions:" -ForegroundColor White
        Write-Host "   chmod 700 ~/.ssh" -ForegroundColor Gray
        Write-Host "   chmod 600 ~/.ssh/authorized_keys" -ForegroundColor Gray
        Write-Host ""
        Write-Host "5. Exit the server and test the connection" -ForegroundColor White
        Write-Host ""
        Write-Host "Press Enter when you've completed these steps..." -ForegroundColor Yellow
        Read-Host
    }
    
    "3" {
        Write-Host "`n📋 Commands to run on the server:" -ForegroundColor Cyan
        Write-Host "===================================" -ForegroundColor Cyan
        Write-Host "mkdir -p ~/.ssh" -ForegroundColor Gray
        Write-Host "echo '$publicKey' >> ~/.ssh/authorized_keys" -ForegroundColor Gray
        Write-Host "chmod 700 ~/.ssh" -ForegroundColor Gray
        Write-Host "chmod 600 ~/.ssh/authorized_keys" -ForegroundColor Gray
        Write-Host ""
        return
    }
    
    default {
        Write-Host "❌ Invalid choice. Exiting." -ForegroundColor Red
        exit 1
    }
}

# Test the SSH connection
Write-Host "`n🧪 Testing SSH connection..." -ForegroundColor Cyan
Write-Host "Attempting to connect using the private key..." -ForegroundColor Gray

$testResult = ssh -i $PRIVATE_KEY_PATH -o ConnectTimeout=10 -o StrictHostKeyChecking=no "${SERVER_USER}@${SERVER_IP}" "echo 'SSH connection successful!'"

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ SSH key authentication is working!" -ForegroundColor Green
    Write-Host "🎉 You can now use passwordless SSH and SCP!" -ForegroundColor Green
    
    Write-Host "`n🚀 Quick test commands:" -ForegroundColor Cyan
    Write-Host "ssh tripxplo-crm" -ForegroundColor Gray
    Write-Host "scp file.txt tripxplo-crm:/tmp/" -ForegroundColor Gray
    
} else {
    Write-Host "❌ SSH key authentication failed." -ForegroundColor Red
    Write-Host "Please check the manual setup steps above." -ForegroundColor Yellow
}

Write-Host "`n📝 Next Steps:" -ForegroundColor Green
Write-Host "1. Test SSH connection: ssh tripxplo-crm" -ForegroundColor White
Write-Host "2. Run deployment: .\deploy-crm-subdomain.ps1" -ForegroundColor White
Write-Host "3. Enjoy passwordless deployments! 🎉" -ForegroundColor White 