# TripXplo CRM & Quote Environment Configuration

This document explains how the environment variables are configured for the TripXplo CRM and Quote components.

## Overview

The application uses two different Supabase instances:
1. One for the CRM functionality
2. One for the Quote functionality

To make both components work together, we've implemented a configuration system that loads the appropriate environment variables for each component.

## Environment Variables

### CRM Supabase Configuration
```
VITE_SUPABASE_URL_CRM=https://tlfwcnikdlwoliqzavxj.supabase.co
VITE_SUPABASE_ANON_KEY_CRM=your-crm-anon-key
```

### Quote Supabase Configuration
```
VITE_SUPABASE_URL_QUOTE=https://lkqbrlrmrsnbtkoryazq.supabase.co
VITE_SUPABASE_ANON_KEY_QUOTE=your-quote-anon-key
```

### Email Configuration for Quote
```
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-email-password
```

### General Configuration
```
VITE_AUTH_TIMEOUT=10000
NODE_ENV=development
```

### Legacy Variables (for backward compatibility)
```
VITE_SUPABASE_URL=https://tlfwcnikdlwoliqzavxj.supabase.co
VITE_SUPABASE_ANON_KEY=your-crm-anon-key
REACT_APP_SUPABASE_URL=https://lkqbrlrmrsnbtkoryazq.supabase.co
REACT_APP_SUPABASE_ANON_KEY=your-quote-anon-key
```

## How It Works

1. The application uses a central configuration manager (`src/config/env.ts`) to load the appropriate environment variables for each component.

2. The CRM component uses the CRM Supabase client (`src/lib/supabaseClient.ts`), which loads the CRM-specific environment variables.

3. The Quote component uses the Quote Supabase client (`src/quotes/lib/supabaseClient.ts`), which loads the Quote-specific environment variables.

4. If the specific environment variables are not set, the application falls back to the legacy variables for backward compatibility.

## Troubleshooting

If you encounter issues with the environment configuration:

1. Make sure all required environment variables are set in your `.env` file.
2. Check the browser console for any error messages related to Supabase configuration.
3. Verify that the correct Supabase client is being used for each component.

## Security Notes

- Keep your `.env` file secure and never commit it to version control.
- Use the `.env.example` file as a template for setting up your environment variables.
- Regularly rotate your Supabase anon keys for security.
