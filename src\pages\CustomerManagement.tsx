import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import CustomerQuoteManagement from '../components/CustomerQuoteManagement';
import { toast } from 'react-hot-toast';

const CustomerManagement: React.FC = () => {
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState<'quotes' | 'leads'>('quotes');

  const handleQuoteSelect = (quoteId: string) => {
    // Navigate to quotes page with the selected quote
    sessionStorage.setItem('selectedQuoteId', quoteId);
    navigate('/quotes');
    toast.success('Opening quote in editor...');
  };

  // const handleCreateLead = (customerData: any) => {
  //   // Navigate to lead entry with pre-filled customer data
  //   sessionStorage.setItem('prefilledCustomerData', JSON.stringify(customerData));
  //   navigate('/lead-entry');
  //   toast.success('Creating new lead...');
  // };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-col sm:flex-row justify-between items-center py-6">
            <div className="flex items-center mb-4 sm:mb-0">
              <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">Customer Management</h1>
            </div>
            <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-4 w-full sm:w-auto">
              <button
                onClick={() => navigate('/quotes')}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                Create New Quote
              </button>
              <button
                onClick={() => navigate('/lead-entry')}
                className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500"
              >
                Create New Lead
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Navigation Tabs */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex flex-wrap sm:flex-nowrap space-x-8" aria-label="Tabs">
            <button
              onClick={() => setActiveTab('quotes')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'quotes'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Quote Management
            </button>
            <button
              onClick={() => setActiveTab('leads')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'leads'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Lead Integration
            </button>
          </nav>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        {activeTab === 'quotes' && (
          <div>
            <CustomerQuoteManagement onQuoteSelect={handleQuoteSelect} />
          </div>
        )}

        {activeTab === 'leads' && (
          <div className="bg-white rounded-lg shadow-lg p-4 sm:p-6">
            <div className="text-center py-6 sm:py-8">
              <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-blue-100 mb-4">
                <svg className="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">Lead Integration</h3>
              <p className="text-gray-600 mb-6">
                Connect quotes with leads using mobile number mapping. When you create a quote with a mobile number, 
                it will automatically link to existing leads in the Kanban board.
              </p>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 max-w-4xl mx-auto">
                <div className="border border-gray-200 rounded-lg p-4">
                  <h4 className="font-semibold text-gray-900 mb-2">📊 Leads Kanban Board</h4>
                  <p className="text-sm text-gray-600 mb-4">
                    Manage your leads with drag-and-drop interface. Customer mobile numbers 
                    from quotes will automatically sync with lead data.
                  </p>
                  <button
                    onClick={() => navigate('/leads-kanban')}
                    className="w-full px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700"
                  >
                    Open Leads Kanban
                  </button>
                </div>

                <div className="border border-gray-200 rounded-lg p-4">
                  <h4 className="font-semibold text-gray-900 mb-2">🔗 Quote-Lead Sync</h4>
                  <p className="text-sm text-gray-600 mb-4">
                    When you save a quote with a mobile number, the system will:
                  </p>
                  <ul className="text-xs text-gray-600 text-left mb-4 space-y-1">
                    <li>• Find matching leads by mobile number</li>
                    <li>• Update lead status to "QUOTE SENT"</li>
                    <li>• Enable WhatsApp messaging</li>
                    <li>• Group all quotes for easy management</li>
                  </ul>
                  <button
                    onClick={() => {
                      toast.success('This feature will be auto-enabled when you save quotes with mobile numbers!');
                    }}
                    className="w-full px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
                  >
                    Auto-Enabled
                  </button>
                </div>
              </div>

              <div className="mt-8 p-4 bg-blue-50 rounded-lg border border-blue-200">
                <h4 className="font-semibold text-blue-900 mb-2">💡 How It Works</h4>
                <div className="text-sm text-blue-800 space-y-2">
                  <p><strong>1. Create Quote:</strong> Add customer mobile number in the quote form</p>
                  <p><strong>2. Auto-Mapping:</strong> System finds quotes by mobile number</p>
                  <p><strong>3. WhatsApp Ready:</strong> Send messages directly from customer list</p>
                  <p><strong>4. Lead Updates:</strong> Automatically sync with Kanban board status</p>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default CustomerManagement;
