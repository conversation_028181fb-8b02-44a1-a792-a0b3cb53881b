# Check API Server Status on Linode
# This script checks if the API server is running properly

Write-Host "🔍 Checking API Server Status..." -ForegroundColor Green
Write-Host "=================================" -ForegroundColor Green

$SERVER_IP = "*************"
$SERVER_USER = "root"

Write-Host "📡 Connecting to server..." -ForegroundColor Cyan

# Check PM2 status
Write-Host "📊 PM2 Process Status:" -ForegroundColor Yellow
ssh "${SERVER_USER}@${SERVER_IP}" "pm2 status"

Write-Host ""
Write-Host "🌐 Testing API Endpoints:" -ForegroundColor Yellow

# Test website
Write-Host "Testing website..." -ForegroundColor Gray
ssh "${SERVER_USER}@${SERVER_IP}" "curl -I https://family.tripxplo.com"

Write-Host ""
Write-Host "Testing API endpoint..." -ForegroundColor Gray
ssh "${SERVER_USER}@${SERVER_IP}" "curl -I https://family.tripxplo.com/api/submit-contact-details"

Write-Host ""
Write-Host "📋 Server Logs (last 20 lines):" -ForegroundColor Yellow
ssh "${SERVER_USER}@${SERVER_IP}" "pm2 logs family-api --lines 20"

Write-Host ""
Write-Host "✅ Status check completed!" -ForegroundColor Green
