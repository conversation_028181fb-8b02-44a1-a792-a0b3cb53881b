# Production API Fix Guide - family.tripxplo.com

## 🚨 Problem Description

**Error:** 405 (Method Not Allowed) when submitting contact form on https://family.tripxplo.com
**Cause:** The production server is serving static files only, but the Node.js API server is not running
**Solution:** Deploy and configure the Node.js API server with proper Nginx proxy

---

## 🔧 Quick Fix (Automated)

### Option 1: Using Bash Script (Linux/Mac/WSL)
```bash
# Make script executable
chmod +x fix-production-api.sh

# Run the fix
./fix-production-api.sh
```

### Option 2: Using PowerShell Script (Windows)
```powershell
# Run the PowerShell script
.\fix-production-api.ps1
```

---

## 🛠️ Manual Fix Steps

If the automated scripts don't work, follow these manual steps:

### Step 1: Upload API Files
```bash
# Upload API server files to production
scp -r family-tripxplo-production/api/* root@*************:/var/www/family/api/
```

### Step 2: Install Dependencies on Server
```bash
# SSH into the server
ssh root@*************

# Navigate to API directory
cd /var/www/family/api

# Install Node.js (if not installed)
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
apt-get install -y nodejs

# Install PM2 (process manager)
npm install -g pm2

# Install API dependencies
npm install
```

### Step 3: Create Environment File
```bash
# Create .env file in /var/www/family/api/
cat > .env << EOF
NODE_ENV=production
PORT=3000
CORS_ORIGIN=https://family.tripxplo.com

CRM_DB_URL=https://tlfwcnikdlwoliqzavxj.supabase.co
CRM_ANON_KEY=your-actual-crm-key

QUOTE_DB_URL=https://lkqbrlrmrsnbtkoryazq.supabase.co
QUOTE_ANON_KEY=your-actual-quote-key
EOF
```

### Step 4: Start API Server
```bash
# Start the API server with PM2
pm2 start server.js --name family-api
pm2 save
pm2 startup
```

### Step 5: Configure Nginx Proxy
```bash
# Create Nginx configuration
cat > /etc/nginx/sites-available/family.tripxplo.com << 'EOF'
server {
    listen 80;
    server_name family.tripxplo.com www.family.tripxplo.com;
    
    root /var/www/family;
    index index.html index.htm;
    
    # Proxy API requests to Node.js server
    location /api/ {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # CORS headers
        add_header Access-Control-Allow-Origin "https://family.tripxplo.com" always;
        add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS" always;
        add_header Access-Control-Allow-Headers "Content-Type, Authorization" always;
        add_header Access-Control-Allow-Credentials "true" always;
    }
    
    # Static files
    location / {
        try_files $uri $uri/ /index.html;
    }
}
EOF

# Enable the site and reload Nginx
ln -sf /etc/nginx/sites-available/family.tripxplo.com /etc/nginx/sites-enabled/
nginx -t && systemctl reload nginx
```

---

## 🧪 Testing the Fix

### 1. Check API Server Status
```bash
ssh root@************* 'pm2 status family-api'
```

### 2. Test API Endpoint Directly
```bash
ssh root@************* 'curl -X POST http://localhost:3000/api/submit-contact-details -H "Content-Type: application/json" -d "{\"test\":\"data\"}"'
```

### 3. Test from Browser
1. Visit https://family.tripxplo.com
2. Open browser developer tools (F12)
3. Go to Network tab
4. Try submitting the contact form
5. Check if `/api/submit-contact-details` returns 200 instead of 405

---

## 🔍 Troubleshooting

### Issue: API server won't start
**Check logs:**
```bash
ssh root@************* 'pm2 logs family-api'
```

**Common fixes:**
- Check if port 3000 is available: `netstat -tulpn | grep :3000`
- Verify environment variables in `.env` file
- Check Node.js version: `node --version` (should be 18+)

### Issue: 502 Bad Gateway
**Cause:** Nginx can't connect to API server
**Fix:**
```bash
# Check if API server is running
pm2 status family-api

# Restart API server
pm2 restart family-api

# Check Nginx configuration
nginx -t
```

### Issue: CORS errors
**Cause:** CORS headers not properly configured
**Fix:** Ensure Nginx configuration includes CORS headers (see Step 5 above)

### Issue: Database connection errors
**Cause:** Invalid Supabase keys or URLs
**Fix:** Verify the database credentials in `.env` file

---

## 📋 Verification Checklist

After applying the fix, verify:

- [ ] API server is running: `pm2 status family-api`
- [ ] Nginx is configured: `nginx -t`
- [ ] Port 3000 is listening: `netstat -tulpn | grep :3000`
- [ ] Contact form submits without 405 error
- [ ] Browser console shows successful API calls
- [ ] Database records are created in `public_family_quotes` table

---

## 🚀 Success Indicators

When the fix is successful, you should see:

1. **Browser Console:** No more 405 errors
2. **Network Tab:** `/api/submit-contact-details` returns 200 status
3. **Contact Form:** Shows success message after submission
4. **Database:** New records appear in `public_family_quotes` table
5. **Server Logs:** API requests logged in PM2 logs

---

## 📞 Support

If you continue to experience issues:

1. **Check server logs:** `ssh root@************* 'pm2 logs family-api --lines 50'`
2. **Check Nginx logs:** `ssh root@************* 'tail -f /var/log/nginx/error.log'`
3. **Verify server status:** `ssh root@************* 'systemctl status nginx'`

The fix addresses the core issue: the production server was only serving static files but missing the Node.js API server that handles the `/api/submit-contact-details` endpoint.
