import React, { useState, useEffect, useRef } from 'react';
import { ChevronDown } from 'lucide-react';
import { CURRENCIES, getActiveCurrency, setActiveCurrency } from '../utils/formatters';

const CurrencySelector: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedCurrency, setSelectedCurrency] = useState(getActiveCurrency());
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Force update the currency state when component mounts
  useEffect(() => {
    setSelectedCurrency(getActiveCurrency());
  }, []);

  // Listen for currency changes from other components
  useEffect(() => {
    const handleCurrencyChange = (_event: Event) => {
      // Force a refresh of the selected currency
      setTimeout(() => {
        setSelectedCurrency(getActiveCurrency());
      }, 0);
    };

    window.addEventListener('currencyChanged', handleCurrencyChange);
    return () => window.removeEventListener('currencyChanged', handleCurrencyChange);
  }, []);

  // Handle outside clicks to close dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };
    
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Handle currency changes
  const handleCurrencyChange = (currencyCode: string) => {
    if (setActiveCurrency(currencyCode)) {
      // Update local state immediately
      setSelectedCurrency(CURRENCIES[currencyCode]);
      setIsOpen(false);
      
      // Trigger a custom event to notify app of currency change
      const event = new CustomEvent('currencyChanged', { 
        detail: { currency: currencyCode } 
      });
      window.dispatchEvent(event);

      // Force a cache refresh
      localStorage.removeItem('currencyCache');
      localStorage.setItem('currencyCache', Date.now().toString());
    }
  };

  return (
    <div className="relative" ref={dropdownRef}>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center justify-between px-3 py-1.5 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
        data-testid="currency-selector"
      >
        <span className="flex items-center">
          <span className="mr-1">{selectedCurrency?.symbol || '₹'}</span>
          <span>{selectedCurrency?.code || 'INR'}</span>
        </span>
        <ChevronDown className="w-4 h-4 ml-2" />
      </button>

      {isOpen && (
        <div className="absolute right-0 z-50 mt-1 w-48 bg-white border border-gray-200 rounded-md shadow-lg">
          <div className="py-1">
            {Object.entries(CURRENCIES).map(([code, currency]) => (
              <button
                key={code}
                onClick={() => handleCurrencyChange(code)}
                className={`w-full px-4 py-2 text-sm text-left flex items-center justify-between hover:bg-gray-100 ${
                  selectedCurrency?.code === code ? 'bg-green-50 text-green-700' : 'text-gray-700'
                }`}
                data-testid={`currency-option-${code}`}
              >
                <span className="flex items-center">
                  <span className="mr-2">{currency.symbol}</span>
                  <span>{currency.code}</span>
                </span>
                <span className="text-xs text-gray-500">{currency.name}</span>
              </button>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default CurrencySelector; 