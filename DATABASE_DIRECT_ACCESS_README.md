# Direct Database Access for EMI Management

This document explains how the EMI transactions and visited customers now fetch data directly from the database instead of using mock data.

## Overview

The PrepaidEMIManagement component now supports two modes:
1. **Direct Database Access** - For production (family.tripxplo.com) and testing
2. **API Server Access** - For local development with API server

## Key Changes Made

### 1. Enhanced Database Service (`src/utils/databaseService.ts`)

- **fetchEMITransactions()**: Now fetches from `prepaid_emi_transactions` table and joins with `public_family_quotes` for customer data
- **fetchVisitedCustomers()**: Fetches from `public_family_quotes` table with proper formatting
- **shouldUseDirectDatabase()**: Determines whether to use direct DB or API based on environment
- **Debug functions**: Added debugging utilities for testing

### 2. Updated PrepaidEMIManagement Component

- Removed fallback to mock data
- Added debug panel for testing
- Better error handling with user-friendly messages
- Proper loading states

### 3. Test Data Utilities (`src/utils/insertTestData.ts`)

- Functions to insert sample data for testing
- Functions to clear test data
- Available globally via browser console

## How to Use

### For Production (family.tripxplo.com)
- Direct database access is automatically enabled
- Data is fetched directly from Supabase tables

### For Local Testing
1. Enable direct database access:
   ```javascript
   // In browser console
   tripxploDebug.enableDirectDatabaseForTesting();
   ```

2. Insert test data:
   ```javascript
   // In browser console
   tripxploTestData.insertAllSampleData();
   ```

3. Check database status:
   ```javascript
   // In browser console
   tripxploDebug.debugDatabaseStatus();
   ```

### Using the Debug Panel
1. Open the PrepaidEMIManagement page
2. Click the "Debug" button in the header
3. Use the debug panel buttons:
   - **Check Database Status**: Logs table accessibility to console
   - **Insert Test Data**: Adds sample EMI transactions and customers
   - **Clear Test Data**: Removes test data
   - **Check Environment**: Shows current configuration

## Database Tables Used

### prepaid_emi_transactions
- Stores EMI transaction records
- Links to customers via `customer_id` (references `public_family_quotes.id`)

### public_family_quotes
- Stores customer quote/contact information
- Used for visited customers display
- Referenced by EMI transactions

### emi_payment_history
- Stores individual payment records
- Links to transactions via `transaction_id`

## Troubleshooting

### No Data Showing
1. Check browser console for errors
2. Use debug panel to check database status
3. Insert test data if tables are empty
4. Verify network connectivity to Supabase

### Environment Issues
1. Check if direct database mode is enabled correctly
2. Verify Supabase credentials and URL
3. Check browser console for configuration logs

### Console Commands

```javascript
// Enable direct database for testing
tripxploDebug.enableDirectDatabaseForTesting();

// Disable direct database (use API server)
tripxploDebug.disableDirectDatabaseForTesting();

// Check current configuration
tripxploDebug.shouldUseDirectDatabase();

// Debug database connectivity
tripxploDebug.debugDatabaseStatus();

// Insert test data
tripxploTestData.insertAllSampleData();

// Clear test data
tripxploTestData.clearAllTestData();

// Fetch data manually
tripxploDebug.fetchEMITransactions().then(console.log);
tripxploDebug.fetchVisitedCustomers().then(console.log);
```

## Data Flow

1. **Component Mount**: Checks environment and loads data
2. **Direct DB Mode**: Calls Supabase directly via `databaseService.ts`
3. **API Mode**: Makes HTTP requests to local API server
4. **Error Handling**: Shows user-friendly messages instead of falling back to mock data
5. **Debug Mode**: Provides tools for testing and troubleshooting

## Benefits

- **Real Data**: No more mock data, always uses actual database records
- **Flexible**: Works in both production and development environments
- **Debuggable**: Comprehensive debugging tools and logging
- **Testable**: Easy to insert and clear test data
- **Reliable**: Better error handling and user feedback
