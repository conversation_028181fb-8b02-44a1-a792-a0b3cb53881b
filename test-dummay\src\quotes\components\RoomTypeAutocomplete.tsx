import React, { useState, useEffect, useRef } from 'react';

const ROOM_TYPES = [
  'Standard',
  'Deluxe',
  'Superior',
  'Suite',
  'Executive',
  'Premium',
  'Luxury',
  'Family',
  'Ocean View',
  'Mountain View',
  'Garden View',
  'Pool View',
  'Base Category',
  'Super Deluxe',
  'Villa',
  'Cottage',
  'Chalet',
  'Bungalow',
  'Penthouse',
  'Presidential Suite'
];

interface RoomTypeAutocompleteProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  className?: string;
}

export const RoomTypeAutocomplete: React.FC<RoomTypeAutocompleteProps> = ({
  value,
  onChange,
  placeholder = "Enter room type",
  className = ""
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [suggestions, setSuggestions] = useState<string[]>([]);
  const wrapperRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (wrapperRef.current && !wrapperRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleInputChange = (inputValue: string) => {
    onChange(inputValue);
    if (inputValue.trim() === '') {
      setSuggestions(ROOM_TYPES);
    } else {
      const filtered = ROOM_TYPES.filter(type =>
        type.toLowerCase().includes(inputValue.toLowerCase())
      );
      setSuggestions(filtered);
    }
    setIsOpen(true);
  };

  const handleFocus = () => {
    setSuggestions(ROOM_TYPES);
    setIsOpen(true);
  };

  return (
    <div ref={wrapperRef} className="relative">
      <input
        type="text"
        value={value}
        onChange={(e) => handleInputChange(e.target.value)}
        onFocus={handleFocus}
        placeholder={placeholder}
        className={`w-full px-2 py-1.5 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#00B69B] focus:border-[#00B69B] ${className}`}
      />
      {isOpen && (
        <div className="absolute z-[9999] w-full mt-1 bg-white border border-gray-200 rounded-md shadow-xl">
          <div className="max-h-[300px] overflow-y-auto">
            {suggestions.map((suggestion, index) => (
              <div
                key={index}
                onClick={() => {
                  onChange(suggestion);
                  setIsOpen(false);
                }}
                className="px-4 py-2.5 hover:bg-[#00B69B]/10 cursor-pointer text-sm border-b border-gray-100 last:border-0"
              >
                {suggestion}
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}; 