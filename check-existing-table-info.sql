-- Comprehensive Table Information Check for TripXplo Quote Database
-- Run this in your Quote Supabase SQL Editor: https://lkqbrlrmrsnbtkoryazq.supabase.co

-- ============================================================================
-- 1. CHECK IF quote_mappings TABLE EXISTS
-- ============================================================================
SELECT 
    'TABLE EXISTENCE CHECK' as check_type,
    CASE 
        WHEN EXISTS (
            SELECT FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_name = 'quote_mappings'
        ) THEN '✅ quote_mappings table EXISTS'
        ELSE '❌ quote_mappings table DOES NOT EXIST'
    END as result;

-- ============================================================================
-- 2. TABLE STRUCTURE AND COLUMNS (if table exists)
-- ============================================================================
SELECT 
    'COLUMN INFORMATION' as info_type,
    column_name,
    data_type,
    is_nullable,
    column_default,
    character_maximum_length,
    ordinal_position
FROM information_schema.columns 
WHERE table_name = 'quote_mappings' 
AND table_schema = 'public'
ORDER BY ordinal_position;

-- ============================================================================
-- 3. CHECK INDEXES
-- ============================================================================
SELECT 
    'INDEX INFORMATION' as info_type,
    indexname,
    indexdef
FROM pg_indexes 
WHERE tablename = 'quote_mappings'
AND schemaname = 'public';

-- ============================================================================
-- 4. CHECK CONSTRAINTS
-- ============================================================================
SELECT 
    'CONSTRAINT INFORMATION' as info_type,
    constraint_name,
    constraint_type,
    table_name
FROM information_schema.table_constraints 
WHERE table_name = 'quote_mappings'
AND table_schema = 'public';

-- ============================================================================
-- 5. CHECK RLS (Row Level Security) STATUS
-- ============================================================================
SELECT 
    'RLS STATUS' as info_type,
    schemaname,
    tablename,
    rowsecurity as rls_enabled,
    CASE 
        WHEN rowsecurity THEN '✅ RLS is ENABLED'
        ELSE '❌ RLS is DISABLED'
    END as rls_status
FROM pg_tables 
WHERE tablename = 'quote_mappings' 
AND schemaname = 'public';

-- ============================================================================
-- 6. CHECK RLS POLICIES
-- ============================================================================
SELECT 
    'RLS POLICIES' as info_type,
    policyname,
    cmd as operation,
    permissive,
    roles,
    qual as condition_check,
    with_check
FROM pg_policies 
WHERE tablename = 'quote_mappings'
ORDER BY cmd;

-- ============================================================================
-- 7. CHECK TABLE PERMISSIONS
-- ============================================================================
SELECT 
    'TABLE PERMISSIONS' as info_type,
    grantee,
    privilege_type,
    is_grantable
FROM information_schema.table_privileges 
WHERE table_name = 'quote_mappings' 
AND table_schema = 'public'
ORDER BY grantee, privilege_type;

-- ============================================================================
-- 8. CHECK FOREIGN KEY RELATIONSHIPS
-- ============================================================================
SELECT 
    'FOREIGN KEY INFO' as info_type,
    tc.constraint_name,
    tc.table_name,
    kcu.column_name,
    ccu.table_name AS foreign_table_name,
    ccu.column_name AS foreign_column_name
FROM information_schema.table_constraints AS tc 
JOIN information_schema.key_column_usage AS kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage AS ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
WHERE tc.constraint_type = 'FOREIGN KEY' 
AND tc.table_name = 'quote_mappings';

-- ============================================================================
-- 9. CHECK SAMPLE DATA (if table exists and has data)
-- ============================================================================
DO $$
BEGIN
    IF EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'quote_mappings'
    ) THEN
        -- Check if table has data
        PERFORM 1 FROM quote_mappings LIMIT 1;
        IF FOUND THEN
            RAISE NOTICE 'Table has data - showing sample records';
        ELSE
            RAISE NOTICE 'Table exists but is empty';
        END IF;
    ELSE
        RAISE NOTICE 'Table does not exist - cannot check data';
    END IF;
END $$;

-- Show sample data if table exists (limit to 3 records for safety)
SELECT 
    'SAMPLE DATA' as info_type,
    id,
    quote_id,
    quote_name,
    customer_name,
    destination,
    created_at,
    updated_at,
    jsonb_array_length(hotel_mappings) as hotel_count,
    jsonb_array_length(vehicle_mappings) as vehicle_count
FROM quote_mappings 
LIMIT 3;

-- ============================================================================
-- 10. CHECK RELATED TABLES (quotes table that quote_mappings might reference)
-- ============================================================================
SELECT 
    'RELATED TABLES' as info_type,
    table_name,
    CASE 
        WHEN table_name = 'quotes' THEN '✅ quotes table exists'
        WHEN table_name = 'hotel_rows' THEN '✅ hotel_rows table exists'
        WHEN table_name = 'costs' THEN '✅ costs table exists'
        ELSE '✅ ' || table_name || ' table exists'
    END as status
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN ('quotes', 'hotel_rows', 'costs', 'family_types')
ORDER BY table_name;

-- ============================================================================
-- 11. SUMMARY REPORT
-- ============================================================================
SELECT 
    'SUMMARY REPORT' as report_type,
    CASE 
        WHEN EXISTS (
            SELECT FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_name = 'quote_mappings'
        ) THEN 'quote_mappings table EXISTS'
        ELSE 'quote_mappings table MISSING'
    END as table_status,
    
    CASE 
        WHEN EXISTS (
            SELECT FROM pg_tables 
            WHERE tablename = 'quote_mappings' 
            AND schemaname = 'public' 
            AND rowsecurity = true
        ) THEN 'RLS ENABLED'
        WHEN EXISTS (
            SELECT FROM pg_tables 
            WHERE tablename = 'quote_mappings' 
            AND schemaname = 'public'
        ) THEN 'RLS DISABLED'
        ELSE 'TABLE MISSING'
    END as rls_status,
    
    COALESCE((
        SELECT COUNT(*)::text 
        FROM pg_policies 
        WHERE tablename = 'quote_mappings'
    ), '0') as policy_count,
    
    CASE 
        WHEN EXISTS (
            SELECT FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_name = 'quotes'
        ) THEN 'quotes table available'
        ELSE 'quotes table MISSING'
    END as quotes_table_status;

-- ============================================================================
-- 12. RECOMMENDED ACTIONS
-- ============================================================================
SELECT 
    'RECOMMENDED ACTIONS' as action_type,
    CASE 
        WHEN NOT EXISTS (
            SELECT FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_name = 'quote_mappings'
        ) THEN '1. CREATE quote_mappings table using create-quote-mappings-table.sql'
        
        WHEN EXISTS (
            SELECT FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_name = 'quote_mappings'
        ) AND NOT EXISTS (
            SELECT FROM pg_tables 
            WHERE tablename = 'quote_mappings' 
            AND schemaname = 'public' 
            AND rowsecurity = true
        ) THEN '1. ENABLE RLS on quote_mappings table'
        
        WHEN (
            SELECT COUNT(*) 
            FROM pg_policies 
            WHERE tablename = 'quote_mappings'
        ) = 0 THEN '1. CREATE RLS policies for quote_mappings table'
        
        ELSE '✅ Table appears to be properly configured'
    END as recommendation;
