<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Package Card Generator - TripXplo</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        button:hover {
            background: #0056b3;
        }
        .preview-area {
            text-align: center;
            margin: 20px 0;
        }
        canvas {
            border: 1px solid #ddd;
            border-radius: 10px;
            max-width: 100%;
        }
        .success {
            background: #d4edda;
            color: #155724;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 Package Card Generator Test</h1>
        <p>Test the package card generation functionality</p>

        <div class="test-section">
            <h3>Test Package Cards</h3>
            <button onclick="testAndamanCard()">🏝️ Generate Andaman Card</button>
            <button onclick="testKashmirCard()">🏔️ Generate Kashmir Card</button>
            <button onclick="testGoaCard()">🏖️ Generate Goa Card</button>
            <button onclick="testCustomCard()">🎯 Generate Custom Card</button>
        </div>

        <div class="preview-area" id="previewArea">
            <p>Click a button above to generate a package card</p>
        </div>

        <div class="test-section">
            <h3>Actions</h3>
            <button onclick="downloadCard()" id="downloadBtn" disabled>📥 Download Card</button>
            <button onclick="shareCard()" id="shareBtn" disabled>📤 Share Card</button>
        </div>

        <div id="results"></div>
    </div>

    <script src="js/packageCardGenerator.js"></script>
    <script>
        let cardGenerator = null;
        let currentCanvas = null;
        let currentCardData = null;

        function addResult(message, type = 'success') {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = type;
            div.textContent = message;
            results.appendChild(div);
        }

        function initGenerator() {
            if (!cardGenerator) {
                cardGenerator = new PackageCardGenerator();
                addResult('Package card generator initialized');
            }
        }

        async function testAndamanCard() {
            initGenerator();
            
            const cardData = {
                destination: 'Andaman',
                duration: '5N/6D',
                image: './img/rectangle-14-3.png',
                travelMode: 'FLIGHT',
                nights: 5,
                mealPlan: 'Breakfast + Dinner',
                family: {
                    name: 'FAMILY NEST',
                    composition: '2 ADULTS + 2 CHILDREN (6-11 YRS)'
                },
                emi: {
                    months: 10
                },
                price: 3899
            };

            await generateCard(cardData);
        }

        async function testKashmirCard() {
            initGenerator();
            
            const cardData = {
                destination: 'Kashmir',
                duration: '6N/7D',
                image: './img/rectangle-14.png',
                travelMode: 'TRAIN (3AC)',
                nights: 6,
                mealPlan: 'Breakfast included',
                family: {
                    name: 'STELLAR DUO',
                    composition: '2 ADULTS'
                },
                emi: {
                    months: 12
                },
                price: 4299
            };

            await generateCard(cardData);
        }

        async function testGoaCard() {
            initGenerator();
            
            const cardData = {
                destination: 'Goa',
                duration: '4N/5D',
                image: './img/rectangle-14-2.png',
                travelMode: 'FLIGHT',
                nights: 4,
                mealPlan: 'Breakfast + Dinner',
                family: {
                    name: 'BABY BLISS',
                    composition: '2 ADULTS + 1 CHILD (BELOW 5 YRS)'
                },
                emi: {
                    months: 6
                },
                price: 2999
            };

            await generateCard(cardData);
        }

        async function testCustomCard() {
            initGenerator();

            const cardData = {
                destination: 'Munnar',
                duration: '3N/4D',
                image: './img/rectangle-14-4.png',
                travelMode: 'TRAIN (3AC)',
                nights: 3,
                mealPlan: 'Breakfast + Dinner',
                family: {
                    name: 'STELLAR TEEN DUO',
                    composition: '2 ADULTS + 1 CHILD (2-5 YRS) + 1 CHILDREN (6-11 YRS) + 1 TEENAGER (ABOVE 11 YRS)'
                },
                emi: {
                    months: 12
                },
                price: 4299
            };

            await generateCard(cardData);
        }

        async function generateCard(cardData) {
            try {
                addResult(`Generating ${cardData.destination} package card...`);
                
                const canvas = await cardGenerator.generatePackageCard(cardData);
                
                // Display the card
                const previewArea = document.getElementById('previewArea');
                previewArea.innerHTML = '<h4>Generated Package Card:</h4>';
                
                // Create a display canvas
                const displayCanvas = document.createElement('canvas');
                displayCanvas.width = canvas.width;
                displayCanvas.height = canvas.height;
                displayCanvas.style.width = '300px';
                displayCanvas.style.height = 'auto';
                
                const displayCtx = displayCanvas.getContext('2d');
                displayCtx.drawImage(canvas, 0, 0);
                
                previewArea.appendChild(displayCanvas);
                
                // Store for download/share
                currentCanvas = canvas;
                currentCardData = cardData;
                
                // Enable action buttons
                document.getElementById('downloadBtn').disabled = false;
                document.getElementById('shareBtn').disabled = false;
                
                addResult(`✅ ${cardData.destination} card generated successfully!`);
                
            } catch (error) {
                console.error('Error generating card:', error);
                addResult(`❌ Error generating ${cardData.destination} card: ${error.message}`, 'error');
            }
        }

        function downloadCard() {
            if (currentCanvas && currentCardData) {
                cardGenerator.downloadCard(currentCanvas, `${currentCardData.destination}-package-card.png`);
                addResult(`📥 ${currentCardData.destination} card downloaded!`);
            }
        }

        async function shareCard() {
            if (currentCanvas && currentCardData) {
                try {
                    await cardGenerator.shareCard(currentCanvas, currentCardData);
                    addResult(`📤 ${currentCardData.destination} card shared!`);
                } catch (error) {
                    addResult(`📤 ${currentCardData.destination} card download initiated (sharing not supported)`, 'error');
                }
            }
        }

        // Auto-test on page load
        window.addEventListener('load', () => {
            addResult('Package card test page loaded. Click buttons to test card generation.');
        });
    </script>
</body>
</html>
