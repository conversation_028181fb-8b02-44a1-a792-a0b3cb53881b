# Simple Direct Deployment for TripXplo CRM
Write-Host "🚀 Deploying TripXplo CRM to crm.tripxplo.com" -ForegroundColor Green
Write-Host "===============================================" -ForegroundColor Green

$SERVER_IP = "*************"
$SERVER_USER = "root"
$DOMAIN = "crm.tripxplo.com"

Write-Host "📦 Preparing server directory..." -ForegroundColor Cyan

# Step 1: Prepare server directory
& ssh "$SERVER_USER@$SERVER_IP" "mkdir -p /var/www/crm"
& ssh "$SERVER_USER@$SERVER_IP" "rm -rf /var/www/crm/*"

Write-Host "📤 Uploading built files..." -ForegroundColor Cyan

# Step 2: Upload files (using PowerShell compatible approach)
& scp -r .\dist\* "$SERVER_USER@$SERVER_IP`:/var/www/crm/"

if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Failed to upload files" -ForegroundColor Red
    exit 1
}

Write-Host "✅ Files uploaded successfully" -ForegroundColor Green

# Step 3: Create Nginx config locally
$nginxConfig = @"
server {
    listen 80;
    server_name crm.tripxplo.com www.crm.tripxplo.com;
    
    root /var/www/crm;
    index index.html index.htm;
    
    location / {
        try_files `$uri `$uri/ /index.html;
    }
    
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot|map)`$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Access-Control-Allow-Origin "*" always;
    add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS" always;
    add_header Access-Control-Allow-Headers "DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization" always;
    
    if (`$request_method = 'OPTIONS') {
        add_header Access-Control-Allow-Origin "*";
        add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS";
        add_header Access-Control-Allow-Headers "DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization";
        add_header Access-Control-Max-Age 1728000;
        add_header Content-Type "text/plain charset=UTF-8";
        add_header Content-Length 0;
        return 204;
    }
}
"@

$nginxConfig | Out-File -FilePath "crm.conf" -Encoding UTF8

Write-Host "🌐 Uploading Nginx configuration..." -ForegroundColor Cyan

# Step 4: Upload Nginx config
& scp "crm.conf" "$SERVER_USER@$SERVER_IP`:/etc/nginx/sites-available/$DOMAIN"

Write-Host "⚙️ Configuring server..." -ForegroundColor Cyan

# Step 5: Configure server (one command at a time)
& ssh "$SERVER_USER@$SERVER_IP" "chown -R www-data:www-data /var/www/crm"
& ssh "$SERVER_USER@$SERVER_IP" "chmod -R 755 /var/www/crm"
& ssh "$SERVER_USER@$SERVER_IP" "ln -sf /etc/nginx/sites-available/$DOMAIN /etc/nginx/sites-enabled/"
& ssh "$SERVER_USER@$SERVER_IP" "rm -f /etc/nginx/sites-enabled/default"

Write-Host "🧪 Testing Nginx configuration..." -ForegroundColor Cyan

# Step 6: Test and reload Nginx
& ssh "$SERVER_USER@$SERVER_IP" "nginx -t"

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ Nginx configuration is valid" -ForegroundColor Green
    & ssh "$SERVER_USER@$SERVER_IP" "systemctl reload nginx"
    Write-Host "✅ Nginx reloaded successfully" -ForegroundColor Green
} else {
    Write-Host "❌ Nginx configuration test failed" -ForegroundColor Red
    exit 1
}

Write-Host "🔒 Setting up SSL certificate..." -ForegroundColor Cyan

# Step 7: Setup SSL
& ssh "$SERVER_USER@$SERVER_IP" "which certbot"

if ($LASTEXITCODE -eq 0) {
    Write-Host "📜 Certbot found, obtaining SSL certificate..." -ForegroundColor Cyan
    & ssh "$SERVER_USER@$SERVER_IP" "certbot --nginx -d $DOMAIN -d www.$DOMAIN --non-interactive --agree-tos --email <EMAIL> --redirect"
} else {
    Write-Host "📦 Installing Certbot..." -ForegroundColor Yellow
    & ssh "$SERVER_USER@$SERVER_IP" "apt update"
    & ssh "$SERVER_USER@$SERVER_IP" "apt install -y certbot python3-certbot-nginx"
    & ssh "$SERVER_USER@$SERVER_IP" "certbot --nginx -d $DOMAIN -d www.$DOMAIN --non-interactive --agree-tos --email <EMAIL> --redirect"
}

# Clean up local files
Remove-Item "crm.conf" -ErrorAction SilentlyContinue

Write-Host ""
Write-Host "🎉 Deployment completed successfully!" -ForegroundColor Green
Write-Host "🌐 Your CRM is now live at: https://crm.tripxplo.com" -ForegroundColor Cyan
Write-Host ""
Write-Host "✅ What was deployed:" -ForegroundColor Yellow
Write-Host "• Enhanced lead editing functionality" -ForegroundColor White
Write-Host "• Material Design Trello-like Kanban board" -ForegroundColor White
Write-Host "• Priority management system" -ForegroundColor White
Write-Host "• Real-time updates and activity logging" -ForegroundColor White
Write-Host "• Smart auto-loading (once per session)" -ForegroundColor White
Write-Host ""

# Test the deployment
Write-Host "🧪 Testing deployment..." -ForegroundColor Cyan
try {
    $response = Invoke-WebRequest -Uri "http://crm.tripxplo.com" -Method Head -TimeoutSec 10 -ErrorAction Stop
    Write-Host "✅ HTTP test passed (Status: $($response.StatusCode))" -ForegroundColor Green
} catch {
    Write-Host "⚠️ HTTP test - site may still be starting up" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "📋 Next steps:" -ForegroundColor Green
Write-Host "1. Test your CRM at https://crm.tripxplo.com" -ForegroundColor White
Write-Host "2. Verify all lead editing features work" -ForegroundColor White
Write-Host "3. For future updates: npm run build + run this script" -ForegroundColor White 