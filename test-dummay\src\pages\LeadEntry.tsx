import React, { useState, useEffect } from 'react';
import { createLead, LeadData, testManualFetchLeadInsert } from '../lib/leadService';
import { supabase } from '../lib/supabaseClient';
import { useAuth } from '../contexts/AuthContext';
import { LEAD_STATUSES, isValidLeadStatus } from '../constants/leadStatus';

const LeadEntry: React.FC = () => {
  // USER DEBUGGING STEP: If standard lead creation hangs,
  // click this button WHILE THE PROBLEM IS HAPPENING. Check the console
  // for '[testManualFetchLeadInsert]' logs to see if a direct network call works or also fails/hangs.
  const { user } = useAuth();
  const handleManualPostTest = async () => {
    if (!user) {
      console.error('[Manual POST Test] No user logged in!');
      return;
    }
    // Use current formData or a sample
    const sampleLead = {
      customer_name: 'Debug User',
      email: '<EMAIL>',
      phone: '1234567890',
      destination: 'DebugLand',
      departure_city: 'DebugCity',
      travel_date: '2025-01-01',
      adults: 2,
      children: 0,
      infants: 0,
      nights: 3,
      budget_range: '1000-2000',
      lead_source: 'debug',
      priority: 'LOW',
      notes: 'Manual POST test',
      special_requests: '',
      assigned_to: user.id,
      status: 'NEW LEAD',
    };
    const result = await testManualFetchLeadInsert(sampleLead, user.id);
    console.log('[Manual POST Test] testManualFetchLeadInsert result:', result);
  };

  // (removed duplicate 'const { user } = useAuth();')
  const initialState: LeadData = {
    customer_name: '',
    email: '',
    phone: '',
    destination: '',
    departure_city: '',
    travel_date: '',
    adults: 1,
    children: 0,
    infants: 0,
    nights: 1,
    budget_range: '',
    lead_source: 'instagram',
    priority: 'low',
    notes: '',
    special_requests: '',
    status: LEAD_STATUSES[0], // Default to 'NEW LEAD'
  };

  const [formData, setFormData] = useState<LeadData>(initialState);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [authStatus, setAuthStatus] = useState<string>('Checking...');

  // Check authentication status on component mount
  useEffect(() => {
    async function checkAuth() {
      try {
        console.log('Checking authentication status...');
        const { data, error } = await supabase.auth.getSession();
        
        if (error) {
          console.error('Auth check error:', error);
          setAuthStatus('Error: ' + error.message);
          return;
        }
        
        console.log('Auth session result:', data);
        
        if (data.session) {
          setAuthStatus('Authenticated as: ' + data.session.user.email);
          console.log('User is authenticated:', data.session.user);
        } else {
          setAuthStatus('Not authenticated');
          console.log('User is not authenticated');
        }
      } catch (err) {
        console.error('Exception during auth check:', err);
        setAuthStatus('Auth check failed');
      }
    }
    
    checkAuth();
  }, []);

  const handleChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >
  ) => {
    const { name, type, value } = e.target as any;
    
    // Handle numbers specially to avoid NaN issues
    if (type === 'number') {
      // If the input is cleared (empty string), use default values instead of NaN
      const parsedValue = value === '' ? 0 : parseInt(value, 10);
      // Use null check to handle both NaN and undefined cases
      const validValue = isNaN(parsedValue) ? 0 : parsedValue;
      
      setFormData((prev) => ({
        ...prev,
        [name]: validValue,
      }));
    } else {
      // For non-numeric inputs, just use the value as-is
      setFormData((prev) => ({
        ...prev,
        [name]: value,
      }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    console.log('***** FORM SUBMISSION STARTED *****');
    console.log('Form submitted!');
    setError(null);
    setSuccess(null);
    
    // Check authentication via AuthContext
    if (!user) {
      setError('You must be logged in to submit a lead.');
      return;
    }
    console.log('Authentication verified for user:', user.id);
    
    console.log('Validation check:', formData);
    if (!formData.customer_name || !formData.phone || !formData.destination) {
      console.error('Validation failed: required fields missing', formData);
      setError('Please fill in all required fields.');
      return;
    }
    setIsSubmitting(true);

    // Validate status
    if (!isValidLeadStatus(formData.status)) {
      setError('Invalid status selected.');
      setIsSubmitting(false);
      return;
    }
    // Create a specific payload for the service
    console.log('***** PREPARING PAYLOAD *****');
    const payload = {
      ...formData
    };
    
    console.log('Form data state being passed:', formData);
    console.log('Payload sent to createLead:', payload);
    
    try {
      console.log('***** CALLING createLead FUNCTION *****');
      console.log('Imported createLead function exists:', typeof createLead === 'function');
      // Use user ID from AuthContext
      console.log('Creating lead with user ID:', user.id);
      const result = await createLead(payload, user.id);
      console.log('Response from createLead:', result); // Log the full result for debugging
      if (!result.success) {
        console.error('createLead returned error:', result.error);
      }
      setIsSubmitting(false);
      if (result.success) {
        setSuccess('Lead created successfully!');
        setFormData(initialState);
      } else {
        // Show detailed error info in UI for debugging
        let errorMessage = 'Unknown error';
        if (result.error) {
          if (typeof result.error === 'string') {
            errorMessage = result.error;
          } else if (result.error.message) {
            errorMessage = result.error.message;
          } else {
            errorMessage = JSON.stringify(result.error);
          }
        }
        setError('Error creating lead: ' + errorMessage);
      }
    } catch (error: any) {
      console.error('***** EXCEPTION CAUGHT DURING LEAD CREATION *****');
      console.error('Exception caught during lead creation:', error);
      console.error('Error type:', typeof error);
      console.error('Error toString():', error.toString());
      if (error.stack) console.error('Error stack:', error.stack);
      setIsSubmitting(false);
      setError('Error creating lead: ' + (error.message || error.toString() || 'Unknown error'));
    }
    console.log('***** FORM SUBMISSION COMPLETED *****');
  };

  return (
    <div>
      <div className="bg-white rounded-lg shadow overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
          <h1 className="text-primary text-2xl font-bold">New Lead Entry</h1>
          <p className="text-gray-600 text-sm mt-1">Add a new travel inquiry to your leads database</p>
          <p className="text-xs mt-1 font-medium" style={{ color: authStatus.includes('Authenticated') ? 'green' : 'red' }}>
            {authStatus}
          </p>
        </div>
        <div className="p-6">
          {error && <div className="text-red-500 mb-4 p-3 bg-red-50 rounded">{error}</div>}
          {success && <div className="text-green-500 mb-4 p-3 bg-green-50 rounded">{success}</div>}
          
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Customer Information */}
            <div className="mb-6">
              <h2 className="text-lg font-medium text-primary-dark mb-4 pb-2 border-b border-gray-200">
                Customer Information
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label htmlFor="customer_name" className="block text-primary-dark font-medium mb-1">
                    Customer Name*
                  </label>
                  <input
                    type="text"
                    id="customer_name"
                    name="customer_name"
                    value={formData.customer_name}
                    onChange={handleChange}
                    required
                    className="mt-1 block w-full border border-primary-light rounded px-3 py-2 focus:outline-none focus:border-primary focus:ring-1 focus:ring-primary"
                  />
                </div>

                <div>
                  <label htmlFor="phone" className="block text-primary-dark font-medium mb-1">
                    Phone*
                  </label>
                  <input
                    type="tel"
                    id="phone"
                    name="phone"
                    value={formData.phone}
                    onChange={handleChange}
                    required
                    className="mt-1 block w-full border border-primary-light rounded px-3 py-2 focus:outline-none focus:border-primary focus:ring-1 focus:ring-primary"
                  />
                </div>

                <div>
                  <label htmlFor="email" className="block text-primary-dark font-medium mb-1">
                    Email
                  </label>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    value={formData.email}
                    onChange={handleChange}
                    className="mt-1 block w-full border border-primary-light rounded px-3 py-2 focus:outline-none focus:border-primary focus:ring-1 focus:ring-primary"
                  />
                </div>
              </div>
            </div>

            {/* Trip Details */}
            <div className="mb-6">
              <h2 className="text-lg font-medium text-primary-dark mb-4 pb-2 border-b border-gray-200">
                Trip Details
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label htmlFor="destination" className="block text-primary-dark font-medium mb-1">
                    Destination*
                  </label>
                  <input
                    type="text"
                    id="destination"
                    name="destination"
                    value={formData.destination}
                    onChange={handleChange}
                    required
                    className="mt-1 block w-full border border-primary-light rounded px-3 py-2 focus:outline-none focus:border-primary focus:ring-1 focus:ring-primary"
                  />
                </div>

                <div>
                  <label htmlFor="departure_city" className="block text-primary-dark font-medium mb-1">
                    Departure City
                  </label>
                  <input
                    type="text"
                    id="departure_city"
                    name="departure_city"
                    value={formData.departure_city}
                    onChange={handleChange}
                    className="mt-1 block w-full border border-primary-light rounded px-3 py-2 focus:outline-none focus:border-primary focus:ring-1 focus:ring-primary"
                  />
                </div>

                <div>
                  <label htmlFor="travel_date" className="block text-primary-dark font-medium mb-1">
                    Travel Date
                  </label>
                  <input
                    type="date"
                    id="travel_date"
                    name="travel_date"
                    value={formData.travel_date}
                    onChange={handleChange}
                    className="mt-1 block w-full border border-primary-light rounded px-3 py-2 focus:outline-none focus:border-primary focus:ring-1 focus:ring-primary"
                  />
                </div>

                <div>
                  <label htmlFor="nights" className="block text-primary-dark font-medium mb-1">
                    Nights
                  </label>
                  <input
                    type="number"
                    id="nights"
                    name="nights"
                    min={1}
                    value={formData.nights}
                    onChange={handleChange}
                    className="mt-1 block w-full border border-primary-light rounded px-3 py-2 focus:outline-none focus:border-primary focus:ring-1 focus:ring-primary"
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
                <div>
                  <label htmlFor="adults" className="block text-primary-dark font-medium mb-1">
                    Adults
                  </label>
                  <input
                    type="number"
                    id="adults"
                    name="adults"
                    min={1}
                    value={formData.adults}
                    onChange={handleChange}
                    className="mt-1 block w-full border border-primary-light rounded px-3 py-2 focus:outline-none focus:border-primary focus:ring-1 focus:ring-primary"
                  />
                </div>
                <div>
                  <label htmlFor="children" className="block text-primary-dark font-medium mb-1">
                    Children
                  </label>
                  <input
                    type="number"
                    id="children"
                    name="children"
                    min={0}
                    value={formData.children}
                    onChange={handleChange}
                    className="mt-1 block w-full border border-primary-light rounded px-3 py-2 focus:outline-none focus:border-primary focus:ring-1 focus:ring-primary"
                  />
                </div>
                <div>
                  <label htmlFor="infants" className="block text-primary-dark font-medium mb-1">
                    Infants
                  </label>
                  <input
                    type="number"
                    id="infants"
                    name="infants"
                    min={0}
                    value={formData.infants}
                    onChange={handleChange}
                    className="mt-1 block w-full border border-primary-light rounded px-3 py-2 focus:outline-none focus:border-primary focus:ring-1 focus:ring-primary"
                  />
                </div>
              </div>
            </div>

            {/* Additional Information */}
            <div className="mb-6">
              <h2 className="text-lg font-medium text-primary-dark mb-4 pb-2 border-b border-gray-200">
                Additional Information
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label htmlFor="budget_range" className="block text-primary-dark font-medium mb-1">
                    Budget Range
                  </label>
                  <input
                    type="text"
                    id="budget_range"
                    name="budget_range"
                    value={formData.budget_range}
                    onChange={handleChange}
                    className="mt-1 block w-full border border-primary-light rounded px-3 py-2 focus:outline-none focus:border-primary focus:ring-1 focus:ring-primary"
                  />
                </div>

                <div>
                  <label htmlFor="lead_source" className="block text-primary-dark font-medium mb-1">
                    Lead Source
                  </label>
                  <select
                    id="lead_source"
                    name="lead_source"
                    value={formData.lead_source}
                    onChange={handleChange}
                    className="mt-1 block w-full border border-primary-light rounded px-3 py-2 focus:outline-none focus:border-primary focus:ring-1 focus:ring-primary"
                  >
                    <option value="instagram">Instagram</option>
                    <option value="whatsapp">WhatsApp</option>
                    <option value="website">Website</option>
                    <option value="referral">Referral</option>
                    <option value="partner">Partner</option>
                    <option value="direct">Direct</option>
                    <option value="other">Other</option>
                  </select>
                </div>

                <div>
                  <label htmlFor="priority" className="block text-primary-dark font-medium mb-1">
                    Priority
                  </label>
                  <select
                    id="priority"
                    name="priority"
                    value={formData.priority}
                    onChange={handleChange}
                    className="mt-1 block w-full border border-primary-light rounded px-3 py-2 focus:outline-none focus:border-primary focus:ring-1 focus:ring-primary"
                  >
                    <option value="low">Low</option>
                    <option value="medium">Medium</option>
                    <option value="high">High</option>
                  </select>
                </div>
              </div>

              {/* Status Dropdown */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                <div>
                  <label htmlFor="status" className="block text-primary-dark font-medium mb-1">
                    Status
                  </label>
                  <select
                    id="status"
                    name="status"
                    value={formData.status}
                    onChange={handleChange}
                    className="mt-1 block w-full border border-primary-light rounded px-3 py-2 focus:outline-none focus:border-primary focus:ring-1 focus:ring-primary"
                  >
                    {LEAD_STATUSES.map((status) => (
                      <option key={status} value={status}>{status}</option>
                    ))}
                  </select>
                </div>
                <div>
                  <label htmlFor="notes" className="block text-primary-dark font-medium mb-1">
                    Notes
                  </label>
                  <textarea
                    id="notes"
                    name="notes"
                    value={formData.notes}
                    onChange={handleChange}
                    rows={3}
                    className="mt-1 block w-full border border-primary-light rounded px-3 py-2 focus:outline-none focus:border-primary focus:ring-1 focus:ring-primary"
                  />
                </div>

                <div>
                  <label htmlFor="special_requests" className="block text-primary-dark font-medium mb-1">
                    Special Requests
                  </label>
                  <textarea
                    id="special_requests"
                    name="special_requests"
                    value={formData.special_requests}
                    onChange={handleChange}
                    rows={3}
                    className="mt-1 block w-full border border-primary-light rounded px-3 py-2 focus:outline-none focus:border-primary focus:ring-1 focus:ring-primary"
                  />
                </div>
              </div>
            </div>

            <button
              type="submit"
              disabled={isSubmitting}
              className={`w-full py-3 px-4 bg-primary hover:bg-primary-dark text-white font-bold rounded focus:outline-none focus:shadow-outline ${
                isSubmitting ? 'opacity-50 cursor-not-allowed' : ''
              }`}
            >
              {isSubmitting ? 'Submitting…' : 'Submit'}
            </button>
          </form>
          {/* Manual test insert button for debugging */}
          <button
            type="button"
            onClick={async () => {
              console.log('Running manual test insert...');
              const testPayload: LeadData = {
                customer_name: 'Test User',
                phone: '************',
                email: '<EMAIL>',
                destination: 'Test Destination',
                departure_city: '',
                travel_date: '',
                adults: 1,
                children: 0,
                infants: 0,
                nights: 1,
                budget_range: '',
                lead_source: 'website',
                priority: 'low',
                notes: 'Manual test insert',
                special_requests: '',
                status: LEAD_STATUSES[0],
              };
              // Use user from AuthContext
              if (!user) {
                alert('You must be logged in to perform this test insert.');
                return;
              }
              console.log('Running manual test insert with user ID:', user.id);
              const result = await createLead(testPayload, user.id);
              console.log('Manual test insert result:', result);
              if (result.success) {
                alert('Test insert succeeded!');
              } else {
                alert('Test insert failed: ' + (result.error?.message || JSON.stringify(result.error)));
              }
            }}
            className="mt-4 w-full py-3 px-4 bg-gray-500 hover:bg-gray-600 text-white font-bold rounded focus:outline-none focus:shadow-outline"
          >
            Test Insert
          </button>
        </div>
      </div>
    </div>
  );
};

export default LeadEntry; 