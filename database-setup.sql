
-- Drop the table if it exists to ensure a clean slate
DROP TABLE IF EXISTS public.followups;

-- Create the followups table
CREATE TABLE public.followups (
    id bigint NOT NULL GENERATED BY DEFAULT AS IDENTITY,
    created_at timestamp with time zone NOT NULL DEFAULT now(),
    quote_id uuid NULL,
    customer_name text NULL,
    customer_phone text NULL,
    customer_email text NULL,
    status text NULL,
    follow_up_date date NULL,
    notes text NULL,
    cab_payment numeric NULL,
    CONSTRAINT followups_pkey PRIMARY KEY (id),
    CONSTRAINT followups_quote_id_fkey FOREIGN KEY (quote_id) REFERENCES quotes(id) ON UPDATE CASCADE ON DELETE SET NULL
);

-- Add comments to the table and columns for better understanding
COMMENT ON TABLE public.followups IS 'Stores follow-up information for customer quotes.';
COMMENT ON COLUMN public.followups.id IS 'Unique identifier for each followup.';
COMMENT ON COLUMN public.followups.created_at IS 'Timestamp of when the followup was created.';
COMMENT ON COLUMN public.followups.quote_id IS 'Foreign key referencing the quote associated with this followup.';
COMMENT ON COLUMN public.followups.customer_name IS 'The name of the customer, sourced from the quote.';
COMMENT ON COLUMN public.followups.customer_phone IS 'The phone number of the customer, sourced from the quote.';
COMMENT ON COLUMN public.followups.customer_email IS 'The email address of the customer, sourced from the quote.';
COMMENT ON COLUMN public.followups.status IS 'The current status of the followup (e.g., open, closed, pending).';
COMMENT ON COLUMN public.followups.follow_up_date IS 'The date scheduled for the next follow-up.';
COMMENT ON COLUMN public.followups.notes IS 'Additional notes or details about the followup.';
COMMENT ON COLUMN public.followups.cab_payment IS 'Payment amount for the cab service, if applicable.';

-- Enable Row Level Security (RLS) for the table
ALTER TABLE public.followups ENABLE ROW LEVEL SECURITY;

-- RLS Policies
CREATE POLICY "Allow individual read access" ON public.followups
FOR SELECT
USING (auth.role() = 'authenticated');

CREATE POLICY "Allow individual insert access" ON public.followups
FOR INSERT
WITH CHECK (auth.role() = 'authenticated');

CREATE POLICY "Allow individual update access" ON public.followups
FOR UPDATE
USING (auth.role() = 'authenticated')
WITH CHECK (auth.role() = 'authenticated');

CREATE POLICY "Allow individual delete access" ON public.followups
FOR DELETE
USING (auth.role() = 'authenticated');
