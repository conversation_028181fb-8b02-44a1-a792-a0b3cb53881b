/**
 * Supabase Manager
 *
 * This file provides a unified interface for accessing both the CRM and Quote Supabase clients.
 * It helps prevent multiple client instances and manages client initialization.
 */

import { SupabaseClient } from '@supabase/supabase-js';
import { supabase as crmSupabase } from './supabaseClient';

// Import the Quote Supabase client only when needed (lazy loading)
let quoteSupabaseInstance: SupabaseClient | null = null;

/**
 * Get the CRM Supabase client
 * @returns The CRM Supabase client instance
 */
export const getCrmClient = (): SupabaseClient => {
  return crmSupabase;
};

/**
 * Get the Quote Supabase client
 * This uses lazy loading to only import the Quote client when needed
 * @returns The Quote Supabase client instance
 */
export const getQuoteClient = async (): Promise<SupabaseClient> => {
  // If we already have an instance, return it
  if (quoteSupabaseInstance) {
    return quoteSupabaseInstance;
  }

  try {
    // Set a timeout for the import
    const importPromise = import('../quotes/lib/supabaseClient');
    const timeoutPromise = new Promise<never>((_, reject) => {
      setTimeout(() => {
        reject(new Error('Timeout importing Quote Supabase client'));
      }, 10000); // 10 second timeout
    });

    // Race the import against the timeout
    const { supabase } = await Promise.race([importPromise, timeoutPromise]);
    quoteSupabaseInstance = supabase;
    return supabase;
  } catch (error) {
    console.error('[supabaseManager] Error loading Quote Supabase client:', error);
    throw new Error('Failed to load Quote Supabase client');
  }
};

/**
 * Check if a client is initialized
 * @param clientType The type of client to check ('crm' or 'quote')
 * @returns True if the client is initialized, false otherwise
 */
export const isClientInitialized = (clientType: 'crm' | 'quote'): boolean => {
  if (clientType === 'crm') {
    return !!crmSupabase;
  } else {
    return !!quoteSupabaseInstance;
  }
};

/**
 * Clear all Supabase client instances
 * This is useful for testing or when you need to reset the clients
 */
export const clearClients = (): void => {
  // We can't actually clear the singleton instances, but we can log that this was attempted
  console.log('[supabaseManager] Attempt to clear Supabase clients - note that singleton instances cannot be fully cleared');
  quoteSupabaseInstance = null;
};
