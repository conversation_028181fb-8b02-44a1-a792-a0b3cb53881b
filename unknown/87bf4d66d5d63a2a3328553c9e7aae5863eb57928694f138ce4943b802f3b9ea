import React from 'react';
import { Navigate, Outlet, useLocation } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';

const ProtectedRoute: React.FC = () => {
  const { session } = useAuth();
  const location = useLocation();

  // If no active session, redirect to login, preserving the attempted URL
  if (!session) {
    return <Navigate to="/login" replace state={{ from: location }} />;
  }

  // Otherwise, render the requested protected route
  return <Outlet />;
};

export default ProtectedRoute; 