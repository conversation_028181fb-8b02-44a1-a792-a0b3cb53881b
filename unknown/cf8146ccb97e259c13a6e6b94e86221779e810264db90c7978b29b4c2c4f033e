import React, { useEffect, useState } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { Navigate, Outlet } from 'react-router-dom';

const ProtectedRoute: React.FC = () => {
  const { isLoading, isAuthenticated } = useAuth();
  const [showLoading, setShowLoading] = useState(false);

  // Only show loading indicator after a short delay to avoid flashing
  useEffect(() => {
    const timer = setTimeout(() => {
      if (isLoading) {
        setShowLoading(true);
      }
    }, 300); // Short delay before showing loading indicator

    return () => clearTimeout(timer);
  }, [isLoading]);

  // If loading but not yet showing indicator, render nothing (blank screen)
  if (isLoading && !showLoading) {
    return null;
  }

  // If loading and showing indicator, show a nicer loading state
  if (isLoading && showLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-50">
        <div className="text-center p-6 max-w-sm mx-auto">
          <div className="animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-gray-600">Loading your session...</p>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }

  return <Outlet />;
};

export default ProtectedRoute;