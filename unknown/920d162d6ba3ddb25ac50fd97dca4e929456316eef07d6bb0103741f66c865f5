import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import LeadCard, { Lead } from '../components/LeadCard';
import LeadDetailModal from '../components/LeadDetailModal';
import { 
  DndContext, 
  DragEndEvent, 
  DragOverlay, 
  DragStartEvent,
  MouseSensor,
  TouchSensor,
  useSensor,
  useSensors,
  UniqueIdentifier,
  DragOverEvent,
  closestCenter,
  closestCorners,
} from '@dnd-kit/core';
import { SortableContext, verticalListSortingStrategy } from '@dnd-kit/sortable';
import { toast } from 'react-hot-toast';
import { Spinner } from '../components/Spinner';
// Use Supabase API directly for leads
import { fetchLeadsByUser, updateLeadStatus, testManualFetchLeads } from '../lib/leadService';

// We'll use this as fallback if there are no leads or if there's an error

// Updated list of statuses for Kanban columns
// NOTE: If drag-and-drop issues persist, check for CSS interference (transform, z-index, absolute/fixed positioning) on cards, columns, or parents. Such styles can confuse dnd-kit drop detection.
const statuses = [
  'NEW LEAD',
  'CALL CUSTOMER',
  'CALL NOT ANSWERED',
  'CONTACTED',
  'QUOTE SENT',
  'MORE INFO',
  'FOLLOW-UP',
  'APPROVED',
  'NEGOTIATION',
  'BOOKED WITH US',
  'ON-HOLD',
  'NO RESPONSE',
  'POSTPONE TRIP',
  'BOOKING - OTHERS',
  'TRIP CANCELLED',
  'NOT INTERESTED',
  'TRIP COMPLETED'
];

const SAMPLE_LEADS: Lead[] = [
  {
    id: '1',
    customer_name: 'John Smith',
    email: '<EMAIL>',
    phone: '************',
    destination: 'Paris, France',
    travel_date: '2023-12-15',
    lead_source: 'Website',
    status: 'NEW LEAD',
    priority: 'HIGH',
    assigned_to: 'user-1',
    created_at: '2023-06-01'
  },
  {
    id: '2',
    customer_name: 'Maria Garcia',
    email: '<EMAIL>',
    phone: '************',
    destination: 'Tokyo, Japan',
    travel_date: '2023-11-10',
    lead_source: 'Referral',
    status: 'CONTACTED',
    priority: 'MEDIUM',
    assigned_to: 'user-1',
    created_at: '2023-06-02'
  },
  {
    id: '3',
    customer_name: 'David Wilson',
    email: '<EMAIL>',
    phone: '************',
    destination: 'Rome, Italy',
    travel_date: '2023-10-05',
    lead_source: 'Partner',
    status: 'QUOTE SENT',
    priority: 'LOW',
    assigned_to: 'user-1',
    created_at: '2023-06-03'
  },
  {
    id: '4',
    customer_name: 'Sarah Johnson',
    email: '<EMAIL>',
    phone: '************',
    destination: 'Bali, Indonesia',
    travel_date: '2023-09-20',
    lead_source: 'Instagram',
    status: 'APPROVED',
    priority: 'HIGH',
    assigned_to: 'user-1',
    created_at: '2023-06-04'
  },
  {
    id: '5',
    customer_name: 'Michael Brown',
    email: '<EMAIL>',
    phone: '************',
    destination: 'Cape Town, South Africa',
    travel_date: '2023-08-15',
    lead_source: 'Facebook',
    status: 'BOOKED',
    priority: 'MEDIUM',
    assigned_to: 'user-1',
    created_at: '2023-06-05'
  },
  {
    id: '6',
    customer_name: 'Jennifer Lee',
    email: '<EMAIL>',
    phone: '************',
    destination: 'Cancun, Mexico',
    travel_date: '2023-07-10',
    lead_source: 'Partner',
    status: 'CLOSED_LOST',
    priority: 'LOW',
    assigned_to: 'user-1',
    created_at: '2023-06-06'
  }
];

const LeadsKanban: React.FC = () => {
  // USER DEBUGGING STEP: If standard Kanban loading hangs,
  // click this button WHILE THE PROBLEM IS HAPPENING. Check the console
  // for '[testManualFetchLeads]' logs to see if a direct network call works or also fails/hangs.
  const handleManualGetTest = async () => {
    const result = await testManualFetchLeads();
    console.log('[Manual GET Test] testManualFetchLeads result:', result);
  };

  const { user, isLoading: authLoading } = useAuth();
  // Start with sample data but then fetch real data
  const [leads, setLeads] = useState<Lead[]>(SAMPLE_LEADS);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [updatingLeadId, setUpdatingLeadId] = useState<string | null>(null);
  const [activeLead, setActiveLead] = useState<Lead | null>(null);
  const [activeColumn, setActiveColumn] = useState<string | null>(null);
  const [viewingLeadId, setViewingLeadId] = useState<string | null>(null);

  // Configure sensors for mouse and touch interactions
  const sensors = useSensors(
    useSensor(MouseSensor, {
      // Reduced activation delay for better responsiveness
      activationConstraint: {
        delay: 50,
        tolerance: 5,
      },
    }),
    useSensor(TouchSensor, {
      // Reduced delay for touch devices
      activationConstraint: {
        delay: 150,
        tolerance: 5,
      },
    })
  );

  useEffect(() => {
    // Only fetch if we have a user
    const fetchLeads = async () => {
      if (!user?.id) {
        console.log("No user ID, using sample data");
        setIsLoading(false);
        return;
      }

      try {
        setIsLoading(true);
        setError(null);
        
        console.log("Fetching leads for user:", user.id);
        const fetchedLeads = await fetchLeadsByUser(user.id);
        
        console.log("Fetched leads:", fetchedLeads);
        if (fetchedLeads.success && fetchedLeads.data) {
          setLeads(fetchedLeads.data);
        } else {
          console.error("Error fetching leads:", fetchedLeads.error);
          setError(fetchedLeads.error || 'Failed to fetch leads');
        }
      } catch (error: any) {
        console.error("Exception fetching leads:", error);
        setError('Failed to fetch leads: ' + (error.message || 'Unknown error'));
      } finally {
        setIsLoading(false);
      }
    };

    fetchLeads();
  }, [user?.id]);

  // Group leads by status
  const getLeadsByStatus = (status: string) => {
    return leads.filter(lead => lead.status === status);
  };

  const handleViewDetails = (leadId: string) => {
    setViewingLeadId(leadId);
  };

  const handleCreateQuote = (lead: Lead) => {
    // TODO: Implement quote creation
  };

  const handleDragStart = (event: DragStartEvent) => {
    const { active } = event;
    const lead = leads.find(l => l.id === active.id);
    
    if (lead) {
      setActiveLead(lead);
      setActiveColumn(lead.status);
    }
  };

  const handleDragOver = (event: DragOverEvent) => {
    const { active, over } = event;
    
    // Nothing is happening here yet, but maybe we'll add a feature
    // like visual cues to show where an item will be placed
  };

  // Helper function to find the column ID from any element inside it
  const findColumnFromTarget = (targetId: UniqueIdentifier): string | null => {
    console.log('[DND] findColumnFromTarget called with targetId:', targetId);
    
    // If the target is a column, return its ID
    if (typeof targetId === 'string' && targetId.startsWith('column-')) {
      console.log('[DND] findColumnFromTarget returning column:', targetId);
      return targetId;
    }
    
    // If the target is a lead, find which column it belongs to
    const lead = leads.find(l => l.id === targetId?.toString());
    if (lead) {
      const col = `column-${lead.status}`;
      console.log('[DND] findColumnFromTarget returning for lead:', col);
      return col;
    }
    
    console.log('[DND] findColumnFromTarget returning null');
    return null;
  };

  const handleDragEnd = async (event: DragEndEvent) => {
    // === DND DEBUG LOGGING START ===
    console.log('[DND] Drag End Event:', event);
    const { active, over } = event;
    console.log('[DND] active.id:', active?.id);
    console.log('[DND] over.id:', over?.id);
    const columnId = findColumnFromTarget(over?.id);
    console.log('[DND] findColumnFromTarget(over.id):', columnId);
    const newStatus = columnId ? columnId.replace('column-', '') : null;
    console.log('[DND] newStatus:', newStatus);
    const leadId = active?.id?.toString();
    const leadToUpdate = leads.find(lead => lead.id === leadId);
    console.log('[DND] leadToUpdate:', leadToUpdate || 'NOT FOUND');
    // === DND DEBUG LOGGING END ===

    setActiveLead(null);
    setActiveColumn(null);

    if (!active || !over) {
      console.log("Dropped outside a valid target.");
      return;
    }

    // The rest of your original logic, now using the already-declared variables
    const leadIdStr = active.id.toString();
    
    // Find the column where the item was dropped, handling drops on leads or columns
    const columnIdStr = findColumnFromTarget(over.id);
    console.log("Found column ID:", columnIdStr);

    if (columnIdStr) {
      setUpdatingLeadId(leadId);
      
      // Extract target column status from column ID
      const newStatusStr = columnIdStr.replace('column-', '');
      
      console.log("Extracted newStatus:", newStatusStr);
      
      // Find the lead we're updating
      const leadToUpdateObj = leads.find((lead) => lead.id === leadIdStr);
      
      if (!leadToUpdateObj) {
        setUpdatingLeadId(null);
        console.log("Lead not found:", leadIdStr);
        return;
      }
      
      // Clone the leads array to avoid mutating state directly
      const updatedLeads = [...leads];
      const leadIndex = updatedLeads.findIndex((lead) => lead.id === leadIdStr);
      
      // Store the original status in case we need to revert
      const originalStatus = updatedLeads[leadIndex].status;
      console.log("Original status:", originalStatus, "New status:", newStatusStr);
      
      // Return if status hasn't changed
      if (originalStatus === newStatusStr) {
        setUpdatingLeadId(null);
        console.log("Status unchanged, returning early");
        return;
      }
      
      // Optimistically update UI
      updatedLeads[leadIndex] = {
        ...updatedLeads[leadIndex],
        status: newStatusStr
      };
      
      setLeads(updatedLeads);
      console.log("Updated local state with new status");
      
      // Re-enable API update with improved error handling
      try {
        // Only update API if we have a real user
        if (user?.id) {
          // Use the service function
          console.log("Calling updateLeadStatus service with:", {
            leadId: leadIdStr,
            newStatus: newStatusStr,
            userId: user.id
          });
          
          const result = await updateLeadStatus(leadIdStr, newStatusStr);
          
          console.log("Status update response:", result);
          
          if (!result.success) {
            throw new Error(result.error || 'Failed to update lead status');
          }
          
          console.log("Database update successful");
          toast.success(`Lead moved to ${newStatusStr}`);
        } else {
          console.log("No user ID, skipping database update");
          toast.success(`Lead moved to ${newStatusStr} (local only)`);
        }
      } catch (error: any) {
        console.error('Error updating lead status:', error);
        console.error('Error details:', {
          name: error.name,
          message: error.message,
          stack: error.stack
        });
        
        // Revert the change in UI if the database update fails
        const revertedLeads = [...updatedLeads];
        revertedLeads[leadIndex] = {
          ...revertedLeads[leadIndex],
          status: originalStatus
        };
        
        setLeads(revertedLeads);
        console.log("Reverted to original status due to error");
        toast.error(error.message || 'Failed to update lead status. Please try again.');
      } finally {
        setUpdatingLeadId(null);
      }
    } else {
      console.log("Dropped on an invalid target (not a column and not a lead in a column)");
    }
  };

  // Show loading state but with an informative message
  if (isLoading) {
    return (
      <div className="flex flex-col justify-center items-center h-64 space-y-4">
        <Spinner size="xl" />
        <p className="text-gray-500">Loading leads from database...</p>
        {error && (
          <div className="p-4 my-3 bg-red-50 text-red-600 rounded-lg">
            <h2 className="text-lg font-semibold">Error</h2>
            <p>{error}</p>
          </div>
        )}
      </div>
    );
  }

  // Log leads state before rendering columns
  console.log('[KANBAN] leads state before rendering:', leads);

  return (
    <>
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-primary">Leads Kanban Board</h1>
        <p className="text-gray-500">
          {error ? 'Showing sample data due to an error' : 'Track and manage your travel leads'}
          {!user?.id && ' (using sample data)'}
        </p>
        {error && (
          <div className="p-4 my-3 bg-red-50 text-red-600 rounded-lg">
            <h2 className="text-lg font-semibold">Error</h2>
            <p>{error}</p>
          </div>
        )}
      </div>

      <DndContext
        sensors={sensors}
        // DIAGNOSTIC: changed collisionDetection from closestCenter to closestCorners
        collisionDetection={closestCorners}
        onDragStart={handleDragStart}
        onDragOver={handleDragOver}
        onDragEnd={handleDragEnd}
      >
        <div className="flex space-x-4 overflow-x-auto p-4">
          {statuses.map(status => (
            <div 
              key={`column-${status}`} 
              id={`column-${status}`}
              className="bg-gray-100 rounded-lg p-3 w-72 flex-shrink-0"
            >
              <h3 className="font-semibold mb-3 text-gray-700">
                {status} 
                <span className="ml-2 text-xs bg-gray-200 text-gray-700 py-1 px-2 rounded-full">
                  {getLeadsByStatus(status).length}
                </span>
              </h3>
              <div 
                id={`column-content-${status}`}
                className="p-2 flex flex-col gap-2 min-h-[300px] max-h-[70vh] overflow-y-auto"
              >
                <SortableContext 
                  items={getLeadsByStatus(status).map(lead => lead.id)}
                  strategy={verticalListSortingStrategy}
                >
                  {getLeadsByStatus(status).length === 0 ? (
                    <div className="text-gray-400 text-sm p-4 text-center italic">
                      No leads in this status
                    </div>
                  ) : (
                    getLeadsByStatus(status).map((lead) => (
                      <LeadCard 
                        key={lead.id} 
                        lead={lead} 
                        isUpdating={updatingLeadId === lead.id}
                        onViewDetails={handleViewDetails}
                      />
                    ))
                  )}
                </SortableContext>
              </div>
            </div>
          ))}
        </div>
        
        <DragOverlay>
          {activeLead && (
            <LeadCard 
              lead={activeLead}
              onViewDetails={handleViewDetails}
            />
          )}
        </DragOverlay>
      </DndContext>

      {/* Lead Detail Modal */}
      <LeadDetailModal 
        leadId={viewingLeadId} 
        onClose={() => setViewingLeadId(null)} 
      />
    </>
  );
};

export default LeadsKanban;
