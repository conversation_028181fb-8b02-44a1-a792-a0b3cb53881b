#!/bin/bash

# TripXplo Family EMI - Production Deployment Preparation Script
# This script creates a clean production build for deployment to family.tripxplo.com

echo "🚀 Preparing TripXplo Family EMI for production deployment..."

# Create production directory
PROD_DIR="family-tripxplo-production"
rm -rf $PROD_DIR
mkdir $PROD_DIR

echo "📁 Created production directory: $PROD_DIR"

# Copy main application files
echo "📄 Copying main application files..."
cp src/nest/index.html $PROD_DIR/
cp src/nest/style.css $PROD_DIR/
cp src/nest/styleguide.css $PROD_DIR/
cp src/nest/globals.css $PROD_DIR/

# Copy JavaScript files
echo "📜 Copying JavaScript files..."
mkdir $PROD_DIR/js
cp src/nest/js/config.js $PROD_DIR/js/
cp src/nest/js/databaseService.js $PROD_DIR/js/
cp src/nest/js/apiService.js $PROD_DIR/js/
cp src/nest/js/packageCardGenerator.js $PROD_DIR/js/

# Copy image assets
echo "🖼️ Copying image assets..."
cp -r src/nest/img $PROD_DIR/

# Copy API server (optional)
echo "🔧 Copying API server..."
mkdir $PROD_DIR/api
cp src/nest/api/server.js $PROD_DIR/api/

# Create .htaccess for Apache (if needed)
echo "⚙️ Creating .htaccess file..."
cat > $PROD_DIR/.htaccess << 'EOF'
# TripXplo Family EMI - Apache Configuration

# Enable compression
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# Cache static assets
<IfModule mod_expires.c>
    ExpiresActive on
    ExpiresByType text/css "access plus 1 year"
    ExpiresByType application/javascript "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
    ExpiresByType image/svg+xml "access plus 1 year"
</IfModule>

# Security headers
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
</IfModule>

# Redirect to HTTPS
RewriteEngine On
RewriteCond %{HTTPS} off
RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]
EOF

# Create deployment info file
echo "📋 Creating deployment info..."
cat > $PROD_DIR/DEPLOYMENT_INFO.txt << EOF
TripXplo Family EMI - Production Deployment
==========================================

Deployment Date: $(date)
Version: 1.0.0
Domain: family.tripxplo.com

Files Included:
- index.html (Main application)
- style.css (Main styles)
- styleguide.css (Style guide)
- globals.css (Global styles)
- js/ (JavaScript modules)
- img/ (Image assets)
- api/ (Optional API server)
- .htaccess (Apache configuration)

Deployment Instructions:
1. Upload all files to your web server
2. Point family.tripxplo.com to your server
3. Configure SSL certificate
4. Test all functionality

For detailed deployment guide, see LINODE_DEPLOYMENT_GUIDE.md
EOF

# Create file list
echo "📝 Creating file list..."
find $PROD_DIR -type f | sort > $PROD_DIR/FILE_LIST.txt

# Calculate total size
TOTAL_SIZE=$(du -sh $PROD_DIR | cut -f1)

echo ""
echo "✅ Production build completed successfully!"
echo "📁 Location: $PROD_DIR"
echo "📊 Total size: $TOTAL_SIZE"
echo "📄 Files included:"
echo "   - Main application files"
echo "   - JavaScript modules"
echo "   - Image assets"
echo "   - Configuration files"
echo ""
echo "🚀 Ready for deployment to family.tripxplo.com"
echo ""
echo "Next steps:"
echo "1. Review files in $PROD_DIR"
echo "2. Upload to your Linode server"
echo "3. Configure domain and SSL"
echo "4. Test the deployment"
echo ""
echo "For detailed deployment instructions, see LINODE_DEPLOYMENT_GUIDE.md"
EOF
