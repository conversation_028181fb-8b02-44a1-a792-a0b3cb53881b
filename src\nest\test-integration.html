<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Family EMI - Database Integration Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #8B5CF6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #7C3AED;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        .success { border-left: 4px solid #28a745; }
        .error { border-left: 4px solid #dc3545; }
        .loading { border-left: 4px solid #ffc107; }
        h1 { color: #333; text-align: center; }
        h2 { color: #666; border-bottom: 2px solid #8B5CF6; padding-bottom: 5px; }
    </style>
</head>
<body>
    <h1>🧪 Family EMI Database Integration Test</h1>
    
    <div class="test-section">
        <h2>📊 Database Connection Test</h2>
        <p>Test direct connection to your Supabase databases:</p>
        <button class="test-button" onclick="testFamilyTypes()">Test Family Types (CRM DB)</button>
        <button class="test-button" onclick="testDestinations()">Test Destinations (Quote DB)</button>
        <div id="connectionResult" class="result"></div>
    </div>

    <div class="test-section">
        <h2>🔍 Package Search Test</h2>
        <p>Test package search functionality:</p>
        <input type="text" id="testDestination" placeholder="Enter destination (e.g., Kashmir)" style="padding: 8px; margin: 5px; width: 200px;">
        <button class="test-button" onclick="testPackageSearch()">Search Packages</button>
        <div id="searchResult" class="result"></div>
    </div>

    <div class="test-section">
        <h2>👨‍👩‍👧‍👦 Family Type Detection Test</h2>
        <p>Test family type auto-detection:</p>
        <label>Adults: <input type="number" id="testAdults" value="2" min="1" style="width: 60px; padding: 5px;"></label>
        <label>Children: <input type="number" id="testChildren" value="0" min="0" style="width: 60px; padding: 5px;"></label>
        <label>Infants: <input type="number" id="testInfants" value="0" min="0" style="width: 60px; padding: 5px;"></label>
        <button class="test-button" onclick="testFamilyTypeDetection()">Detect Family Type</button>
        <div id="familyTypeResult" class="result"></div>
    </div>

    <div class="test-section">
        <h2>📝 Quote Submission Test</h2>
        <p>Test quote request submission (uses test data):</p>
        <button class="test-button" onclick="testQuoteSubmission()">Submit Test Quote</button>
        <div id="quoteResult" class="result"></div>
    </div>

    <div class="test-section">
        <h2>⚙️ Configuration Status</h2>
        <button class="test-button" onclick="checkConfiguration()">Check Config</button>
        <div id="configResult" class="result"></div>
    </div>

    <!-- Include required scripts -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <script src="js/config.js"></script>
    <script src="js/databaseService.js"></script>

    <script>
        // Test Functions
        async function testFamilyTypes() {
            const resultDiv = document.getElementById('connectionResult');
            resultDiv.className = 'result loading';
            resultDiv.textContent = 'Testing family types connection...';

            try {
                const response = await databaseService.getFamilyTypes();
                resultDiv.className = 'result success';
                resultDiv.textContent = `✅ SUCCESS: Loaded ${response.data?.length || 0} family types\n\n` + 
                                       JSON.stringify(response, null, 2);
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ ERROR: ${error.message}\n\n` + JSON.stringify(error, null, 2);
            }
        }

        async function testDestinations() {
            const resultDiv = document.getElementById('connectionResult');
            resultDiv.className = 'result loading';
            resultDiv.textContent = 'Testing destinations connection...';

            try {
                const response = await databaseService.getDestinations();
                resultDiv.className = 'result success';
                resultDiv.textContent = `✅ SUCCESS: Loaded ${response.data?.length || 0} destinations\n\n` + 
                                       JSON.stringify(response, null, 2);
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ ERROR: ${error.message}\n\n` + JSON.stringify(error, null, 2);
            }
        }

        async function testPackageSearch() {
            const destination = document.getElementById('testDestination').value || 'Kashmir';
            const resultDiv = document.getElementById('searchResult');
            resultDiv.className = 'result loading';
            resultDiv.textContent = `Searching packages for ${destination}...`;

            try {
                const response = await databaseService.searchPackages({
                    destination: destination,
                    adults: 2,
                    children: 0,
                    infants: 0
                });
                resultDiv.className = 'result success';
                resultDiv.textContent = `✅ SUCCESS: Found ${response.packages?.length || 0} packages\n\n` + 
                                       JSON.stringify(response, null, 2);
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ ERROR: ${error.message}\n\n` + JSON.stringify(error, null, 2);
            }
        }

        async function testFamilyTypeDetection() {
            const adults = parseInt(document.getElementById('testAdults').value);
            const children = parseInt(document.getElementById('testChildren').value);
            const infants = parseInt(document.getElementById('testInfants').value);
            const resultDiv = document.getElementById('familyTypeResult');
            resultDiv.className = 'result loading';
            resultDiv.textContent = `Detecting family type for ${adults} adults, ${children} children, ${infants} infants...`;

            try {
                const familyType = await databaseService.detectFamilyType(adults, children, infants);
                resultDiv.className = 'result success';
                resultDiv.textContent = `✅ SUCCESS: Detected family type\n\n` + JSON.stringify(familyType, null, 2);
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ ERROR: ${error.message}\n\n` + JSON.stringify(error, null, 2);
            }
        }

        async function testQuoteSubmission() {
            const resultDiv = document.getElementById('quoteResult');
            resultDiv.className = 'result loading';
            resultDiv.textContent = 'Submitting test quote request...';

            try {
                const testQuoteData = {
                    customer_email: '<EMAIL>',
                    customer_phone: '+91XXXXXXXXXX',
                    customer_name: 'Test User',
                    destination: 'Kashmir',
                    travel_date: '2024-12',
                    adults: 2,
                    children: 0,
                    infants: 0,
                    selected_package_id: 'test-package-id',
                    selected_emi_plan_id: 'test-emi-plan-id'
                };

                const response = await databaseService.submitQuoteRequest(testQuoteData);
                resultDiv.className = 'result success';
                resultDiv.textContent = `✅ SUCCESS: Quote submitted with ID ${response.quote_id}\n\n` + 
                                       JSON.stringify(response, null, 2);
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ ERROR: ${error.message}\n\n` + JSON.stringify(error, null, 2);
            }
        }

        function checkConfiguration() {
            const resultDiv = document.getElementById('configResult');
            
            const configStatus = {
                configLoaded: typeof CONFIG !== 'undefined',
                supabaseLoaded: typeof supabase !== 'undefined',
                databaseServiceLoaded: typeof databaseService !== 'undefined',
                crmKeySet: CONFIG?.CRM_ANON_KEY !== 'YOUR_CRM_DATABASE_ANON_KEY_HERE',
                quoteKeySet: CONFIG?.QUOTE_ANON_KEY !== 'YOUR_QUOTE_DATABASE_ANON_KEY_HERE',
                environment: CONFIG?.IS_PRODUCTION ? 'production' : 'development',
                features: CONFIG?.FEATURES
            };

            const allGood = configStatus.configLoaded && 
                           configStatus.supabaseLoaded && 
                           configStatus.databaseServiceLoaded && 
                           configStatus.crmKeySet && 
                           configStatus.quoteKeySet;

            resultDiv.className = allGood ? 'result success' : 'result error';
            resultDiv.textContent = (allGood ? '✅ CONFIGURATION OK' : '❌ CONFIGURATION ISSUES') + 
                                   '\n\n' + JSON.stringify(configStatus, null, 2);
        }

        // Auto-check configuration on load
        window.addEventListener('load', () => {
            setTimeout(checkConfiguration, 1000);
        });
    </script>
</body>
</html>
