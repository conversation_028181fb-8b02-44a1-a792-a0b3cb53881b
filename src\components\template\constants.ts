import { TravelData, ElementStyle, TemplateLayout } from "./types"

export const defaultElementStyle: ElementStyle = {
  fontFamily: "Poppins",
  fontSize: 14,
  fontWeight: "400",
  color: "#1a1a1a",
  backgroundColor: "transparent",
  padding: 8,
  borderRadius: 4,
  textAlign: "left",
  alignItems: "flex-start",
  opacity: 1,
}

export const defaultTravelData: TravelData = {
  title: "Amazing Bali Adventure",
  date: "March 15-22, 2024",
  destination: "Bali",
  packageDetails: "7 Days / 6 Nights",
  itinerary:
    "Day 1: Arrival & Ubud\nDay 2: Rice Terraces\nDay 3: Volcano Trek\nDay 4: Beach Day\nDay 5: Cultural Tour\nDay 6: Water Sports\nDay 7: Departure",
  plan: "All-inclusive luxury experience with guided tours and premium accommodations",
  overlays: "Free WiFi, Airport Transfer, Welcome Drink",
  inclusions: "Accommodation, Meals, Transportation, Guide, Activities",
  exclusions: "International Flights, Personal Expenses, Travel Insurance",
  price: "19,000",
  currency: "₹",
}

// Sample data for different categories and destinations
export const honeymoonBaliData: TravelData = {
  title: "Romantic Bali Honeymoon",
  date: "Available Year Round",
  destination: "Bali",
  packageDetails: "5 Days / 4 Nights",
  itinerary: "Private villa • Sunset dinner cruise • Couple spa • Beach picnic",
  plan: "Romantic escape with private villa, couple spa sessions, candlelight dinners and beach experiences",
  overlays: "💕 Honeymoon Special • Free Couple Spa • Private Pool Villa",
  inclusions: "Luxury Villa, Airport Transfer, Couple Spa, All Meals, Activities",
  exclusions: "Flights, Personal Shopping, Extra Activities",
  price: "45,000",
  currency: "₹",
}

export const familyAndamanData: TravelData = {
  title: "Andaman Family Adventure",
  date: "Nov 2024 - Apr 2025",
  destination: "Andaman",
  packageDetails: "6 Days / 5 Nights",
  itinerary: "Glass bottom boat • Water sports • Beach activities • Island hopping",
  plan: "Perfect family getaway with safe water activities, beach fun and educational experiences for kids",
  overlays: "🏖️ Family Special • Kids Activities • Safe Water Sports",
  inclusions: "Family Rooms, All Meals, Water Sports, Island Tours, Activities",
  exclusions: "Flights, Personal Expenses, Extra Water Sports",
  price: "32,000",
  currency: "₹",
}

export const coupleManaliData: TravelData = {
  title: "Couple's Manali Retreat",
  date: "Oct 2024 - Mar 2025",
  destination: "Manali",
  packageDetails: "4 Days / 3 Nights",
  itinerary: "Cozy mountain stays • Adventure activities • Local sightseeing • Romantic dinners",
  plan: "Perfect couple getaway with mountain views, adventure activities and romantic experiences",
  overlays: "🏔️ Couple Special • Adventure Activities • Mountain Views",
  inclusions: "Hotel Stay, All Meals, Sightseeing, Adventure Activities",
  exclusions: "Transport to Manali, Personal Expenses, Extra Activities",
  price: "18,000",
  currency: "₹",
}

export const friendsBaliData: TravelData = {
  title: "Bali Friends Adventure",
  date: "Dec 2024 - Jan 2025",
  destination: "Bali",
  packageDetails: "7 Days / 6 Nights",
  itinerary: "Beach parties • Adventure sports • Temple tours • Nightlife experiences",
  plan: "Ultimate friends trip with group activities, adventure sports, cultural experiences and vibrant nightlife",
  overlays: "🎉 Group Special • Adventure Sports • Beach Parties",
  inclusions: "Group Accommodation, Meals, Activities, Transfers, Guide",
  exclusions: "Flights, Personal Expenses, Nightlife Expenses",
  price: "28,000",
  currency: "₹",
}

export const templateFormats: TemplateLayout[] = [
  {
    id: "square",
    name: "Square\n1080 x 1080 px",
    dimensions: { width: 1080, height: 1080 },
    displaySize: { width: 400, height: 400 },
    backgroundStyle: {
      backgroundColor: "#1e293b",
      backgroundImage: "https://images.unsplash.com/photo-1488646953014-85cb44e25828?w=800",
      overlay: true,
      overlayOpacity: 0.3,
    },
    elements: [
      // Main destination banner
      {
        id: "destination",
        type: "text",
        content: "destination",
        field: "destination",
        position: { x: 30, y: 40 },
        size: { width: 340, height: 50 },
        style: { 
          ...defaultElementStyle, 
          fontSize: 36, 
          fontFamily: "Prata",
          fontWeight: "700",
          color: "#ffffff", 
          textAlign: "center",
          letterSpacing: "1px" as any,
          padding: 0,
        },
      },
      // Title/package name
      {
        id: "title",
        type: "text",
        content: "title",
        field: "title",
        position: { x: 30, y: 100 },
        size: { width: 340, height: 35 },
        style: { 
          ...defaultElementStyle, 
          fontSize: 20, 
          fontFamily: "Quicksand",
          fontWeight: "600", 
          textAlign: "center", 
          color: "#f8fafc",
        },
      },
      // Package details with better styling  
      {
        id: "package",
        type: "text",
        content: "packageDetails",
        field: "packageDetails",
        position: { x: 30, y: 170 },
        size: { width: 160, height: 35 },
        style: { 
          ...defaultElementStyle, 
          fontSize: 14, 
          fontFamily: "Nunito Sans",
          fontWeight: "600",
          textAlign: "center", 
          color: "#1f2937",
          backgroundColor: "#fbbf24",
          borderRadius: 18,
          padding: 10,
        },
      },
      // Date with modern styling
      {
        id: "date",
        type: "text",
        content: "date",
        field: "date",
        position: { x: 210, y: 170 },
        size: { width: 160, height: 35 },
        style: { 
          ...defaultElementStyle, 
          fontSize: 14,
          fontFamily: "Nunito Sans", 
          fontWeight: "600",
          textAlign: "center",
          color: "#ffffff",
          backgroundColor: "#10b981",
          borderRadius: 18,
          padding: 10,
        },
      },
      // Description/plan
      {
        id: "plan",
        type: "text",
        content: "plan",
        field: "plan",
        position: { x: 30, y: 220 },
        size: { width: 340, height: 50 },
        style: { 
          ...defaultElementStyle, 
          fontSize: 13, 
          fontFamily: "Nunito Sans",
          fontWeight: "400",
          textAlign: "center", 
          color: "#e2e8f0",
          lineHeight: "1.4" as any,
          backgroundColor: "rgba(0,0,0,0.3)" as any,
          borderRadius: 8,
          padding: 5,
        },
      },
      // Price banner - moved to bottom
      {
        id: "price-banner",
        type: "text",
        content: "STARTING FROM",
        position: { x: 80, y: 290 },
        size: { width: 240, height: 25 },
        style: { 
          ...defaultElementStyle, 
          fontSize: 11, 
          fontFamily: "Montserrat",
          fontWeight: "500",
          textAlign: "center", 
          color: "#fbbf24",
          padding: 3,
          textTransform: "uppercase" as any,
          letterSpacing: "2px" as any
        },
      },
      {
        id: "price",
        type: "price",
        content: "price",
        field: "price",
        position: { x: 80, y: 315 },
        size: { width: 240, height: 55 },
        style: {
          ...defaultElementStyle,
          fontSize: 32,
          fontFamily: "Quicksand",
          fontWeight: "700",
          color: "#ffffff",
          backgroundColor: "linear-gradient(135deg, #3b82f6, #1d4ed8)" as any,
          textAlign: "center",
          borderRadius: 28,
          padding: 3,
        },
      },
    ],
  },

  // HONEYMOON TEMPLATES
  {
    id: "honeymoon-romantic",
    name: "Honeymoon Romance\n1080 x 1080 px",
    dimensions: { width: 1080, height: 1080 },
    displaySize: { width: 400, height: 400 },
    backgroundStyle: {
      backgroundColor: "#fdf2f8",
      backgroundImage: "/images/11.jpg",
      overlay: true,
      overlayOpacity: 0.4,
    },
    elements: [
      // Romantic header with hearts
      {
        id: "category-badge",
        type: "text",
        content: "💕 HONEYMOON SPECIAL",
        position: { x: 80, y: 25 },
        size: { width: 240, height: 30 },
        style: { 
          ...defaultElementStyle, 
          fontSize: 16, 
          fontFamily: "Pacifico",
          fontWeight: "400",
          color: "#be185d", 
          backgroundColor: "rgba(255, 255, 255, 0.95)",
          textAlign: "center",
          borderRadius: 20,
          padding: 8,
        },
      },
      // Destination with elegant styling
      {
        id: "destination",
        type: "text",
        content: "destination",
        field: "destination",
        position: { x: 50, y: 75 },
        size: { width: 300, height: 45 },
        style: { 
          ...defaultElementStyle, 
          fontSize: 28, 
          fontFamily: "Playfair Display",
          fontWeight: "700",
          color: "#ffffff", 
          textAlign: "center",
          letterSpacing: "1px" as any,
        },
      },
      // Romantic title
      {
        id: "title",
        type: "text",
        content: "title",
        field: "title",
        position: { x: 40, y: 130 },
        size: { width: 320, height: 40 },
        style: { 
          ...defaultElementStyle, 
          fontSize: 18, 
          fontFamily: "Dancing Script",
          fontWeight: "700", 
          textAlign: "center", 
          color: "#fdf2f8",
        },
      },
      // Package details with hearts
      {
        id: "package",
        type: "text",
        content: "packageDetails",
        field: "packageDetails",
        position: { x: 60, y: 185 },
        size: { width: 130, height: 35 },
        style: { 
          ...defaultElementStyle, 
          fontSize: 13, 
          fontFamily: "Nunito Sans",
          fontWeight: "600",
          textAlign: "center", 
          color: "#be185d",
          backgroundColor: "rgba(255, 255, 255, 0.95)",
          borderRadius: 18,
          padding: 10,
          border: "2px solid #f9a8d4" as any,
        },
      },
      // Date
      {
        id: "date",
        type: "text",
        content: "date",
        field: "date",
        position: { x: 210, y: 185 },
        size: { width: 130, height: 35 },
        style: { 
          ...defaultElementStyle, 
          fontSize: 13,
          fontFamily: "Nunito Sans", 
          fontWeight: "600",
          textAlign: "center",
          color: "#ffffff",
          backgroundColor: "#f472b6",
          borderRadius: 18,
          padding: 10,
        },
      },
      // Romantic features
      {
        id: "plan",
        type: "text",
        content: "plan",
        field: "plan",
        position: { x: 30, y: 240 },
        size: { width: 340, height: 40 },
        style: { 
          ...defaultElementStyle, 
          fontSize: 12, 
          fontFamily: "Nunito Sans",
          fontWeight: "400",
          textAlign: "center", 
          color: "#1f2937",
          lineHeight: "1.4" as any,
          backgroundColor: "rgba(255, 255, 255, 0.9)" as any,
          borderRadius: 12,
          padding: 12,
          border: "1px solid #f9a8d4" as any,
        },
      },
      // Heart-shaped price container - moved to bottom
      {
        id: "price-label",
        type: "text",
        content: "Starting from",
        position: { x: 140, y: 300 },
        size: { width: 120, height: 18 },
        style: { 
          ...defaultElementStyle, 
          fontSize: 11, 
          fontFamily: "Quicksand",
          fontWeight: "500",
          textAlign: "center", 
          color: "#be185d",
          backgroundColor: "rgba(255, 255, 255, 0.9)",
          borderRadius: 10,
          padding: 4,
        },
      },
      {
        id: "price",
        type: "price",
        content: "price",
        field: "price",
        position: { x: 120, y: 325 },
        size: { width: 160, height: 50 },
        style: {
          ...defaultElementStyle,
          fontSize: 28,
          fontFamily: "Quicksand",
          fontWeight: "700",
          color: "#ffffff",
          backgroundColor: "linear-gradient(135deg, #ec4899, #be185d)" as any,
          textAlign: "center",
          borderRadius: 25,
          padding: 12,
        },
      },
    ],
  },

  // COUPLE ADVENTURE TEMPLATE
  {
    id: "couple-adventure",
    name: "Couple Adventure\n1080 x 1080 px", 
    dimensions: { width: 1080, height: 1080 },
    displaySize: { width: 400, height: 400 },
    backgroundStyle: {
      backgroundColor: "#0c4a6e",
      backgroundImage: "/images/22.jpg",
      overlay: true,
      overlayOpacity: 0.5,
    },
    elements: [
      // Adventure badge
      {
        id: "category-badge",
        type: "text",
        content: "🌟 COUPLE ADVENTURE",
        position: { x: 80, y: 25 },
        size: { width: 240, height: 30 },
        style: { 
          ...defaultElementStyle, 
          fontSize: 15, 
          fontFamily: "Fredoka One",
          fontWeight: "400",
          color: "#0369a1", 
          backgroundColor: "rgba(224, 247, 250, 0.95)",
          textAlign: "center",
          borderRadius: 20,
          padding: 8,
        },
      },
      // Bold destination
      {
        id: "destination",
        type: "text",
        content: "destination",
        field: "destination",
        position: { x: 30, y: 75 },
        size: { width: 340, height: 50 },
        style: { 
          ...defaultElementStyle, 
          fontSize: 34, 
          fontFamily: "Oswald",
          fontWeight: "700",
          color: "#ffffff", 
          textAlign: "center",
          letterSpacing: "2px" as any,
        },
      },
      // Dynamic title
      {
        id: "title",
        type: "text",
        content: "title",
        field: "title",
        position: { x: 30, y: 130 },
        size: { width: 340, height: 35 },
        style: { 
          ...defaultElementStyle, 
          fontSize: 19, 
          fontFamily: "Quicksand",
          fontWeight: "600", 
          textAlign: "center", 
          color: "#e0f7fa",
        },
      },
      // Activity tags
      {
        id: "package",
        type: "text",
        content: "packageDetails",
        field: "packageDetails",
        position: { x: 50, y: 180 },
        size: { width: 120, height: 35 },
        style: { 
          ...defaultElementStyle, 
          fontSize: 12, 
          fontFamily: "Nunito Sans",
          fontWeight: "700",
          textAlign: "center", 
          color: "#0c4a6e",
          backgroundColor: "#facc15",
          borderRadius: 8,
          padding: 10,
          textTransform: "uppercase" as any,
        },
      },
      {
        id: "date",
        type: "text",
        content: "date",
        field: "date",
        position: { x: 190, y: 180 },
        size: { width: 130, height: 35 },
        style: { 
          ...defaultElementStyle, 
          fontSize: 12,
          fontFamily: "Nunito Sans", 
          fontWeight: "700",
          textAlign: "center",
          color: "#ffffff",
          backgroundColor: "#0ea5e9",
          borderRadius: 8,
          padding: 10,
          textTransform: "uppercase" as any,
        },
      },
      // Adventure description
      {
        id: "plan",
        type: "text",
        content: "plan",
        field: "plan",
        position: { x: 30, y: 230 },
        size: { width: 340, height: 50 },
        style: { 
          ...defaultElementStyle, 
          fontSize: 12, 
          fontFamily: "Nunito Sans",
          fontWeight: "400",
          color: "#ffffff", 
          lineHeight: "1.4" as any,
          backgroundColor: "rgba(14, 165, 233, 0.8)" as any,
          borderRadius: 12,
          padding: 12,
          border: "1px solid #facc15" as any,
        },
      },
      // Action-style price - moved to bottom
      {
        id: "price-banner",
        type: "text",
        content: "ADVENTURE STARTS AT",
        position: { x: 80, y: 300 },
        size: { width: 240, height: 18 },
        style: { 
          ...defaultElementStyle, 
          fontSize: 11, 
          fontFamily: "Oswald",
          fontWeight: "600",
          textAlign: "center", 
          color: "#facc15",
          textTransform: "uppercase" as any,
          letterSpacing: "3px" as any
        },
      },
      {
        id: "price",
        type: "price",
        content: "price",
        field: "price",
        position: { x: 100, y: 325 },
        size: { width: 200, height: 50 },
        style: {
          ...defaultElementStyle,
          fontSize: 28,
          fontFamily: "Oswald",
          fontWeight: "700",
          color: "#ffffff",
          backgroundColor: "linear-gradient(135deg, #0ea5e9, #0369a1)" as any,
          textAlign: "center",
          borderRadius: 8,
          padding: 12,
          border: "2px solid #facc15" as any,
        },
      },
      // Special highlights banner
      {
        id: "highlights",
        type: "text",
        content: "overlays",
        field: "overlays",
        position: { x: 20, y: 280 },
        size: { width: 185, height: 35 },
        style: {
          ...defaultElementStyle,
          fontSize: 11,
          fontFamily: "Nunito Sans",
          fontWeight: "600",
          color: "#fbbf24",
          backgroundColor: "linear-gradient(135deg, #1e293b, #334155)" as any,
          textAlign: "center",
          padding: 10,
          borderRadius: 8,
          border: "1px solid #fbbf24" as any,
        },
      },
      // Inclusions with clean card
      {
        id: "inclusions",
        type: "text",
        content: "inclusions",
        field: "inclusions",
        position: { x: 20, y: 330 },
        size: { width: 185, height: 55 },
        style: { 
          ...defaultElementStyle, 
          fontSize: 10, 
          fontFamily: "Nunito Sans",
          fontWeight: "400",
          color: "#374151",
          backgroundColor: "rgba(255,255,255,0.9)" as any,
          borderRadius: 8,
          padding: 8,
          lineHeight: "1.3" as any,
        },
      },
    ],
  },

  // FAMILY FUN TEMPLATE
  {
    id: "family-fun",
    name: "Family Fun\n1080 x 1080 px",
    dimensions: { width: 1080, height: 1080 },
    displaySize: { width: 400, height: 400 },
    backgroundStyle: {
      backgroundColor: "#dcfce7",
      backgroundImage: "/images/33.jpg",
      overlay: true,
      overlayOpacity: 0.3,
    },
    elements: [
      // Family badge with emoji
      {
        id: "category-badge",
        type: "text",
        content: "👨‍👩‍👧‍👦 FAMILY SPECIAL",
        position: { x: 80, y: 25 },
        size: { width: 240, height: 30 },
        style: { 
          ...defaultElementStyle, 
          fontSize: 15, 
          fontFamily: "Comfortaa",
          fontWeight: "700",
          color: "#166534", 
          backgroundColor: "rgba(220, 252, 231, 0.95)",
          textAlign: "center",
          borderRadius: 20,
          padding: 8,
        },
      },
      // Friendly destination
      {
        id: "destination",
        type: "text",
        content: "destination",
        field: "destination",
        position: { x: 30, y: 75 },
        size: { width: 340, height: 50 },
        style: { 
          ...defaultElementStyle, 
          fontSize: 32, 
          fontFamily: "Quicksand",
          fontWeight: "700",
          color: "#ffffff", 
          textAlign: "center",
          letterSpacing: "1px" as any,
        },
      },
      // Fun title
      {
        id: "title",
        type: "text",
        content: "title",
        field: "title",
        position: { x: 30, y: 130 },
        size: { width: 340, height: 35 },
        style: { 
          ...defaultElementStyle, 
          fontSize: 18, 
          fontFamily: "Nunito Sans",
          fontWeight: "600", 
          textAlign: "center", 
          color: "#f0fdf4",
        },
      },
      // Family-friendly price
      {
        id: "price-banner",
        type: "text",
        content: "FAMILY PACKAGE FROM",
        position: { x: 80, y: 300 },
        size: { width: 240, height: 18 },
        style: { 
          ...defaultElementStyle, 
          fontSize: 11, 
          fontFamily: "Nunito Sans",
          fontWeight: "600",
          textAlign: "center", 
          color: "#166534",
          backgroundColor: "rgba(220, 252, 231, 0.9)",
          textTransform: "uppercase" as any,
          letterSpacing: "1px" as any,
          borderRadius: 10,
          padding: 4,
        },
      },
      {
        id: "price",
        type: "price",
        content: "price",
        field: "price",
        position: { x: 100, y: 325 },
        size: { width: 200, height: 50 },
        style: {
          ...defaultElementStyle,
          fontSize: 28,
          fontFamily: "Quicksand",
          fontWeight: "700",
          color: "#ffffff",
          backgroundColor: "linear-gradient(135deg, #22c55e, #16a34a)" as any,
          textAlign: "center",
          borderRadius: 25,
          padding: 12,
        },
      },
      // Kids-friendly tags
      {
        id: "package",
        type: "text",
        content: "packageDetails",
        field: "packageDetails",
        position: { x: 50, y: 180 },
        size: { width: 120, height: 35 },
        style: { 
          ...defaultElementStyle, 
          fontSize: 12, 
          fontFamily: "Nunito Sans",
          fontWeight: "600",
          textAlign: "center", 
          color: "#166534",
          backgroundColor: "#bbf7d0",
          borderRadius: 18,
          padding: 10,
          border: "2px solid #22c55e" as any,
        },
      },
      {
        id: "date",
        type: "text",
        content: "date",
        field: "date",
        position: { x: 190, y: 180 },
        size: { width: 130, height: 35 },
        style: { 
          ...defaultElementStyle, 
          fontSize: 12,
          fontFamily: "Nunito Sans", 
          fontWeight: "600",
          textAlign: "center",
          color: "#ffffff",
          backgroundColor: "#f59e0b",
          borderRadius: 18,
          padding: 10,
        },
      },
      // Family activities
      {
        id: "plan",
        type: "text",
        content: "plan",
        field: "plan",
        position: { x: 30, y: 230 },
        size: { width: 340, height: 50 },
        style: { 
          ...defaultElementStyle, 
          fontSize: 12, 
          fontFamily: "Nunito Sans",
          fontWeight: "400",
          textAlign: "center", 
          color: "#166534",
          lineHeight: "1.4" as any,
          backgroundColor: "rgba(220, 252, 231, 0.95)" as any,
          borderRadius: 12,
          padding: 12,
          border: "1px solid #22c55e" as any,
        },
      },
    ],
  },

  // FRIENDS GROUP TEMPLATE
  {
    id: "friends-group",
    name: "Friends Group\n1080 x 1080 px",
    dimensions: { width: 1080, height: 1080 },
    displaySize: { width: 400, height: 400 },
    backgroundStyle: {
      backgroundColor: "#1e1b4b",
      backgroundImage: "/images/1.jpg",
      overlay: true,
      overlayOpacity: 0.4,
    },
    elements: [
      // Party badge
      {
        id: "category-badge",
        type: "text",
        content: "🎉 SQUAD GOALS",
        position: { x: 80, y: 25 },
        size: { width: 240, height: 30 },
        style: { 
          ...defaultElementStyle, 
          fontSize: 16, 
          fontFamily: "Bangers",
          fontWeight: "400",
          color: "#fbbf24", 
          backgroundColor: "rgba(30, 27, 75, 0.95)",
          textAlign: "center",
          borderRadius: 20,
          padding: 8,
          textTransform: "uppercase" as any,
          letterSpacing: "2px" as any,
          border: "2px solid #fbbf24" as any,
        },
      },
      // Bold destination with neon effect
      {
        id: "destination",
        type: "text",
        content: "destination",
        field: "destination",
        position: { x: 30, y: 75 },
        size: { width: 340, height: 50 },
        style: { 
          ...defaultElementStyle, 
          fontSize: 36, 
          fontFamily: "Bebas Neue",
          fontWeight: "400",
          color: "#fbbf24", 
          textAlign: "center",
          letterSpacing: "3px" as any,
        },
      },
      // Fun title
      {
        id: "title",
        type: "text",
        content: "title",
        field: "title",
        position: { x: 30, y: 130 },
        size: { width: 340, height: 35 },
        style: { 
          ...defaultElementStyle, 
          fontSize: 18, 
          fontFamily: "Righteous",
          fontWeight: "400", 
          textAlign: "center", 
          color: "#ffffff",
        },
      },
      // Party details
      {
        id: "package",
        type: "text",
        content: "packageDetails",
        field: "packageDetails",
        position: { x: 50, y: 180 },
        size: { width: 120, height: 35 },
        style: { 
          ...defaultElementStyle, 
          fontSize: 12, 
          fontFamily: "Nunito Sans",
          fontWeight: "700",
          textAlign: "center", 
          color: "#1e1b4b",
          backgroundColor: "#a855f7",
          borderRadius: 18,
          padding: 10,
          textTransform: "uppercase" as any,
        },
      },
      {
        id: "date",
        type: "text",
        content: "date",
        field: "date",
        position: { x: 190, y: 180 },
        size: { width: 130, height: 35 },
        style: { 
          ...defaultElementStyle, 
          fontSize: 12,
          fontFamily: "Nunito Sans", 
          fontWeight: "700",
          textAlign: "center",
          color: "#ffffff",
          backgroundColor: "#ec4899",
          borderRadius: 18,
          padding: 10,
          textTransform: "uppercase" as any,
        },
      },
      // Squad activities
      {
        id: "plan",
        type: "text",
        content: "plan",
        field: "plan",
        position: { x: 30, y: 230 },
        size: { width: 340, height: 50 },
        style: { 
          ...defaultElementStyle, 
          fontSize: 12, 
          fontFamily: "Nunito Sans",
          fontWeight: "500",
          textAlign: "center", 
          color: "#fbbf24",
          lineHeight: "1.4" as any,
          backgroundColor: "rgba(30, 27, 75, 0.9)" as any,
          borderRadius: 12,
          padding: 12,
          border: "1px solid #fbbf24" as any,
        },
      },
      // Group discount - moved to bottom
      {
        id: "price-banner",
        type: "text",
        content: "GROUP DISCOUNT",
        position: { x: 80, y: 300 },
        size: { width: 240, height: 18 },
        style: { 
          ...defaultElementStyle, 
          fontSize: 12, 
          fontFamily: "Oswald",
          fontWeight: "600",
          textAlign: "center", 
          color: "#a855f7",
          textTransform: "uppercase" as any,
          letterSpacing: "2px" as any,
          backgroundColor: "rgba(255, 255, 255, 0.1)",
          borderRadius: 10,
          padding: 4,
        },
      },
      {
        id: "price",
        type: "price",
        content: "price",
        field: "price",
        position: { x: 100, y: 325 },
        size: { width: 200, height: 50 },
        style: {
          ...defaultElementStyle,
          fontSize: 28,
          fontFamily: "Bebas Neue",
          fontWeight: "400",
          color: "#1e1b4b",
          backgroundColor: "linear-gradient(135deg, #fbbf24, #f59e0b)" as any,
          textAlign: "center",
          borderRadius: 12,
          padding: 12,
        },
      },
    ],
  },

  // STORY FORMAT TEMPLATES
  {
    id: "story",
    name: "Story\n1080 x 1920 px",
    dimensions: { width: 1080, height: 1920 },
    displaySize: { width: 225, height: 400 },
    backgroundStyle: {
      backgroundColor: "#0f172a",
      backgroundImage: "https://images.unsplash.com/photo-1488646953014-85cb44e25828?w=800",
      overlay: true,
      overlayOpacity: 0.5,
    },
    elements: [
      // Top destination tag
      {
        id: "destination-tag",
        type: "text",
        content: "destination",
        field: "destination",
        position: { x: 20, y: 25 },
        size: { width: 120, height: 25 },
        style: { 
          ...defaultElementStyle, 
          fontSize: 11, 
          fontFamily: "Montserrat",
          fontWeight: "600",
          color: "#1f2937", 
          backgroundColor: "#fbbf24",
          textAlign: "center",
          letterSpacing: "1px" as any,
          borderRadius: 12,
          padding: 6,
        },
      },
      // Price tag - top right
      {
        id: "price",
        type: "price",
        content: "price",
        field: "price",
        position: { x: 155, y: 25 },
        size: { width: 50, height: 25 },
        style: { 
          ...defaultElementStyle, 
          fontSize: 12, 
          fontFamily: "Quicksand",
          fontWeight: "700", 
          color: "#ffffff", 
          backgroundColor: "#10b981",
          textAlign: "center",
          borderRadius: 12,
          padding: 4,
        },
      },
      // Main title - prominent
      {
        id: "title",
        type: "text",
        content: "title",
        field: "title",
        position: { x: 20, y: 70 },
        size: { width: 185, height: 70 },
        style: { 
          ...defaultElementStyle, 
          fontSize: 22, 
          fontFamily: "Playfair Display",
          fontWeight: "700", 
          color: "#ffffff", 
          lineHeight: "1.2" as any,
        },
      },
      // Package type with modern styling
      {
        id: "package",
        type: "text",
        content: "packageDetails",
        field: "packageDetails",
        position: { x: 20, y: 155 },
        size: { width: 80, height: 20 },
        style: { 
          ...defaultElementStyle, 
          fontSize: 9, 
          fontFamily: "Nunito Sans",
          fontWeight: "600",
          color: "#1f2937", 
          backgroundColor: "#e2e8f0",
          textAlign: "center",
          borderRadius: 10,
          padding: 4,
          textTransform: "uppercase" as any,
        },
      },
      // Date badge
      {
        id: "date",
        type: "text",
        content: "date",
        field: "date",
        position: { x: 115, y: 155 },
        size: { width: 90, height: 20 },
        style: { 
          ...defaultElementStyle, 
          fontSize: 9, 
          fontFamily: "Nunito Sans",
          fontWeight: "600",
          color: "#ffffff",
          backgroundColor: "#3b82f6",
          textAlign: "center",
          borderRadius: 10,
          padding: 4,
        },
      },
      // Main description with card styling
      {
        id: "plan",
        type: "text",
        content: "plan",
        field: "plan",
        position: { x: 20, y: 190 },
        size: { width: 185, height: 75 },
        style: { 
          ...defaultElementStyle, 
          fontSize: 12, 
          fontFamily: "Nunito Sans",
          fontWeight: "400",
          color: "#1f2937", 
          lineHeight: "1.4" as any,
          backgroundColor: "rgba(255,255,255,0.95)" as any,
          borderRadius: 12,
          padding: 12,
        },
      },
      // Special highlights banner
      {
        id: "highlights",
        type: "text",
        content: "overlays",
        field: "overlays",
        position: { x: 20, y: 280 },
        size: { width: 185, height: 35 },
        style: {
          ...defaultElementStyle,
          fontSize: 11,
          fontFamily: "Nunito Sans",
          fontWeight: "600",
          color: "#fbbf24",
          backgroundColor: "linear-gradient(135deg, #1e293b, #334155)" as any,
          textAlign: "center",
          padding: 10,
          borderRadius: 8,
          border: "1px solid #fbbf24" as any,
        },
      },
      // Inclusions with clean card
      {
        id: "inclusions",
        type: "text",
        content: "inclusions",
        field: "inclusions",
        position: { x: 20, y: 330 },
        size: { width: 185, height: 55 },
        style: { 
          ...defaultElementStyle, 
          fontSize: 10, 
          fontFamily: "Nunito Sans",
          fontWeight: "400",
          color: "#374151",
          backgroundColor: "rgba(255,255,255,0.9)" as any,
          borderRadius: 8,
          padding: 8,
          lineHeight: "1.3" as any,
        },
      },
    ],
  },

  // HONEYMOON STORY
  {
    id: "honeymoon-story",
    name: "Honeymoon Story\n1080 x 1920 px",
    dimensions: { width: 1080, height: 1920 },
    displaySize: { width: 225, height: 400 },
    backgroundStyle: {
      backgroundColor: "#fdf2f8",
      backgroundImage: "/images/2.jpg",
      overlay: true,
      overlayOpacity: 0.3,
    },
    elements: [
      // Romantic top badge
      {
        id: "category-badge",
        type: "text",
        content: "💕 HONEYMOON",
        position: { x: 20, y: 20 },
        size: { width: 100, height: 25 },
        style: { 
          ...defaultElementStyle, 
          fontSize: 11, 
          fontFamily: "Dancing Script",
          fontWeight: "700",
          color: "#be185d", 
          backgroundColor: "rgba(255, 255, 255, 0.95)",
          textAlign: "center",
          borderRadius: 15,
          padding: 6,
        },
      },
      // Price in top corner
      {
        id: "price",
        type: "price",
        content: "price",
        field: "price",
        position: { x: 140, y: 20 },
        size: { width: 65, height: 25 },
        style: { 
          ...defaultElementStyle, 
          fontSize: 12, 
          fontFamily: "Quicksand",
          fontWeight: "700", 
          color: "#ffffff", 
          backgroundColor: "#ec4899",
          textAlign: "center",
          borderRadius: 15,
          padding: 6,
        },
      },
      // Romantic destination
      {
        id: "destination",
        type: "text",
        content: "destination",
        field: "destination",
        position: { x: 20, y: 60 },
        size: { width: 185, height: 45 },
        style: { 
          ...defaultElementStyle, 
          fontSize: 24, 
          fontFamily: "Playfair Display",
          fontWeight: "700",
          color: "#ffffff", 
          textAlign: "center",
          letterSpacing: "1px" as any,
        },
      },
      // Love story title
      {
        id: "title",
        type: "text",
        content: "title",
        field: "title",
        position: { x: 20, y: 110 },
        size: { width: 185, height: 55 },
        style: { 
          ...defaultElementStyle, 
          fontSize: 18, 
          fontFamily: "Dancing Script",
          fontWeight: "700", 
          color: "#fdf2f8", 
          lineHeight: "1.3" as any,
          textAlign: "center",
        },
      },
      // Package details
      {
        id: "package",
        type: "text",
        content: "packageDetails",
        field: "packageDetails",
        position: { x: 20, y: 180 },
        size: { width: 85, height: 25 },
        style: { 
          ...defaultElementStyle, 
          fontSize: 9, 
          fontFamily: "Nunito Sans",
          fontWeight: "600",
          color: "#be185d", 
          backgroundColor: "rgba(255, 255, 255, 0.9)",
          textAlign: "center",
          borderRadius: 12,
          padding: 6,
        },
      },
      // Date
      {
        id: "date",
        type: "text",
        content: "date",
        field: "date",
        position: { x: 120, y: 180 },
        size: { width: 85, height: 25 },
        style: { 
          ...defaultElementStyle, 
          fontSize: 9, 
          fontFamily: "Nunito Sans",
          fontWeight: "600",
          color: "#ffffff",
          backgroundColor: "#f472b6",
          textAlign: "center",
          borderRadius: 12,
          padding: 6,
        },
      },
      // Romantic description
      {
        id: "plan",
        type: "text",
        content: "plan",
        field: "plan",
        position: { x: 20, y: 220 },
        size: { width: 185, height: 80 },
        style: { 
          ...defaultElementStyle, 
          fontSize: 11, 
          fontFamily: "Nunito Sans",
          fontWeight: "400",
          color: "#be185d", 
          lineHeight: "1.4" as any,
          backgroundColor: "rgba(255, 255, 255, 0.95)" as any,
          borderRadius: 15,
          padding: 12,
          border: "1px solid #f9a8d4" as any,
        },
      },
      // Special romantic offers
      {
        id: "highlights",
        type: "text",
        content: "overlays",
        field: "overlays",
        position: { x: 20, y: 315 },
        size: { width: 185, height: 35 },
        style: {
          ...defaultElementStyle,
          fontSize: 10,
          fontFamily: "Nunito Sans",
          fontWeight: "600",
          color: "#be185d",
          backgroundColor: "rgba(251, 207, 232, 0.9)" as any,
          textAlign: "center",
          padding: 10,
          borderRadius: 12,
          border: "1px solid #ec4899" as any,
        },
      },
      // Inclusions
      {
        id: "inclusions",
        type: "text",
        content: "inclusions",
        field: "inclusions",
        position: { x: 20, y: 365 },
        size: { width: 185, height: 40 },
        style: { 
          ...defaultElementStyle, 
          fontSize: 9, 
          fontFamily: "Nunito Sans",
          fontWeight: "400",
          color: "#92125a",
          backgroundColor: "rgba(255, 255, 255, 0.9)" as any,
          borderRadius: 10,
          padding: 8,
          lineHeight: "1.3" as any,
        },
      },
    ],
  },

  // PORTRAIT FORMAT TEMPLATES
  {
    id: "portrait",
    name: "Portrait\n1080 x 1350 px",
    dimensions: { width: 1080, height: 1350 },
    displaySize: { width: 320, height: 400 },
    backgroundStyle: {
      backgroundColor: "#f8fafc",
      backgroundImage: "/images/2.jpg",
      overlay: true,
      overlayOpacity: 0.3,
    },
    elements: [
      // Header with destination and price
      {
        id: "destination",
        type: "text",
        content: "destination",
        field: "destination",
        position: { x: 20, y: 25 },
        size: { width: 200, height: 35 },
        style: { 
          ...defaultElementStyle, 
          fontSize: 24, 
          fontFamily: "Playfair Display",
          fontWeight: "700",
          color: "#1e293b",
          textTransform: "uppercase" as any,
          letterSpacing: "1px" as any,
        },
      },
      {
        id: "price",
        type: "price",
        content: "price",
        field: "price",
        position: { x: 240, y: 20 },
        size: { width: 60, height: 45 },
        style: {
          ...defaultElementStyle,
          fontSize: 18,
          fontFamily: "Quicksand",
          fontWeight: "700",
          color: "#ffffff",
          backgroundColor: "#3b82f6",
          textAlign: "center",
          borderRadius: 22,
          padding: 8,
        },
      },
      // Main title with better typography
      {
        id: "title",
        type: "text",
        content: "title",
        field: "title",
        position: { x: 20, y: 75 },
        size: { width: 280, height: 50 },
        style: { 
          ...defaultElementStyle, 
          fontSize: 18, 
          fontFamily: "Quicksand",
          fontWeight: "600", 
          color: "#374151",
          lineHeight: "1.3" as any,
        },
      },
      // Package and date badges
      {
        id: "package",
        type: "text",
        content: "packageDetails",
        field: "packageDetails",
        position: { x: 20, y: 140 },
        size: { width: 120, height: 30 },
        style: { 
          ...defaultElementStyle, 
          fontSize: 12, 
          fontFamily: "Nunito Sans",
          fontWeight: "600",
          color: "#1f2937", 
          backgroundColor: "#fbbf24",
          textAlign: "center",
          borderRadius: 15,
          padding: 8,
          textTransform: "uppercase" as any,
        },
      },
      {
        id: "date",
        type: "text",
        content: "date",
        field: "date",
        position: { x: 155, y: 140 },
        size: { width: 145, height: 30 },
        style: { 
          ...defaultElementStyle, 
          fontSize: 12, 
          fontFamily: "Nunito Sans",
          fontWeight: "600",
          color: "#ffffff",
          backgroundColor: "#10b981",
          textAlign: "center",
          borderRadius: 15,
          padding: 8,
        },
      },
      // Main description card
      {
        id: "plan",
        type: "text",
        content: "plan",
        field: "plan",
        position: { x: 20, y: 185 },
        size: { width: 280, height: 80 },
        style: {
          ...defaultElementStyle,
          fontSize: 13,
          fontFamily: "Nunito Sans",
          fontWeight: "400",
          color: "#374151",
          lineHeight: "1.5" as any,
          backgroundColor: "rgba(255,255,255,0.95)" as any,
          padding: 15,
          borderRadius: 12,
        },
      },
      // Inclusions and exclusions with modern cards
      {
        id: "inclusions",
        type: "text",
        content: "inclusions",
        field: "inclusions",
        position: { x: 20, y: 285 },
        size: { width: 130, height: 90 },
        style: {
          ...defaultElementStyle,
          fontSize: 11,
          fontFamily: "Nunito Sans",
          fontWeight: "400",
          color: "#1f2937",
          backgroundColor: "rgba(16, 185, 129, 0.1)" as any,
          border: "1px solid #10b981" as any,
          borderRadius: 10,
          padding: 12,
          lineHeight: "1.4" as any,
        },
      },
      {
        id: "exclusions",
        type: "text",
        content: "exclusions",
        field: "exclusions",
        position: { x: 170, y: 285 },
        size: { width: 130, height: 90 },
        style: {
          ...defaultElementStyle,
          fontSize: 11,
          fontFamily: "Nunito Sans",
          fontWeight: "400",
          color: "#1f2937",
          backgroundColor: "rgba(239, 68, 68, 0.1)" as any,
          border: "1px solid #ef4444" as any,
          borderRadius: 10,
          padding: 12,
          lineHeight: "1.4" as any,
        },
      },
    ],
  },

  // LANDSCAPE PROMOTIONAL TEMPLATES
  {
    id: "landscape-promo",
    name: "Landscape Promo\n1200 x 630 px",
    dimensions: { width: 1200, height: 630 },
    displaySize: { width: 400, height: 210 },
    backgroundStyle: {
      backgroundColor: "#1e40af",
      backgroundImage: "/images/11.jpg",
      overlay: true,
      overlayOpacity: 0.6,
    },
    elements: [
      // Big promotional banner
      {
        id: "promo-banner",
        type: "text",
        content: "LIMITED TIME OFFER",
        position: { x: 20, y: 15 },
        size: { width: 180, height: 25 },
        style: { 
          ...defaultElementStyle, 
          fontSize: 14, 
          fontFamily: "Bangers",
          fontWeight: "400",
          color: "#ffffff", 
          backgroundColor: "#dc2626",
          textAlign: "center",
          borderRadius: 5,
          padding: 6,
          textTransform: "uppercase" as any,
          letterSpacing: "2px" as any,
        },
      },
      // Destination
      {
        id: "destination",
        type: "text",
        content: "destination",
        field: "destination",
        position: { x: 20, y: 50 },
        size: { width: 200, height: 40 },
        style: { 
          ...defaultElementStyle, 
          fontSize: 28, 
          fontFamily: "Oswald",
          fontWeight: "700",
          color: "#ffffff", 
          textTransform: "uppercase" as any,
          letterSpacing: "2px" as any,
        },
      },
      // Main title
      {
        id: "title",
        type: "text",
        content: "title",
        field: "title",
        position: { x: 20, y: 95 },
        size: { width: 200, height: 35 },
        style: { 
          ...defaultElementStyle, 
          fontSize: 16, 
          fontFamily: "Quicksand",
          fontWeight: "600", 
          color: "#e2e8f0",
          lineHeight: "1.2" as any,
        },
      },
      // Details section
      {
        id: "package",
        type: "text",
        content: "packageDetails",
        field: "packageDetails",
        position: { x: 20, y: 140 },
        size: { width: 90, height: 25 },
        style: { 
          ...defaultElementStyle, 
          fontSize: 11, 
          fontFamily: "Nunito Sans",
          fontWeight: "600",
          color: "#1f2937", 
          backgroundColor: "#fbbf24",
          textAlign: "center",
          borderRadius: 12,
          padding: 6,
          textTransform: "uppercase" as any,
        },
      },
      {
        id: "date",
        type: "text",
        content: "date",
        field: "date",
        position: { x: 125, y: 140 },
        size: { width: 95, height: 25 },
        style: { 
          ...defaultElementStyle, 
          fontSize: 11, 
          fontFamily: "Nunito Sans",
          fontWeight: "600",
          color: "#ffffff",
          backgroundColor: "#10b981",
          textAlign: "center",
          borderRadius: 12,
          padding: 6,
        },
      },
      // Price section - large and prominent
      {
        id: "price-label",
        type: "text",
        content: "STARTS FROM",
        position: { x: 250, y: 25 },
        size: { width: 130, height: 20 },
        style: { 
          ...defaultElementStyle, 
          fontSize: 12, 
          fontFamily: "Oswald",
          fontWeight: "600",
          textAlign: "center", 
          color: "#fbbf24",
          textTransform: "uppercase" as any,
          letterSpacing: "2px" as any,
        },
      },
      {
        id: "price",
        type: "price",
        content: "price",
        field: "price",
        position: { x: 250, y: 50 },
        size: { width: 130, height: 70 },
        style: {
          ...defaultElementStyle,
          fontSize: 32,
          fontFamily: "Oswald",
          fontWeight: "700",
          color: "#ffffff",
          backgroundColor: "linear-gradient(135deg, #3b82f6, #1d4ed8)" as any,
          textAlign: "center",
          borderRadius: 15,
          padding: 18,
          border: "2px solid #fbbf24" as any,
        },
      },
      // Features
      {
        id: "plan",
        type: "text",
        content: "plan",
        field: "plan",
        position: { x: 20, y: 175 },
        size: { width: 360, height: 25 },
        style: { 
          ...defaultElementStyle, 
          fontSize: 11, 
          fontFamily: "Nunito Sans",
          fontWeight: "400",
          color: "#e2e8f0",
          lineHeight: "1.3" as any,
          backgroundColor: "rgba(0,0,0,0.4)" as any,
          borderRadius: 8,
          padding: 8,
        },
      },
    ],
  },

  // DESTINATION-SPECIFIC TEMPLATES
  
  // BALI THEMED TEMPLATE
  {
    id: "bali-tropical",
    name: "Bali Tropical\n1080 x 1080 px",
    dimensions: { width: 1080, height: 1080 },
    displaySize: { width: 400, height: 400 },
    backgroundStyle: {
      backgroundColor: "#0c4a6e",
      backgroundImage: "/images/11.jpg",
      overlay: true,
      overlayOpacity: 0.4,
    },
    elements: [
      // Tropical badge
      {
        id: "destination-badge",
        type: "text",
        content: "🌺 BALI PARADISE",
        position: { x: 80, y: 20 },
        size: { width: 240, height: 35 },
        style: { 
          ...defaultElementStyle, 
          fontSize: 16, 
          fontFamily: "Pacifico",
          fontWeight: "400",
          color: "#0c4a6e", 
          backgroundColor: "rgba(224, 247, 250, 0.95)",
          textAlign: "center",
          borderRadius: 25,
          padding: 10,
        },
      },
      // Bali title
      {
        id: "title",
        type: "text",
        content: "title",
        field: "title",
        position: { x: 30, y: 75 },
        size: { width: 340, height: 50 },
        style: { 
          ...defaultElementStyle, 
          fontSize: 26, 
          fontFamily: "Dancing Script",
          fontWeight: "700", 
          textAlign: "center", 
          color: "#ffffff",
          letterSpacing: "1px" as any,
        },
      },
      // Tropical features
      {
        id: "features",
        type: "text",
        content: "🏝️ Private Beach • 🌴 Villa Stay • 🐚 Water Sports",
        position: { x: 30, y: 135 },
        size: { width: 340, height: 35 },
        style: { 
          ...defaultElementStyle, 
          fontSize: 13, 
          fontFamily: "Nunito Sans",
          fontWeight: "500",
          color: "#e0f7fa", 
          textAlign: "center",
          backgroundColor: "rgba(12, 74, 110, 0.8)" as any,
          borderRadius: 18,
          padding: 10,
          border: "1px solid #0891b2" as any,
        },
      },
      // Ocean-themed price
      {
        id: "price-banner",
        type: "text",
        content: "TROPICAL ESCAPE FROM",
        position: { x: 80, y: 185 },
        size: { width: 240, height: 20 },
        style: { 
          ...defaultElementStyle, 
          fontSize: 11, 
          fontFamily: "Montserrat",
          fontWeight: "600",
          textAlign: "center", 
          color: "#0c4a6e",
          backgroundColor: "rgba(224, 247, 250, 0.9)",
          textTransform: "uppercase" as any,
          letterSpacing: "2px" as any,
          borderRadius: 10,
          padding: 4,
        },
      },
      {
        id: "price",
        type: "price",
        content: "price",
        field: "price",
        position: { x: 100, y: 210 },
        size: { width: 200, height: 55 },
        style: {
          ...defaultElementStyle,
          fontSize: 30,
          fontFamily: "Quicksand",
          fontWeight: "700",
          color: "#ffffff",
          backgroundColor: "linear-gradient(135deg, #0891b2, #0c4a6e)" as any,
          textAlign: "center",
          borderRadius: 28,
          padding: 15,
        },
      },
      // Tropical details
      {
        id: "package",
        type: "text",
        content: "packageDetails",
        field: "packageDetails",
        position: { x: 60, y: 285 },
        size: { width: 120, height: 35 },
        style: { 
          ...defaultElementStyle, 
          fontSize: 12, 
          fontFamily: "Nunito Sans",
          fontWeight: "600",
          textAlign: "center", 
          color: "#0c4a6e",
          backgroundColor: "#a7f3d0",
          borderRadius: 18,
          padding: 10,
        },
      },
      {
        id: "date",
        type: "text",
        content: "date",
        field: "date",
        position: { x: 200, y: 285 },
        size: { width: 120, height: 35 },
        style: { 
          ...defaultElementStyle, 
          fontSize: 12,
          fontFamily: "Nunito Sans", 
          fontWeight: "600",
          textAlign: "center",
          color: "#ffffff",
          backgroundColor: "#06b6d4",
          borderRadius: 18,
          padding: 10,
        },
      },
      // Beach experience
      {
        id: "plan",
        type: "text",
        content: "plan",
        field: "plan",
        position: { x: 30, y: 340 },
        size: { width: 340, height: 40 },
        style: { 
          ...defaultElementStyle, 
          fontSize: 12, 
          fontFamily: "Nunito Sans",
          fontWeight: "400",
          textAlign: "center", 
          color: "#ffffff",
          lineHeight: "1.4" as any,
          backgroundColor: "rgba(8, 145, 178, 0.8)" as any,
          borderRadius: 12,
          padding: 12,
        },
      },
    ],
  },

  // ANDAMAN BEACH TEMPLATE
  {
    id: "andaman-beach",
    name: "Andaman Beach\n1080 x 1080 px",
    dimensions: { width: 1080, height: 1080 },
    displaySize: { width: 400, height: 400 },
    backgroundStyle: {
      backgroundColor: "#065f46",
      backgroundImage: "/images/33.jpg",
      overlay: true,
      overlayOpacity: 0.4,
    },
    elements: [
      // Island badge
      {
        id: "destination-badge",
        type: "text",
        content: "🏖️ ANDAMAN ISLANDS",
        position: { x: 70, y: 20 },
        size: { width: 260, height: 35 },
        style: { 
          ...defaultElementStyle, 
          fontSize: 16, 
          fontFamily: "Comfortaa",
          fontWeight: "700",
          color: "#065f46", 
          backgroundColor: "rgba(167, 243, 208, 0.95)",
          textAlign: "center",
          borderRadius: 25,
          padding: 10,
        },
      },
      // Island title
      {
        id: "title",
        type: "text",
        content: "title",
        field: "title",
        position: { x: 30, y: 75 },
        size: { width: 340, height: 50 },
        style: { 
          ...defaultElementStyle, 
          fontSize: 28, 
          fontFamily: "Quicksand",
          fontWeight: "700", 
          textAlign: "center", 
          color: "#ffffff",
          letterSpacing: "1px" as any,
        },
      },
      // Island activities
      {
        id: "activities",
        type: "text",
        content: "🚤 Island Hopping • 🤿 Snorkeling • 🏝️ Beach Fun",
        position: { x: 30, y: 135 },
        size: { width: 340, height: 35 },
        style: { 
          ...defaultElementStyle, 
          fontSize: 13, 
          fontFamily: "Nunito Sans",
          fontWeight: "500",
          color: "#d1fae5", 
          textAlign: "center",
          backgroundColor: "rgba(6, 95, 70, 0.8)" as any,
          borderRadius: 18,
          padding: 10,
          border: "1px solid #10b981" as any,
        },
      },
      // Crystal clear pricing
      {
        id: "price-banner",
        type: "text",
        content: "CRYSTAL CLEAR DEALS",
        position: { x: 80, y: 185 },
        size: { width: 240, height: 20 },
        style: { 
          ...defaultElementStyle, 
          fontSize: 11, 
          fontFamily: "Montserrat",
          fontWeight: "600",
          textAlign: "center", 
          color: "#065f46",
          backgroundColor: "rgba(167, 243, 208, 0.9)",
          textTransform: "uppercase" as any,
          letterSpacing: "2px" as any,
          borderRadius: 10,
          padding: 4,
        },
      },
      {
        id: "price",
        type: "price",
        content: "price",
        field: "price",
        position: { x: 100, y: 210 },
        size: { width: 200, height: 55 },
        style: {
          ...defaultElementStyle,
          fontSize: 30,
          fontFamily: "Quicksand",
          fontWeight: "700",
          color: "#ffffff",
          backgroundColor: "linear-gradient(135deg, #10b981, #065f46)" as any,
          textAlign: "center",
          borderRadius: 28,
          padding: 15,
        },
      },
      // Island details
      {
        id: "package",
        type: "text",
        content: "packageDetails",
        field: "packageDetails",
        position: { x: 60, y: 285 },
        size: { width: 120, height: 35 },
        style: { 
          ...defaultElementStyle, 
          fontSize: 12, 
          fontFamily: "Nunito Sans",
          fontWeight: "600",
          textAlign: "center", 
          color: "#065f46",
          backgroundColor: "#a7f3d0",
          borderRadius: 18,
          padding: 10,
        },
      },
      {
        id: "date",
        type: "text",
        content: "date",
        field: "date",
        position: { x: 200, y: 285 },
        size: { width: 120, height: 35 },
        style: { 
          ...defaultElementStyle, 
          fontSize: 12,
          fontFamily: "Nunito Sans", 
          fontWeight: "600",
          textAlign: "center",
          color: "#ffffff",
          backgroundColor: "#0891b2",
          borderRadius: 18,
          padding: 10,
        },
      },
      // Island experience
      {
        id: "plan",
        type: "text",
        content: "plan",
        field: "plan",
        position: { x: 30, y: 340 },
        size: { width: 340, height: 40 },
        style: { 
          ...defaultElementStyle, 
          fontSize: 12, 
          fontFamily: "Nunito Sans",
          fontWeight: "400",
          textAlign: "center", 
          color: "#ffffff",
          lineHeight: "1.4" as any,
          backgroundColor: "rgba(16, 185, 129, 0.8)" as any,
          borderRadius: 12,
          padding: 12,
        },
      },
    ],
  },

  // MANALI MOUNTAIN TEMPLATE
  {
    id: "manali-mountain",
    name: "Manali Mountain\n1080 x 1080 px",
    dimensions: { width: 1080, height: 1080 },
    displaySize: { width: 400, height: 400 },
    backgroundStyle: {
      backgroundColor: "#374151",
      backgroundImage: "/images/22.jpg",
      overlay: true,
      overlayOpacity: 0.5,
    },
    elements: [
      // Mountain badge
      {
        id: "destination-badge",
        type: "text",
        content: "🏔️ MANALI MOUNTAINS",
        position: { x: 70, y: 20 },
        size: { width: 260, height: 35 },
        style: { 
          ...defaultElementStyle, 
          fontSize: 16, 
          fontFamily: "Oswald",
          fontWeight: "600",
          color: "#374151", 
          backgroundColor: "rgba(243, 244, 246, 0.95)",
          textAlign: "center",
          borderRadius: 25,
          padding: 10,
          textTransform: "uppercase" as any,
          letterSpacing: "1px" as any,
        },
      },
      // Mountain title
      {
        id: "title",
        type: "text",
        content: "title",
        field: "title",
        position: { x: 30, y: 75 },
        size: { width: 340, height: 50 },
        style: { 
          ...defaultElementStyle, 
          fontSize: 28, 
          fontFamily: "Montserrat",
          fontWeight: "700", 
          textAlign: "center", 
          color: "#ffffff",
          letterSpacing: "1px" as any,
          textTransform: "uppercase" as any,
        },
      },
      // Mountain activities
      {
        id: "activities",
        type: "text",
        content: "🎿 Adventure Sports • 🏂 Snow Activities • 🚡 Cable Car",
        position: { x: 30, y: 135 },
        size: { width: 340, height: 35 },
        style: { 
          ...defaultElementStyle, 
          fontSize: 13, 
          fontFamily: "Nunito Sans",
          fontWeight: "500",
          color: "#f3f4f6", 
          textAlign: "center",
          backgroundColor: "rgba(55, 65, 81, 0.8)" as any,
          borderRadius: 18,
          padding: 10,
          border: "1px solid #9ca3af" as any,
        },
      },
      // Mountain pricing
      {
        id: "price-banner",
        type: "text",
        content: "MOUNTAIN ADVENTURE",
        position: { x: 80, y: 185 },
        size: { width: 240, height: 20 },
        style: { 
          ...defaultElementStyle, 
          fontSize: 11, 
          fontFamily: "Oswald",
          fontWeight: "600",
          textAlign: "center", 
          color: "#374151",
          backgroundColor: "rgba(243, 244, 246, 0.9)",
          textTransform: "uppercase" as any,
          letterSpacing: "3px" as any,
          borderRadius: 10,
          padding: 4,
        },
      },
      {
        id: "price",
        type: "price",
        content: "price",
        field: "price",
        position: { x: 100, y: 210 },
        size: { width: 200, height: 55 },
        style: {
          ...defaultElementStyle,
          fontSize: 30,
          fontFamily: "Oswald",
          fontWeight: "700",
          color: "#ffffff",
          backgroundColor: "linear-gradient(135deg, #6b7280, #374151)" as any,
          textAlign: "center",
          borderRadius: 8,
          padding: 15,
          border: "2px solid #d1d5db" as any,
        },
      },
      // Mountain details
      {
        id: "package",
        type: "text",
        content: "packageDetails",
        field: "packageDetails",
        position: { x: 60, y: 285 },
        size: { width: 120, height: 35 },
        style: { 
          ...defaultElementStyle, 
          fontSize: 12, 
          fontFamily: "Nunito Sans",
          fontWeight: "600",
          textAlign: "center", 
          color: "#374151",
          backgroundColor: "#d1d5db",
          borderRadius: 18,
          padding: 10,
          textTransform: "uppercase" as any,
        },
      },
      {
        id: "date",
        type: "text",
        content: "date",
        field: "date",
        position: { x: 200, y: 285 },
        size: { width: 120, height: 35 },
        style: { 
          ...defaultElementStyle, 
          fontSize: 12,
          fontFamily: "Nunito Sans", 
          fontWeight: "600",
          textAlign: "center",
          color: "#ffffff",
          backgroundColor: "#6b7280",
          borderRadius: 18,
          padding: 10,
          textTransform: "uppercase" as any,
        },
      },
      // Mountain experience
      {
        id: "plan",
        type: "text",
        content: "plan",
        field: "plan",
        position: { x: 30, y: 340 },
        size: { width: 340, height: 40 },
        style: { 
          ...defaultElementStyle, 
          fontSize: 12, 
          fontFamily: "Nunito Sans",
          fontWeight: "400",
          textAlign: "center", 
          color: "#ffffff",
          lineHeight: "1.4" as any,
          backgroundColor: "rgba(107, 114, 128, 0.8)" as any,
          borderRadius: 12,
          padding: 12,
        },
      },
    ],
  },
]

export const fontOptions = [
  // New Popular Fonts
  "Ruthfully",
  "Montserrat", 
  "Anek Bangla",
  "Saira",
  
  // Expressive & Adventurous Scripts
  "Kaushan Script",
  "Pacifico", 
  "Dancing Script",
  "Lobster",
  "Satisfy",
  "Caveat",
  "Kalam",
  "The Nautigal",
  "Bad Script",
  "Nothing You Could Do",
  "Gochi Hand",
  "Patrick Hand",
  
  // Bold & Impactful Display
  "Oswald",
  "Bebas Neue",
  "Anton",
  "Archivo Black",
  "Passion One",
  "Alfa Slab One",
  "Luckiest Guy",
  "Bangers",
  "Righteous",
  "Fredoka One",
  "Paytone One",
  "Graduate",
  
  // Elegant & Classic Serifs
  "Playfair Display",
  "Lora",
  "Merriweather",
  "Cormorant Garamond",
  "Libre Baskerville",
  "Abril Fatface",
  "Prata",
  "Vidaloka",
  "Yeseva One",
  "Cinzel",
  "Vollkorn",
  "Cardo",
  
  // Unique & Thematic
  "Amatic SC",
  "Permanent Marker",
  "Rock Salt",
  "Cinzel Decorative",
  "Uncial Antiqua",
  "Special Elite",
  "Architects Daughter",
  "Shrikhand",
  
  // Modern & Clean (Original)
  "Quicksand",
  "Comfortaa",
  "Nunito Sans",
  "Rubik",
  "Poppins",
  "Raleway",
  "Source Sans Pro",
  "Lato",
  "Montserrat",
  "Open Sans"
]
export const fontWeights = ["300", "400", "500", "600", "700", "800"]

// Currency options for international travel packages
export const currencyOptions = [
  { symbol: "₹", code: "INR", name: "Indian Rupee" },
  { symbol: "$", code: "USD", name: "US Dollar" }, 
  { symbol: "€", code: "EUR", name: "Euro" },
  { symbol: "£", code: "GBP", name: "British Pound" },
  { symbol: "¥", code: "JPY", name: "Japanese Yen" },
  { symbol: "A$", code: "AUD", name: "Australian Dollar" },
  { symbol: "C$", code: "CAD", name: "Canadian Dollar" }
]

// Default currency symbol
export const defaultCurrency = "₹"

// Utility function to get preset data based on category and destination
export const getTravelPresetData = (category: string, destination: string): TravelData => {
  const key = `${category.toLowerCase()}-${destination.toLowerCase()}`
  
  switch (key) {
    case 'honeymoon-bali':
      return honeymoonBaliData
    case 'family-andaman':
      return familyAndamanData
    case 'couple-manali':
      return coupleManaliData
    case 'friends-bali':
      return friendsBaliData
    
    // Additional combinations
    case 'honeymoon-andaman':
      return {
        ...honeymoonBaliData,
        title: "Romantic Andaman Honeymoon",
        destination: "Andaman",
        plan: "Romantic island escape with private beach dinners, sunset cruises and luxury water villa experiences",
        overlays: "💕 Honeymoon Special • Private Beach • Luxury Villa",
        price: "52,000",
      }
    
    case 'honeymoon-manali':
      return {
        ...honeymoonBaliData,
        title: "Mountain Honeymoon Retreat",
        destination: "Manali",
        plan: "Romantic mountain retreat with cozy fireplaces, snow activities and scenic valley views",
        overlays: "💕 Honeymoon Special • Mountain Views • Cozy Stays",
        price: "35,000",
      }
    
    case 'couple-bali':
      return {
        ...coupleManaliData,
        title: "Bali Couple Adventure",
        destination: "Bali",
        plan: "Perfect couple getaway with beach activities, temple visits, adventure sports and romantic sunsets",
        overlays: "🌟 Couple Special • Beach Activities • Adventure Sports",
        price: "38,000",
      }
    
    case 'couple-andaman':
      return {
        ...coupleManaliData,
        title: "Andaman Couple Escape",
        destination: "Andaman",
        plan: "Romantic couple escape with water sports, island hopping and pristine beach experiences",
        overlays: "🌟 Couple Special • Island Hopping • Water Sports",
        price: "42,000",
      }
    
    case 'family-bali':
      return {
        ...familyAndamanData,
        title: "Bali Family Paradise",
        destination: "Bali",
        plan: "Perfect family vacation with cultural experiences, safe beach activities and kid-friendly attractions",
        overlays: "👨‍👩‍👧‍👦 Family Special • Cultural Tours • Kid Activities",
        price: "48,000",
      }
    
    case 'family-manali':
      return {
        ...familyAndamanData,
        title: "Manali Family Adventure",
        destination: "Manali",
        plan: "Family mountain adventure with scenic spots, adventure activities and comfortable stays for all ages",
        overlays: "👨‍👩‍👧‍👦 Family Special • Mountain Fun • Adventure Activities",
        price: "25,000",
      }
    
    case 'friends-andaman':
      return {
        ...friendsBaliData,
        title: "Andaman Squad Adventure",
        destination: "Andaman",
        plan: "Epic friends trip with water sports, island hopping, beach parties and group adventure activities",
        overlays: "🎉 Group Special • Beach Parties • Water Adventures",
        price: "35,000",
      }
    
    case 'friends-manali':
      return {
        ...friendsBaliData,
        title: "Manali Friends Getaway",
        destination: "Manali",
        plan: "Ultimate friends trip with adventure sports, group activities and scenic mountain experiences",
        overlays: "🎉 Group Special • Adventure Sports • Mountain Views",
        price: "22,000",
      }
    
    default:
      return defaultTravelData
  }
}

// Travel category options for easy selection
export const travelCategories = [
  { id: 'honeymoon', name: 'Honeymoon', emoji: '💕' },
  { id: 'couple', name: 'Couple', emoji: '🌟' },
  { id: 'family', name: 'Family', emoji: '👨‍👩‍👧‍👦' },
  { id: 'friends', name: 'Friends', emoji: '🎉' },
]

// Popular destinations
export const popularDestinations = [
  { id: 'bali', name: 'Bali', emoji: '🌺' },
  { id: 'andaman', name: 'Andaman', emoji: '🏖️' },
  { id: 'manali', name: 'Manali', emoji: '🏔️' },
]
