#!/usr/bin/env pwsh
# PowerShell script to deploy the EMI calculation fix to production server

Write-Host "🚀 Deploying EMI calculation fix to production server..." -ForegroundColor Green

# Production server details
$SERVER = "root@*************"
$REMOTE_PATH = "/var/www/family"
$LOCAL_PATH = "family-tripxplo-production"

try {
    Write-Host "🔧 Uploading fixed databaseService.js to production server..." -ForegroundColor Yellow
    
    # Upload the fixed JavaScript file
    scp "$LOCAL_PATH/js/databaseService.js" "${SERVER}:${REMOTE_PATH}/js/"
    if ($LASTEXITCODE -ne 0) {
        throw "Failed to upload databaseService.js"
    }
    
    Write-Host "✅ EMI calculation fix deployed successfully!" -ForegroundColor Green
    Write-Host ""
    Write-Host "🧪 Please test the website now:" -ForegroundColor Cyan
    Write-Host "1. Visit: https://family.tripxplo.com" -ForegroundColor White
    Write-Host "2. Search for 'Andaman' packages" -ForegroundColor White
    Write-Host "3. Check that EMI amounts match package prices" -ForegroundColor White
    Write-Host "4. Expected: Package ₹63,199 should show EMI ₹10,533/month for 6 months" -ForegroundColor White
    Write-Host "5. Expected: Package ₹54,096 should show EMI ₹9,016/month for 6 months" -ForegroundColor White
    
} catch {
    Write-Host "❌ Deployment failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}
