import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import LeadCard, { Lead } from '../components/LeadCard';
import LeadDetailModal from '../components/LeadDetailModal';
import {
  DndContext,
  DragEndEvent,
  DragOverlay,
  DragStartEvent,
  DragOverEvent,
  MouseSensor,
  TouchSensor,
  useSensor,
  useSensors,
  useDroppable,
  UniqueIdentifier,
  rectIntersection,
} from '@dnd-kit/core';
import { SortableContext, verticalListSortingStrategy } from '@dnd-kit/sortable';
import { toast } from 'react-hot-toast';
import { Spinner } from '../components/Spinner';
// Use Supabase API directly for leads
import { fetchLeadsByUser, updateLeadStatus, testManualFetchLeads } from '../lib/leadService';

// We'll use this as fallback if there are no leads or if there's an error

// Updated list of statuses for Kanban columns
// NOTE: If drag-and-drop issues persist, check for CSS interference (transform, z-index, absolute/fixed positioning) on cards, columns, or parents. Such styles can confuse dnd-kit drop detection.
const statuses = [
  'NEW LEAD',            // Start of the process
  'CALL CUSTOMER',       // Initial action/attempt
  'CONTACTED',           // Successfully reached
  'CALL NOT ANSWERED',   // Call specific failed
  'NO RESPONSE',         // General contact failed
  'MORE INFO',           // Requires more details
  'FOLLOW-UP',           // Re-contact action
  'QUOTE SENT',          // Proposal sent
  'NEGOTIATION',         // Discussion in progress
  'APPROVED',            // Lead approves
  'BOOKED WITH US',      // Booking confirmed
  'TRIP COMPLETED',      // Post-sale complete
  'ON-HOLD',             // Process paused
  'POSTPONE TRIP',       // Lead delays
  'NOT INTERESTED',      // Deal lost - declined
  'BOOKING - OTHERS',    // Deal lost - competitor
  'TRIP CANCELLED'       // Deal lost - cancelled plans
];

const SAMPLE_LEADS: Lead[] = [
  {
    id: '1',
    customer_name: 'John Smith',
    email: '<EMAIL>',
    phone: '************',
    destination: 'Paris, France',
    travel_date: '2023-12-15',
    lead_source: 'Website',
    status: 'NEW LEAD',
    priority: 'HIGH',
    assigned_to: 'user-1',
    created_at: '2023-06-01'
  },
  {
    id: '2',
    customer_name: 'Maria Garcia',
    email: '<EMAIL>',
    phone: '************',
    destination: 'Tokyo, Japan',
    travel_date: '2023-11-10',
    lead_source: 'Referral',
    status: 'CONTACTED',
    priority: 'MEDIUM',
    assigned_to: 'user-1',
    created_at: '2023-06-02'
  },
  {
    id: '3',
    customer_name: 'David Wilson',
    email: '<EMAIL>',
    phone: '************',
    destination: 'Rome, Italy',
    travel_date: '2023-10-05',
    lead_source: 'Partner',
    status: 'QUOTE SENT',
    priority: 'LOW',
    assigned_to: 'user-1',
    created_at: '2023-06-03'
  },
  {
    id: '4',
    customer_name: 'Sarah Johnson',
    email: '<EMAIL>',
    phone: '************',
    destination: 'Bali, Indonesia',
    travel_date: '2023-09-20',
    lead_source: 'Instagram',
    status: 'APPROVED',
    priority: 'HIGH',
    assigned_to: 'user-1',
    created_at: '2023-06-04'
  },
  {
    id: '5',
    customer_name: 'Michael Brown',
    email: '<EMAIL>',
    phone: '************',
    destination: 'Cape Town, South Africa',
    travel_date: '2023-08-15',
    lead_source: 'Facebook',
    status: 'BOOKED',
    priority: 'MEDIUM',
    assigned_to: 'user-1',
    created_at: '2023-06-05'
  },
  {
    id: '6',
    customer_name: 'Jennifer Lee',
    email: '<EMAIL>',
    phone: '************',
    destination: 'Cancun, Mexico',
    travel_date: '2023-07-10',
    lead_source: 'Partner',
    status: 'CLOSED_LOST',
    priority: 'LOW',
    assigned_to: 'user-1',
    created_at: '2023-06-06'
  }
];

// Define a Kanban column with droppable area
interface KanbanColumnProps {
  status: string;
  leads: Lead[];
  updatingLeadId: string | null;
  onViewDetails: (id: string) => void;
}
const KanbanColumn: React.FC<KanbanColumnProps> = ({ status, leads, updatingLeadId, onViewDetails }) => {
  const { setNodeRef } = useDroppable({ id: `column-${status}` });
  return (
    <div
      ref={setNodeRef}
      id={`column-${status}`}
      className="bg-gray-100 rounded-lg p-3 w-72 flex-shrink-0"
      data-status={status}
    >
      <h3 className="font-semibold mb-3 text-gray-700">
        {status}
        <span className="ml-2 text-xs bg-gray-200 text-gray-700 py-1 px-2 rounded-full">
          {leads.length}
        </span>
      </h3>
      <div
        id={`column-content-${status}`}
        className="p-2 flex flex-col gap-2 min-h-[300px] max-h-[calc(100vh-300px)] overflow-y-auto"
        data-status={status}
      >
        {leads.length === 0 ? (
          <div className="text-gray-400 text-sm p-4 text-center italic">
            No leads in this status
          </div>
        ) : (
          leads.map((lead) => (
            <LeadCard
              key={lead.id}
              lead={lead}
              isUpdating={updatingLeadId === lead.id}
              onViewDetails={onViewDetails}
            />
          ))
        )}
      </div>
    </div>
  );
};

const LeadsKanban: React.FC = () => {
  console.log('[KANBAN] Component rendering / re-rendering...');
  // USER DEBUGGING STEP: If standard Kanban loading hangs,
  // click this button WHILE THE PROBLEM IS HAPPENING. Check the console
  // for '[testManualFetchLeads]' logs to see if a direct network call works or also fails/hangs.
  const handleManualGetTest = async () => {
    const result = await testManualFetchLeads();
    console.log('[Manual GET Test] testManualFetchLeads result:', result);
  };

  const { user } = useAuth();
  // Start with sample data but then fetch real data
  const [leads, setLeads] = useState<Lead[]>(SAMPLE_LEADS);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [updatingLeadId, setUpdatingLeadId] = useState<string | null>(null);
  const [activeLead, setActiveLead] = useState<Lead | null>(null);
  const [viewingLeadId, setViewingLeadId] = useState<string | null>(null);
  // New state for filtered statuses
  const [selectedStatuses, setSelectedStatuses] = useState<string[]>(statuses);

  // Configure sensors for mouse and touch interactions
  const sensors = useSensors(
    useSensor(MouseSensor, {
      // Reduced activation delay for better responsiveness
      activationConstraint: {
        delay: 50,
        tolerance: 5,
      },
    }),
    useSensor(TouchSensor, {
      // Reduced delay for touch devices
      activationConstraint: {
        delay: 150,
        tolerance: 5,
      },
    })
  );

  useEffect(() => {
    console.log('[KANBAN] useEffect hook starting...');
    // Only fetch if we have a user
    const fetchLeads = async () => {
      if (!user?.id) {
        console.log("No user ID, using sample data");
        setLeads(SAMPLE_LEADS);
        setIsLoading(false);
        return;
      }

      try {
        setIsLoading(true);
        setError(null);

        console.log("[KANBAN] Fetching leads for user:", user.id);
        const fetchedLeads = await fetchLeadsByUser(user.id);

        console.log("Fetched leads:", fetchedLeads);
        if (fetchedLeads.success && fetchedLeads.data) {
          setLeads(fetchedLeads.data);
        } else {
          console.error("Error fetching leads:", fetchedLeads.error);
          setError(fetchedLeads.error || 'Failed to fetch leads');
          setLeads(SAMPLE_LEADS);
        }
      } catch (error: any) {
        console.error("Exception fetching leads:", error);
        setError('Failed to fetch leads: ' + (error.message || 'Unknown error'));
        setLeads(SAMPLE_LEADS);
      } finally {
        setIsLoading(false);
      }
    };

    fetchLeads();
  }, [user?.id]);

  // Group leads by status
  const getLeadsByStatus = (status: string) => {
    return leads.filter(lead => lead.status === status);
  };

  const handleViewDetails = (leadId: string) => {
    console.log('[LeadsKanban] handleViewDetails called with leadId:', leadId);
    const lead = leads.find(l => l.id === leadId);
    console.log('[LeadsKanban] Found lead:', lead);
    setViewingLeadId(leadId);
    console.log('[LeadsKanban] Set viewingLeadId to:', leadId);
  };

  const handleCreateQuote = (_lead: Lead) => {
    // TODO: Implement quote creation
  };

  const handleDragStart = (event: DragStartEvent) => {
    const { active } = event;
    const lead = leads.find(l => l.id === active.id);

    if (lead) {
      setActiveLead(lead);
    }
  };

  const handleDragOver = (event: DragOverEvent) => {
    const { active, over } = event;
    console.log('[DND] DragOver active:', active.id, 'over:', over?.id);
  };

  // Helper function to find the column ID from any element inside it
  const findColumnFromTarget = (targetId: UniqueIdentifier): string | null => {
    console.log('[DND] findColumnFromTarget called with targetId:', targetId);

    // If the target is a column, return its ID
    if (typeof targetId === 'string' && targetId.startsWith('column-')) {
      console.log('[DND] findColumnFromTarget returning column:', targetId);
      return targetId;
    }

    // If target is a column content, extract column ID
    if (typeof targetId === 'string' && targetId.startsWith('column-content-')) {
      const columnId = 'column-' + targetId.replace('column-content-', '');
      console.log('[DND] findColumnFromTarget returning from content area:', columnId);
      return columnId;
    }

    // If the target is a lead, find which column it belongs to
    const lead = leads.find(l => l.id === targetId?.toString());
    if (lead) {
      const col = `column-${lead.status}`;
      console.log('[DND] findColumnFromTarget returning for lead:', col);
      return col;
    }

    // Try to walk up the DOM tree if possible to find a parent column id
    if (typeof window !== 'undefined' && targetId) {
      try {
        const el = document.getElementById(targetId.toString());
        if (el) {
          let parent = el.parentElement;
          while (parent) {
            if (parent.id && parent.id.startsWith('column-')) {
              console.log('[DND] findColumnFromTarget found parent column:', parent.id);
              return parent.id;
            }
            if (parent.dataset && parent.dataset.status) {
              const columnId = `column-${parent.dataset.status}`;
              console.log('[DND] findColumnFromTarget found column via data-status:', columnId);
              return columnId;
            }
            parent = parent.parentElement;
          }
        }
      } catch (err) {
        console.error('[DND] Error walking DOM tree:', err);
      }
    }

    console.log('[DND] findColumnFromTarget returning null');
    return null;
  };

  const handleDragEnd = async (event: DragEndEvent) => {
    // --- Drag End Event Logging ---
    console.log("--- Drag End Event ---", event);
    console.log("Active (Dragged Item) ID:", event.active.id);
    console.log("Over (Dropped On) ID:", event.over?.id);

    setActiveLead(null);

    const { active, over } = event;
    if (!active || !over) {
      console.log("Dropped outside a valid target.");
      return;
    }

    const leadId = active.id.toString();

    // Find the column where the item was dropped, handling drops on leads or columns
    const columnId = findColumnFromTarget(over.id);
    console.log("Found column ID:", columnId);

    if (columnId) {
      setUpdatingLeadId(leadId);

      // Extract target column status from column ID
      const newStatus = columnId.replace('column-', '');

      console.log("Extracted newStatus:", newStatus);

      // Find the lead we're updating
      const leadToUpdate = leads.find((lead) => lead.id === leadId);

      if (!leadToUpdate) {
        setUpdatingLeadId(null);
        console.log("Lead not found:", leadId);
        return;
      }

      // Clone the leads array to avoid mutating state directly
      const updatedLeads = [...leads];
      const leadIndex = updatedLeads.findIndex((lead) => lead.id === leadId);

      // Store the original status in case we need to revert
      const originalStatus = updatedLeads[leadIndex].status;
      console.log("Original status:", originalStatus, "New status:", newStatus);

      // Return if status hasn't changed
      if (originalStatus === newStatus) {
        setUpdatingLeadId(null);
        console.log("Status unchanged, returning early");
        return;
      }

      // Optimistically update UI
      updatedLeads[leadIndex] = {
        ...updatedLeads[leadIndex],
        status: newStatus
      };

      setLeads(updatedLeads);
      console.log("Updated local state with new status");

      // Re-enable API update with improved error handling
      try {
        // Only update API if we have a real user
        if (user?.id) {
          // Use the service function
          console.log("Calling updateLeadStatus service with:", {
            leadId: leadId,
            newStatus: newStatus,
            userId: user.id
          });

          const result = await updateLeadStatus(leadId, newStatus);

          console.log("Status update response:", result);

          if (!result.success) {
            throw new Error(result.error || 'Failed to update lead status');
          }

          console.log("Database update successful");
          toast.success(`Lead moved to ${newStatus}`);
        } else {
          console.log("No user ID, skipping database update");
          toast.success(`Lead moved to ${newStatus} (local only)`);
        }
      } catch (error: any) {
        console.error('Error updating lead status:', error);
        console.error('Error details:', {
          name: error.name,
          message: error.message,
          stack: error.stack
        });

        // Revert the change in UI if the database update fails
        const revertedLeads = [...updatedLeads];
        revertedLeads[leadIndex] = {
          ...revertedLeads[leadIndex],
          status: originalStatus
        };

        setLeads(revertedLeads);
        console.log("Reverted to original status due to error");
        toast.error(error.message || 'Failed to update lead status. Please try again.');
      } finally {
        setUpdatingLeadId(null);
      }
    } else {
      console.log("Dropped on an invalid target (not a column and not a lead in a column)");
    }
  };

  // Toggle a status in the filter menu
  const handleToggleStatus = (status: string) => {
    setSelectedStatuses(prev =>
      prev.includes(status)
        ? prev.filter(s => s !== status)
        : [...prev, status]
    );
  };

  // Show loading state but with an informative message
  if (isLoading) {
    return (
      <div className="flex flex-col justify-center items-center h-64 space-y-4">
        <Spinner size="xl" />
        <p className="text-gray-500">Loading leads from database...</p>
        {error && (
          <div className="p-4 my-3 bg-red-50 text-red-600 rounded-lg">
            <h2 className="text-lg font-semibold">Error</h2>
            <p>{error}</p>
          </div>
        )}
      </div>
    );
  }

  // Log leads state before rendering columns
  console.log('[KANBAN] leads state before rendering:', leads);

  return (
    <>
      <div className="mb-8 p-6 bg-white rounded-lg shadow">
        <h1 className="text-3xl font-bold text-gray-800">Leads Kanban Board</h1>
        <p className="text-gray-600 mt-1">
          {error ? 'Showing sample data due to an error' : 'Track and manage your travel leads'}
          {!user?.id && ' (using sample data)'}
        </p>
        {error && (
          <div className="mt-4 p-4 bg-red-50 text-red-700 rounded-lg">
            <h2 className="text-lg font-semibold">Error</h2>
            <p>{error}</p>
          </div>
        )}
      </div>
      {/* Horizontal filter menu */}
      <div className="mb-6 px-6 flex flex-wrap gap-2">
        {statuses.map(status => (
          <button
            key={status}
            onClick={() => handleToggleStatus(status)}
            className={`px-3 py-1 text-sm rounded-full transition ${
              selectedStatuses.includes(status)
                ? 'bg-primary text-white'
                : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
            }`}
          >
            {status} ({getLeadsByStatus(status).length})
          </button>
        ))}
      </div>

      {/* DIAGNOSTIC TOOLS - Only visible during development */}
      <div className="mb-4 px-6">
        <details className="bg-gray-100 p-3 rounded-lg">
          <summary className="font-medium text-gray-700 cursor-pointer">
            🛠️ Diagnostic Tools
          </summary>
          <div className="mt-3 p-3 bg-white rounded border border-gray-200">
            <h4 className="font-medium text-sm text-gray-700 mb-2">Lead Distribution Debug</h4>
            <div className="text-xs text-gray-600 mb-3">
              <p><strong>Total Leads:</strong> {leads.length}</p>
              <div className="grid grid-cols-2 gap-2 mt-2">
                {statuses.map(status => {
                  const statusLeads = getLeadsByStatus(status);
                  return (
                    <div key={status} className="text-xs">
                      <strong>{status}:</strong> {statusLeads.length} leads
                      {statusLeads.length > 0 && (
                        <div className="ml-2 text-gray-500">
                          {statusLeads.map(lead => lead.customer_name).join(', ')}
                        </div>
                      )}
                    </div>
                  );
                })}
              </div>
            </div>

            <h4 className="font-medium text-sm text-gray-700 mb-2">Connection Issue Diagnostics</h4>
            <p className="text-xs text-gray-600 mb-2">
              <strong>IMPORTANT:</strong> If you experience connection issues (loading freezes/hangs),
              click this button <strong>WHILE THE ISSUE IS HAPPENING</strong> to test if direct API calls work.
              Check browser console for results.
            </p>
            <button
              onClick={handleManualGetTest}
              className="px-3 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700 transition"
            >
              Test Direct API Connection
            </button>
          </div>
        </details>
      </div>

      <DndContext
        sensors={sensors}
        collisionDetection={rectIntersection}
        onDragStart={handleDragStart}
        onDragOver={handleDragOver}
        onDragEnd={handleDragEnd}
      >
        <div className="flex space-x-6 overflow-x-auto p-6 bg-gray-50 rounded-lg shadow-inner min-h-[calc(100vh-400px)]">
          {statuses.filter(status => selectedStatuses.includes(status)).map((status) => (
            <SortableContext
              key={status}
              items={getLeadsByStatus(status).map((l) => l.id)}
              strategy={verticalListSortingStrategy}
            >
              <KanbanColumn
                status={status}
                leads={getLeadsByStatus(status)}
                updatingLeadId={updatingLeadId}
                onViewDetails={handleViewDetails}
              />
            </SortableContext>
          ))}
        </div>

        <DragOverlay>
          {activeLead && (
            <LeadCard
              lead={activeLead}
              onViewDetails={handleViewDetails}
            />
          )}
        </DragOverlay>
      </DndContext>

      {/* Lead Detail Modal */}
      <LeadDetailModal
        leadId={viewingLeadId}
        onClose={() => setViewingLeadId(null)}
        leads={leads}
      />
    </>
  );
};

export default LeadsKanban;
