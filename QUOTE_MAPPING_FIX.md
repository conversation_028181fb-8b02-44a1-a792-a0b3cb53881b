# Quote Mapping Fix - Data Loading Logic

## Problem
The Quote Mapping component was mixing database data with Quote Generator data even for existing saved mappings, causing confusion about what data was being displayed.

## Solution
Updated the `handleQuoteSelection` function to properly separate two distinct scenarios:

### 1. **Saved Mapping** (Existing in Database)
- **Data Source**: `quote_mappings` table only
- **Behavior**: Displays data exactly as saved in database
- **No Mixing**: Does not overlay Quote Generator data

```javascript
if (existingMapping) {
  // **SAVED MAPPING**: Use database data only
  setQuoteMappingData({
    id: existingMapping.id,
    quote_id: existingMapping.quote_id,
    quote_name: existingMapping.quote_name,
    customer_name: existingMapping.customer_name,
    destination: existingMapping.destination,
    hotel_mappings: existingMapping.hotel_mappings || [],
    vehicle_mappings: existingMapping.vehicle_mappings || DEFAULT_VEHICLE_MAPPINGS,
    additional_costs: existingMapping.additional_costs || {
      meal_cost_per_person: 0,
      ferry_cost: 0,
      activity_cost_per_person: 0,
      guide_cost_per_day: 0,
      parking_toll_multiplier: 1.0
    }
  });
}
```

### 2. **New Mapping** (Not in Database)
- **Data Source**: Quote Generator tables (`hotel_rows`, `costs`)
- **Behavior**: Auto-populates from Quote Generator form data
- **Fresh Data**: Fetches current quote data for mapping setup

```javascript
} else {
  // **NEW MAPPING**: Use Quote Generator data
  // Fetch hotel data and costs data from quote tables
  // Auto-populate mappings based on Quote Generator form
}
```

## Key Benefits

### ✅ **Clear Data Separation**
- Saved mappings show database data only
- New mappings show Quote Generator data only
- No confusion about data source

### ✅ **Proper Ferry Cost Handling**
- For saved mappings: Uses `ferry_cost` from database
- For new mappings: Uses `ferry_cost` from Quote Generator
- Maintains per-person calculation logic

### ✅ **Consistent User Experience**
- Users see exactly what they saved previously
- New mappings start with sensible defaults from Quote Generator
- Clear distinction between editing saved vs creating new

## Ferry Cost Calculation
Ferry cost is calculated as per-person rate multiplied by eligible persons:

```javascript
// Ferry cost = per-person cost * eligible persons (excluding infants)
const ferryEligiblePersons = familyType.no_of_adults + 
                            (familyType.no_of_children || 0) + 
                            (familyType.no_of_child || 0);
const ferryCost = (mappingData.additional_costs.ferry_cost || 0) * ferryEligiblePersons;
```

**Eligible Persons**:
- ✅ Adults (12+): Charged ferry rate
- ✅ Children (6-11): Charged ferry rate  
- ✅ Child below 5 (2-5): Charged ferry rate
- ❌ Infants (under 2): Free (excluded)

## Files Updated
- `src/quotes/Tabs/QuoteMapping.tsx`
- `test-dummay/src/quotes/Tabs/QuoteMapping.tsx`
- Family Type Price Generator utilities
- Database service functions
- API server functions
