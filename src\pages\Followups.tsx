import React, { useState, useEffect } from 'react';
import { Link, useParams, useNavigate, useLocation } from 'react-router-dom';
import { User, ArrowLeft, Edit, Save, X, Loader, AlertCircle, Plus, Eye, Trash2, Search, RotateCcw, ChevronDown, ChevronUp, Users, DollarSign, Package, Clock, Phone, Mail, CheckCircle, XCircle, MinusCircle, Info} from 'lucide-react';
import { getQuoteClient } from '../lib/supabaseManager';

// Interfaces
interface Followup {
  id: number;
  customer_name: string;
  customer_phone?: string;
  customer_email?: string;
  quotes: Quote | null;
  hotel_status: string;
  cab_status: string;
  flight_train_status: string;
  transportation_status: string;
  cab_payment?: number;
  quote_id?: string;
  hotel_details?: string; // Hotel Name
  hotel_full_amount?: number;
  hotel_advance_amount?: number;
  hotel_full_amount_paid?: number;
  cab_name?: string;
  cab_full_amount?: number;
  cab_advance_amount?: number;
  cab_full_amount_paid?: number;
  flight_train_amount?: number;
  transportation_amount?: number;
  notes?: string;
  profit?: number;
  marketing_amount?: number;
  total_cost?: number;
  total_paid?: number;
}

interface Quote {
  id: string;
  quote_id: string;
  customer_name: string;
  customer_phone: string;
  customer_email: string;
  package_name?: string;
  destination?: string;
  created_at?: string;
  is_draft?: boolean;
  trip_duration?: string;
  family_type?: string;
  validity_date?: string;
  subtotal?: number;
  total_cost?: number;
  no_of_persons?: number;
  extra_adults?: number;
  children?: number;
  commission_rate?: number;
  costs?: Costs;
}

interface QuoteListItem {
  id: string;
  package_name: string;
  customer_name: string;
  customer_phone?: string;
  customer_email?: string;
  destination: string;
  created_at: string;
  is_draft: boolean;
  trip_duration?: string;
  family_type?: string;
  validity_date?: string;
  subtotal?: number;
  total_cost?: number;
  no_of_persons?: number;
  extra_adults?: number;
  children?: number;
}

interface HotelDetail {
  name: string;
  price: number;
  advance: number;
  full_amount_paid: number;
  status: string;
}

interface Costs {
  id?: string;
  quote_id?: string;
  transportation?: number;
  cab_sightseeing?: number;
  train_cost?: number;
  ferry_cost?: number;
  marketing?: number;
  gst?: number;
  commission?: number;
}

// Main Component
const Followups: React.FC = () => {
  const { id } = useParams<{ id: string; action: string }>();
  const location = useLocation();

  if (location.pathname === '/followups/create') {
    return <CreateFollowup />;
  }
  if (location.pathname.includes('/saved-quotes') || location.pathname.includes('/select-quote')) {
    return <SelectQuote />;
  }
  if (id) {
    return <FollowupCustomer id={id} />;
  }

  return <FollowupsList />;
};

// Followups List View
const FollowupsList: React.FC = () => {
  const [followups, setFollowups] = useState<Followup[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [supabase, setSupabase] = useState<any>(null);

  const [searchTerm, setSearchTerm] = useState(() => sessionStorage.getItem('followups_searchTerm') || '');
  const [sortColumn, setSortColumn] = useState<keyof Followup>(() => (sessionStorage.getItem('followups_sortColumn') as keyof Followup) || 'customer_name');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>(() => (sessionStorage.getItem('followups_sortDirection') as 'asc' | 'desc') || 'asc');
  const [currentPage, setCurrentPage] = useState(() => parseInt(sessionStorage.getItem('followups_currentPage') || '1', 10));
  const [itemsPerPage, setItemsPerPage] = useState(() => parseInt(sessionStorage.getItem('followups_itemsPerPage') || '10', 10));

  useEffect(() => {
    sessionStorage.setItem('followups_searchTerm', searchTerm);
    sessionStorage.setItem('followups_sortColumn', sortColumn);
    sessionStorage.setItem('followups_sortDirection', sortDirection);
    sessionStorage.setItem('followups_currentPage', currentPage.toString());
    sessionStorage.setItem('followups_itemsPerPage', itemsPerPage.toString());
  }, [searchTerm, sortColumn, sortDirection, currentPage, itemsPerPage]);

  const handleSort = (column: keyof Followup) => {
    if (sortColumn === column) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortColumn(column);
      setSortDirection('asc');
    }
  };

  const handleDelete = async (followupId: number) => {
    if (!supabase) return;

    if (window.confirm('Are you sure you want to delete this followup?')) {
      try {
        const { error } = await supabase
          .from('followups')
          .delete()
          .eq('id', followupId);

        if (error) throw error;

        setFollowups(followups.filter(f => f.id !== followupId));
      } catch (err) {
        setError(err instanceof Error ? `Error deleting followup: ${err.message}` : 'An unknown error occurred.');
      }
    }
  };

  useEffect(() => {
    const initSupabase = async () => {
      try {
        const client = await getQuoteClient();
        setSupabase(client);
      } catch (error) {
        console.error('Error initializing Supabase client:', error);
        setError('Could not connect to the database.');
        setIsLoading(false);
      }
    };
    initSupabase();
  }, []);

  useEffect(() => {
    if (!supabase) return;

    const fetchFollowups = async () => {
      setIsLoading(true);
      setError(null);
      try {
        const { data, error } = await supabase
          .from('followups')
          .select(`
            *,
            quotes(*)
          `);

        if (error) throw error;
        setFollowups(data as any[] || []);
      } catch (err) {
        setError(err instanceof Error ? `Error: ${err.message}` : 'An unknown error occurred.');
      } finally {
        setIsLoading(false);
      }
    };

    fetchFollowups();
  }, [supabase]);

  const sortedAndFilteredFollowups = [...followups]
    .filter(followup =>
      followup.customer_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (followup.customer_phone && followup.customer_phone.includes(searchTerm)) ||
      (followup.customer_email && followup.customer_email.toLowerCase().includes(searchTerm.toLowerCase()))
    )
    .sort((a, b) => {
      const aValue = a[sortColumn];
      const bValue = b[sortColumn];

      if (aValue === null || aValue === undefined) return 1;
      if (bValue === null || bValue === undefined) return -1;

      if (aValue < bValue) return sortDirection === 'asc' ? -1 : 1;
      if (aValue > bValue) return sortDirection === 'asc' ? 1 : -1;
      return 0;
    });

  const totalPages = Math.ceil(sortedAndFilteredFollowups.length / itemsPerPage);
  const paginatedFollowups = sortedAndFilteredFollowups.slice((currentPage - 1) * itemsPerPage, currentPage * itemsPerPage);

  const getStatusStyle = (status: string) => {
    if (!status) return { bgColor: 'bg-gray-100', textColor: 'text-gray-800', icon: <Clock className="w-4 h-4" /> };
    const lowerStatus = status.toLowerCase();
    if (lowerStatus.includes('completed') || lowerStatus.includes('paid') || lowerStatus.includes('booked') || lowerStatus.includes('advance paid') || lowerStatus.includes('flight paid') || lowerStatus.includes('train paid') || lowerStatus.includes('ferry booked') || lowerStatus.includes('bus booked')) {
        return { bgColor: 'bg-green-100', textColor: 'text-green-800', icon: <CheckCircle className="w-4 h-4" /> };
    }
    if (lowerStatus.includes('pending')) {
        return { bgColor: 'bg-yellow-100', textColor: 'text-yellow-800', icon: <Loader className="w-4 h-4 animate-spin" /> };
    }
    if (lowerStatus.includes('not completed') || lowerStatus.includes('not paid') || lowerStatus.includes('not booked')) {
        return { bgColor: 'bg-red-100', textColor: 'text-red-800', icon: <XCircle className="w-4 h-4" /> };
    }
    if (lowerStatus.includes('no flight/train') || lowerStatus.includes('no ferry/bus')) {
        return { bgColor: 'bg-gray-100', textColor: 'text-gray-800', icon: <MinusCircle className="w-4 h-4" /> };
    }
    return { bgColor: 'bg-blue-100', textColor: 'text-blue-800', icon: <Info className="w-4 h-4" /> };
  };

  const StatusBadge: React.FC<{ status: string }> = ({ status }) => {
    const { bgColor, textColor, icon } = getStatusStyle(status);
    return (
        <div className={`inline-flex items-center gap-2 px-3 py-1 rounded-full text-xs font-medium ${bgColor} ${textColor}`}>
            {icon}
            <span>{status || 'N/A'}</span>
        </div>
    );
  };

  const getInsights = () => {
    const totalFollowups = followups.length;
    const completed = followups.filter(f => f.hotel_status.toLowerCase().includes('completed') || f.hotel_status.toLowerCase().includes('paid')).length;
    const pending = followups.filter(f => f.hotel_status.toLowerCase().includes('pending')).length;
    const totalProfit = followups.reduce((acc, f) => acc + (f.profit || 0), 0);

    return {
      totalFollowups,
      completed,
      pending,
      totalProfit,
    };
  };

  return (
    <div className="p-4 sm:p-6 bg-gray-50 min-h-screen">
      <div className="flex flex-col sm:flex-row justify-between sm:items-center mb-6 gap-4">
        <div>
          <h1 className="text-2xl sm:text-3xl font-bold text-gray-800">Customer Follow-ups</h1>
          <p className="text-gray-600 mt-1">Track and manage customer follow-ups.</p>
        </div>
        <Link to="/followups/select-quote">
          <button className="w-full sm:w-auto bg-primary text-white px-4 py-2 rounded-lg hover:bg-primary-dark flex items-center justify-center shadow-sm transition-all duration-300 ease-in-out transform hover:scale-105">
            <Plus className="w-5 h-5 mr-2" />
            Create Follow-up
          </button>
        </Link>
      </div>

      {/* Filters */}
      <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200 mb-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              placeholder="Search by customer, phone, or email..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary"
            />
          </div>
        </div>
      </div>

      {/* Stats */}
      <div>
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Stats</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="bg-blue-50 p-4 rounded-lg flex items-center">
            <div className="bg-blue-100 p-3 rounded-full">
              <Users className="w-6 h-6 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-2xl font-bold text-blue-900">{getInsights().totalFollowups}</p>
              <p className="text-sm text-blue-700">Total Follow-ups</p>
            </div>
          </div>
          <div className="bg-green-50 p-4 rounded-lg flex items-center">
            <div className="bg-green-100 p-3 rounded-full">
              <CheckCircle className="w-6 h-6 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-2xl font-bold text-green-900">{getInsights().completed}</p>
              <p className="text-sm text-green-700">Completed</p>
            </div>
          </div>
          <div className="bg-yellow-50 p-4 rounded-lg flex items-center">
            <div className="bg-yellow-100 p-3 rounded-full">
              <Loader className="w-6 h-6 text-yellow-600" />
            </div>
            <div className="ml-4">
              <p className="text-2xl font-bold text-yellow-900">{getInsights().pending}</p>
              <p className="text-sm text-yellow-700">Pending</p>
            </div>
          </div>
          <div className="bg-indigo-50 p-4 rounded-lg flex items-center">
            <div className="bg-indigo-100 p-3 rounded-full">
              <DollarSign className="w-6 h-6 text-indigo-600" />
            </div>
            <div className="ml-4">
              <p className="text-2xl font-bold text-indigo-900">₹{getInsights().totalProfit.toLocaleString()}</p>
              <p className="text-sm text-indigo-700">Total Profit</p>
            </div>
          </div>
        </div>
      </div>

      {/* Table for larger screens */}
      <div className="mt-6 hidden md:block bg-white rounded-lg shadow-md overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-100">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider cursor-pointer" onClick={() => handleSort('customer_name')}>
                <div className="flex items-center">
                  <User className="w-4 h-4 mr-2" />Customer {sortColumn === 'customer_name' && (sortDirection === 'asc' ? '▲' : '▼')}
                </div>
              </th>
              <th className="px-6 py-3 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider cursor-pointer" onClick={() => handleSort('hotel_status')}>
                Hotel {sortColumn === 'hotel_status' && (sortDirection === 'asc' ? '▲' : '▼')}
              </th>
              <th className="px-6 py-3 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider cursor-pointer" onClick={() => handleSort('cab_status')}>
                Cab {sortColumn === 'cab_status' && (sortDirection === 'asc' ? '▲' : '▼')}
              </th>
              <th className="px-6 py-3 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider cursor-pointer" onClick={() => handleSort('flight_train_status')}>
                Flight/Train {sortColumn === 'flight_train_status' && (sortDirection === 'asc' ? '▲' : '▼')}
              </th>
              <th className="px-6 py-3 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider cursor-pointer" onClick={() => handleSort('transportation_status')}>
                Transport {sortColumn === 'transportation_status' && (sortDirection === 'asc' ? '▲' : '▼')}
              </th>
              <th className="px-6 py-3 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider cursor-pointer" onClick={() => handleSort('profit')}>
                Profit {sortColumn === 'profit' && (sortDirection === 'asc' ? '▲' : '▼')}
              </th>
              <th className="px-6 py-3 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Actions</th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {isLoading ? (
              <tr><td colSpan={7} className="text-center py-16"><Loader className="w-10 h-10 text-primary animate-spin inline-block" /><p className="mt-4 text-lg text-gray-600">Loading...</p></td></tr>
            ) : error ? (
              <tr><td colSpan={7} className="text-center py-16"><AlertCircle className="w-10 h-10 text-red-500 inline-block" /><p className="mt-4 text-lg text-red-600">{error}</p></td></tr>
            ) : paginatedFollowups.length === 0 ? (
              <tr><td colSpan={7} className="text-center py-16"><p className="text-lg text-gray-500">{searchTerm ? 'No results.' : 'No follow-ups.'}</p></td></tr>
            ) : (
              paginatedFollowups.map((followup, index) => (
                <tr key={followup.id} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50 hover:bg-gray-100'}>
                  <td className="px-6 py-4 whitespace-nowrap"><div className="font-medium text-gray-900">{followup.customer_name || 'N/A'}</div></td>
                  <td className="px-6 py-4 whitespace-nowrap"><StatusBadge status={followup.hotel_status || 'pending'} /></td>
                  <td className="px-6 py-4 whitespace-nowrap"><StatusBadge status={followup.cab_status || 'pending'} /></td>
                  <td className="px-6 py-4 whitespace-nowrap"><StatusBadge status={followup.flight_train_status || 'pending'} /></td>
                  <td className="px-6 py-4 whitespace-nowrap"><StatusBadge status={followup.transportation_status || 'pending'} /></td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-green-600">
                    ₹{followup.profit?.toLocaleString() || '0'}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex items-center space-x-2">
                      <Link to={`/followups/${followup.id}`} className="text-primary hover:text-primary-dark p-2 rounded-md hover:bg-gray-200"><Eye className="w-5 h-5" /></Link>
                      <button onClick={() => handleDelete(followup.id)} className="text-red-600 hover:text-red-800 p-2 rounded-md hover:bg-red-100"><Trash2 className="w-5 h-5" /></button>
                    </div>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>

      {/* Cards for mobile screens */}
      <div className="mt-6 md:hidden space-y-4">
        {isLoading ? (
          <div className="text-center py-16"><Loader className="w-10 h-10 text-primary animate-spin inline-block" /><p className="mt-4 text-lg text-gray-600">Loading...</p></div>
        ) : error ? (
          <div className="text-center py-16"><AlertCircle className="w-10 h-10 text-red-500 inline-block" /><p className="mt-4 text-lg text-red-600">{error}</p></div>
        ) : paginatedFollowups.length === 0 ? (
          <div className="text-center py-16 bg-white rounded-lg shadow-md"><p className="text-lg text-gray-500">{searchTerm ? 'No results.' : 'No follow-ups.'}</p></div>
        ) : (
          paginatedFollowups.map(followup => (
            <div key={followup.id} className="bg-white rounded-lg shadow-md p-4 border border-gray-200">
              <div className="flex justify-between items-start">
                <div className="font-bold text-lg text-gray-800">{followup.customer_name || 'N/A'}</div>
                <div className="flex items-center space-x-1">
                  <Link to={`/followups/${followup.id}`} className="text-primary hover:text-primary-dark p-2 rounded-md hover:bg-gray-100"><Eye className="w-5 h-5" /></Link>
                  <button onClick={() => handleDelete(followup.id)} className="text-red-600 hover:text-red-800 p-2 rounded-md hover:bg-red-100"><Trash2 className="w-5 h-5" /></button>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4 mt-4 text-sm">
                <div className="space-y-1">
                  <p className="text-gray-500">Hotel</p>
                  <StatusBadge status={followup.hotel_status || 'pending'} />
                </div>
                <div className="space-y-1">
                  <p className="text-gray-500">Cab</p>
                  <StatusBadge status={followup.cab_status || 'pending'} />
                </div>
                <div className="space-y-1">
                  <p className="text-gray-500">Flight/Train</p>
                  <StatusBadge status={followup.flight_train_status || 'pending'} />
                </div>
                <div className="space-y-1">
                  <p className="text-gray-500">Transport</p>
                  <StatusBadge status={followup.transportation_status || 'pending'} />
                </div>
                <div className="space-y-1">
                  <p className="text-gray-500">Profit</p>
                  <p className="font-medium text-green-600">₹{followup.profit?.toLocaleString() || '0'}</p>
                </div>
              </div>
            </div>
          ))
        )}
      </div>

      {totalPages > 1 && (
        <div className="flex flex-col sm:flex-row justify-between items-center mt-4 gap-4">
          <div className="text-sm text-gray-700">
            Page {currentPage} of {totalPages}
          </div>
          <div className="flex items-center">
            <button
              onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
              disabled={currentPage === 1}
              className="px-3 py-1 border border-gray-300 rounded-md disabled:opacity-50"
            >
              Previous
            </button>
            <button
              onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
              disabled={currentPage === totalPages}
              className="ml-2 px-3 py-1 border border-gray-300 rounded-md disabled:opacity-50"
            >
              Next
            </button>
            <select
              value={itemsPerPage}
              onChange={e => {
                setItemsPerPage(Number(e.target.value));
                setCurrentPage(1);
              }}
              className="ml-4 px-2 py-1 border border-gray-300 rounded-md"
            >
              <option value={10}>10/page</option>
              <option value={20}>20/page</option>
              <option value={50}>50/page</option>
            </select>
          </div>
        </div>
      )}
    </div>
  );
};

// Helper components for FollowupCustomer view - REFINED
const CustomerAndQuoteCard: React.FC<{
  followup: Followup,
  isEditing?: boolean,
  onFollowupFieldChange?: (field: keyof Followup, value: string) => void,
  onQuoteFieldChange?: (field: keyof Quote, value: string | number) => void
}> = ({ followup, isEditing, onFollowupFieldChange, onQuoteFieldChange }) => (
    <div className="bg-white rounded-2xl shadow-lg p-6 border border-gray-200 hover:shadow-xl transition-shadow duration-300">
        <div className="flex items-center mb-6">
            <div className="bg-gradient-to-br from-blue-100 to-indigo-100 p-4 rounded-full mr-4 shadow-inner">
                <User className="w-6 h-6 text-blue-600" />
            </div>
            <div className="flex-1">
                <h2 className="text-xl font-bold text-gray-800">{isEditing ? 'Edit Customer & Quote Details' : followup.customer_name}</h2>
                {!isEditing && <p className="text-sm text-gray-500">Customer & Quote Information</p>}
            </div>
        </div>

        {isEditing && onFollowupFieldChange && onQuoteFieldChange ? (
            <div className="space-y-6">
                {/* Customer Information Section */}
                <div>
                    <h3 className="text-lg font-semibold text-gray-700 mb-4 flex items-center">
                        <div className="w-1 h-5 bg-blue-500 rounded-full mr-3"></div>
                        Customer Information
                    </h3>
                    <div className="space-y-4">
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">Full Name</label>
                            <input
                                type="text"
                                value={followup.customer_name}
                                onChange={(e) => onFollowupFieldChange('customer_name', e.target.value)}
                                className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-primary focus:border-primary transition-all duration-200"
                                placeholder="Enter customer name"
                            />
                        </div>
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">Phone Number</label>
                            <input
                                type="text"
                                value={followup.customer_phone || ''}
                                onChange={(e) => onFollowupFieldChange('customer_phone', e.target.value)}
                                className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-primary focus:border-primary transition-all duration-200"
                                placeholder="Enter phone number"
                            />
                        </div>
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">Email Address</label>
                            <input
                                type="email"
                                value={followup.customer_email || ''}
                                onChange={(e) => onFollowupFieldChange('customer_email', e.target.value)}
                                className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-primary focus:border-primary transition-all duration-200"
                                placeholder="Enter email address"
                            />
                        </div>
                    </div>
                </div>

                {/* Quote Information Section - Only Package Name and Trip Duration */}
                {followup.quotes && (
                    <div>
                        <h3 className="text-lg font-semibold text-gray-700 mb-4 flex items-center">
                            <div className="w-1 h-5 bg-purple-500 rounded-full mr-3"></div>
                            Quote Information
                        </h3>
                        <div className="space-y-4">
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">Package Name</label>
                                <input
                                    type="text"
                                    value={followup.quotes.package_name || ''}
                                    onChange={(e) => onQuoteFieldChange('package_name', e.target.value)}
                                    className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-primary focus:border-primary transition-all duration-200"
                                    placeholder="Enter package name"
                                />
                            </div>
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">Trip Duration</label>
                                <input
                                    type="text"
                                    value={followup.quotes.trip_duration || ''}
                                    onChange={(e) => onQuoteFieldChange('trip_duration', e.target.value)}
                                    className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-primary focus:border-primary transition-all duration-200"
                                    placeholder="e.g., 5 Days 4 Nights"
                                />
                            </div>
                        </div>
                    </div>
                )}
            </div>
        ) : (
            <div className="space-y-6">
                {/* Customer Information Display */}
                <div>
                    <h3 className="text-lg font-semibold text-gray-700 mb-4 flex items-center">
                        <div className="w-1 h-5 bg-blue-500 rounded-full mr-3"></div>
                        Customer Information
                    </h3>
                    <div className="space-y-3">
                        {followup.customer_phone && (
                            <div className="flex items-center p-3 bg-blue-50 rounded-xl border border-blue-100">
                                <Phone className="w-5 h-5 mr-3 text-blue-600" />
                                <span className="text-gray-700 font-medium">{followup.customer_phone}</span>
                            </div>
                        )}
                        {followup.customer_email && (
                            <div className="flex items-center p-3 bg-green-50 rounded-xl border border-green-100">
                                <Mail className="w-5 h-5 mr-3 text-green-600" />
                                <span className="text-gray-700 font-medium">{followup.customer_email}</span>
                            </div>
                        )}
                    </div>
                </div>

                {/* Quote Information Display - Only Package Name and Trip Duration */}
                {followup.quotes && (
                    <div>
                        <h3 className="text-lg font-semibold text-gray-700 mb-4 flex items-center">
                            <div className="w-1 h-5 bg-purple-500 rounded-full mr-3"></div>
                            Quote Information
                        </h3>
                        <div className="space-y-3">
                            {followup.quotes.package_name && (
                                <div className="flex items-start p-3 bg-purple-50 rounded-xl border border-purple-100">
                                    <Package className="w-5 h-5 mr-3 text-purple-600 mt-0.5 flex-shrink-0" />
                                    <div>
                                        <span className="block text-sm font-medium text-gray-600">Package</span>
                                        <span className="text-gray-800 font-semibold">{followup.quotes.package_name}</span>
                                    </div>
                                </div>
                            )}
                            {followup.quotes.trip_duration && (
                                <div className="flex items-center p-3 bg-indigo-50 rounded-xl border border-indigo-100">
                                    <Clock className="w-5 h-5 mr-3 text-indigo-600" />
                                    <div>
                                        <span className="block text-sm font-medium text-gray-600">Duration</span>
                                        <span className="text-gray-800 font-semibold">{followup.quotes.trip_duration}</span>
                                    </div>
                                </div>
                            )}
                        </div>
                    </div>
                )}
            </div>
        )}
    </div>
);

const getStatusStyle = (status: string) => {
    if (!status) return { bgColor: 'bg-gray-100', textColor: 'text-gray-800', icon: <Clock className="w-4 h-4" /> };
    const lowerStatus = status.toLowerCase();
    if (lowerStatus.includes('completed') || lowerStatus.includes('paid') || lowerStatus.includes('booked') || lowerStatus.includes('advance paid') || lowerStatus.includes('flight paid') || lowerStatus.includes('train paid') || lowerStatus.includes('ferry booked') || lowerStatus.includes('bus booked')) {
        return { bgColor: 'bg-green-100', textColor: 'text-green-800', icon: <CheckCircle className="w-4 h-4" /> };
    }
    if (lowerStatus.includes('pending')) {
        return { bgColor: 'bg-yellow-100', textColor: 'text-yellow-800', icon: <Loader className="w-4 h-4 animate-spin" /> };
    }
    if (lowerStatus.includes('not completed') || lowerStatus.includes('not paid') || lowerStatus.includes('not booked')) {
        return { bgColor: 'bg-red-100', textColor: 'text-red-800', icon: <XCircle className="w-4 h-4" /> };
    }
    if (lowerStatus.includes('no flight/train') || lowerStatus.includes('no ferry/bus')) {
        return { bgColor: 'bg-gray-100', textColor: 'text-gray-800', icon: <MinusCircle className="w-4 h-4" /> };
    }
    return { bgColor: 'bg-blue-100', textColor: 'text-blue-800', icon: <Info className="w-4 h-4" /> };
};

const StatusBadge: React.FC<{ status: string }> = ({ status }) => {
    const { bgColor, textColor, icon } = getStatusStyle(status);
    return (
        <div className={`inline-flex items-center gap-2 px-3 py-1 rounded-full text-xs font-medium ${bgColor} ${textColor}`}>
            {icon}
            <span>{status || 'N/A'}</span>
        </div>
    );
};

const CollapsibleSection: React.FC<{ title: string; children: React.ReactNode; isEditing: boolean; }> = ({ title, children, isEditing }) => {
    const [isOpen, setIsOpen] = useState(true);

    return (
        <div className={`bg-white rounded-2xl shadow-lg border border-gray-200 transition-all duration-300 hover:shadow-xl ${isEditing ? 'ring-2 ring-primary ring-offset-4 shadow-primary/20' : ''}`}>
            <button
                className="w-full flex justify-between items-center p-6 text-left hover:bg-gray-50/50 rounded-t-2xl transition-colors duration-200"
                onClick={() => setIsOpen(!isOpen)}
            >
                <h3 className="text-xl font-bold text-gray-800 flex items-center">
                    <div className="w-1 h-6 bg-gradient-to-b from-primary to-primary-dark rounded-full mr-3"></div>
                    {title}
                </h3>
                <div className="flex items-center">
                    {isEditing && (
                        <div className="w-2 h-2 bg-primary rounded-full mr-3 animate-pulse"></div>
                    )}
                    <ChevronDown className={`w-6 h-6 text-gray-500 transform transition-all duration-300 ${isOpen ? 'rotate-180 text-primary' : 'hover:text-gray-700'}`} />
                </div>
            </button>
            <div className={`overflow-hidden transition-all duration-500 ease-in-out ${isOpen ? 'max-h-screen opacity-100' : 'max-h-0 opacity-0'}`}>
                <div className="p-6 border-t border-gray-100">
                    {children}
                </div>
            </div>
        </div>
    );
};

const StatusItem: React.FC<{
  label: string;
  value: string;
  isEditing: boolean;
  options: string[];
  onChange: (value: string) => void;
  amount?: number | null;
  onAmountChange?: (amount: number) => void;
  showAmountInput?: boolean;
}> = ({ label, value, isEditing, options, onChange, amount, onAmountChange, showAmountInput }) => (
    <div className="p-5 border border-gray-200 rounded-xl bg-gradient-to-br from-gray-50 to-white shadow-sm hover:shadow-md transition-shadow duration-200">
        <p className="text-sm font-semibold text-gray-700 mb-4 flex items-center">
            <div className="w-2 h-2 bg-primary rounded-full mr-2"></div>
            {label}
        </p>
        {isEditing ? (
            <div className="space-y-4">
                <div>
                    <label className="block text-xs font-medium text-gray-600 mb-2">Status</label>
                    <select
                        value={value}
                        onChange={(e) => onChange(e.target.value)}
                        className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-primary focus:border-primary bg-white shadow-sm transition-all duration-200"
                    >
                        {options.map(opt => <option key={opt} value={opt}>{opt}</option>)}
                    </select>
                </div>
                {showAmountInput && onAmountChange && (
                    <div>
                        <label className="block text-xs font-medium text-gray-600 mb-2">Amount (₹)</label>
                        <input
                            type="number"
                            placeholder="Enter amount"
                            value={amount || ''}
                            onChange={(e) => onAmountChange(parseFloat(e.target.value) || 0)}
                            className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-primary focus:border-primary shadow-sm transition-all duration-200"
                        />
                    </div>
                )}
            </div>
        ) : (
            <div className="space-y-3">
                <StatusBadge status={value} />
                {showAmountInput && (
                    <div className="bg-white p-3 rounded-lg border border-gray-100">
                        <span className="text-xs text-gray-500 block">Amount</span>
                        <p className="text-xl font-bold text-gray-800">₹{amount?.toLocaleString() || 'N/A'}</p>
                    </div>
                )}
            </div>
        )}
    </div>
);

const PaymentSummaryCard: React.FC<{ followup: Followup }> = ({ followup }) => {
    const totalCost = Math.round(followup.quotes?.total_cost || 0);
    const hotelQuoteAmount = followup.hotel_full_amount || 0;
    const hotelAdvanceAmount = followup.hotel_advance_amount || 0;
    const hotelFullAmountPaid = followup.hotel_full_amount_paid || 0;

    const cabQuoteAmount = followup.cab_full_amount || 0;
    const cabAdvanceAmount = followup.cab_advance_amount || 0;
    const cabFullAmountPaid = followup.cab_full_amount_paid || 0;

    // Only include flight/train and transportation amounts if status indicates paid/booked
    const shouldIncludeFlightTrain = followup.flight_train_status === 'flight paid' || followup.flight_train_status === 'train paid';
    const shouldIncludeTransportation = followup.transportation_status === 'ferry booked' || followup.transportation_status === 'bus booked';

    const flightTrainPaid = shouldIncludeFlightTrain ? (followup.flight_train_amount || 0) : 0;
    const transportPaid = shouldIncludeTransportation ? (followup.transportation_amount || 0) : 0;

    // Calculate hotel amounts
    const hotelRemaining = hotelQuoteAmount - hotelAdvanceAmount;
    const hotelProfit = hotelFullAmountPaid > 0 ? hotelQuoteAmount - hotelFullAmountPaid : 0;

    // Calculate cab amounts
    const cabRemaining = cabQuoteAmount - cabAdvanceAmount;
    const cabProfit = cabFullAmountPaid > 0 ? cabQuoteAmount - cabFullAmountPaid : 0;

    // Parse hotel details safely
    const parseHotelDetailsForCalculation = (data: Followup): HotelDetail[] => {
        if (!data.hotel_details) return [];
        try {
            const parsed = JSON.parse(data.hotel_details);
            if (Array.isArray(parsed)) {
                return parsed.map(item => ({
                    name: item.name || 'Unnamed Hotel',
                    price: item.price || 0,
                    advance: item.advance || 0,
                    full_amount_paid: item.full_amount_paid || 0,
                    status: item.status || 'pending',
                }));
            }
        } catch (e) {
            // Return empty array if parsing fails
            return [];
        }
        return [];
    };

    // Use existing calculation function for overall profit, GST, and commission
    const hotelDetails = parseHotelDetailsForCalculation(followup);
    const subtotal = followup.quotes ? recalculateSubtotal(followup, hotelDetails) : 0;
    const calculationResult = followup.quotes ? calculateProfitWithGST(followup, hotelDetails, subtotal) : {
        profit: 0, profitBeforeGst: 0, gst: 0, commission: 0, totalExpenses: 0, breakdown: {
            hotelExpenses: 0, cabExpenses: 0, transportationCost: 0, cabSightseeingCost: 0,
            trainCost: 0, ferryCost: 0, marketingCost: 0
        }
    };

    // Add GST amount and commission amount to totalPaid
    const totalPaid = Math.round(hotelFullAmountPaid + hotelAdvanceAmount + cabFullAmountPaid + cabAdvanceAmount + flightTrainPaid + transportPaid + calculationResult.gst + calculationResult.commission);
    const remainingAmount = totalCost - totalPaid;
    const paymentProgress = totalCost > 0 ? (totalPaid / totalCost) * 100 : 0;

    // Enhanced payment completion check with exact status matching
    const isHotelPaid = followup.hotel_status?.includes('full amount paid');
    const isCabPaid = followup.cab_status?.includes('full amount paid');
    const isFlightTrainPaid = followup.flight_train_status === 'flight paid' || followup.flight_train_status === 'train paid';
    const isTransportationBooked = followup.transportation_status === 'ferry booked' || followup.transportation_status === 'bus booked';

    // Check if flight/train is not applicable
    const isFlightTrainNA = followup.flight_train_status === 'no flight/train';
    // Check if transportation is not applicable
    const isTransportationNA = followup.transportation_status === 'no ferry/bus';

    // Check if all applicable payments are completed
    const isAllPaid = isHotelPaid && isCabPaid &&
                     (isFlightTrainPaid || isFlightTrainNA) &&
                     (isTransportationBooked || isTransportationNA);

    return (
        <div className="bg-white rounded-xl shadow-lg p-6 border border-gray-200 hover:shadow-xl transition-shadow duration-300">
            <div className="flex items-center mb-6">
                <div className="bg-gradient-to-br from-green-100 to-emerald-100 p-3 rounded-full mr-4">
                    <DollarSign className="w-6 h-6 text-green-600" />
                </div>
                <div>
                    <h2 className="text-xl font-bold text-gray-800">Payment Summary</h2>
                    <p className="text-sm text-gray-500">Complete financial overview</p>
                </div>
            </div>

            <div className="space-y-6">
                {/* Payment Progress Overview */}
                <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-4 rounded-xl border border-blue-100">
                    <div className="text-center mb-3">
                        <span className="text-3xl font-bold text-blue-600">₹{totalPaid.toLocaleString()}</span>
                        <span className="text-gray-500 text-lg"> / ₹{totalCost.toLocaleString()}</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-4 mb-2">
                        <div
                            className="bg-gradient-to-r from-blue-400 to-blue-600 h-4 rounded-full transition-all duration-500"
                            style={{ width: `${Math.min(paymentProgress, 100)}%` }}
                        ></div>
                    </div>
                    <div className="flex justify-between text-sm font-medium">
                        <span className="text-blue-600">Paid ({paymentProgress.toFixed(1)}%)</span>
                        {!isAllPaid ? (
                            <span className="text-orange-600">Remaining: ₹{remainingAmount.toLocaleString()}</span>
                        ) : (
                            <span className="text-green-600 font-bold flex items-center">
                                <CheckCircle className="w-4 h-4 mr-1" />
                                Payment Complete
                            </span>
                        )}
                    </div>
                </div>

                {/* Hotel Payment Details */}
                <div className="space-y-3">
                    <div className="flex items-center mb-2">
                        <div className="w-1 h-6 bg-purple-500 rounded-full mr-3"></div>
                        <h4 className="font-semibold text-gray-700">Hotel Payments</h4>
                        {isHotelPaid && <CheckCircle className="w-5 h-5 text-green-500 ml-2" />}
                    </div>
                    <div className="bg-purple-50 p-3 rounded-lg border border-purple-100 space-y-2 text-sm">
                        {hotelQuoteAmount > 0 && (
                            <div className="flex justify-between items-center">
                                <span className="text-gray-600">Quote Amount:</span>
                                <span className="text-gray-800 font-medium">₹{hotelQuoteAmount.toLocaleString()}</span>
                            </div>
                        )}
                        {hotelAdvanceAmount > 0 && (
                            <div className="flex justify-between items-center">
                                <span className="text-gray-600">Advance Paid:</span>
                                <span className="text-green-600 font-medium">₹{hotelAdvanceAmount.toLocaleString()}</span>
                            </div>
                        )}
                        {hotelAdvanceAmount > 0 && hotelFullAmountPaid === 0 && (
                            <div className="flex justify-between items-center">
                                <span className="text-gray-600">Remaining:</span>
                                <span className="text-red-600 font-medium">₹{hotelRemaining.toLocaleString()}</span>
                            </div>
                        )}
                        {hotelFullAmountPaid > 0 && (
                            <div className="flex justify-between items-center">
                                <span className="text-gray-600">Full Amount Paid:</span>
                                <span className="text-blue-600 font-medium">₹{hotelFullAmountPaid.toLocaleString()}</span>
                            </div>
                        )}
                        {hotelProfit > 0 && (
                            <div className="flex justify-between items-center">
                                <span className="text-gray-600">Hotel Profit:</span>
                                <span className="text-green-600 font-medium">₹{hotelProfit.toLocaleString()}</span>
                            </div>
                        )}
                    </div>
                </div>

                {/* Cab Payment Details */}
                <div className="space-y-3">
                    <div className="flex items-center mb-2">
                        <div className="w-1 h-6 bg-blue-500 rounded-full mr-3"></div>
                        <h4 className="font-semibold text-gray-700">Cab Payments</h4>
                        {isCabPaid && <CheckCircle className="w-5 h-5 text-green-500 ml-2" />}
                    </div>
                    <div className="bg-blue-50 p-3 rounded-lg border border-blue-100 space-y-2 text-sm">
                        {cabQuoteAmount > 0 && (
                            <div className="flex justify-between items-center">
                                <span className="text-gray-600">Quote Amount:</span>
                                <span className="text-gray-800 font-medium">₹{cabQuoteAmount.toLocaleString()}</span>
                            </div>
                        )}
                        {cabAdvanceAmount > 0 && (
                            <div className="flex justify-between items-center">
                                <span className="text-gray-600">Advance Paid:</span>
                                <span className="text-green-600 font-medium">₹{cabAdvanceAmount.toLocaleString()}</span>
                            </div>
                        )}
                        {cabAdvanceAmount > 0 && cabFullAmountPaid === 0 && (
                            <div className="flex justify-between items-center">
                                <span className="text-gray-600">Remaining:</span>
                                <span className="text-red-600 font-medium">₹{cabRemaining.toLocaleString()}</span>
                            </div>
                        )}
                        {cabFullAmountPaid > 0 && (
                            <div className="flex justify-between items-center">
                                <span className="text-gray-600">Full Amount Paid:</span>
                                <span className="text-blue-600 font-medium">₹{cabFullAmountPaid.toLocaleString()}</span>
                            </div>
                        )}
                        {cabProfit > 0 && (
                            <div className="flex justify-between items-center">
                                <span className="text-gray-600">Cab Profit:</span>
                                <span className="text-blue-600 font-medium">₹{cabProfit.toLocaleString()}</span>
                            </div>
                        )}
                    </div>
                </div>

                {/* Other Payments */}
                {(flightTrainPaid > 0 || transportPaid > 0) && (
                    <div className="space-y-3">
                        <div className="flex items-center mb-2">
                            <div className="w-1 h-6 bg-indigo-500 rounded-full mr-3"></div>
                            <h4 className="font-semibold text-gray-700">Other Payments</h4>
                            {(isFlightTrainPaid && isTransportationBooked) && <CheckCircle className="w-5 h-5 text-green-500 ml-2" />}
                        </div>
                        <div className="bg-indigo-50 p-3 rounded-lg border border-indigo-100 space-y-2 text-sm">
                            {flightTrainPaid > 0 && (
                                <div className="flex justify-between items-center">
                                    <span className="text-gray-600">Flight/Train:</span>
                                    <span className="text-gray-800 font-medium">₹{flightTrainPaid.toLocaleString()}</span>
                                </div>
                            )}
                            {transportPaid > 0 && (
                                <div className="flex justify-between items-center">
                                    <span className="text-gray-600">Transportation:</span>
                                    <span className="text-gray-800 font-medium">₹{transportPaid.toLocaleString()}</span>
                                </div>
                            )}
                        </div>
                    </div>
                )}

                {/* Financial Summary */}
                <div className="bg-gradient-to-r from-emerald-50 to-green-50 p-4 rounded-xl border border-emerald-200">
                    <div className="flex items-center mb-3">
                        <div className="w-1 h-6 bg-emerald-500 rounded-full mr-3"></div>
                        <h4 className="font-semibold text-gray-700">Financial Summary</h4>
                    </div>
                    <div className="space-y-2 text-sm">
                        {calculationResult.commission > 0 && (
                            <div className="flex justify-between items-center">
                                <span className="text-gray-600">Commission Amount:</span>
                                <span className="text-purple-600 font-medium">₹{calculationResult.commission.toLocaleString()}</span>
                            </div>
                        )}
                        {calculationResult.gst > 0 && (
                            <div className="flex justify-between items-center">
                                <span className="text-gray-600">GST Amount (5%):</span>
                                <span className="text-orange-600 font-medium">₹{Math.max(0, calculationResult.gst).toLocaleString()}</span>
                            </div>
                        )}

                        {/* Enhanced Total Profit Section */}
                        <div className="mt-4 pt-3 border-t border-emerald-200">
                            {isAllPaid ? (
                                <div className="bg-green-100 p-3 rounded-lg border-2 border-green-300">
                                    <div className="flex justify-between items-center">
                                        <div className="flex items-center">
                                            <CheckCircle className="w-5 h-5 text-green-600 mr-2" />
                                            <span className="text-green-800 font-semibold">Total Profit</span>
                                        </div>
                                        <span className={`text-2xl font-bold ${calculationResult.profit >= 0 ? 'text-green-700' : 'text-red-700'}`}>
                                            ₹{calculationResult.profit.toLocaleString()}
                                        </span>
                                    </div>
                                    <p className="text-xs text-green-600 mt-1">All payments completed successfully</p>
                                </div>
                            ) : (
                                <div className="bg-yellow-100 p-3 rounded-lg border-2 border-yellow-300">
                                    <div className="flex justify-between items-center">
                                        <div className="flex items-center">
                                            <Clock className="w-5 h-5 text-yellow-600 mr-2" />
                                            <span className="text-yellow-800 font-semibold">Expected Profit</span>
                                        </div>
                                        <span className={`text-xl font-bold ${calculationResult.profit >= 0 ? 'text-yellow-700' : 'text-red-700'}`}>
                                            ₹{calculationResult.profit.toLocaleString()}
                                        </span>
                                    </div>
                                    <p className="text-xs text-yellow-600 mt-1">Pending payment completion</p>
                                </div>
                            )}
                        </div>
                    </div>
                </div>

                {/* Payment Status Overview */}
                <div className="bg-gray-50 p-4 rounded-xl border border-gray-200">
                    <h4 className="font-semibold text-gray-700 mb-3">Payment Status Overview</h4>
                    <div className="grid grid-cols-2 gap-3 text-xs">
                        <div className={`flex items-center p-2 rounded-lg ${isHotelPaid ? 'bg-green-100 text-green-700' : 'bg-orange-100 text-orange-700'}`}>
                            {isHotelPaid ? <CheckCircle className="w-4 h-4 mr-2" /> : <Clock className="w-4 h-4 mr-2" />}
                            <span>Hotel: {followup.hotel_status || 'Pending'}</span>
                        </div>
                        <div className={`flex items-center p-2 rounded-lg ${isCabPaid ? 'bg-green-100 text-green-700' : 'bg-orange-100 text-orange-700'}`}>
                            {isCabPaid ? <CheckCircle className="w-4 h-4 mr-2" /> : <Clock className="w-4 h-4 mr-2" />}
                            <span>Cab: {followup.cab_status || 'Pending'}</span>
                        </div>
                        <div className={`flex items-center p-2 rounded-lg ${
                            isFlightTrainPaid || isFlightTrainNA ? 'bg-green-100 text-green-700' : 'bg-orange-100 text-orange-700'
                        }`}>
                            {isFlightTrainPaid || isFlightTrainNA ? <CheckCircle className="w-4 h-4 mr-2" /> : <Clock className="w-4 h-4 mr-2" />}
                            <span>Flight/Train: {followup.flight_train_status || 'Pending'}</span>
                        </div>
                        <div className={`flex items-center p-2 rounded-lg ${
                            isTransportationBooked || isTransportationNA ? 'bg-green-100 text-green-700' : 'bg-orange-100 text-orange-700'
                        }`}>
                            {isTransportationBooked || isTransportationNA ? <CheckCircle className="w-4 h-4 mr-2" /> : <Clock className="w-4 h-4 mr-2" />}
                            <span>Transport: {followup.transportation_status || 'Pending'}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};


// Enhanced Calculation Functions
const recalculateSubtotal = (
  followup: Followup,
  hotelDetails: HotelDetail[]
): number => {
  const hotelTotal = hotelDetails.reduce((sum, hotel) => sum + (hotel.price || 0), 0);
  const cabTotal = followup.cab_full_amount || 0;
  const flightTrainTotal = followup.flight_train_amount || 0;
  const transportationTotal = followup.transportation_amount || 0;
  const marketingTotal = followup.marketing_amount || 0;

  return hotelTotal + cabTotal + flightTrainTotal + transportationTotal + marketingTotal;
};

const calculateProfitWithGST = (
  followup: Followup,
  hotelDetails: HotelDetail[],
  subtotal: number
): { 
  profit: number; 
  profitBeforeGst: number; 
  gst: number; 
  commission: number; 
  totalExpenses: number;
  breakdown: {
    hotelExpenses: number;
    cabExpenses: number;
    transportationCost: number;
    cabSightseeingCost: number;
    trainCost: number;
    ferryCost: number;
    marketingCost: number;
  }
} => {
  // Calculate commission from quote and round it
  const commissionRate = followup.quotes?.commission_rate || 0;
  const commissionAmount = Math.round(subtotal * (commissionRate / 100));

  // Get costs from the costs table
  const costs = followup.quotes?.costs;
  const transportationCost = costs?.transportation || 0;
  const cabSightseeingCost = costs?.cab_sightseeing || 0;
  const trainCost = costs?.train_cost || 0;
  const ferryCost = costs?.ferry_cost || 0;
  const marketingCost = costs?.marketing || 0;

  // Calculate hotel expenses (only paid amounts)
  const hotelExpenses = hotelDetails.reduce((sum, hotel) => sum + (hotel.full_amount_paid || 0), 0);
  
  // Calculate cab expenses (only paid amounts)
  const cabExpenses = followup.cab_full_amount_paid || 0;

  // Calculate total expenses
  const totalExpenses = 
    hotelExpenses +
    cabExpenses +
    transportationCost +
    cabSightseeingCost +
    trainCost +
    ferryCost +
    marketingCost;

  // Calculate total with commission and GST, and round GST
  const totalWithCommission = subtotal + commissionAmount;
  const gst = Math.round(totalWithCommission * 0.05); // 5% GST

  // New profit calculation: totalCost - totalPaid
  const totalCost = followup.total_cost || 0;
  const totalPaid = followup.total_paid || 0;
  const profit = totalCost - totalPaid;

  return {
    profit: Math.round(profit),
    profitBeforeGst: Math.round(profit), // Keep for compatibility
    gst: gst,
    commission: commissionAmount,
    totalExpenses: Math.round(totalExpenses),
    breakdown: {
      hotelExpenses,
      cabExpenses,
      transportationCost,
      cabSightseeingCost,
      trainCost,
      ferryCost,
      marketingCost,
    }
  };
};

const calculateTotalQuoteCost = (
  subtotal: number,
  commission: number,
  gst: number
): number => {
  return Math.round(subtotal + commission + gst);
};

// Followup Customer Details View - ENHANCED
const FollowupCustomer: React.FC<{ id: string }> = ({ id }) => {
  const [followup, setFollowup] = useState<Followup | null>(null);
  const [editedFollowup, setEditedFollowup] = useState<Followup | null>(null);
  const [isEditing, setIsEditing] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [supabase, setSupabase] = useState<any>(null);
  const [editedHotelDetails, setEditedHotelDetails] = useState<HotelDetail[]>([]);
  const [notes, setNotes] = useState('');

  useEffect(() => {
    const initSupabase = async () => {
      try {
        const client = await getQuoteClient();
        setSupabase(client);
      } catch (error) {
        console.error('Error initializing Supabase client:', error);
        setError('Could not connect to the database.');
        setIsLoading(false);
      }
    };
    initSupabase();
  }, []);

  const parseHotelDetails = (data: Followup): HotelDetail[] => {
    if (!data.hotel_details) return [];
    try {
      const parsed = JSON.parse(data.hotel_details);
      if (Array.isArray(parsed) && parsed.length > 0) {
        return parsed.map(item => ({
          name: item.name || 'Unnamed Hotel',
          price: item.price || 0,
          advance: item.advance || 0,
          full_amount_paid: item.full_amount_paid || 0,
          status: item.status || 'pending',
        }));
      }
    } catch (e) {
      // Fallback for old string format
      return (data.hotel_details || '')
        .split('\n')
        .filter(line => line.trim())
        .map((line, index) => {
          const parts = line.split(': ₹');
          const advance = index === 0 ? (data.hotel_advance_amount || 0) : 0;
          return {
            name: parts[0],
            price: parts.length > 1 ? parseFloat(parts[1].replace(/,/g, '')) : 0,
            advance: advance,
            full_amount_paid: 0,
            status: data.hotel_status || 'pending'
          };
        });
    }
    return [];
  };

  const fetchFollowupData = async () => {
    if (!id || !supabase) return;
    setIsLoading(true);
    setError(null);
    try {
      const { data, error } = await supabase
        .from('followups')
        .select(`*, quotes(*, costs(*))`)
        .eq('id', id)
        .single();

      if (error) throw error;
      const followupData = data as any;
      setFollowup(followupData);
      setEditedFollowup(JSON.parse(JSON.stringify(followupData)));
      setEditedHotelDetails(parseHotelDetails(followupData));
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unknown error occurred.');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchFollowupData();
  }, [id, supabase]);

  useEffect(() => {
    if (followup) {
      setNotes(followup.notes || '');
    }
  }, [followup]);

  const handleHotelDetailChange = (index: number, field: keyof HotelDetail, value: string | number) => {
    const newDetails = [...editedHotelDetails];
    const hotel = newDetails[index];
    (hotel as any)[field] = value;

    if (field === 'advance' || field === 'full_amount_paid') {
        const advance = field === 'advance' ? Number(value) : hotel.advance;
        const fullAmountPaid = field === 'full_amount_paid' ? Number(value) : hotel.full_amount_paid;

        if (fullAmountPaid > 0) {
            hotel.status = 'full amount paid';
        } else if (advance > 0) {
            hotel.status = 'advance paid';
        } else {
            hotel.status = 'pending';
        }
    }
    setEditedHotelDetails(newDetails);
  };

  const addHotel = () => {
    setEditedHotelDetails([...editedHotelDetails, { name: '', price: 0, advance: 0, full_amount_paid: 0, status: 'pending' }]);
  };

  const removeHotel = (index: number) => {
    const newDetails = [...editedHotelDetails];
    newDetails.splice(index, 1);
    setEditedHotelDetails(newDetails);
  };

  const handleFollowupFieldChange = (field: keyof Followup, value: string) => {
    if (editedFollowup) {
      setEditedFollowup({ ...editedFollowup, [field]: value });
    }
  };

  const handleQuoteFieldChange = (field: keyof Quote, value: string | number) => {
    if (editedFollowup && editedFollowup.quotes) {
      setEditedFollowup({
        ...editedFollowup,
        quotes: { ...editedFollowup.quotes, [field]: value },
      });
    }
  };

  const handleSave = async () => {
    if (editedFollowup && supabase) {
      try {
        const hotel_full_amount = editedHotelDetails.reduce((sum, hotel) => sum + (hotel.price || 0), 0);
        const hotel_advance_amount = editedHotelDetails.reduce((sum, hotel) => sum + (hotel.advance || 0), 0);
        const hotel_full_amount_paid = editedHotelDetails.reduce((sum, hotel) => sum + (hotel.full_amount_paid || 0), 0);

        const subtotal = recalculateSubtotal(editedFollowup, editedHotelDetails);
        const calculationResult = calculateProfitWithGST(editedFollowup, editedHotelDetails, subtotal);
        const totalCost = calculateTotalQuoteCost(subtotal, calculationResult.commission, calculationResult.gst);

        const cab_full_amount_paid = editedFollowup.cab_full_amount_paid || 0;
        const cab_advance_amount = editedFollowup.cab_advance_amount || 0;
        const shouldIncludeFlightTrain = editedFollowup.flight_train_status === 'flight paid' || editedFollowup.flight_train_status === 'train paid';
        const flightTrainPaid = shouldIncludeFlightTrain ? (editedFollowup.flight_train_amount || 0) : 0;
        const shouldIncludeTransportation = editedFollowup.transportation_status === 'ferry booked' || editedFollowup.transportation_status === 'bus booked';
        const transportPaid = shouldIncludeTransportation ? (editedFollowup.transportation_amount || 0) : 0;
        const totalPaid = Math.round(
            hotel_full_amount_paid +
            hotel_advance_amount +
            cab_full_amount_paid +
            cab_advance_amount +
            flightTrainPaid +
            transportPaid +
            calculationResult.gst +
            calculationResult.commission
        );

        const profit = totalCost - totalPaid;

        const { quotes, ...followupToUpdate } = editedFollowup;

        const updatePayload: Partial<Followup> = {
            ...followupToUpdate,
            hotel_details: JSON.stringify(editedHotelDetails),
            hotel_full_amount,
            hotel_advance_amount,
            hotel_full_amount_paid,
            notes,
            profit,
            marketing_amount: editedFollowup.marketing_amount,
            total_cost: totalCost,
            total_paid: totalPaid,
        };
        
        if (editedHotelDetails.length > 0) {
            const statuses = editedHotelDetails.map(h => h.status);
            if (statuses.every(s => s === 'full amount paid')) {
                updatePayload.hotel_status = 'full amount paid';
            } else if (statuses.some(s => s === 'pending')) {
                updatePayload.hotel_status = 'pending';
            } else if (statuses.some(s => s === 'advance paid' || s === 'full amount paid')) {
                updatePayload.hotel_status = 'advance paid';
            }
        }

        const { error: followupError } = await supabase
          .from('followups')
          .update(updatePayload)
          .eq('id', editedFollowup.id);

        if (followupError) throw followupError;

        if (quotes) {
          const { costs, ...quoteToUpdate } = quotes;
          const updatedQuote = { ...quoteToUpdate, subtotal: subtotal, total_cost: totalCost };
          const { error: quoteError } = await supabase
            .from('quotes')
            .update(updatedQuote)
            .eq('id', quotes.id);
          if (quoteError) throw quoteError;
        }
        
        await fetchFollowupData();
        setIsEditing(false);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An unknown error occurred.');
      }
    }
  };

  if (isLoading) return <div className="flex justify-center items-center min-h-screen"><Loader className="w-12 h-12 text-primary animate-spin" /></div>;
  if (error) return <div className="p-6 text-center text-red-500">Error: {error}</div>;
  if (!followup) return <div className="p-6 text-center text-gray-500">Follow-up not found</div>;

  const currentFollowup = isEditing && editedFollowup ? editedFollowup : followup;

  if (!currentFollowup) return <div className="flex justify-center items-center min-h-screen"><Loader className="w-12 h-12 text-primary animate-spin" /></div>;

  const hotelDetailsToDisplay = isEditing ? editedHotelDetails : parseHotelDetails(currentFollowup);

  // Compute calculationResult for use in JSX
  const subtotal = currentFollowup.quotes ? recalculateSubtotal(currentFollowup, hotelDetailsToDisplay) : 0;
  const calculationResult = currentFollowup.quotes
    ? calculateProfitWithGST(currentFollowup, hotelDetailsToDisplay, subtotal)
    : {
        profit: 0,
        profitBeforeGst: 0,
        gst: 0,
        commission: 0,
        totalExpenses: 0,
        breakdown: {
          hotelExpenses: 0,
          cabExpenses: 0,
          transportationCost: 0,
          cabSightseeingCost: 0,
          trainCost: 0,
          ferryCost: 0,
          marketingCost: 0,
        },
      };

  return (
    <div className="p-4 sm:p-6 md:p-8 bg-gradient-to-br from-gray-50 via-blue-50/30 to-indigo-50/30 min-h-screen">
      <div className="max-w-7xl mx-auto">
        {/* Navigation */}
        <div className="mb-8">
          <Link to="/followups" className="inline-flex items-center text-primary hover:text-primary-dark font-semibold transition-colors group">
            <ArrowLeft className="w-5 h-5 mr-2 group-hover:-translate-x-1 transition-transform" />
            Back to Follow-ups
          </Link>
        </div>

        {/* Header Section */}
        <div className="bg-white rounded-2xl shadow-lg p-6 mb-8 border border-gray-100">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <div>
              <h1 className="text-3xl font-bold text-gray-800 mb-2">Follow-up Details</h1>
              <p className="text-gray-600">Manage customer payments and track progress</p>
            </div>
            <div>
              {isEditing ? (
                <div className="flex gap-3">
                  <button
                    onClick={handleSave}
                    className="bg-gradient-to-r from-green-500 to-emerald-600 text-white px-6 py-3 rounded-xl hover:from-green-600 hover:to-emerald-700 flex items-center shadow-lg transition-all duration-300 transform hover:scale-105 hover:shadow-xl"
                  >
                    <Save className="w-5 h-5 mr-2" />
                    Save Changes
                  </button>
                  <button
                    onClick={() => { setIsEditing(false); fetchFollowupData(); }}
                    className="bg-gray-100 text-gray-700 px-6 py-3 rounded-xl hover:bg-gray-200 flex items-center shadow-md transition-all duration-300"
                  >
                    <X className="w-5 h-5 mr-2" />
                    Cancel
                  </button>
                </div>
              ) : (
                <button
                  onClick={() => setIsEditing(true)}
                  className="bg-gradient-to-r from-primary to-primary-dark text-white px-6 py-3 rounded-xl hover:from-primary-dark hover:to-primary flex items-center shadow-lg transition-all duration-300 transform hover:scale-105 hover:shadow-xl"
                >
                  <Edit className="w-5 h-5 mr-2" />
                  Edit Details
                </button>
              )}
            </div>
          </div>
        </div>

        {/* Content Grid */}
        <div className="grid grid-cols-1 xl:grid-cols-3 gap-8">
          {/* Left Column - Customer Info & Payment Summary */}
          <div className="xl:col-span-1 space-y-8">
            <CustomerAndQuoteCard
              followup={currentFollowup}
              isEditing={isEditing}
              onFollowupFieldChange={handleFollowupFieldChange}
              onQuoteFieldChange={handleQuoteFieldChange}
            />
            <PaymentSummaryCard followup={currentFollowup} />
          </div>

          {/* Right Column - Payment Details */}
          <div className="xl:col-span-2 space-y-8">
            <CollapsibleSection title="Hotel Details" isEditing={isEditing}>
              {isEditing ? (
                <div className="space-y-4 max-h-[60vh] overflow-y-auto">
                  {editedHotelDetails.map((hotel, index) => (
                    <div key={index} className="p-4 border rounded-lg bg-gray-50/50 space-y-3 relative">
                      <button type="button" onClick={() => removeHotel(index)} className="absolute top-2 right-2 text-red-500 hover:text-red-700"><XCircle size={18} /></button>
                      <div>
                          <label className="text-sm font-medium text-gray-700">Hotel Name</label>
                          <input type="text" placeholder="Hotel Name" value={hotel.name} onChange={(e) => handleHotelDetailChange(index, 'name', e.target.value)} className="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm" />
                      </div>
                      <div className="grid grid-cols-1 sm:grid-cols-3 gap-2">
                          <div>
                              <label className="text-sm font-medium text-gray-700">Quote Amount</label>
                              <input type="number" placeholder="Full Amount" value={hotel.price} onChange={(e) => handleHotelDetailChange(index, 'price', parseFloat(e.target.value) || 0)} className="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm" />
                          </div>
                          <div>
                              <label className="text-sm font-medium text-gray-700">Advance Paid</label>
                              <input type="number" placeholder="Advance Paid" value={hotel.advance} onChange={(e) => handleHotelDetailChange(index, 'advance', parseFloat(e.target.value) || 0)} className="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm" />
                          </div>
                          <div>
                              <label className="text-sm font-medium text-gray-700">Full Amount Paid</label>
                              <input type="number" placeholder="Full Amount Paid" value={hotel.full_amount_paid} onChange={(e) => handleHotelDetailChange(index, 'full_amount_paid', parseFloat(e.target.value) || 0)} className="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm" />
                          </div>
                      </div>
                      <div>
                          <label className="text-sm font-medium text-gray-700">Remaining</label>
                          <input type="number" placeholder="Remaining" value={(hotel.price || 0) - (hotel.advance || 0)} readOnly className="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-100" />
                      </div>
                      <div>
                          <label className="text-sm font-medium text-gray-700">Status</label>
                          <select value={hotel.status} onChange={(e) => handleHotelDetailChange(index, 'status', e.target.value)} className="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm">
                            <option value="pending">Pending</option>
                            <option value="advance paid">Advance Paid</option>
                            <option value="full amount paid">Full Amount Paid</option>
                            <option value="not completed">Not Completed</option>
                          </select>
                      </div>
                    </div>
                  ))}
                  <button type="button" onClick={addHotel} className="text-primary hover:text-primary-dark font-semibold flex items-center gap-2"><Plus size={16} /> Add Hotel</button>
                </div>
              ) : (
                <div className="space-y-4">
                  {hotelDetailsToDisplay.length > 0 ? hotelDetailsToDisplay.map((hotel, index) => (
                    <div key={index} className="p-4 border rounded-lg bg-gray-50/50 shadow-sm">
                      <div className="flex justify-between items-start">
                        <p className="font-semibold text-md text-gray-800">{hotel.name}</p>
                        <StatusBadge status={hotel.status} />
                      </div>
                      <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 text-sm mt-3 pt-3 border-t">
                        <div>
                          <span className="text-gray-500">Quote Amount:</span><br/><span className="font-medium text-gray-800">₹{hotel.price.toLocaleString()}</span>
                        </div>
                        {hotel.advance > 0 && (
                          <>
                            <div>
                              <span className="text-gray-500">Advance Paid:</span><br/><span className="font-medium text-green-600">₹{hotel.advance.toLocaleString()}</span>
                            </div>
                            <div>
                              <span className="text-gray-500">Remaining:</span><br/><span className="font-medium text-red-600">₹{(hotel.price - hotel.advance).toLocaleString()}</span>
                            </div>
                          </>
                        )}
                        {hotel.full_amount_paid > 0 && (
                          <div>
                            <span className="text-gray-500">Full Amount Paid:</span><br/><span className="font-medium text-green-600">₹{hotel.full_amount_paid.toLocaleString()}</span>
                          </div>
                        )}
                      </div>
                    </div>
                  )) : <p className="text-gray-500">No hotel details available.</p>}
                </div>
              )}
            </CollapsibleSection>
            <CollapsibleSection title="Cab Details" isEditing={isEditing}>
              {isEditing && editedFollowup ? (
                <div className="space-y-3">

                  <div className="grid grid-cols-1 sm:grid-cols-3 gap-2">
                      <div>
                          <label className="text-sm font-medium text-gray-700">Quote Amount</label>
                          <input type="number" placeholder="Full Amount" value={editedFollowup.cab_full_amount || ''} onChange={(e) => editedFollowup && setEditedFollowup({ ...editedFollowup, cab_full_amount: parseFloat(e.target.value) || 0 })} className="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm" />
                      </div>
                      <div>
                          <label className="text-sm font-medium text-gray-700">Advance Paid</label>
                          <input type="number" placeholder="Advance Paid" value={editedFollowup.cab_advance_amount || ''} onChange={(e) => {
                            if (editedFollowup) {
                                const amount = parseFloat(e.target.value) || 0;
                                setEditedFollowup(prev => {
                                    if (!prev) return null;
                                    let newStatus = prev.cab_status;
                                    if (amount > 0 && newStatus === 'pending') {
                                        newStatus = 'advance paid';
                                    } else if (amount <= 0 && newStatus === 'advance paid') {
                                        newStatus = 'pending';
                                    }
                                    return { ...prev, cab_advance_amount: amount, cab_status: newStatus };
                                });
                            }
                          }} className="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm" />
                      </div>
                      <div>
                          <label className="text-sm font-medium text-gray-700">Full Amount Paid</label>
                          <input type="number" placeholder="Full Amount Paid" value={editedFollowup.cab_full_amount_paid || ''} onChange={(e) => {
                            if (editedFollowup) {
                                const amount = parseFloat(e.target.value) || 0;
                                setEditedFollowup(prev => {
                                    if (!prev) return null;
                                    let newStatus = prev.cab_status;
                                    if (amount > 0) {
                                        newStatus = 'full amount paid';
                                    } else if ((prev.cab_advance_amount || 0) > 0) {
                                        newStatus = 'advance paid';
                                    } else {
                                        newStatus = 'pending';
                                    }
                                    return { ...prev, cab_full_amount_paid: amount, cab_status: newStatus };
                                });
                            }
                          }} className="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm" />
                      </div>
                  </div>
                  <div>
                      <label className="text-sm font-medium text-gray-700">Remaining</label>
                      <input type="number" placeholder="Remaining" value={(editedFollowup.cab_full_amount || 0) - (editedFollowup.cab_advance_amount || 0)} readOnly className="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-100" />
                  </div>
                  <div>
                      <label className="text-sm font-medium text-gray-700">Cab Status</label>
                      <select value={editedFollowup.cab_status} onChange={(e) => editedFollowup && setEditedFollowup({ ...editedFollowup, cab_status: e.target.value })} className="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm">
                        <option value="pending">Pending</option>
                        <option value="advance paid">Advance Paid</option>
                        <option value="full amount paid">Full Amount Paid</option>
                        <option value="not completed">Not Completed</option>
                      </select>
                  </div>
                </div>
              ) : (
                <div>
                  <div className="flex justify-between items-start">
                    <div>
                      <p className="font-semibold text-md text-gray-800">Cab Service</p>
                      {currentFollowup.cab_name && <p className="text-sm text-gray-500">{currentFollowup.cab_name}</p>}
                    </div>
                    <StatusBadge status={currentFollowup.cab_status} />
                  </div>
                  <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 text-sm mt-3 pt-3 border-t">
                    <div><span className="text-gray-500">Quote Amount:</span><br/><span className="font-medium text-gray-800">₹{currentFollowup.cab_full_amount?.toLocaleString() ?? '0'}</span></div>
                    {currentFollowup.cab_advance_amount != null && currentFollowup.cab_advance_amount > 0 &&
                    <>
                    <div><span className="text-gray-500">Advance Paid:</span><br/><span className="font-medium text-green-600">₹{currentFollowup.cab_advance_amount.toLocaleString()}</span></div>
                    <div><span className="text-gray-500">Remaining:</span><br/><span className="font-medium text-red-600">₹{((currentFollowup.cab_full_amount || 0) - currentFollowup.cab_advance_amount).toLocaleString()}</span></div>
                    </>
                    }
                    {currentFollowup.cab_full_amount_paid != null && currentFollowup.cab_full_amount_paid > 0 &&
                    <div><span className="text-gray-500">Full Amount Paid:</span><br/><span className="font-medium text-green-600">₹{currentFollowup.cab_full_amount_paid.toLocaleString()}</span></div>
                    }
                  </div>
                </div>
              )}
            </CollapsibleSection>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
              <CollapsibleSection title="Flight/Train Status" isEditing={isEditing}>
                <StatusItem
                  label="Flight/Train Status"
                  value={currentFollowup.flight_train_status}
                  isEditing={isEditing}
                  options={['pending', 'flight paid', 'train paid', 'flight not paid', 'train not paid', 'no flight/train']}
                  onChange={(val) => editedFollowup && setEditedFollowup({ ...editedFollowup, flight_train_status: val, flight_train_amount: val === 'no flight/train' ? 0 : editedFollowup.flight_train_amount })}
                  amount={currentFollowup.flight_train_amount}
                  onAmountChange={(amount) => editedFollowup && setEditedFollowup({ ...editedFollowup, flight_train_amount: amount })}
                  showAmountInput={currentFollowup.flight_train_status !== 'no flight/train'}
                />
              </CollapsibleSection>

              <CollapsibleSection title="Transportation Status" isEditing={isEditing}>
                <StatusItem
                  label="Transportation Status"
                  value={currentFollowup.transportation_status}
                  isEditing={isEditing}
                  options={['pending', 'ferry booked', 'bus booked', 'ferry not booked', 'bus not booked', 'no ferry/bus']}
                  onChange={(val) => editedFollowup && setEditedFollowup({ ...editedFollowup, transportation_status: val, transportation_amount: val === 'no ferry/bus' ? 0 : editedFollowup.transportation_amount })}
                  amount={currentFollowup.transportation_amount}
                  onAmountChange={(amount) => editedFollowup && setEditedFollowup({ ...editedFollowup, transportation_amount: amount })}
                  showAmountInput={currentFollowup.transportation_status !== 'no ferry/bus'}
                />
              </CollapsibleSection>
            </div>
            {/* Split into two columns */}
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
              <CollapsibleSection title="Other Cost" isEditing={isEditing}>
                <div className="space-y-4">
                  {/* Marketing Amount - No Status, Only Amount */}
                  <div className="p-4 border border-gray-200 rounded-lg bg-gray-50/50">
                    <p className="text-sm font-semibold text-gray-600 mb-3">Marketing Cost</p>
                    {isEditing ? (
                      <input
                        type="number"
                        value={editedFollowup?.marketing_amount || 0}
                        onChange={(e) => editedFollowup && setEditedFollowup({ ...editedFollowup, marketing_amount: parseFloat(e.target.value) || 0 })}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg mt-1 shadow-sm"
                      />
                    ) : (
                      <div className="flex flex-col items-start">
                        <p className="text-xl font-semibold text-gray-800">₹{currentFollowup.marketing_amount?.toLocaleString() || '0'}</p>
                      </div>
                    )}
                  </div>

                  {/* GST Amount - Editable */}
                  <div className="p-4 border border-gray-200 rounded-lg bg-gray-50/50">
                    <p className="text-sm font-semibold text-gray-600 mb-3">GST Amount</p>
                    {isEditing ? (
                      <input
                        type="number"
                        value={editedFollowup?.quotes?.costs?.gst || calculationResult.gst}
                        onChange={(e) => {
                          if (editedFollowup && editedFollowup.quotes) {
                            const updatedCosts = {
                              ...(editedFollowup.quotes.costs || {}),
                              gst: parseFloat(e.target.value) || 0
                            };
                            setEditedFollowup({
                              ...editedFollowup,
                              quotes: {
                                ...editedFollowup.quotes,
                                costs: updatedCosts
                              }
                            });
                          }
                        }}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg mt-1 shadow-sm"
                      />
                    ) : (
                      <div className="flex flex-col items-start">
                        <p className="text-xl font-semibold text-orange-600">₹{Math.max(0, calculationResult.gst).toLocaleString()}</p>
                      </div>
                    )}
                  </div>

                  {/* Commission Amount - Editable */}
                  <div className="p-4 border border-gray-200 rounded-lg bg-gray-50/50">
                    <p className="text-sm font-semibold text-gray-600 mb-3">Commission Amount</p>
                    {isEditing ? (
                      <input
                        type="number"
                        value={editedFollowup?.quotes?.costs?.commission || calculationResult.commission}
                        onChange={(e) => {
                          if (editedFollowup && editedFollowup.quotes) {
                            const updatedCosts = {
                              ...(editedFollowup.quotes.costs || {}),
                              commission: parseFloat(e.target.value) || 0
                            };
                            setEditedFollowup({
                              ...editedFollowup,
                              quotes: {
                                ...editedFollowup.quotes,
                                costs: updatedCosts
                              }
                            });
                          }
                        }}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg mt-1 shadow-sm"
                      />
                    ) : (
                      <div className="flex flex-col items-start">
                        <p className="text-xl font-semibold text-purple-600">₹{calculationResult.commission.toLocaleString()}</p>
                      </div>
                    )}
                  </div>
                </div>
              </CollapsibleSection>

              {/* Notes Section - Moved to second column */}
              <CollapsibleSection title="Notes" isEditing={isEditing}>
                  <textarea
                      placeholder={isEditing ? "Add a new note..." : "No notes available."}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-primary focus:border-primary shadow-sm"
                      rows={5}
                      value={isEditing ? notes : currentFollowup.notes || ''}
                      onChange={(e) => setNotes(e.target.value)}
                      disabled={!isEditing}
                  ></textarea>
              </CollapsibleSection>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

interface HotelRow {
  hotel_name: string;
  stay_price: number;
  price?: number; // Keep price optional for backward compatibility if needed
}

// Create Followup View
const CreateFollowup: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [selectedQuoteId, setSelectedQuoteId] = useState<string>('');
  const [customerName, setCustomerName] = useState('');
  const [customerPhone, setCustomerPhone] = useState('');
  const [customerEmail, setCustomerEmail] = useState('');

  const [hotelRows, setHotelRows] = useState<HotelRow[]>([]);
  const [costs, setCosts] = useState<Costs>({
    transportation: 0,
    cab_sightseeing: 0,
    train_cost: 0,
    ferry_cost: 0,
    marketing: 0,
  });
  const [supabase, setSupabase] = useState<any>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const desiredCosts: (keyof Costs)[] = [
    'transportation',
    'cab_sightseeing',
    'train_cost',
    'ferry_cost',
    'marketing',
  ];

  useEffect(() => {
    const initSupabase = async () => {
      try {
        const client = await getQuoteClient();
        setSupabase(client);
      } catch (error) {
        console.error('Error initializing Supabase client:', error);
        setError('Could not connect to the database.');
      }
    };
    initSupabase();
  }, []);

  useEffect(() => {
    const params = new URLSearchParams(location.search);
    const quoteId = params.get('quoteId');
    if (quoteId) {
      setSelectedQuoteId(quoteId);
    }
  }, [location.search]);

  useEffect(() => {
    if (supabase && selectedQuoteId) {
      const fetchQuoteDetails = async () => {
        try {
          const { data, error } = await supabase
            .from('quotes')
            .select('*, hotel_rows(*), costs(*)')
            .eq('id', selectedQuoteId)
            .single();

          if (error) throw error;

          if (data) {
            setCustomerName(data.customer_name || '');
            setCustomerPhone(data.customer_phone || '');
            setCustomerEmail(data.customer_email || '');
            setHotelRows(data.hotel_rows || []);
            setCosts(data.costs ? data.costs[0] : {
              transportation: 0,
              cab_sightseeing: 0,
              train_cost: 0,
              ferry_cost: 0,
              marketing: 0,
            });

          }
        } catch (err) {
          setError(err instanceof Error ? `Error fetching quote details: ${err.message}` : 'An unknown error occurred.');
        }
      };
      fetchQuoteDetails();
    }
  }, [supabase, selectedQuoteId]);

  const handleHotelRowChange = (index: number, field: keyof HotelRow, value: string) => {
    const updatedRows = [...hotelRows];
    if (field === 'stay_price' || field === 'price') {
      updatedRows[index][field] = parseFloat(value) || 0;
    } else {
      updatedRows[index][field] = value;
    }
    setHotelRows(updatedRows);
  };

  const handleCostChange = (field: keyof Costs, value: string) => {
    setCosts(prev => ({ ...prev, [field]: parseFloat(value) || 0 }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setError(null);

    if (!supabase) {
      setError('Database connection not available.');
      setIsSubmitting(false);
      return;
    }

    try {
      const hotelDetails: HotelDetail[] = hotelRows.map(h => ({
        name: h.hotel_name,
        price: h.stay_price,
        advance: 0, // Default advance to 0
        full_amount_paid: 0,
        status: 'pending' // Default status
      }));

      const hotel_full_amount = hotelDetails.reduce((sum, hotel) => sum + hotel.price, 0);

      // Create the followup entry
      const { error: insertError } = await supabase.from('followups').insert({
        quote_id: selectedQuoteId || null,
        customer_name: customerName,
        customer_phone: customerPhone,
        customer_email: customerEmail,

        hotel_status: 'pending', // Overall status, can be deprecated later
        cab_status: 'pending',
        flight_train_status: 'pending',
        transportation_status: 'pending',
        hotel_details: JSON.stringify(hotelDetails),
        hotel_full_amount: hotel_full_amount,
        hotel_advance_amount: 0,
        transportation_amount: costs.transportation,
        cab_full_amount: costs.cab_sightseeing,
        flight_train_amount: (costs.train_cost || 0) + (costs.ferry_cost || 0),
        marketing_amount: costs.marketing,
      });

      if (insertError) {
        console.error('Insert error:', insertError);
        throw insertError;
      }

      navigate('/followups');
    } catch (err) {
      setError(err instanceof Error ? `Error: ${err.message}` : 'An unknown error occurred.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      <h1 className="text-3xl font-bold text-gray-800 mb-6">Create New Follow-up</h1>
      <div className="bg-white rounded-lg shadow-md p-8 max-w-3xl mx-auto">
        <form onSubmit={handleSubmit}>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            {/* Customer Name (Read-only) */}
            <div>
              <label htmlFor="customerName" className="block text-sm font-medium text-gray-700 mb-2">
                <User className="w-4 h-4 inline mr-2" />Customer
              </label>
              <input
                type="text"
                id="customerName"
                value={customerName}
                onChange={(e) => setCustomerName(e.target.value)}
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-primary focus:border-primary"
              />
            </div>

            {/* Customer Phone (Read-only) */}
            <div>
              <label htmlFor="customerPhone" className="block text-sm font-medium text-gray-700 mb-2">
                <User className="w-4 h-4 inline mr-2" />Phone
              </label>
              <input
                type="text"
                id="customerPhone"
                value={customerPhone}
                onChange={(e) => setCustomerPhone(e.target.value)}
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-primary focus:border-primary"
              />
            </div>

            {/* Customer Email (Read-only) */}
            <div>
              <label htmlFor="customerEmail" className="block text-sm font-medium text-gray-700 mb-2">
                <User className="w-4 h-4 inline mr-2" />Email
              </label>
              <input
                type="text"
                id="customerEmail"
                value={customerEmail}
                onChange={(e) => setCustomerEmail(e.target.value)}
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-primary focus:border-primary"
              />
            </div>
            

          </div>

          {/* Costs Information Display */}
          <div className="mb-6">
            <h3 className="text-lg font-medium text-gray-800 mb-4">Cost Breakdown</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {desiredCosts.map((key) => (
                <div key={key}>
                  <label htmlFor={key} className="block text-sm font-medium text-gray-700 mb-2">
                    <DollarSign className="w-4 h-4 inline mr-2" />
                    {key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                  </label>
                  <input
                    type="number"
                    id={key}
                    value={costs[key] || ''}
                    onChange={(e) => handleCostChange(key, e.target.value)}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-primary focus:border-primary"
                  />
                </div>
              ))}
            </div>
          </div>

          {/* Hotel Rows */}
          <div className="mb-6">
            <h3 className="text-lg font-medium text-gray-800 mb-4">Hotel Details</h3>
            <div className="space-y-4">
              {hotelRows.map((row, index) => (
                <div key={index} className="grid grid-cols-1 md:grid-cols-2 gap-4 p-4 border border-gray-200 rounded-lg">
                  <div>
                    <label htmlFor={`hotelName-${index}`} className="block text-sm font-medium text-gray-700 mb-1">
                      Hotel Name
                    </label>
                    <input
                      type="text"
                      id={`hotelName-${index}`}
                      value={row.hotel_name}
                      onChange={(e) => handleHotelRowChange(index, 'hotel_name', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-primary focus:border-primary"
                    />
                  </div>
                  <div>
                    <label htmlFor={`hotelPrice-${index}`} className="block text-sm font-medium text-gray-700 mb-1">
                      Price
                    </label>
                    <input
                      type="number"
                      id={`hotelPrice-${index}`}
                      value={row.stay_price}
                      onChange={(e) => handleHotelRowChange(index, 'stay_price', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-primary focus:border-primary"
                    />
                  </div>
                </div>
              ))}
            </div>
          </div>

          {error && <p className="text-red-600 bg-red-50 p-3 rounded-lg text-sm mb-4">{error}</p>}
          
          <div className="flex justify-end gap-4 mt-8">
            <button
              type="button"
              onClick={() => navigate('/followups')}
              className="bg-gray-200 text-gray-800 px-6 py-2 rounded-lg hover:bg-gray-300"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isSubmitting}
              className="bg-primary text-white px-6 py-2 rounded-lg hover:bg-primary-dark"
            >
              {isSubmitting ? 'Submitting...' : 'Create Follow-up'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

// Select Quote View
const SelectQuote: React.FC = () => {
  const navigate = useNavigate();
  const [supabase, setSupabase] = useState<any>(null);
  const [isClientLoading, setIsClientLoading] = useState(true);
  const [savedQuotes, setSavedQuotes] = useState<QuoteListItem[]>([]);
  const [isLoadingQuotes, setIsLoadingQuotes] = useState(false);
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [itemsPerPage, setItemsPerPage] = useState<number>(10);
  const [collapsedCustomers, setCollapsedCustomers] = useState<Set<string>>(new Set());

  useEffect(() => {
    const initSupabase = async () => {
      try {
        setIsClientLoading(true);
        const client = await getQuoteClient();
        setSupabase(client);
      } catch (error) {
        console.error('Error initializing Quote Supabase client:', error);
      } finally {
        setIsClientLoading(false);
      }
    };

    initSupabase();
  }, []);

  const fetchSavedQuotes = async () => {
    if (!supabase) return;
    setIsLoadingQuotes(true);
    try {
      const { data, error } = await supabase
        .from('quotes')
        .select('id, package_name, customer_name, customer_phone, customer_email, destination, created_at, is_draft, trip_duration, family_type, validity_date, subtotal, total_cost, no_of_persons, extra_adults, children')
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching saved quotes:', error);
        setSavedQuotes([]);
        return;
      }
      setSavedQuotes(data || []);
    } catch (error) {
      console.error('Exception fetching saved quotes:', error);
      setSavedQuotes([]);
    } finally {
      setIsLoadingQuotes(false);
    }
  };

  useEffect(() => {
    if (supabase) {
      fetchSavedQuotes();
    }
  }, [supabase]);

  const handleSelectQuote = (quote: QuoteListItem) => {
    const queryParams = new URLSearchParams();
    queryParams.set('quoteId', quote.id);
    if (quote.customer_name) queryParams.set('customerName', quote.customer_name);
    if (quote.customer_phone) queryParams.set('customerPhone', quote.customer_phone);
    if (quote.customer_email) queryParams.set('customerEmail', quote.customer_email);
    
    navigate(`/followups/create?${queryParams.toString()}`);
  };

  const deleteQuote = async (quoteId: string) => {
    if (!supabase) return;
    setIsLoadingQuotes(true);
    try {
      const { error } = await supabase.from('quotes').delete().eq('id', quoteId);
      if (error) throw error;

      await fetchSavedQuotes();
      alert('Quote deleted successfully');
    } catch (error) {
      console.error('Error deleting quote:', error);
      alert('Failed to delete quote');
    } finally {
      setIsLoadingQuotes(false);
    }
  };

  const filteredQuotes = savedQuotes.filter((quote) => {
    const term = searchTerm.toLowerCase();
    return (
      (quote.destination || '').toLowerCase().includes(term) ||
      (quote.customer_name || '').toLowerCase().includes(term) ||
      (quote.customer_phone || '').toLowerCase().includes(term) ||
      (quote.customer_email || '').toLowerCase().includes(term)
    );
  });

  useEffect(() => {
    setCurrentPage(1);
    setCollapsedCustomers(new Set());
  }, [searchTerm, itemsPerPage]);

  const handlePageChange = (page: number) => {
    if (page < 1 || page > totalPages) return;
    setCurrentPage(page);
  };

  const handleItemsPerPageChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setItemsPerPage(Number(e.target.value));
  };

  const formatPax = (quote: any) => {
    const adults = (quote.no_of_persons || 0) + (quote.extra_adults || 0);
    const children = quote.children || 0;
    return `${adults} Adult${adults > 1 ? 's' : ''}${children ? `, ${children} Child${children > 1 ? 'ren' : ''}` : ''}`;
  };

  const formatDateDisplay = (dateString: string) => {
    if (!dateString) return '-';
    const date = new Date(dateString);
    const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    const day = date.getDate().toString().padStart(2, '0');
    const month = months[date.getMonth()];
    const year = date.getFullYear();
    return `${day}-${month}-${year}`;
  };

  const groupQuotesByCustomer = (quotes: QuoteListItem[]) => {
    const grouped: { [key: string]: QuoteListItem[] } = {};
    quotes.forEach(quote => {
      const key = quote.customer_phone || quote.customer_name || 'Unknown Customer';
      if (!grouped[key]) {
        grouped[key] = [];
      }
      grouped[key].push(quote);
    });
    return grouped;
  };

  const groupedQuotes = groupQuotesByCustomer(filteredQuotes);
  const customerKeys = Object.keys(groupedQuotes);
  const totalPages = Math.ceil(customerKeys.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = Math.min(startIndex + itemsPerPage, customerKeys.length);
  const paginatedCustomerKeys = customerKeys.slice(startIndex, endIndex);

  const toggleCustomerCollapse = (customerKey: string) => {
    const newCollapsed = new Set(collapsedCustomers);
    if (newCollapsed.has(customerKey)) {
      newCollapsed.delete(customerKey);
    } else {
      newCollapsed.add(customerKey);
    }
    setCollapsedCustomers(newCollapsed);
  };

  if (isClientLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center p-8 max-w-md">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#00B69B] mx-auto mb-4"></div>
          <h2 className="text-xl font-semibold text-gray-800 mb-2">Loading Quotes</h2>
          <p className="text-gray-600">Please wait while we connect to the database...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-7xl mx-auto">
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-xl font-bold text-gray-800">Select a Quote</h2>
            <button onClick={() => fetchSavedQuotes()} className="px-4 py-2 flex items-center gap-2 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors">
              <RotateCcw size={14} />
              Refresh
            </button>
          </div>
          <div className="mb-4">
            <div className="relative">
              <input
                type="text"
                placeholder="Search by customer name, phone, email, or destination..."
                value={searchTerm}
                onChange={e => setSearchTerm(e.target.value)}
                className="w-full px-4 py-3 pl-10 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#00B69B] focus:border-[#00B69B]"
              />
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Search size={18} className="text-gray-400" />
              </div>
              {searchTerm && (
                <button
                  onClick={() => setSearchTerm('')}
                  className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600"
                >
                  <X size={18} />
                </button>
              )}
            </div>
          </div>
          {isLoadingQuotes ? (
            <div className="flex justify-center py-16"><div className="animate-spin rounded-full h-16 w-16 border-t-2 border-b-2 border-[#00B69B]"></div></div>
          ) : (
            <>
              {filteredQuotes.length === 0 ? (
                <div className="text-center py-16 bg-gray-50 rounded-lg border border-gray-200">
                  <h3 className="text-lg font-medium text-gray-700 mb-2">No Saved Quotes Found</h3>
                </div>
              ) : (
                <div className="space-y-6">
                  {paginatedCustomerKeys.map((customerKey) => {
                    const customerQuotes = groupedQuotes[customerKey];
                    const firstQuote = customerQuotes[0];
                    const customerName = firstQuote.customer_name || 'Unknown Customer';
                    const customerPhone = firstQuote.customer_phone;
                    const customerEmail = firstQuote.customer_email;
                    const isCollapsed = collapsedCustomers.has(customerKey);

                    return (
                      <div key={customerKey} className="border border-gray-200 rounded-lg overflow-hidden">
                        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 px-6 py-4 border-b border-gray-200 cursor-pointer" onClick={() => toggleCustomerCollapse(customerKey)}>
                          <div className="flex justify-between items-center">
                            <div className="flex items-center space-x-4">
                              <button className="flex items-center justify-center w-8 h-8 bg-blue-100 rounded-full hover:bg-blue-200 transition-colors">
                                {isCollapsed ? <ChevronDown size={16} className="text-blue-600" /> : <ChevronUp size={16} className="text-blue-600" />}
                              </button>
                              <div className="flex items-center justify-center w-10 h-10 bg-blue-100 rounded-full">
                                <Users size={20} className="text-blue-600" />
                              </div>
                              <div>
                                <h3 className="text-lg font-semibold text-gray-900">{customerName}</h3>
                                <div className="flex items-center space-x-4 text-sm text-gray-600">
                                  {customerPhone && <span className="flex items-center">📱 {customerPhone}</span>}
                                  {customerEmail && <span className="flex items-center">📧 {customerEmail}</span>}
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                        {!isCollapsed && (
                          <div className="divide-y divide-gray-100">
                            {customerQuotes.map((quote) => (
                              <div key={quote.id} className="px-6 py-4 hover:bg-gray-50 transition-colors">
                                <div className="flex justify-between items-center">
                                  <div className="flex-1 grid grid-cols-1 md:grid-cols-5 gap-4">
                                    <div>
                                      <div className="font-medium text-gray-900">{quote.package_name || 'Untitled'}</div>
                                      <div className="text-sm text-gray-500">{quote.trip_duration || '-'}</div>
                                    </div>
                                    <div>
                                      <div className="text-sm font-medium text-gray-700">{quote.destination || 'N/A'}</div>
                                      <div className="text-xs text-gray-500">{quote.family_type || '-'}</div>
                                    </div>
                                    <div>
                                      <div className="text-sm text-gray-700">₹{quote.total_cost?.toLocaleString() || '0'}</div>
                                      <div className="text-xs text-gray-500">{formatPax(quote)}</div>
                                    </div>
                                    <div>
                                      <div className="text-sm text-gray-700">Valid until</div>
                                      <div className="text-sm font-medium text-blue-600">{formatDateDisplay(quote.validity_date || '')}</div>
                                    </div>
                                    <div>
                                      <span className={`px-2 py-1 text-xs font-semibold rounded-full ${quote.is_draft ? 'bg-yellow-100 text-yellow-800' : 'bg-green-100 text-green-800'}`}>
                                        {quote.is_draft ? 'Draft' : 'Final'}
                                      </span>
                                      <div className="text-xs text-gray-500 mt-1">{formatDateDisplay(quote.created_at)}</div>
                                    </div>
                                  </div>
                                  <div className="flex items-center space-x-2 ml-4">
                                    <button
                                      onClick={() => handleSelectQuote(quote)}
                                      className="px-3 py-1 bg-[#00B69B] text-white rounded hover:bg-[#008577] transition-colors text-sm"
                                    >
                                      Select
                                    </button>
                                    <button
                                      onClick={() => deleteQuote(quote.id)}
                                      className="p-1.5 bg-red-50 text-red-600 hover:bg-red-100 rounded-md transition-colors"
                                      title="Delete quote"
                                    >
                                      <Trash2 className="w-4 h-4" />
                                    </button>
                                  </div>
                                </div>
                              </div>
                            ))}
                          </div>
                        )}
                      </div>
                    );
                  })}
                </div>
              )}
              {filteredQuotes.length > 0 && totalPages > 1 && (
                <div className="flex justify-between items-center mt-6 pt-4 border-t border-gray-200">
                  <div className="text-sm text-gray-600">
                    Showing <span className="font-semibold">{startIndex + 1}</span> to <span className="font-semibold">{endIndex}</span> of <span className="font-semibold">{customerKeys.length}</span> customers
                  </div>
                  <div className="flex items-center gap-4">
                    <div className="flex items-center gap-2">
                      <label htmlFor="itemsPerPage" className="text-sm font-medium text-gray-700">Rows per page:</label>
                      <select
                        id="itemsPerPage"
                        value={itemsPerPage}
                        onChange={handleItemsPerPageChange}
                        className="px-2 py-1 border border-gray-300 rounded-md focus:ring-primary focus:border-primary"
                      >
                        <option value={5}>5</option>
                        <option value={10}>10</option>
                        <option value={20}>20</option>
                        <option value={50}>50</option>
                      </select>
                    </div>
                    <div className="flex items-center gap-2">
                      <button
                        onClick={() => handlePageChange(currentPage - 1)}
                        disabled={currentPage === 1}
                        className="px-3 py-1 border border-gray-300 rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-100"
                      >
                        Previous
                      </button>
                      <span className="text-sm text-gray-700">
                        Page {currentPage} of {totalPages}
                      </span>
                      <button
                        onClick={() => handlePageChange(currentPage + 1)}
                        disabled={currentPage === totalPages}
                        className="px-3 py-1 border border-gray-300 rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-100"
                      >
                        Next
                      </button>
                    </div>
                  </div>
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default Followups;
