import { supabase } from './supabaseClient';
import { Lead } from '../components/LeadCard';
import { isValidLeadStatus } from '../constants/leadStatus';

// Define the shape of a lead
export interface LeadData {
  customer_name: string;
  email: string;
  phone?: string;
  destination?: string;
  departure_city?: string;
  travel_date?: string;
  adults?: number;
  children?: number;
  infants?: number;
  nights?: number;
  budget_range?: string;
  lead_source?: string;
  priority?: string;
  notes?: string;
  special_requests?: string;
  assigned_to?: string;
  status: string; // <-- Added for status validation and DB insert
  package_type?: string; // <-- Added to fix build error
}

/**
 * Fetch leads assigned to a specific user
 * @param userId User ID to filter leads by
 * @returns Promise containing the leads or an error
 */
export const fetchLeadsByUser = async (_userId: string): Promise<{ success: boolean; data: Lead[]; error: any }> => {
  try {
    console.log('[fetchLeadsByUser] BEFORE getSession');

    // Get current session directly and log result
    let session;
    try {
      const { data: sessionData, error: sessionError } = await supabase.auth.getSession();
      if (sessionError) {
        console.error('[fetchLeadsByUser] Error getting session:', sessionError);
        console.log('[fetchLeadsByUser] Continuing without session validation');
      } else {
        session = sessionData.session;
      }
    } catch (sessionError) {
      console.error('[fetchLeadsByUser] Exception getting session:', sessionError);
      console.log('[fetchLeadsByUser] Continuing without session validation');
    }
    console.log('[fetchLeadsByUser] AFTER getSession:', session ? 'Active' : 'Inactive/Null', session);
    console.log('[fetchLeadsByUser] About to run SELECT...');

    // Run select directly and log result
    let data: Lead[] | null = null;
    let error: any = null;
    try {
      const result = await supabase
        .from('leads')
        .select('*')
        .order('created_at', { ascending: false });
      data = result.data;
      error = result.error;
    } catch (err) {
      console.error('[fetchLeadsByUser] SELECT exception:', err);
      throw err;
    }
    console.log('[fetchLeadsByUser] Raw data:', data);
    console.log('[fetchLeadsByUser] Error:', error);
    console.log('[fetchLeadsByUser] Fetched leads count:', data?.length);
    if (error) {
      if (error.message && error.message.toLowerCase().includes('row level security')) {
        console.error('[fetchLeadsByUser] RLS policy may be blocking access:', error.message);
      } else if (error.code === '42501' || error.code === 'PGRST301') {
        console.error('[fetchLeadsByUser] RLS or permission denied error:', error.code, error.message);
      }
      throw error;
    }
    console.log('[fetchLeadsByUser] Returning leads array:', data || []);
    return { success: true, data: data || [], error: null };
  } catch (error: any) {
    console.error('[fetchLeadsByUser] Exception thrown:', error);
    return { success: false, data: [], error };
  }
};

/**
 * Update a lead's status and log the activity
 * @param leadId ID of the lead to update
 * @param newStatus New status to set
 * @returns Promise with success/failure indicator and error message if applicable
 */
export const updateLeadStatus = async (
  leadId: string,
  newStatus: string
): Promise<{ success: boolean; error?: string }> => {
  try {
    // Validate status before update
    if (!isValidLeadStatus(newStatus)) {
      console.error('Invalid status for lead update:', newStatus);
      return { success: false, error: 'Invalid status value for lead.' };
    }
    // 1. Update the lead status
    const { error: updateError } = await supabase
      .from('leads')
      .update({
        status: newStatus,
        updated_at: new Date().toISOString()
      })
      .eq('id', leadId);

    if (updateError) {
      throw updateError;
    }

    // 2. Get the current user ID
    const { data: userData, error: userError } = await supabase.auth.getUser();

    if (userError) {
      console.error('Error getting current user:', userError);
      // Continue without logging if user can't be determined
    }

    const userId = userData?.user?.id || 'system';

    // 3. Log the activity in lead_activities table
    const { error: logError } = await supabase
      .from('lead_activities')
      .insert({
        lead_id: leadId,
        activity_type: 'status_change',
        description: `Status changed to ${newStatus}`,
        performed_by: userId,
        created_at: new Date().toISOString()
      });

    if (logError) {
      console.error('Error logging lead activity:', logError);
      // We don't throw here as the primary operation succeeded
    }

    return { success: true };
  } catch (error: any) {
    console.error('Error updating lead status:', error);
    return {
      success: false,
      error: error.message || 'Failed to update lead status'
    };
  }
};

/**
 * Update specific lead details
 * @param leadId ID of the lead to update
 * @param updateData Partial lead data to update
 * @returns Promise with success/failure indicator and error message if applicable
 */
export const updateLeadDetails = async (
  leadId: string,
  updateData: Partial<LeadData>
): Promise<{ success: boolean; error?: string }> => {
  try {
    console.log('[updateLeadDetails] Updating lead:', leadId, 'with data:', updateData);
    const { error } = await supabase
      .from('leads')
      .update(updateData)
      .eq('id', leadId);

    if (error) {
      throw error;
    }

    return { success: true };
  } catch (error: any) {
    console.error('Error updating lead details:', error);
    return {
      success: false,
      error: error.message || 'Failed to update lead details',
    };
  }
};

/**
 * Update a lead's data
 * @param leadId ID of the lead to update
 * @param updateData Partial lead data to update
 * @returns Promise with success/failure indicator and error message if applicable
 */
export const updateLead = async (
  leadId: string,
  updateData: Partial<LeadData>
): Promise<{ success: boolean; error?: string; data?: Lead }> => {
  try {
    console.log('[updateLead] Updating lead:', leadId, 'with data:', updateData);

    // Validate status if it's being updated
    if (updateData.status && !isValidLeadStatus(updateData.status)) {
      console.error('Invalid status for lead update:', updateData.status);
      return { success: false, error: 'Invalid status value for lead.' };
    }

    // Prepare the update data
    const dataToUpdate = {
      ...updateData,
      updated_at: new Date().toISOString()
    };

    // 1. Update the lead
    const { data, error: updateError } = await supabase
      .from('leads')
      .update(dataToUpdate)
      .eq('id', leadId)
      .select()
      .single();

    if (updateError) {
      throw updateError;
    }

    // 2. Get the current user ID for activity logging
    const { data: userData, error: userError } = await supabase.auth.getUser();
    const userId = userData?.user?.id || 'system';

    if (userError) {
      console.error('Error getting current user:', userError);
    }

    // 3. Log the activity in lead_activities table
    const updatedFields = Object.keys(updateData).filter(key => key !== 'updated_at');
    if (updatedFields.length > 0) {
      const description = `Updated fields: ${updatedFields.join(', ')}`;
      
      const { error: logError } = await supabase
        .from('lead_activities')
        .insert({
          lead_id: leadId,
          activity_type: 'data_update',
          description,
          performed_by: userId,
          created_at: new Date().toISOString()
        });

      if (logError) {
        console.error('Error logging lead activity:', logError);
        // We don't throw here as the primary operation succeeded
      }
    }

    console.log('[updateLead] Successfully updated lead:', data);
    return { success: true, data: data as Lead };
  } catch (error: any) {
    console.error('Error updating lead:', error);
    return {
      success: false,
      error: error.message || 'Failed to update lead'
    };
  }
};

/**
 * Create a new lead
 * @param lead Lead data to create
 * @param userId User ID to assign the lead to
 * @returns Promise with success/failure indicator and error message if applicable
 */
export const createLead = async (
  lead: LeadData,
  userId: string
): Promise<{ success: boolean; error?: any; data?: any }> => {
  console.log('≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈');
  console.log('createLead SERVICE FUNCTION CALLED');
  console.log('Lead object received:', lead);

  try {
    // Use the provided userId
    console.log('Using provided user ID:', userId);

    // Add a check here before proceeding
    if (!userId) {
      console.error("Aborting insert because userId is missing.");
      return { success: false, error: new Error('User ID is required for lead creation.') };
    }

    console.log('Preparing data to insert with userId:', userId);
    // Validate status before insert
    if (!isValidLeadStatus(lead.status)) {
      console.error('Invalid status for lead insert:', lead.status);
      return { success: false, error: new Error('Invalid status value for lead.') };
    }
    const dataToInsert = {
      ...lead,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      status: lead.status,
      created_by: userId,
      assigned_to: userId
    };
    console.log('Data prepared for insertion:', dataToInsert);

    // Perform Supabase insert directly and log result
    try {
      const { data: insertData, error: insertError } = await supabase
        .from('leads')
        .insert([dataToInsert])
        .select();
      console.log('>>> Supabase insert await completed. Error object:', insertError);
      console.log('Supabase insert result:', { data: insertData, error: insertError });
      if (insertError) {
        console.error('Supabase insert error object:', insertError);
        console.error('Supabase error details:', insertError.details);
        console.error('Supabase error hint:', insertError.hint);
        return { success: false, error: insertError };
      }
      return { success: true, data: insertData };
    } catch (err: any) {
      console.error('Supabase insert exception:', err);
      return { success: false, error: err };
    }
  } catch (error) {
    console.error('OUTER ERROR IN createLead service:', error);
    if (error instanceof Error) {
      console.error('Error name:', error.name);
      console.error('Error message:', error.message);
      console.error('Error stack:', error.stack);
    } else {
      console.error('Error as string:', String(error));
    }
    return { success: false, error };
  } finally {
    console.log('≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈≈');
  }
};

/**
 * Create a new quote request from public form (no authentication required)
 * @param lead Lead data to create
 * @returns Promise with success/failure indicator and error message if applicable
 */
export const createPublicLead = async (
  lead: LeadData
): Promise<{ success: boolean; error?: any; data?: any }> => {
  console.log('createPublicLead SERVICE FUNCTION CALLED');
  console.log('Quote request data received:', lead);

  try {
    // Validate status before insert
    if (!isValidLeadStatus(lead.status)) {
      console.error('Invalid status for lead insert:', lead.status);
      return { success: false, error: new Error('Invalid status value for lead.') };
    }

    // Sign in anonymously for public form submissions
    let currentUser = null;
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        // Create anonymous session for public submissions
        const { data: anonData, error: anonError } = await supabase.auth.signInAnonymously();
        if (anonError) {
          console.warn('Anonymous sign-in failed:', anonError);
          // Fallback: try without authentication
        } else {
          currentUser = anonData.user;
        }
      } else {
        currentUser = user;
      }
    } catch (authError) {
      console.warn('Auth error during lead creation:', authError);
    }
    
    const dataToInsert = {
      customer_name: lead.customer_name,
      email: lead.email,
      phone: lead.phone,
      destination: lead.destination,
      departure_city: lead.departure_city || '',
      travel_date: lead.travel_date,
      adults: lead.adults || 1,
      children: lead.children || 0,
      infants: lead.infants || 0,
      nights: lead.nights || 1,
      budget_range: lead.budget_range || '',
      special_requests: lead.special_requests || '',
      lead_source: 'website', // Valid value from leads_lead_source_check constraint
      status: lead.status,
      priority: lead.priority || 'medium',
      notes: lead.notes || '',
      assigned_to: null,
      partner_id: null,
      created_by: currentUser?.id || '00000000-0000-0000-0000-000000000000', // Required field
      package_type: null
    };
    console.log('Data prepared for public insertion:', dataToInsert);

    // Use the leads table with proper field mapping
    const { data: insertData, error: insertError } = await supabase
      .from('leads')
      .insert(dataToInsert)
      .select();

    console.log('Supabase public insert result:', { data: insertData, error: insertError });
    
    if (insertError) {
      console.error('Supabase public insert error:', insertError);
      return { success: false, error: insertError };
    }

    return { success: true, data: insertData };
  } catch (error) {
    console.error('Error in createPublicLead:', error);
    return { success: false, error };
  }
};

/**
 * Fetch a single lead by ID
 * @param leadId ID of the lead to fetch
 * @returns Promise containing the lead data or an error
 */
export const fetchLeadById = async (leadId: string): Promise<{ success: boolean; data: Lead | null; error: any }> => {
  try {
    console.log('[fetchLeadById] Fetching lead with ID:', leadId);

    const { data, error } = await supabase
      .from('leads')
      .select('*')
      .eq('id', leadId)
      .single();

    if (error) {
      console.error('[fetchLeadById] Error fetching lead:', error);
      return { success: false, data: null, error };
    }

    console.log('[fetchLeadById] Successfully fetched lead:', data);
    return { success: true, data: data as Lead, error: null };
  } catch (error: any) {
    console.error('[fetchLeadById] Exception thrown:', error);
    return { success: false, data: null, error };
  }
};

/**
 * Delete a lead
 * @param leadId ID of the lead to delete
 */
export const deleteLead = async (leadId: string): Promise<void> => {
  try {
    const { error } = await supabase
      .from('leads')
      .delete()
      .eq('id', leadId);

    if (error) {
      throw error;
    }
  } catch (error) {
    console.error('Error deleting lead:', error);
    throw error;
  }
};

// Manual GET test: Direct REST call bypassing Supabase client
export async function testManualFetchLeads() {
  const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
  const anonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;
  const url = `${supabaseUrl}/rest/v1/leads`;

  // Retrieve current session for access token
  let accessToken: string | undefined;
  try {
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    if (sessionError) {
      console.error('[testManualFetchLeads] session error:', sessionError);
    } else {
      accessToken = session?.access_token;
    }
  } catch (e) {
    console.error('[testManualFetchLeads] exception getting session:', e);
  }

  const headers: Record<string, string> = {
    apikey: anonKey,
    Authorization: `Bearer ${accessToken}`,
    'Content-Type': 'application/json',
  };
  console.log('[testManualFetchLeads] Using Authorization header:', accessToken ? 'Bearer token present' : 'NO TOKEN!');

  try {
    const response = await fetch(url, {
      method: 'GET',
      headers,
    });
    const body = await response.json();
    console.log('[testManualFetchLeads] status:', response.status, 'body:', body);
    return body;
  } catch (err) {
    console.error('[testManualFetchLeads] fetch exception:', err);
    return null;
  }
}

// New function for manual fetch-based insert testing
export async function testManualFetchLeadInsert(
  leadData: LeadData,
  userId: string
): Promise<{ success: boolean; status: number; data?: any; error?: any }> {
  console.log('[testManualFetchLeadInsert] called with:', { leadData, userId });
  const supabaseUrl = import.meta.env.VITE_SUPABASE_URL ?? 'https://tlfwcnikdlwoliqzavxj.supabase.co';
  const anonKey = import.meta.env.VITE_SUPABASE_ANON_KEY ?? '';
  const url = `${supabaseUrl}/rest/v1/leads`;

  let accessToken: string | undefined;
  try {
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    if (sessionError) console.error('[testManualFetchLeadInsert] session error:', sessionError);
    accessToken = session?.access_token;
  } catch (e: any) {
    console.error('[testManualFetchLeadInsert] exception getting session:', e);
  }

  const headers: Record<string, string> = {
    apikey: anonKey,
    Authorization: `Bearer ${accessToken}`,
    'Content-Type': 'application/json',
    Prefer: 'return=representation'
  };
  const bodyArray = [{ ...leadData, created_by: userId, assigned_to: userId }];
  console.log('[testManualFetchLeadInsert] fetch details:', { url, headers, body: bodyArray });

  try {
    const response = await fetch(url, {
      method: 'POST',
      headers,
      body: JSON.stringify(bodyArray)
    });
    const status = response.status;
    let responseBody: any;
    try {
      responseBody = await response.json();
    } catch {
      responseBody = await response.text();
    }
    console.log('[testManualFetchLeadInsert] response:', { status, body: responseBody });
    return { success: response.ok, status, data: responseBody };
  } catch (fetchErr: any) {
    console.error('[testManualFetchLeadInsert] fetch exception:', fetchErr);
    return { success: false, status: 0, error: fetchErr };
  }
}
