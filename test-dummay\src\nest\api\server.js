/**
 * Family EMI API Server
 * Backend API for family.tripxplo.com
 */

const express = require('express');
const cors = require('cors');
const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(cors({
  origin: process.env.CORS_ORIGIN || 'http://localhost:8080',
  credentials: true
}));
app.use(express.json());
app.use(express.static('public'));

// Database connections
const crmDB = createClient(
  process.env.CRM_DB_URL || 'https://tlfwcnikdlwoliqzavxj.supabase.co',
  process.env.CRM_ANON_KEY
);

const quoteDB = createClient(
  process.env.QUOTE_DB_URL || 'https://lkqbrlrmrsnbtkoryazq.supabase.co',
  process.env.QUOTE_ANON_KEY
);

// Utility Functions
const detectFamilyType = async (adults, children, infants) => {
  try {
    const { data: familyTypes, error } = await crmDB
      .from('family_type')
      .select('*');
    
    if (error) throw error;
    
    // Find exact match first
    let match = familyTypes.find(ft => 
      ft.no_of_adults === adults && 
      ft.no_of_children === children && 
      ft.no_of_infants === infants
    );
    
    // If no exact match, find closest match
    if (!match) {
      match = familyTypes.find(ft => 
        ft.no_of_adults === adults && 
        ft.no_of_children >= children && 
        ft.no_of_infants >= infants
      );
    }
    
    // Default to Stellar Duo if no match
    if (!match) {
      match = familyTypes.find(ft => ft.family_id === 'SD') || familyTypes[0];
    }
    
    return match;
  } catch (error) {
    console.error('Error detecting family type:', error);
    // Return default family type
    return {
      family_id: 'SD',
      family_type: 'Stellar Duo',
      no_of_adults: 2,
      no_of_children: 0,
      no_of_infants: 0,
      composition: '2 Adults'
    };
  }
};

const formatPackageForFrontend = (packageData) => {
  return {
    id: packageData.id,
    title: packageData.package_title || `${packageData.destination} Package`,
    destination: packageData.destination,
    duration_days: packageData.package_duration_days || 5,
    total_price: packageData.total_price,
    family_type: packageData.family_type_name,
    emi_options: packageData.family_type_emi_plans?.map(emi => ({
      id: emi.id,
      months: emi.emi_months,
      monthly_amount: emi.monthly_amount,
      total_amount: emi.total_amount,
      processing_fee: emi.processing_fee,
      label: emi.marketing_label || `${emi.emi_months} Months`,
      is_featured: emi.is_featured
    })) || [],
    inclusions: ['Flights', 'Hotels', 'Meals', 'Sightseeing'],
    images: [`/img/rectangle-14.png`], // Default image
    offer_badge: packageData.total_price > 40000 ? '15% OFF' : 'Best Value'
  };
};

// API Routes

// Health check
app.get('/api/health', (req, res) => {
  res.json({ 
    success: true, 
    message: 'Family EMI API is running',
    timestamp: new Date().toISOString()
  });
});

// Get all family types
app.get('/api/family-types', async (req, res) => {
  try {
    const { data, error } = await crmDB
      .from('family_type')
      .select('*')
      .order('family_type');
    
    if (error) throw error;
    
    const formattedData = data.map(ft => ({
      ...ft,
      composition: `${ft.no_of_adults} Adult${ft.no_of_adults > 1 ? 's' : ''}${
        ft.no_of_children > 0 ? ` + ${ft.no_of_children} Child${ft.no_of_children > 1 ? 'ren' : ''}` : ''
      }${
        ft.no_of_infants > 0 ? ` + ${ft.no_of_infants} Infant${ft.no_of_infants > 1 ? 's' : ''}` : ''
      }`
    }));
    
    res.json({ success: true, data: formattedData });
  } catch (error) {
    console.error('Error fetching family types:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

// Get available destinations
app.get('/api/destinations', async (req, res) => {
  try {
    const { data, error } = await quoteDB
      .from('family_type_prices')
      .select('destination_category')
      .eq('is_public_visible', true)
      .not('destination_category', 'is', null);
    
    if (error) throw error;
    
    // Get unique destinations with counts
    const destinationMap = new Map();
    data.forEach(item => {
      if (item.destination) {
        const dest = item.destination;
        if (!destinationMap.has(dest)) {
          destinationMap.set(dest, {
            destination: dest,
            category: item.destination_category || 'General',
            packages_available: 0
          });
        }
        destinationMap.get(dest).packages_available++;
      }
    });
    
    const destinations = Array.from(destinationMap.values())
      .sort((a, b) => a.destination.localeCompare(b.destination));
    
    res.json({ success: true, data: destinations });
  } catch (error) {
    console.error('Error fetching destinations:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

// Search packages
app.post('/api/search-packages', async (req, res) => {
  try {
    const { destination, travel_date, adults, children, infants } = req.body;
    
    // Validate input
    if (!destination || !adults) {
      return res.status(400).json({ 
        success: false, 
        error: 'Destination and number of adults are required' 
      });
    }
    
    // Detect family type
    const familyType = await detectFamilyType(adults, children || 0, infants || 0);
    
    // Search packages
    const { data: packages, error } = await quoteDB
      .from('family_type_prices')
      .select(`
        *,
        family_type_emi_plans(*)
      `)
      .ilike('destination', `%${destination}%`)
      .eq('is_public_visible', true)
      .limit(10);
    
    if (error) throw error;
    
    // Format packages for frontend
    const formattedPackages = packages.map(formatPackageForFrontend);
    
    res.json({ 
      success: true, 
      matched_family_type: {
        ...familyType,
        composition: `${familyType.no_of_adults} Adult${familyType.no_of_adults > 1 ? 's' : ''}${
          familyType.no_of_children > 0 ? ` + ${familyType.no_of_children} Child${familyType.no_of_children > 1 ? 'ren' : ''}` : ''
        }${
          familyType.no_of_infants > 0 ? ` + ${familyType.no_of_infants} Infant${familyType.no_of_infants > 1 ? 's' : ''}` : ''
        }`
      },
      packages: formattedPackages,
      search_params: { destination, travel_date, adults, children, infants }
    });
  } catch (error) {
    console.error('Error searching packages:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

// Get package details
app.get('/api/packages/:id', async (req, res) => {
  try {
    const { id } = req.params;
    
    const { data: packageData, error } = await quoteDB
      .from('family_type_prices')
      .select(`
        *,
        family_type_emi_plans(*)
      `)
      .eq('id', id)
      .single();
    
    if (error) throw error;
    
    if (!packageData) {
      return res.status(404).json({ success: false, error: 'Package not found' });
    }
    
    const formattedPackage = {
      ...formatPackageForFrontend(packageData),
      description: `Experience the beauty of ${packageData.destination} with our carefully crafted family package.`,
      itinerary: [
        {
          day: 1,
          title: `Arrival in ${packageData.destination}`,
          description: 'Airport pickup, hotel check-in, welcome dinner'
        },
        {
          day: 2,
          title: 'Sightseeing Tour',
          description: 'Visit famous attractions and local markets'
        },
        {
          day: 3,
          title: 'Adventure Activities',
          description: 'Enjoy adventure sports and outdoor activities'
        }
      ],
      inclusions: [
        'Round-trip flights',
        '4-star hotel accommodation',
        'Daily breakfast and dinner',
        'All sightseeing and transfers',
        'Professional guide'
      ],
      exclusions: [
        'Personal expenses',
        'Travel insurance',
        'Lunch (except on specified days)',
        'Tips and gratuities'
      ]
    };
    
    res.json({ success: true, package: formattedPackage });
  } catch (error) {
    console.error('Error fetching package details:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

// Submit quote request
app.post('/api/quote-request', async (req, res) => {
  try {
    const {
      customer_email,
      customer_phone,
      customer_name,
      destination,
      travel_date,
      adults,
      children,
      infants,
      selected_package_id,
      selected_emi_plan_id,
      utm_source,
      session_id
    } = req.body;
    
    // Validate required fields
    if (!customer_email || !destination || !adults) {
      return res.status(400).json({ 
        success: false, 
        error: 'Email, destination, and number of adults are required' 
      });
    }
    
    // Insert into public_family_quotes table
    const { data, error } = await quoteDB
      .from('public_family_quotes')
      .insert({
        customer_email,
        customer_phone,
        customer_name,
        destination,
        travel_date,
        no_of_adults: adults,
        no_of_children: children || 0,
        no_of_infants: infants || 0,
        matched_price_id: selected_package_id,
        selected_emi_plan_id,
        utm_source: utm_source || 'direct',
        session_id: session_id || `sess_${Date.now()}`,
        lead_source: 'family_website'
      })
      .select()
      .single();
    
    if (error) throw error;
    
    res.json({ 
      success: true, 
      quote_id: data.id,
      message: 'Quote request submitted successfully. Our team will contact you soon!'
    });
  } catch (error) {
    console.error('Error submitting quote request:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error('Unhandled error:', err);
  res.status(500).json({ 
    success: false, 
    error: 'Internal server error' 
  });
});

// 404 handler
app.use((req, res) => {
  res.status(404).json({ 
    success: false, 
    error: 'Endpoint not found' 
  });
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 Family EMI API server running on port ${PORT}`);
  console.log(`📊 CRM Database: ${process.env.CRM_DB_URL?.substring(0, 30)}...`);
  console.log(`💰 Quote Database: ${process.env.QUOTE_DB_URL?.substring(0, 30)}...`);
});

module.exports = app;
