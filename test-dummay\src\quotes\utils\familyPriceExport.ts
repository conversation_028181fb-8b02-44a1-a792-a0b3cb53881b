interface FamilyTypeWithPrice {
  id: string;
  name: string;
  description: string;
  adults: number;
  infants: number;
  children: number;
  totalCount: number;
  cabType?: string;
  cab_type?: string;
  rooms?: number;
  rooms_need?: number;
  calculatedPrice?: number;
}

interface BaselineQuote {
  id: string;
  package_name: string;
  customer_name: string;
  destination: string;
  family_type: string;
  total_cost: number;
}

export const exportFamilyTypePricing = (
  familyTypes: FamilyTypeWithPrice[],
  baselineQuote: BaselineQuote | null
) => {
  // Filter only family types with calculated prices
  const familyTypesWithPrices = familyTypes.filter(ft => ft.calculatedPrice);

  if (familyTypesWithPrices.length === 0) {
    alert('No calculated prices to export. Please calculate prices first.');
    return;
  }

  // Create CSV headers
  const headers = [
    'Family ID',
    'Family Type',
    'Description',
    'Adults',
    'Children',
    'Infants',
    'Total Members',
    'Rooms Required',
    'Vehicle Type',
    'Calculated Price (INR)',
    'Price Per Person (INR)',
    'Baseline Quote',
    'Baseline Destination',
    'Baseline Customer'
  ];

  // Create CSV rows
  const rows = familyTypesWithPrices.map(familyType => [
    familyType.id,
    familyType.name,
    familyType.description,
    familyType.adults,
    familyType.children,
    familyType.infants,
    familyType.totalCount,
    familyType.rooms || familyType.rooms_need || 1,
    familyType.cabType || familyType.cab_type || 'Standard',
    familyType.calculatedPrice || 0,
    familyType.totalCount > 0 ? Math.round((familyType.calculatedPrice || 0) / familyType.totalCount) : 0,
    baselineQuote?.package_name || '',
    baselineQuote?.destination || '',
    baselineQuote?.customer_name || ''
  ]);

  // Combine headers and rows
  const csvContent = [headers, ...rows]
    .map(row => row.map(field => `"${field}"`).join(','))
    .join('\n');

  // Create and download the file
  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
  const link = document.createElement('a');
  
  if (link.download !== undefined) {
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `family-type-pricing-${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }
};

export const formatPriceForDisplay = (price: number): string => {
  return new Intl.NumberFormat('en-IN', {
    style: 'currency',
    currency: 'INR',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(price);
};

export const calculatePricingInsights = (familyTypes: FamilyTypeWithPrice[]) => {
  const familyTypesWithPrices = familyTypes.filter(ft => ft.calculatedPrice);
  
  if (familyTypesWithPrices.length === 0) {
    return null;
  }

  const prices = familyTypesWithPrices.map(ft => ft.calculatedPrice || 0);
  const minPrice = Math.min(...prices);
  const maxPrice = Math.max(...prices);
  const avgPrice = prices.reduce((sum, price) => sum + price, 0) / prices.length;

  const minPriceFamily = familyTypesWithPrices.find(ft => ft.calculatedPrice === minPrice);
  const maxPriceFamily = familyTypesWithPrices.find(ft => ft.calculatedPrice === maxPrice);

  return {
    totalFamilyTypes: familyTypesWithPrices.length,
    minPrice,
    maxPrice,
    avgPrice: Math.round(avgPrice),
    minPriceFamily: minPriceFamily?.name || '',
    maxPriceFamily: maxPriceFamily?.name || '',
    priceRange: maxPrice - minPrice
  };
}; 