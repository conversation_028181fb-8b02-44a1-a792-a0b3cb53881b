/**
 * Test Data Insertion Utility
 * This file contains functions to insert sample data for testing the EMI management system
 */

import { createClient } from '@supabase/supabase-js';

// Database configuration
const QUOTE_DB_URL = 'https://lkqbrlrmrsnbtkoryazq.supabase.co';
const QUOTE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImxrcWJybHJtcnNuYnRrb3J5YXpxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDU0MDA2ODYsImV4cCI6MjA2MDk3NjY4Nn0.0E4Z87L9j32k3jKa15n4LpmFsVx8YCJuwovi-mSw4SE';

// Initialize Supabase client
const quoteDB = createClient(QUOTE_DB_URL, QUOTE_ANON_KEY);

/**
 * Insert sample visited customers (public_family_quotes)
 */
export const insertSampleVisitedCustomers = async (): Promise<void> => {
  try {
    console.log('🌱 Inserting sample visited customers...');

    const sampleCustomers = [
      {
        customer_email: '<EMAIL>',
        customer_phone: '+91 98765 43210',
        customer_name: '<PERSON><PERSON>',
        destination: 'Goa',
        travel_date: '2024-03-15',
        estimated_total_cost: 45000,
        selected_emi_months: 6,
        monthly_emi_amount: 8000,
        quote_status: 'generated'
      },
      {
        customer_email: '<EMAIL>',
        customer_phone: '+91 87654 32109',
        customer_name: 'Priya Sharma',
        destination: 'Kerala',
        travel_date: '2024-04-20',
        estimated_total_cost: 65000,
        selected_emi_months: 8,
        monthly_emi_amount: 9000,
        quote_status: 'contacted'
      },
      {
        customer_email: '<EMAIL>',
        customer_phone: '+91 76543 21098',
        customer_name: 'Amit Patel',
        destination: 'Rajasthan',
        travel_date: '2024-05-10',
        estimated_total_cost: 85000,
        selected_emi_months: 12,
        monthly_emi_amount: 8500,
        quote_status: 'generated'
      },
      {
        customer_email: '<EMAIL>',
        customer_phone: '+91 65432 10987',
        customer_name: 'Sunita Singh',
        destination: 'Himachal Pradesh',
        travel_date: '2024-06-05',
        estimated_total_cost: 55000,
        selected_emi_months: 6,
        monthly_emi_amount: 10000,
        quote_status: 'generated'
      }
    ];

    const { data, error } = await quoteDB
      .from('public_family_quotes')
      .insert(sampleCustomers)
      .select();

    if (error) {
      console.error('❌ Error inserting sample customers:', error);
      throw error;
    }

    console.log('✅ Successfully inserted sample customers:', data?.length || 0);

  } catch (error) {
    console.error('❌ Error in insertSampleVisitedCustomers:', error);
    throw error;
  }
};

/**
 * Insert sample EMI transactions (prepaid_emi_transactions)
 */
export const insertSampleEMITransactions = async (): Promise<void> => {
  try {
    console.log('🌱 Inserting sample EMI transactions...');

    // First, get some customer IDs from public_family_quotes
    let { data: customers, error: customerError } = await quoteDB
      .from('public_family_quotes')
      .select('id')
      .limit(4);

    if (customerError || !customers || customers.length === 0) {
      console.log('⚠️ No customers found, inserting customers first...');
      await insertSampleVisitedCustomers();
      
      // Try again to get customer IDs
      const { data: newCustomers, error: newCustomerError } = await quoteDB
        .from('public_family_quotes')
        .select('id')
        .limit(4);

      if (newCustomerError || !newCustomers) {
        throw new Error('Failed to get customer IDs for EMI transactions');
      }
      
      customers = newCustomers;
    }

    const sampleTransactions = [
      {
        booking_reference: 'TXP-001234',
        customer_id: customers[0]?.id,
        advance_payment_amount: 10000,
        advance_payment_status: 'completed',
        advance_payment_date: '2024-01-15',
        total_emi_amount: 40000,
        monthly_emi_amount: 8000,
        remaining_emi_months: 4,
        next_emi_due_date: '2024-02-15',
        total_paid_amount: 18000,
        pending_amount: 32000,
        payment_status: 'active',
        auto_debit_enabled: true,
        payment_method: 'UPI'
      },
      {
        booking_reference: 'TXP-001235',
        customer_id: customers[1]?.id,
        advance_payment_amount: 15000,
        advance_payment_status: 'pending',
        total_emi_amount: 60000,
        monthly_emi_amount: 10000,
        remaining_emi_months: 6,
        next_emi_due_date: '2024-02-10',
        total_paid_amount: 0,
        pending_amount: 75000,
        payment_status: 'active',
        auto_debit_enabled: false,
        payment_method: 'Bank Transfer'
      },
      {
        booking_reference: 'TXP-001236',
        customer_id: customers[2]?.id,
        advance_payment_amount: 20000,
        advance_payment_status: 'completed',
        advance_payment_date: '2024-01-20',
        total_emi_amount: 70000,
        monthly_emi_amount: 8500,
        remaining_emi_months: 8,
        next_emi_due_date: '2024-02-20',
        total_paid_amount: 28500,
        pending_amount: 61500,
        payment_status: 'active',
        auto_debit_enabled: true,
        payment_method: 'Credit Card'
      }
    ];

    const { data, error } = await quoteDB
      .from('prepaid_emi_transactions')
      .insert(sampleTransactions)
      .select();

    if (error) {
      console.error('❌ Error inserting sample EMI transactions:', error);
      throw error;
    }

    console.log('✅ Successfully inserted sample EMI transactions:', data?.length || 0);

  } catch (error) {
    console.error('❌ Error in insertSampleEMITransactions:', error);
    throw error;
  }
};

/**
 * Insert all sample data
 */
export const insertAllSampleData = async (): Promise<void> => {
  try {
    console.log('🌱 Inserting all sample data...');
    
    await insertSampleVisitedCustomers();
    await insertSampleEMITransactions();
    
    console.log('✅ All sample data inserted successfully!');
  } catch (error) {
    console.error('❌ Error inserting sample data:', error);
    throw error;
  }
};

/**
 * Clear all test data
 */
export const clearAllTestData = async (): Promise<void> => {
  try {
    console.log('🧹 Clearing all test data...');
    
    // Clear EMI transactions first (due to foreign key constraints)
    const { error: emiError } = await quoteDB
      .from('prepaid_emi_transactions')
      .delete()
      .like('booking_reference', 'TXP-%');

    if (emiError) {
      console.error('❌ Error clearing EMI transactions:', emiError);
    } else {
      console.log('✅ Cleared EMI transactions');
    }

    // Clear visited customers
    const { error: customerError } = await quoteDB
      .from('public_family_quotes')
      .delete()
      .in('customer_email', [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>'
      ]);

    if (customerError) {
      console.error('❌ Error clearing visited customers:', customerError);
    } else {
      console.log('✅ Cleared visited customers');
    }
    
    console.log('✅ All test data cleared successfully!');
  } catch (error) {
    console.error('❌ Error clearing test data:', error);
    throw error;
  }
};

// Make functions available globally for browser console testing
if (typeof window !== 'undefined') {
  (window as any).tripxploTestData = {
    insertSampleVisitedCustomers,
    insertSampleEMITransactions,
    insertAllSampleData,
    clearAllTestData
  };

  // Log the current configuration when the module loads
  console.log('🔧 TripXplo Database Configuration:', {
    hostname: window.location.hostname,
    directDatabaseEnabled: localStorage.getItem('useDirectDatabase') !== 'false',
    message: 'Direct database access is now enabled by default for localhost'
  });
}
