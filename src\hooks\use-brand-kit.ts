"use client"

import { useState, useEffect } from "react"
import { Brand<PERSON><PERSON>, <PERSON><PERSON>ogo, BrandFont } from "../components/template/types"

const BRAND_KIT_STORAGE_KEY = "travel-generator-brand-kit"

export function useBrandKit() {
  const [brandKit, setBrandKit] = useState<BrandKit | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  // Load brand kit from localStorage on mount
  useEffect(() => {
    try {
      const stored = localStorage.getItem(BRAND_KIT_STORAGE_KEY)
      if (stored) {
        const parsed = JSON.parse(stored)
        // Convert date strings back to Date objects
        parsed.createdAt = new Date(parsed.createdAt)
        parsed.updatedAt = new Date(parsed.updatedAt)
        setBrandKit(parsed)
      }
    } catch (error) {
      console.error("Error loading brand kit from localStorage:", error)
    } finally {
      setIsLoading(false)
    }
  }, [])

  // Save brand kit to localStorage
  const saveBrandKit = (newBrandKit: BrandKit) => {
    try {
      setBrandKit(newBrandKit)
      localStorage.setItem(BRAND_KIT_STORAGE_KEY, JSON.stringify(newBrandKit))
    } catch (error) {
      console.error("Error saving brand kit to localStorage:", error)
    }
  }

  // Create a new brand kit
  const createBrandKit = (name: string = "My Brand Kit"): BrandKit => {
    const newBrandKit: BrandKit = {
      id: Date.now().toString(),
      name,
      colors: [
        { id: "1", name: "Primary Blue", hex: "#3B82F6", type: "primary" },
        { id: "2", name: "Secondary Gray", hex: "#6B7280", type: "secondary" },
        { id: "3", name: "Accent Orange", hex: "#F97316", type: "accent" },
      ],
      logos: [],
      fonts: [],
      createdAt: new Date(),
      updatedAt: new Date(),
    }

    saveBrandKit(newBrandKit)
    return newBrandKit
  }

  // Get primary brand color
  const getPrimaryColor = (): string => {
    const primaryColor = brandKit?.colors.find(color => color.type === "primary")
    return primaryColor?.hex || "#3B82F6"
  }

  // Get secondary brand color
  const getSecondaryColor = (): string => {
    const secondaryColor = brandKit?.colors.find(color => color.type === "secondary")
    return secondaryColor?.hex || "#6B7280"
  }

  // Get accent brand color
  const getAccentColor = (): string => {
    const accentColor = brandKit?.colors.find(color => color.type === "accent")
    return accentColor?.hex || "#F97316"
  }

  // Get primary logo
  const getPrimaryLogo = (): BrandLogo | null => {
    return brandKit?.logos.find(logo => logo.isPrimary) || null
  }

  // Get headline font
  const getHeadlineFont = (): BrandFont | null => {
    return brandKit?.fonts.find(font => font.type === "headline") || null
  }

  // Get body font
  const getBodyFont = (): BrandFont | null => {
    return brandKit?.fonts.find(font => font.type === "body") || null
  }

  // Clear brand kit
  const clearBrandKit = () => {
    try {
      localStorage.removeItem(BRAND_KIT_STORAGE_KEY)
      setBrandKit(null)
    } catch (error) {
      console.error("Error clearing brand kit:", error)
    }
  }

  // Check if brand kit exists and has content
  const hasBrandKit = (): boolean => {
    return brandKit !== null && (
      brandKit.colors.length > 0 || 
      brandKit.logos.length > 0 || 
      brandKit.fonts.length > 0
    )
  }

  // Get all brand colors as palette
  const getBrandColorPalette = (): { name: string; colors: string[] } => {
    if (!brandKit || brandKit.colors.length === 0) {
      return { name: "Default", colors: ["#3B82F6", "#6B7280", "#F97316"] }
    }

    return {
      name: brandKit.name,
      colors: brandKit.colors.map(color => color.hex)
    }
  }

  // Inject brand fonts into document
  const injectBrandFonts = () => {
    if (!brandKit?.fonts) return

    brandKit.fonts.forEach(font => {
      if (font.url) {
        injectFont(font)
      }
    })
  }

  const injectFont = (font: BrandFont) => {
    if (!font.url) return

    // Check if font is already injected
    const existingStyle = document.querySelector(`style[data-font-id="${font.id}"]`)
    if (existingStyle) return

    // Create @font-face rule using data URL
    const fontFaceRule = `
      @font-face {
        font-family: '${font.fontFamily}';
        src: url('${font.url}') format('${getFontFormat(font.name)}');
        font-display: swap;
      }
    `

    // Add to stylesheet with identifier
    const styleElement = document.createElement('style')
    styleElement.setAttribute('data-font-id', font.id)
    styleElement.textContent = fontFaceRule
    document.head.appendChild(styleElement)
  }

  const getFontFormat = (fontName: string): string => {
    // Extract extension from font name or default to truetype
    const extension = fontName.split('.').pop()?.toLowerCase() || 'ttf'
    switch (extension) {
      case 'woff2':
        return 'woff2'
      case 'woff':
        return 'woff'
      case 'ttf':
        return 'truetype'
      case 'otf':
        return 'opentype'
      default:
        return 'truetype'
    }
  }

  return {
    brandKit,
    isLoading,
    saveBrandKit,
    createBrandKit,
    clearBrandKit,
    hasBrandKit,
    getPrimaryColor,
    getSecondaryColor,
    getAccentColor,
    getPrimaryLogo,
    getHeadlineFont,
    getBodyFont,
    getBrandColorPalette,
    injectBrandFonts,
  }
}
