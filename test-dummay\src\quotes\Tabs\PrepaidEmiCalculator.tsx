import React, { useState, useCallback } from 'react';
import { Calculator, IndianRupee, Calendar, Percent, Target, Gift, CreditCard } from 'lucide-react';

const PREPAID_EMI_OPTIONS = {
  loanAmounts: [
    100000, 200000, 300000, 500000, 750000, 
    1000000, 1500000, 2000000, 2500000, 3000000
  ],
  interestRates: [
    8.5, 9.0, 9.5, 10.0, 10.5, 11.0, 11.5, 12.0, 12.5, 13.0
  ],
  tenures: [
    { months: 3, label: '3 Months' },
    { months: 6, label: '6 Months' },
    { months: 12, label: '1 Year' },
    { months: 18, label: '18 Months' },
    { months: 24, label: '2 Years' },
    { months: 30, label: '30 Months' },
    { months: 36, label: '3 Years' }
  ],
  advancePaymentOptions: [10, 15, 20, 25, 30, 35, 40],
  discountOptions: [0, 1, 2, 2.5, 3, 5, 7.5, 10]
};

interface PrepaidEMICalculation {
  advancePayment: number;
  emiPrincipal: number;
  monthlyEMI: number;
  totalAmount: number;
  totalInterest: number;
  processingFee: number;
  discountAmount: number;
  finalAmount: number;
  savings: number;
  breakup: {
    month: number;
    emi: number;
    principal: number;
    interest: number;
    balance: number;
  }[];
}

const PrepaidEmiCalculator: React.FC = () => {
  const [loanAmount, setLoanAmount] = useState<number>(PREPAID_EMI_OPTIONS.loanAmounts[3]); // Default to 500k
  const [interestRate, setInterestRate] = useState<number>(PREPAID_EMI_OPTIONS.interestRates[3]); // Default to 10%
  const [tenure, setTenure] = useState<number>(PREPAID_EMI_OPTIONS.tenures[2].months); // Default to 12 months
  const [advancePaymentPercent, setAdvancePaymentPercent] = useState<number>(20); // Default 20%
  const [discountPercent, setDiscountPercent] = useState<number>(2.5); // Default 2.5%
  const [processingFeePercent, setProcessingFeePercent] = useState<number>(1.0); // Default 1%
  
  const [isCustomAmount, setIsCustomAmount] = useState(false);
  const [isCustomRate, setIsCustomRate] = useState(false);
  const [isCustomTenure, setIsCustomTenure] = useState(false);
  const [isCustomAdvance, setIsCustomAdvance] = useState(false);
  const [isCustomDiscount, setIsCustomDiscount] = useState(false);
  
  const [calculation, setCalculation] = useState<PrepaidEMICalculation | null>(null);

  const calculatePrepaidEMI = useCallback(() => {
    const principal = loanAmount;
    const annualRate = interestRate;
    const months = tenure;
    const advancePercent = advancePaymentPercent;
    const discount = discountPercent;
    const processingFee = processingFeePercent;

    // Calculate advance payment
    const advancePayment = principal * advancePercent / 100;
    
    // Calculate discount amount
    const discountAmount = principal * discount / 100;
    
    // Calculate processing fee
    const processingFeeAmount = principal * processingFee / 100;
    
    // Remaining principal after advance payment and discount
    const emiPrincipal = principal - advancePayment - discountAmount;
    
    // Calculate monthly interest rate
    const monthlyRate = annualRate / 12 / 100;
    
    // Calculate monthly EMI using standard formula
    let monthlyEMI: number;
    if (monthlyRate === 0) {
      monthlyEMI = emiPrincipal / months;
    } else {
      monthlyEMI = emiPrincipal * monthlyRate * Math.pow(1 + monthlyRate, months) / (Math.pow(1 + monthlyRate, months) - 1);
    }
    
    // Calculate totals
    const totalEMIAmount = monthlyEMI * months;
    const totalAmount = advancePayment + totalEMIAmount + processingFeeAmount;
    const totalInterest = totalEMIAmount - emiPrincipal;
    const finalAmount = totalAmount - discountAmount;
    
    // Calculate savings compared to no advance payment
    const regularEMI = principal * monthlyRate * Math.pow(1 + monthlyRate, months) / (Math.pow(1 + monthlyRate, months) - 1);
    const regularTotal = regularEMI * months + (principal * processingFee / 100);
    const savings = regularTotal - finalAmount;

    // Calculate monthly breakup
    let balance = emiPrincipal;
    const breakup = [];
    
    for (let month = 1; month <= months; month++) {
      const interest = balance * monthlyRate;
      const principalPart = monthlyEMI - interest;
      balance = balance - principalPart;
      
      breakup.push({
        month,
        emi: monthlyEMI,
        principal: principalPart,
        interest,
        balance: balance < 0 ? 0 : balance
      });
    }

    setCalculation({
      advancePayment,
      emiPrincipal,
      monthlyEMI,
      totalAmount,
      totalInterest,
      processingFee: processingFeeAmount,
      discountAmount,
      finalAmount,
      savings,
      breakup
    });
  }, [loanAmount, interestRate, tenure, advancePaymentPercent, discountPercent, processingFeePercent]);

  const formatCurrency = (amount: number) => {
    return `₹${amount.toLocaleString('en-IN', { maximumFractionDigits: 0 })}`;
  };

  return (
    <div className="max-w-7xl mx-auto p-6">
      <div className="mb-8">
        <h1 className="text-2xl font-bold text-gray-800 mb-2">Prepaid EMI Calculator</h1>
        <p className="text-gray-600">Calculate EMI with advance payment, discounts, and processing fees for prepaid plans</p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Input Section */}
        <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
          <h2 className="text-xl font-semibold mb-6 flex items-center gap-2">
            <Calculator className="w-5 h-5 text-blue-600" />
            Prepaid EMI Calculator
          </h2>
          
          <div className="space-y-6">
            {/* Package Amount */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Package Amount (₹)
              </label>
              <div className="flex gap-4">
                <select
                  value={isCustomAmount ? "custom" : loanAmount}
                  onChange={(e) => {
                    if (e.target.value === "custom") {
                      setIsCustomAmount(true);
                    } else {
                      setIsCustomAmount(false);
                      setLoanAmount(Number(e.target.value));
                    }
                  }}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  {PREPAID_EMI_OPTIONS.loanAmounts.map((amount) => (
                    <option key={amount} value={amount}>
                      ₹{amount.toLocaleString()}
                    </option>
                  ))}
                  <option value="custom">Custom Amount</option>
                </select>
                {isCustomAmount && (
                  <div className="relative flex-1">
                    <IndianRupee className="absolute left-3 top-1/2 -translate-y-1/2 w-4 h-4 text-gray-400" />
                    <input
                      type="number"
                      value={loanAmount}
                      onChange={(e) => setLoanAmount(Number(e.target.value))}
                      className="pl-10 w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="Enter amount"
                    />
                  </div>
                )}
              </div>
            </div>

            {/* Advance Payment Percentage */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Advance Payment (%)
              </label>
              <div className="flex gap-4">
                <select
                  value={isCustomAdvance ? "custom" : advancePaymentPercent}
                  onChange={(e) => {
                    if (e.target.value === "custom") {
                      setIsCustomAdvance(true);
                    } else {
                      setIsCustomAdvance(false);
                      setAdvancePaymentPercent(Number(e.target.value));
                    }
                  }}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  {PREPAID_EMI_OPTIONS.advancePaymentOptions.map((percent) => (
                    <option key={percent} value={percent}>
                      {percent}%
                    </option>
                  ))}
                  <option value="custom">Custom %</option>
                </select>
                {isCustomAdvance && (
                  <div className="relative flex-1">
                    <Target className="absolute left-3 top-1/2 -translate-y-1/2 w-4 h-4 text-gray-400" />
                    <input
                      type="number"
                      value={advancePaymentPercent}
                      onChange={(e) => setAdvancePaymentPercent(Number(e.target.value))}
                      className="pl-10 w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="Enter %"
                      min="0"
                      max="50"
                    />
                  </div>
                )}
              </div>
            </div>

            {/* Discount Percentage */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Prepaid Discount (%)
              </label>
              <div className="flex gap-4">
                <select
                  value={isCustomDiscount ? "custom" : discountPercent}
                  onChange={(e) => {
                    if (e.target.value === "custom") {
                      setIsCustomDiscount(true);
                    } else {
                      setIsCustomDiscount(false);
                      setDiscountPercent(Number(e.target.value));
                    }
                  }}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  {PREPAID_EMI_OPTIONS.discountOptions.map((discount) => (
                    <option key={discount} value={discount}>
                      {discount}%
                    </option>
                  ))}
                  <option value="custom">Custom %</option>
                </select>
                {isCustomDiscount && (
                  <div className="relative flex-1">
                    <Gift className="absolute left-3 top-1/2 -translate-y-1/2 w-4 h-4 text-gray-400" />
                    <input
                      type="number"
                      value={discountPercent}
                      onChange={(e) => setDiscountPercent(Number(e.target.value))}
                      className="pl-10 w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="Enter %"
                      min="0"
                      max="15"
                      step="0.5"
                    />
                  </div>
                )}
              </div>
            </div>

            {/* Interest Rate */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Interest Rate (% per annum)
              </label>
              <div className="flex gap-4">
                <select
                  value={isCustomRate ? "custom" : interestRate}
                  onChange={(e) => {
                    if (e.target.value === "custom") {
                      setIsCustomRate(true);
                    } else {
                      setIsCustomRate(false);
                      setInterestRate(Number(e.target.value));
                    }
                  }}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  {PREPAID_EMI_OPTIONS.interestRates.map((rate) => (
                    <option key={rate} value={rate}>
                      {rate}%
                    </option>
                  ))}
                  <option value="custom">Custom Rate</option>
                </select>
                {isCustomRate && (
                  <div className="relative flex-1">
                    <Percent className="absolute left-3 top-1/2 -translate-y-1/2 w-4 h-4 text-gray-400" />
                    <input
                      type="number"
                      value={interestRate}
                      onChange={(e) => setInterestRate(Number(e.target.value))}
                      className="pl-10 w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="Enter rate"
                      step="0.1"
                    />
                  </div>
                )}
              </div>
            </div>

            {/* EMI Tenure */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                EMI Tenure
              </label>
              <div className="flex gap-4">
                <select
                  value={isCustomTenure ? "custom" : tenure}
                  onChange={(e) => {
                    if (e.target.value === "custom") {
                      setIsCustomTenure(true);
                    } else {
                      setIsCustomTenure(false);
                      setTenure(Number(e.target.value));
                    }
                  }}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  {PREPAID_EMI_OPTIONS.tenures.map((option) => (
                    <option key={option.months} value={option.months}>
                      {option.label}
                    </option>
                  ))}
                  <option value="custom">Custom Tenure</option>
                </select>
                {isCustomTenure && (
                  <div className="relative flex-1">
                    <Calendar className="absolute left-3 top-1/2 -translate-y-1/2 w-4 h-4 text-gray-400" />
                    <input
                      type="number"
                      value={tenure}
                      onChange={(e) => setTenure(Number(e.target.value))}
                      className="pl-10 w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="Enter months"
                    />
                  </div>
                )}
              </div>
            </div>

            {/* Processing Fee */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Processing Fee (%)
              </label>
              <div className="relative">
                <CreditCard className="absolute left-3 top-1/2 -translate-y-1/2 w-4 h-4 text-gray-400" />
                <input
                  type="number"
                  value={processingFeePercent}
                  onChange={(e) => setProcessingFeePercent(Number(e.target.value))}
                  className="pl-10 w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Enter processing fee %"
                  step="0.1"
                  min="0"
                  max="5"
                />
              </div>
            </div>

            <button
              onClick={calculatePrepaidEMI}
              className="w-full px-4 py-3 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors font-medium"
            >
              Calculate Prepaid EMI
            </button>
          </div>
        </div>

        {/* Results Section */}
        {calculation && (
          <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <h2 className="text-xl font-semibold mb-6">Prepaid EMI Details</h2>
            
            <div className="space-y-6">
              {/* Main Results */}
              <div className="grid grid-cols-2 gap-4">
                <div className="bg-green-50 p-4 rounded-lg">
                  <p className="text-sm text-gray-600">Advance Payment</p>
                  <p className="text-xl font-bold text-green-600">
                    {formatCurrency(calculation.advancePayment)}
                  </p>
                </div>

                <div className="bg-blue-50 p-4 rounded-lg">
                  <p className="text-sm text-gray-600">Monthly EMI</p>
                  <p className="text-xl font-bold text-blue-600">
                    {formatCurrency(calculation.monthlyEMI)}
                  </p>
                </div>

                <div className="bg-purple-50 p-4 rounded-lg">
                  <p className="text-sm text-gray-600">Total Savings</p>
                  <p className="text-xl font-bold text-purple-600">
                    {formatCurrency(calculation.savings)}
                  </p>
                </div>

                <div className="bg-orange-50 p-4 rounded-lg">
                  <p className="text-sm text-gray-600">Final Amount</p>
                  <p className="text-xl font-bold text-orange-600">
                    {formatCurrency(calculation.finalAmount)}
                  </p>
                </div>
              </div>

              {/* Detailed Breakdown */}
              <div className="border-t pt-6">
                <h3 className="text-lg font-semibold mb-4">Cost Breakdown</h3>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-gray-600">EMI Principal:</span>
                    <span className="font-semibold">{formatCurrency(calculation.emiPrincipal)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Total Interest:</span>
                    <span className="font-semibold">{formatCurrency(calculation.totalInterest)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Processing Fee:</span>
                    <span className="font-semibold">{formatCurrency(calculation.processingFee)}</span>
                  </div>
                  <div className="flex justify-between text-green-600">
                    <span>Discount Applied:</span>
                    <span className="font-semibold">-{formatCurrency(calculation.discountAmount)}</span>
                  </div>
                  <div className="border-t pt-2 flex justify-between font-bold text-lg">
                    <span>Total Amount:</span>
                    <span>{formatCurrency(calculation.totalAmount)}</span>
                  </div>
                </div>
              </div>

              {/* EMI Schedule */}
              <div className="border-t pt-6">
                <h3 className="text-lg font-semibold mb-4">EMI Schedule</h3>
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Month</th>
                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">EMI</th>
                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Principal</th>
                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Interest</th>
                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Balance</th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {calculation.breakup.slice(0, 6).map((row) => (
                        <tr key={row.month}>
                          <td className="px-4 py-2 text-sm text-gray-900">{row.month}</td>
                          <td className="px-4 py-2 text-sm text-gray-900">{formatCurrency(row.emi)}</td>
                          <td className="px-4 py-2 text-sm text-gray-900">{formatCurrency(row.principal)}</td>
                          <td className="px-4 py-2 text-sm text-gray-900">{formatCurrency(row.interest)}</td>
                          <td className="px-4 py-2 text-sm text-gray-900">{formatCurrency(row.balance)}</td>
                        </tr>
                      ))}
                      {calculation.breakup.length > 6 && (
                        <tr>
                          <td colSpan={5} className="px-4 py-2 text-sm text-gray-500 text-center">
                            ... and {calculation.breakup.length - 6} more months
                          </td>
                        </tr>
                      )}
                    </tbody>
                  </table>
                </div>
              </div>

              {/* Benefits Section */}
              <div className="border-t pt-6">
                <h3 className="text-lg font-semibold mb-4">Prepaid Plan Benefits</h3>
                <div className="grid grid-cols-1 gap-3">
                  <div className="flex items-center text-green-600">
                    <Gift className="w-4 h-4 mr-2" />
                    <span className="text-sm">Save {formatCurrency(calculation.savings)} with prepaid plan</span>
                  </div>
                  <div className="flex items-center text-blue-600">
                    <Target className="w-4 h-4 mr-2" />
                    <span className="text-sm">Lower monthly EMI due to advance payment</span>
                  </div>
                  <div className="flex items-center text-purple-600">
                    <CreditCard className="w-4 h-4 mr-2" />
                    <span className="text-sm">Exclusive discount for prepaid customers</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default PrepaidEmiCalculator; 