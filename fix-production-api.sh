#!/bin/bash

# Fix Production API Server for family.tripxplo.com
# This script deploys the Node.js API server to fix the 405 error

echo "🔧 Fixing Production API Server for family.tripxplo.com"
echo "=================================================="

# Server configuration
SERVER_IP="*************"
SERVER_USER="root"
DOMAIN="family.tripxplo.com"

echo "📋 Server Details:"
echo "   IP: $SERVER_IP"
echo "   Domain: $DOMAIN"
echo ""

# Step 1: Upload API files
echo "📤 Step 1: Uploading API server files..."
scp -r family-tripxplo-production/api/* ${SERVER_USER}@${SERVER_IP}:/var/www/family/api/ && {
    echo "✅ API files uploaded successfully!"
} || {
    echo "❌ Failed to upload API files!"
    exit 1
}

# Step 2: Configure server environment
echo ""
echo "🔧 Step 2: Configuring server environment..."
ssh ${SERVER_USER}@${SERVER_IP} '
    echo "📦 Installing Node.js and PM2..."
    
    # Install Node.js if not present
    if ! command -v node &> /dev/null; then
        curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
        apt-get install -y nodejs
        echo "✅ Node.js installed"
    else
        echo "ℹ️ Node.js already installed: $(node --version)"
    fi
    
    # Install PM2 if not present
    if ! command -v pm2 &> /dev/null; then
        npm install -g pm2
        echo "✅ PM2 installed"
    else
        echo "ℹ️ PM2 already installed: $(pm2 --version)"
    fi
    
    # Navigate to API directory
    cd /var/www/family/api
    
    # Install dependencies
    echo "📦 Installing API dependencies..."
    npm install
    
    # Create environment file
    echo "⚙️ Creating environment configuration..."
    cat > .env << EOF
NODE_ENV=production
PORT=3000
CORS_ORIGIN=https://family.tripxplo.com

# Database Configuration
CRM_DB_URL=https://tlfwcnikdlwoliqzavxj.supabase.co
CRM_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRsZndjbmlrZGx3b2xpcXphdnhqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzQ1MjU5NzQsImV4cCI6MjA1MDEwMTk3NH0.Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8

QUOTE_DB_URL=https://lkqbrlrmrsnbtkoryazq.supabase.co
QUOTE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImxrcWJybHJtcnNuYnRrb3J5YXpxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzQ1MjU5NzQsImV4cCI6MjA1MDEwMTk3NH0.Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8
EOF
    
    # Stop existing API server if running
    pm2 delete family-api 2>/dev/null || true
    
    # Start API server
    echo "🚀 Starting API server..."
    pm2 start server.js --name family-api
    pm2 save
    pm2 startup
    
    echo "✅ API server started successfully!"
'

# Step 3: Configure Nginx
echo ""
echo "🌐 Step 3: Configuring Nginx for API proxy..."
ssh ${SERVER_USER}@${SERVER_IP} '
    # Create Nginx configuration
    cat > /etc/nginx/sites-available/family.tripxplo.com << "NGINX_EOF"
server {
    listen 80;
    server_name family.tripxplo.com www.family.tripxplo.com;
    
    root /var/www/family;
    index index.html index.htm;
    
    # Enable gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/css application/javascript application/json image/svg+xml text/plain text/xml;
    
    # Cache static assets
    location ~* \.(css|js|png|jpg|jpeg|gif|svg|ico)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # Proxy API requests to Node.js server
    location /api/ {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # CORS headers for API
        add_header Access-Control-Allow-Origin "https://family.tripxplo.com" always;
        add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS" always;
        add_header Access-Control-Allow-Headers "Content-Type, Authorization" always;
        add_header Access-Control-Allow-Credentials "true" always;
        
        # Handle preflight requests
        if ($request_method = OPTIONS) {
            add_header Access-Control-Allow-Origin "https://family.tripxplo.com";
            add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS";
            add_header Access-Control-Allow-Headers "Content-Type, Authorization";
            add_header Access-Control-Allow-Credentials "true";
            add_header Content-Length 0;
            add_header Content-Type text/plain;
            return 200;
        }
    }
    
    # Main application
    location / {
        try_files $uri $uri/ /index.html;
    }
}
NGINX_EOF
    
    # Enable the site
    ln -sf /etc/nginx/sites-available/family.tripxplo.com /etc/nginx/sites-enabled/
    
    # Test and reload Nginx
    nginx -t && {
        systemctl reload nginx
        echo "✅ Nginx configured and reloaded successfully!"
    } || {
        echo "❌ Nginx configuration error!"
        exit 1
    }
'

# Step 4: Final verification
echo ""
echo "🔍 Step 4: Verifying deployment..."
ssh ${SERVER_USER}@${SERVER_IP} '
    echo "📊 API Server Status:"
    pm2 status family-api
    
    echo ""
    echo "🌐 Nginx Status:"
    systemctl status nginx --no-pager -l | head -3
    
    echo ""
    echo "🧪 Testing API endpoint:"
    curl -s -o /dev/null -w "%{http_code}" http://localhost:3000/api/submit-contact-details -X POST -H "Content-Type: application/json" -d "{\"test\":\"data\"}" || echo "API test failed"
'

echo ""
echo "🎉 Deployment Complete!"
echo "=================================================="
echo "✅ API server is now running on the production server"
echo "✅ Nginx is configured to proxy /api/ requests"
echo "✅ CORS is configured for family.tripxplo.com"
echo ""
echo "🧪 Test the fix:"
echo "   1. Visit https://family.tripxplo.com"
echo "   2. Try submitting the contact form"
echo "   3. Check browser console for any remaining errors"
echo ""
echo "📋 If issues persist:"
echo "   - Check PM2 logs: ssh ${SERVER_USER}@${SERVER_IP} 'pm2 logs family-api'"
echo "   - Check Nginx logs: ssh ${SERVER_USER}@${SERVER_IP} 'tail -f /var/log/nginx/error.log'"
