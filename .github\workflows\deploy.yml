name: Deploy to Production

on:
  push:
    branches: [ master, main ]
  workflow_dispatch:

jobs:
  deploy:
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
        
      - name: Install dependencies
        run: npm ci
      
      - name: Create environment file
        run: |
          echo "VITE_SUPABASE_URL_CRM=${{ secrets.VITE_SUPABASE_URL_CRM }}" >> .env
          echo "VITE_SUPABASE_ANON_KEY_CRM=${{ secrets.VITE_SUPABASE_ANON_KEY_CRM }}" >> .env
          echo "VITE_SUPABASE_URL_QUOTE=${{ secrets.VITE_SUPABASE_URL_QUOTE }}" >> .env
          echo "VITE_SUPABASE_ANON_KEY_QUOTE=${{ secrets.VITE_SUPABASE_ANON_KEY_QUOTE }}" >> .env
          echo "VITE_SUPABASE_URL=${{ secrets.VITE_SUPABASE_URL }}" >> .env
          echo "VITE_SUPABASE_ANON_KEY=${{ secrets.VITE_SUPABASE_ANON_KEY }}" >> .env
          echo "EMAIL_USER=${{ secrets.EMAIL_USER }}" >> .env
          echo "EMAIL_PASS=${{ secrets.EMAIL_PASS }}" >> .env
          echo "📋 Environment variables configured for build"
        
      - name: Build application
        run: |
          npm run build
          echo "🏗️ Build completed successfully"
        
      - name: Validate secrets
        run: |
          echo "🔍 Validating required secrets..."
          if [ -z "${{ secrets.SERVER_IP }}" ] || [ -z "${{ secrets.SSH_PRIVATE_KEY }}" ]; then 
            echo "❌ SERVER_IP or SSH_PRIVATE_KEY secret not set"
            exit 1
          fi
          echo "✅ Required secrets validated"
          
      - name: Setup SSH key
        run: |
          mkdir -p ~/.ssh
          echo "${{ secrets.SSH_PRIVATE_KEY }}" > ~/.ssh/id_rsa
          chmod 600 ~/.ssh/id_rsa
          
          # Debug SSH key
          echo "🔍 SSH key details:"
          ls -la ~/.ssh/id_rsa
          ssh-keygen -l -f ~/.ssh/id_rsa || echo "❌ SSH key validation failed"
          
          # Add server to known hosts
          ssh-keyscan -H ${{ secrets.SERVER_IP }} >> ~/.ssh/known_hosts
          
          # Test SSH key format
          echo "🔑 SSH key configured and validated"
      
      - name: Verify build output
        run: |
          echo "🔍 Verifying build output..."
          if [ ! -d "dist" ] || [ ! -f "dist/index.html" ]; then
            echo "❌ Build failed - dist directory or index.html not found"
            ls -la
            exit 1
          fi
          echo "✅ Build output verified"
          echo "📊 Build contains $(find dist -type f | wc -l) files"

      - name: Deploy to server
        run: |
          echo "🚀 Starting deployment to /var/www/crm/..."
          
          # Set and trim server variable
          SERVER=$(echo "${{ secrets.SERVER_IP }}" | xargs)
          
          # Test SSH connection with verbose output
          echo "🔍 Testing SSH connection to ${SERVER}..."
          ssh -i ~/.ssh/id_rsa -o StrictHostKeyChecking=no -v root@${SERVER} "echo '✅ SSH connection successful'" || {
            echo "❌ SSH connection failed. Debugging..."
            echo "🔍 SSH key permissions:"
            ls -la ~/.ssh/id_rsa
            echo "🔍 SSH key content (first line):"
            head -1 ~/.ssh/id_rsa
            echo "🔍 Server IP: ${SERVER}"
            exit 1
          }
          
          # Create deployment tarball
          echo "📦 Creating deployment package..."
          tar -czf dist.tar.gz -C dist .
          
          # Upload tarball and deployment script
          echo "📤 Uploading files to server..."
          scp -i ~/.ssh/id_rsa -o StrictHostKeyChecking=no dist.tar.gz root@${SERVER}:/tmp/
          scp -i ~/.ssh/id_rsa -o StrictHostKeyChecking=no scripts/deploy-server.sh root@${SERVER}:/tmp/
          
          # Execute deployment on server
          echo "🔄 Executing deployment on server..."
          ssh -i ~/.ssh/id_rsa -o StrictHostKeyChecking=no root@${SERVER} "chmod +x /tmp/deploy-server.sh && /tmp/deploy-server.sh"
          
          echo "✅ Deployment completed"

      - name: Verify deployment
        run: |
          echo "🧪 Verifying deployment..."
          sleep 15
          
          # Test HTTP connection
          HTTP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" http://crm.tripxplo.com || echo "000")
          echo "HTTP Status: $HTTP_STATUS"
          
          if [ "$HTTP_STATUS" = "200" ]; then
            echo "✅ HTTP test successful"
          else
            echo "⚠️ HTTP test returned: $HTTP_STATUS"
          fi
          
          # Test HTTPS connection
          HTTPS_STATUS=$(curl -s -o /dev/null -w "%{http_code}" https://crm.tripxplo.com || echo "000")
          echo "HTTPS Status: $HTTPS_STATUS"
          
          if [ "$HTTPS_STATUS" = "200" ]; then
            echo "✅ HTTPS test successful"
          else
            echo "⚠️ HTTPS test returned: $HTTPS_STATUS"
          fi
          
          echo "🎉 Deployment verification completed"
          
      - name: Cleanup
        if: always()
        run: |
          rm -f ~/.ssh/id_rsa
          echo "🧹 Cleanup completed"

      - name: Deployment Summary
        if: always()
        run: |
          echo ""
          echo "📋 DEPLOYMENT SUMMARY"
          echo "===================="
          echo "🌐 Live Site: https://crm.tripxplo.com"
          echo "📁 Deployment Target: /var/www/crm/"
          echo "🕐 Completed: $(date)"
          echo "✅ Auto-deployment finished!"