<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Database Connection Test - TripXplo Family EMI</title>
    <style>
        body {
            font-family: 'Inter', sans-serif;
            margin: 0;
            padding: 20px;
            background: #f8f9fa;
        }
        .test-container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }
        .test-header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e9ecef;
        }
        .test-buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }
        .test-btn {
            padding: 12px 24px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        .test-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }
        .test-results {
            margin-top: 20px;
            padding: 20px;
            background: #f8f9ff;
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }
        .test-results pre {
            background: #fff;
            padding: 15px;
            border-radius: 6px;
            overflow-x: auto;
            font-size: 0.9rem;
            border: 1px solid #e9ecef;
            max-height: 400px;
            overflow-y: auto;
        }
        .status-success { color: #28a745; }
        .status-error { color: #dc3545; }
        .status-warning { color: #ffc107; }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1>🔍 Database Connection Test</h1>
            <p>Test real database connections and data fetching</p>
        </div>

        <div class="test-buttons">
            <button class="test-btn" onclick="testFamilyTypes()">Test Family Types</button>
            <button class="test-btn" onclick="testDestinations()">Test Destinations</button>
            <button class="test-btn" onclick="testFamilyTypePrices()">Test Family Type Prices</button>
            <button class="test-btn" onclick="testQuoteMappings()">Test Quote Mappings</button>
            <button class="test-btn" onclick="testQuotes()">Test Quotes Table</button>
            <button class="test-btn" onclick="testPackageSearch()">Test Package Search</button>
        </div>

        <div id="testResults" class="test-results" style="display: none;">
            <h3>Test Results</h3>
            <pre id="resultOutput"></pre>
        </div>
    </div>

    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
    <script src="js/config.js"></script>
    <script src="js/databaseService.js"></script>
    <script>
        function showResults(title, data, status = 'success') {
            const resultsDiv = document.getElementById('testResults');
            const resultOutput = document.getElementById('resultOutput');
            
            resultsDiv.style.display = 'block';
            
            const timestamp = new Date().toLocaleTimeString();
            const statusClass = status === 'success' ? 'status-success' : status === 'error' ? 'status-error' : 'status-warning';
            
            resultOutput.innerHTML = `<span class="${statusClass}">[${timestamp}] ${title}</span>\n` + 
                                   JSON.stringify(data, null, 2);
        }

        async function testFamilyTypes() {
            try {
                showResults('Testing Family Types...', { status: 'loading' }, 'warning');
                const result = await databaseService.getFamilyTypes();
                showResults('Family Types Test', result, result.success ? 'success' : 'error');
            } catch (error) {
                showResults('Family Types Test Error', { error: error.message }, 'error');
            }
        }

        async function testDestinations() {
            try {
                showResults('Testing Destinations...', { status: 'loading' }, 'warning');
                const result = await databaseService.getDestinations();
                showResults('Destinations Test', result, result.success ? 'success' : 'error');
            } catch (error) {
                showResults('Destinations Test Error', { error: error.message }, 'error');
            }
        }

        async function testFamilyTypePrices() {
            try {
                showResults('Testing Family Type Prices...', { status: 'loading' }, 'warning');
                
                // Direct query to family_type_prices table
                const { data, error } = await databaseService.quoteDB
                    .from('family_type_prices')
                    .select('*')
                    .limit(5);
                
                const result = {
                    success: !error,
                    error: error?.message,
                    data: data,
                    count: data?.length || 0
                };
                
                showResults('Family Type Prices Test', result, result.success ? 'success' : 'error');
            } catch (error) {
                showResults('Family Type Prices Test Error', { error: error.message }, 'error');
            }
        }

        async function testQuoteMappings() {
            try {
                showResults('Testing Quote Mappings...', { status: 'loading' }, 'warning');
                
                // Direct query to quote_mappings table
                const { data, error } = await databaseService.quoteDB
                    .from('quote_mappings')
                    .select('*')
                    .limit(5);
                
                const result = {
                    success: !error,
                    error: error?.message,
                    data: data,
                    count: data?.length || 0
                };
                
                showResults('Quote Mappings Test', result, result.success ? 'success' : 'error');
            } catch (error) {
                showResults('Quote Mappings Test Error', { error: error.message }, 'error');
            }
        }

        async function testQuotes() {
            try {
                showResults('Testing Quotes Table...', { status: 'loading' }, 'warning');
                
                // Direct query to quotes table
                const { data, error } = await databaseService.quoteDB
                    .from('quotes')
                    .select('id, package_name, destination, total_cost, customer_name')
                    .limit(5);
                
                const result = {
                    success: !error,
                    error: error?.message,
                    data: data,
                    count: data?.length || 0
                };
                
                showResults('Quotes Table Test', result, result.success ? 'success' : 'error');
            } catch (error) {
                showResults('Quotes Table Test Error', { error: error.message }, 'error');
            }
        }

        async function testPackageSearch() {
            try {
                showResults('Testing Package Search...', { status: 'loading' }, 'warning');
                
                // Test the actual package search functionality
                const result = await databaseService.searchPackages('Goa', 2, 1, 0);
                
                showResults('Package Search Test', {
                    success: result.success,
                    error: result.error,
                    packages_found: result.packages?.length || 0,
                    matched_family_type: result.matched_family_type?.family_type,
                    sample_package: result.packages?.[0] || null
                }, result.success ? 'success' : 'error');
            } catch (error) {
                showResults('Package Search Test Error', { error: error.message }, 'error');
            }
        }

        // Auto-run basic tests on page load
        window.addEventListener('load', async () => {
            console.log('🔍 Starting automatic database tests...');
            
            // Wait a bit for everything to initialize
            setTimeout(async () => {
                await testFamilyTypes();
                
                setTimeout(async () => {
                    await testDestinations();
                }, 1000);
                
                setTimeout(async () => {
                    await testFamilyTypePrices();
                }, 2000);
            }, 1000);
        });
    </script>
</body>
</html>
