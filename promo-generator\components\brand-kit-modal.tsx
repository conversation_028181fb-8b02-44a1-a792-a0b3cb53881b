"use client"

import React, { useState, useRef, useEffect } from "react"
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, <PERSON>alog<PERSON>eader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Upload, Trash2, Star, Palette, Image, Type, Plus, X, Sparkles } from "lucide-react"
import { BrandKit, BrandColor, BrandLogo, BrandFont } from "@/components/template/types"

interface BrandKitModalProps {
  children: React.ReactNode
  brandKit: BrandKit | null
  onBrandKitUpdate: (brandKit: BrandKit) => void
}

// Color extraction utility functions
const extractColorsFromImage = (imageUrl: string, numColors: number = 5): Promise<string[]> => {
  return new Promise((resolve, reject) => {
    const img = new window.Image()
    img.crossOrigin = "anonymous"
    img.onload = () => {
      const canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')
      if (!ctx) {
        reject(new Error('Could not get canvas context'))
        return
      }

      canvas.width = img.width
      canvas.height = img.height
      ctx.drawImage(img, 0, 0)

      const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height)
      const data = imageData.data
      const colorMap = new Map<string, number>()

      // Sample pixels and count color frequencies
      for (let i = 0; i < data.length; i += 4) {
        const r = data[i]
        const g = data[i + 1]
        const b = data[i + 2]
        const a = data[i + 3]

        // Skip transparent pixels
        if (a < 128) continue

        // Convert to hex
        const hex = `#${((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1)}`
        
        // Group similar colors (reduce precision)
        const roundedHex = roundColor(hex)
        colorMap.set(roundedHex, (colorMap.get(roundedHex) || 0) + 1)
      }

      // Sort by frequency and get top colors
      const sortedColors = Array.from(colorMap.entries())
        .sort((a, b) => b[1] - a[1])
        .slice(0, numColors)
        .map(([color]) => color)

      resolve(sortedColors)
    }
    img.onerror = () => reject(new Error('Failed to load image'))
    img.src = imageUrl
  })
}

const roundColor = (hex: string): string => {
  // Round to nearest 16 to group similar colors
  const r = Math.round(parseInt(hex.slice(1, 3), 16) / 16) * 16
  const g = Math.round(parseInt(hex.slice(3, 5), 16) / 16) * 16
  const b = Math.round(parseInt(hex.slice(5, 7), 16) / 16) * 16
  return `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`
}

const generateColorName = (hex: string): string => {
  const r = parseInt(hex.slice(1, 3), 16)
  const g = parseInt(hex.slice(3, 5), 16)
  const b = parseInt(hex.slice(5, 7), 16)

  // Simple color naming based on RGB values
  if (r > 200 && g > 200 && b > 200) return "White"
  if (r < 50 && g < 50 && b < 50) return "Black"
  if (r > g && r > b) return "Red"
  if (g > r && g > b) return "Green"
  if (b > r && b > g) return "Blue"
  if (r > 200 && g > 200 && b < 100) return "Yellow"
  if (r > 200 && g < 100 && b > 200) return "Magenta"
  if (r < 100 && g > 200 && b > 200) return "Cyan"
  if (r > 150 && g > 100 && b < 100) return "Orange"
  if (r > 150 && g < 100 && b > 150) return "Purple"
  
  return "Custom"
}

export function BrandKitModal({ children, brandKit, onBrandKitUpdate }: BrandKitModalProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [activeTab, setActiveTab] = useState("colors")
  const [newColorHex, setNewColorHex] = useState("#3B82F6")
  const [newColorName, setNewColorName] = useState("")
  const [newColorType, setNewColorType] = useState<"primary" | "secondary" | "accent" | "custom">("custom")
  const [isExtractingColors, setIsExtractingColors] = useState(false)
  const [extractedColors, setExtractedColors] = useState<BrandColor[]>([])
  
  const logoFileRef = useRef<HTMLInputElement>(null)
  const fontFileRef = useRef<HTMLInputElement>(null)

  // Initialize default brand kit if none exists
  const getCurrentBrandKit = (): BrandKit => {
    if (brandKit) return brandKit
    
    return {
      id: "default",
      name: "My Brand Kit",
      colors: [
        { id: "1", name: "Primary Blue", hex: "#3B82F6", type: "primary" },
        { id: "2", name: "Secondary Gray", hex: "#6B7280", type: "secondary" },
        { id: "3", name: "Accent Orange", hex: "#F97316", type: "accent" },
      ],
      logos: [],
      fonts: [],
      createdAt: new Date(),
      updatedAt: new Date(),
    }
  }

  const [currentBrandKit, setCurrentBrandKit] = useState<BrandKit>(getCurrentBrandKit())

  useEffect(() => {
    if (brandKit) {
      setCurrentBrandKit(brandKit)
    }
  }, [brandKit])

  const handleAddColor = () => {
    if (!newColorName.trim()) return

    const newColor: BrandColor = {
      id: Date.now().toString(),
      name: newColorName,
      hex: newColorHex,
      type: newColorType,
    }

    const updatedBrandKit = {
      ...currentBrandKit,
      colors: [...currentBrandKit.colors, newColor],
      updatedAt: new Date(),
    }

    setCurrentBrandKit(updatedBrandKit)
    onBrandKitUpdate(updatedBrandKit)
    setNewColorName("")
    setNewColorHex("#3B82F6")
    setNewColorType("custom")
  }

  const handleRemoveColor = (colorId: string) => {
    const updatedBrandKit = {
      ...currentBrandKit,
      colors: currentBrandKit.colors.filter(color => color.id !== colorId),
      updatedAt: new Date(),
    }
    setCurrentBrandKit(updatedBrandKit)
    onBrandKitUpdate(updatedBrandKit)
  }

  const handleExtractColorsFromImage = async (imageUrl: string) => {
    setIsExtractingColors(true)
    try {
      const colors = await extractColorsFromImage(imageUrl, 8)
      const extractedColorsList: BrandColor[] = colors.map((hex, index) => ({
        id: `extracted-${Date.now()}-${index}`,
        name: generateColorName(hex),
        hex,
        type: "custom" as const,
      }))
      setExtractedColors(extractedColorsList)
    } catch (error) {
      console.error('Failed to extract colors:', error)
    } finally {
      setIsExtractingColors(false)
    }
  }

  const handleAddExtractedColor = (color: BrandColor) => {
    const updatedBrandKit = {
      ...currentBrandKit,
      colors: [...currentBrandKit.colors, color],
      updatedAt: new Date(),
    }
    setCurrentBrandKit(updatedBrandKit)
    onBrandKitUpdate(updatedBrandKit)
  }

  const handleLogoUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files
    if (!files || files.length === 0) return

    Array.from(files).forEach((file) => {
      if (file.type.startsWith('image/')) {
        // Convert file to data URL instead of blob URL for persistence
        const reader = new FileReader()
        reader.onload = (e) => {
          const dataUrl = e.target?.result as string
          const newLogo: BrandLogo = {
            id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
            name: file.name.replace(/\.[^/.]+$/, ""),
            url: dataUrl,
            file: null, // Don't store file object for localStorage
            type: "custom",
            isPrimary: currentBrandKit.logos.length === 0, // First logo is primary
          }

          const updatedBrandKit = {
            ...currentBrandKit,
            logos: [...currentBrandKit.logos, newLogo],
            updatedAt: new Date(),
          }

          setCurrentBrandKit(updatedBrandKit)
          onBrandKitUpdate(updatedBrandKit)

          // Auto-extract colors from the uploaded logo
          handleExtractColorsFromImage(dataUrl)
        }
        reader.readAsDataURL(file)
      }
    })

    // Reset input
    if (logoFileRef.current) {
      logoFileRef.current.value = ""
    }
  }

  const handleRemoveLogo = (logoId: string) => {
    const logoToRemove = currentBrandKit.logos.find(logo => logo.id === logoId)
    // No need to revoke URL since we're using data URLs now
    // if (logoToRemove?.url.startsWith('blob:')) {
    //   URL.revokeObjectURL(logoToRemove.url)
    // }

    const updatedBrandKit = {
      ...currentBrandKit,
      logos: currentBrandKit.logos.filter(logo => logo.id !== logoId),
      updatedAt: new Date(),
    }
    setCurrentBrandKit(updatedBrandKit)
    onBrandKitUpdate(updatedBrandKit)
  }

  const handleSetPrimaryLogo = (logoId: string) => {
    const updatedBrandKit = {
      ...currentBrandKit,
      logos: currentBrandKit.logos.map(logo => ({
        ...logo,
        isPrimary: logo.id === logoId,
      })),
      updatedAt: new Date(),
    }
    setCurrentBrandKit(updatedBrandKit)
    onBrandKitUpdate(updatedBrandKit)
  }

  // Font detection utility
  const detectFontsFromImage = async (imageUrl: string): Promise<string[]> => {
    // This is a simplified font detection - in a real implementation,
    // you might use OCR or more sophisticated font recognition
    return new Promise((resolve) => {
      // For now, return some common fonts that might be in logos
      const commonFonts = [
        "Helvetica", "Arial", "Times New Roman", "Georgia", 
        "Verdana", "Tahoma", "Trebuchet MS", "Arial Black"
      ]
      resolve(commonFonts.slice(0, 3))
    })
  }

  const handleFontUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files
    if (!files || files.length === 0) return

    Array.from(files).forEach((file) => {
      const validFontTypes = ['.otf', '.ttf', '.woff', '.woff2']
      const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase()
      
      if (validFontTypes.includes(fileExtension)) {
        const fontFamily = file.name.replace(/\.[^/.]+$/, "").replace(/[-_]/g, ' ')
        
        // Convert file to data URL for persistence
        const reader = new FileReader()
        reader.onload = (e) => {
          const dataUrl = e.target?.result as string
          const newFont: BrandFont = {
            id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
            name: fontFamily,
            file: null, // Don't store file object for localStorage
            url: dataUrl, // Store data URL instead
            fontFamily,
            type: "custom",
            isDefault: currentBrandKit.fonts.length === 0, // First font is default
          }

          const updatedBrandKit = {
            ...currentBrandKit,
            fonts: [...currentBrandKit.fonts, newFont],
            updatedAt: new Date(),
          }

          setCurrentBrandKit(updatedBrandKit)
          onBrandKitUpdate(updatedBrandKit)

          // Inject font into document using data URL
          injectFont(newFont)
        }
        reader.readAsDataURL(file)
      }
    })

    // Reset input
    if (fontFileRef.current) {
      fontFileRef.current.value = ""
    }
  }

  const injectFont = (font: BrandFont) => {
    if (!font.url) return

    // Check if font is already injected
    const existingStyle = document.querySelector(`style[data-font-id="${font.id}"]`)
    if (existingStyle) return

    // Create @font-face rule using data URL
    const fontFaceRule = `
      @font-face {
        font-family: '${font.fontFamily}';
        src: url('${font.url}') format('${getFontFormat(font.name)}');
        font-display: swap;
      }
    `

    // Add to stylesheet with identifier
    const styleElement = document.createElement('style')
    styleElement.setAttribute('data-font-id', font.id)
    styleElement.textContent = fontFaceRule
    document.head.appendChild(styleElement)
  }

  const getFontFormat = (fontName: string): string => {
    // Extract extension from font name or default to truetype
    const extension = fontName.split('.').pop()?.toLowerCase() || 'ttf'
    switch (extension) {
      case 'woff2':
        return 'woff2'
      case 'woff':
        return 'woff'
      case 'ttf':
        return 'truetype'
      case 'otf':
        return 'opentype'
      default:
        return 'truetype'
    }
  }

  const handleRemoveFont = (fontId: string) => {
    const updatedBrandKit = {
      ...currentBrandKit,
      fonts: currentBrandKit.fonts.filter(font => font.id !== fontId),
      updatedAt: new Date(),
    }
    setCurrentBrandKit(updatedBrandKit)
    onBrandKitUpdate(updatedBrandKit)
  }

  const handleSetFontType = (fontId: string, type: "headline" | "body" | "custom") => {
    const updatedBrandKit = {
      ...currentBrandKit,
      fonts: currentBrandKit.fonts.map(font => ({
        ...font,
        type: font.id === fontId ? type : (font.type === type ? "custom" : font.type),
        isDefault: font.id === fontId && (type === "headline" || type === "body"),
      })),
      updatedAt: new Date(),
    }
    setCurrentBrandKit(updatedBrandKit)
    onBrandKitUpdate(updatedBrandKit)
  }

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        {children}
      </DialogTrigger>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Palette className="w-5 h-5" />
            Brand Kit Manager
          </DialogTitle>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="colors" className="flex items-center gap-2">
              <Palette className="w-4 h-4" />
              Colors
            </TabsTrigger>
            <TabsTrigger value="logos" className="flex items-center gap-2">
              <Image className="w-4 h-4" />
              Logos
            </TabsTrigger>
            <TabsTrigger value="fonts" className="flex items-center gap-2">
              <Type className="w-4 h-4" />
              Fonts
            </TabsTrigger>
          </TabsList>

          {/* Colors Tab */}
          <TabsContent value="colors" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Brand Colors</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Add New Color */}
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4 p-4 border rounded-lg bg-gray-50 dark:bg-gray-800">
                  <div>
                    <Label htmlFor="colorName">Color Name</Label>
                    <Input
                      id="colorName"
                      value={newColorName}
                      onChange={(e) => setNewColorName(e.target.value)}
                      placeholder="e.g., Primary Blue"
                    />
                  </div>
                  <div>
                    <Label htmlFor="colorHex">Color</Label>
                    <div className="flex gap-2">
                      <Input
                        type="color"
                        value={newColorHex}
                        onChange={(e) => setNewColorHex(e.target.value)}
                        className="w-12 h-10 p-1"
                      />
                      <Input
                        value={newColorHex}
                        onChange={(e) => setNewColorHex(e.target.value)}
                        placeholder="#3B82F6"
                      />
                    </div>
                  </div>
                  <div>
                    <Label htmlFor="colorType">Type</Label>
                    <Select value={newColorType} onValueChange={(value: any) => setNewColorType(value)}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="primary">Primary</SelectItem>
                        <SelectItem value="secondary">Secondary</SelectItem>
                        <SelectItem value="accent">Accent</SelectItem>
                        <SelectItem value="custom">Custom</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="flex items-end">
                    <Button onClick={handleAddColor} className="w-full">
                      <Plus className="w-4 h-4 mr-2" />
                      Add Color
                    </Button>
                  </div>
                </div>

                {/* Color List */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {currentBrandKit.colors.map((color) => (
                    <div key={color.id} className="flex items-center gap-3 p-3 border rounded-lg">
                      <div
                        className="w-12 h-12 rounded-lg border-2 border-gray-200"
                        style={{ backgroundColor: color.hex }}
                      />
                      <div className="flex-1">
                        <div className="font-medium">{color.name}</div>
                        <div className="text-sm text-gray-500">{color.hex}</div>
                        <Badge variant={color.type === "primary" ? "default" : "secondary"} className="text-xs">
                          {color.type}
                        </Badge>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleRemoveColor(color.id)}
                        className="text-red-500 hover:text-red-700"
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  ))}
                </div>

                {/* Extracted Colors Section */}
                {extractedColors.length > 0 && (
                  <div className="mt-6">
                    <div className="flex items-center gap-2 mb-4">
                      <Sparkles className="w-4 h-4 text-blue-500" />
                      <h3 className="font-medium">Colors Extracted from Logo</h3>
                      <Badge variant="outline" className="text-xs">
                        {extractedColors.length} colors found
                      </Badge>
                    </div>
                    <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-3">
                      {extractedColors.map((color) => (
                        <div key={color.id} className="flex flex-col items-center gap-2 p-3 border rounded-lg bg-gray-50 dark:bg-gray-800">
                          <div
                            className="w-8 h-8 rounded border-2 border-gray-200"
                            style={{ backgroundColor: color.hex }}
                          />
                          <div className="text-center">
                            <div className="text-xs font-medium">{color.name}</div>
                            <div className="text-xs text-gray-500">{color.hex}</div>
                          </div>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleAddExtractedColor(color)}
                            className="text-xs h-6 px-2"
                          >
                            <Plus className="w-3 h-3 mr-1" />
                            Add
                          </Button>
                        </div>
                      ))}
                    </div>
                    <div className="mt-3 text-xs text-gray-500">
                      Click "Add" to include these colors in your brand kit
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          {/* Logos Tab */}
          <TabsContent value="logos" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Brand Logos</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Upload Area */}
                <div className="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-6 text-center">
                  <Upload className="w-12 h-12 mx-auto text-gray-400 mb-4" />
                  <p className="text-gray-600 dark:text-gray-400 mb-4">
                    Upload your brand logos (.png, .svg files)
                  </p>
                  <input
                    ref={logoFileRef}
                    type="file"
                    accept=".png,.svg,.jpg,.jpeg"
                    multiple
                    onChange={handleLogoUpload}
                    className="hidden"
                  />
                  <Button onClick={() => logoFileRef.current?.click()}>
                    <Upload className="w-4 h-4 mr-2" />
                    Choose Files
                  </Button>
                </div>

                {/* Logo Grid */}
                <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                  {currentBrandKit.logos.map((logo) => (
                    <div key={logo.id} className="relative group border rounded-lg p-4 bg-white dark:bg-gray-800">
                      <div className="aspect-square flex items-center justify-center mb-2 bg-gray-100 dark:bg-gray-700 rounded">
                        <img
                          src={logo.url}
                          alt={logo.name}
                          className="max-w-full max-h-full object-contain"
                        />
                      </div>
                      <div className="text-sm font-medium truncate">{logo.name}</div>
                      <div className="flex items-center justify-between mt-2">
                        <Button
                          variant={logo.isPrimary ? "default" : "outline"}
                          size="sm"
                          onClick={() => handleSetPrimaryLogo(logo.id)}
                          className="text-xs"
                        >
                          <Star className="w-3 h-3 mr-1" />
                          {logo.isPrimary ? "Primary" : "Set Primary"}
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleRemoveLogo(logo.id)}
                          className="text-red-500 hover:text-red-700"
                        >
                          <Trash2 className="w-3 h-3" />
                        </Button>
                      </div>
                      {/* Extract Colors Button */}
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleExtractColorsFromImage(logo.url)}
                        disabled={isExtractingColors}
                        className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity"
                      >
                        <Sparkles className="w-3 h-3" />
                      </Button>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Fonts Tab */}
          <TabsContent value="fonts" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Brand Fonts</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Upload Area */}
                <div className="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-6 text-center">
                  <Type className="w-12 h-12 mx-auto text-gray-400 mb-4" />
                  <p className="text-gray-600 dark:text-gray-400 mb-4">
                    Upload your brand fonts (.otf, .ttf, .woff, .woff2 files)
                  </p>
                  <input
                    ref={fontFileRef}
                    type="file"
                    accept=".otf,.ttf,.woff,.woff2"
                    multiple
                    onChange={handleFontUpload}
                    className="hidden"
                  />
                  <Button onClick={() => fontFileRef.current?.click()}>
                    <Upload className="w-4 h-4 mr-2" />
                    Choose Font Files
                  </Button>
                </div>

                {/* Font List */}
                <div className="space-y-4">
                  {currentBrandKit.fonts.map((font) => (
                    <div key={font.id} className="border rounded-lg p-4">
                      <div className="flex items-center justify-between mb-2">
                        <div>
                          <div className="font-medium" style={{ fontFamily: font.fontFamily }}>
                            {font.name}
                          </div>
                          <div className="text-sm text-gray-500">
                            Preview: The quick brown fox jumps over the lazy dog
                          </div>
                        </div>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleRemoveFont(font.id)}
                          className="text-red-500 hover:text-red-700"
                        >
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </div>
                      <div className="flex gap-2">
                        <Button
                          variant={font.type === "headline" ? "default" : "outline"}
                          size="sm"
                          onClick={() => handleSetFontType(font.id, "headline")}
                        >
                          Headline Font
                        </Button>
                        <Button
                          variant={font.type === "body" ? "default" : "outline"}
                          size="sm"
                          onClick={() => handleSetFontType(font.id, "body")}
                        >
                          Body Font
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>

                {/* Font Suggestions Section */}
                {currentBrandKit.logos.length > 0 && (
                  <div className="mt-6">
                    <div className="flex items-center gap-2 mb-4">
                      <Sparkles className="w-4 h-4 text-blue-500" />
                      <h3 className="font-medium">Font Suggestions from Logos</h3>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {currentBrandKit.logos.slice(0, 2).map((logo) => (
                        <div key={logo.id} className="border rounded-lg p-4 bg-gray-50 dark:bg-gray-800">
                          <div className="flex items-center gap-3 mb-3">
                            <img
                              src={logo.url}
                              alt={logo.name}
                              className="w-12 h-12 object-contain bg-white rounded"
                            />
                            <div>
                              <div className="font-medium text-sm">{logo.name}</div>
                              <div className="text-xs text-gray-500">Analyze for fonts</div>
                            </div>
                          </div>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => detectFontsFromImage(logo.url)}
                            className="w-full"
                          >
                            <Sparkles className="w-3 h-3 mr-1" />
                            Analyze Fonts
                          </Button>
                        </div>
                      ))}
                    </div>
                    <div className="mt-3 text-xs text-gray-500">
                      Upload your logo files to get font suggestions based on text analysis
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  )
} 