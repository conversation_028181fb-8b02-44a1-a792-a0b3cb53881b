# TripXplo CRM

A comprehensive Customer Relationship Management system for TripXplo.

## 🚀 Automatic Deployment

This repository is configured for automatic deployment to `crm.tripxplo.com`. Every push to the `master` branch triggers automatic building and deployment.

### Quick Setup for Auto-Deploy

1. **Run the setup script:**
   ```powershell
   .\setup-auto-deploy.ps1
   ```

2. **Configure GitHub secrets** (the script will show you what to add)

3. **Push to deploy:**
   ```bash
   git push origin master
   ```

✅ **Your changes are automatically live at https://crm.tripxplo.com**

For detailed setup instructions, see: [`GITHUB_AUTO_DEPLOY_SETUP.md`](./GITHUB_AUTO_DEPLOY_SETUP.md)

### Manual Deployment (Legacy)

If you need to deploy manually, you can still use:
- `.\deploy-simple.ps1` - Simple PowerShell deployment
- `.\deploy-crm-subdomain.ps1` - Full subdomain deployment
- `.\server-deploy.sh` - Bash deployment script

## 🛠️ Development

```bash
# Install dependencies
npm install

# Start development server
npm run dev

# Build for production
npm run build
```

## 📁 Project Structure

- `/src` - Source code
- `/src/components` - React components
- `/src/pages` - Page components
- `/src/lib` - Utilities and services
- `/src/quotes` - Quote generation system
- `.github/workflows` - GitHub Actions for auto-deployment

## 🔐 Environment Variables

Required environment variables for production:
- `VITE_SUPABASE_URL_CRM` - CRM database URL
- `VITE_SUPABASE_ANON_KEY_CRM` - CRM database anon key
- `VITE_SUPABASE_URL_QUOTE` - Quote database URL  
- `VITE_SUPABASE_ANON_KEY_QUOTE` - Quote database anon key

## 🌐 Live Application

- **Production:** https://crm.tripxplo.com
- **Development:** http://localhost:5173

---

*Powered by React, TypeScript, and automatic GitHub deployment* 🚀
#   A u t o - d e p l o y   t e s t   0 7 / 2 1 / 2 0 2 5   1 9 : 5 0 : 1 7 
 
 # #     A u t o - d e p l o y   t e s t   -   0 7 / 2 1 / 2 0 2 5   2 0 : 4 6 : 3 5 
 
 