import { createClient, SupabaseClient } from '@supabase/supabase-js';
import { getCrmSupabaseConfig } from '../config/env';

// Singleton instance
let supabaseInstance: SupabaseClient | null = null;

/**
 * Creates and returns a Supabase client for the CRM module.
 * Uses a singleton pattern to prevent multiple instances.
 */
const createSupabaseClient = (): SupabaseClient => {
  // If we already have an instance, return it
  if (supabaseInstance) {
    return supabaseInstance;
  }

  // Load from environment configuration
  const crmConfig = getCrmSupabaseConfig();
  const supabaseUrl = crmConfig.url;
  const supabaseAnonKey = crmConfig.anonKey;

  console.log('[supabaseClient] Initializing CRM Supabase client with URL:', supabaseUrl);
  console.log('[supabaseClient] Initializing with Key:', supabaseAnonKey ? 'Exists' : 'MISSING!');

  if (!supabaseUrl || !supabaseAnonKey) {
    throw new Error('[supabaseClient] ERROR: CRM Supabase config missing! Please check your environment configuration.');
  }

  // Define Supabase client options with explicit auth settings and improved connection handling
  const supabaseOptions = {
    auth: {
      persistSession: true,
      autoRefreshToken: true,
      detectSessionInUrl: true,
      storageKey: 'tripxplo-supabase-auth',
      flowType: 'pkce' as const,
    },
    realtime: {
      params: {
        eventsPerSecond: 10,
      },
    },
  };

  // Log the client options for diagnostics
  console.log('[supabaseClient] Client options:', JSON.stringify(supabaseOptions, null, 2));

  // Initialize Supabase client
  console.log('Initializing Supabase client with URL:', supabaseUrl);
  console.log('Using Supabase URL:', supabaseUrl);
  console.log('Using Supabase anon key:', supabaseAnonKey.slice(0,8) + '…');
  supabaseInstance = createClient(supabaseUrl, supabaseAnonKey, supabaseOptions);

  return supabaseInstance;
};

// Export the supabase client - lazy initialization
export const supabase = createSupabaseClient();

// Log when the module has fully loaded
console.log('[supabaseClient] Module initialization complete');

// Add page reload/beforeunload handler to clean up connections properly
window.addEventListener('beforeunload', () => {
  console.log('[supabaseClient] Page unloading, closing connections gracefully...');
  // No need to wait for this to complete since the page is unloading
  // But it helps Supabase know this is an intentional disconnect
  supabase.removeAllChannels();
});
