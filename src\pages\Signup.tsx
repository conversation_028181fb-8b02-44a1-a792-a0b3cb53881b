import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { useNavigate } from 'react-router-dom';

const Signup: React.FC = () => {
  const [fullName, setFullName] = useState<string>('');
  const [email, setEmail] = useState<string>('');
  const [password, setPassword] = useState<string>('');
  const [error, setError] = useState<string | null>(null);
  const [emailSent, setEmailSent] = useState<boolean>(false);
  const { signup, isAuthenticated, isLoading } = useAuth();
  const navigate = useNavigate();

  useEffect(() => {
    if (isAuthenticated) {
      navigate('/dashboard');
    }
  }, [isAuthenticated, navigate]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    try {
      await signup(email, password, fullName);
      setEmailSent(true);
    } catch (err: any) {
      setError(err.message || 'Sign up failed');
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-100">
      <div className="bg-white p-8 rounded shadow-md w-full max-w-md">
        <h1 className="text-primary text-2xl font-bold mb-6 text-center">Sign Up</h1>
        {error && <div className="text-red-500 mb-4">{error}</div>}
        {emailSent && !isAuthenticated ? (
          <div className="text-green-500 mb-4">
            Check your email for a confirmation link to complete your account setup.
          </div>
        ) : (
          <form onSubmit={handleSubmit}>
            <label className="block mb-4">
              <span className="text-primary-dark font-medium mb-1 block">Full Name</span>
              <input
                type="text"
                value={fullName}
                onChange={(e) => setFullName(e.target.value)}
                className="mt-1 block w-full border border-primary-light rounded px-3 py-2 focus:outline-none focus:border-primary focus:ring-1 focus:ring-primary"
                required
              />
            </label>
            <label className="block mb-4">
              <span className="text-primary-dark font-medium mb-1 block">Email</span>
              <input
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="mt-1 block w-full border border-primary-light rounded px-3 py-2 focus:outline-none focus:border-primary focus:ring-1 focus:ring-primary"
                required
              />
            </label>
            <label className="block mb-6">
              <span className="text-primary-dark font-medium mb-1 block">Password</span>
              <input
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="mt-1 block w-full border border-primary-light rounded px-3 py-2 focus:outline-none focus:border-primary focus:ring-1 focus:ring-primary"
                required
              />
            </label>
            <button
              type="submit"
              disabled={isLoading}
              className={`w-full py-2 px-4 bg-primary hover:bg-primary-dark text-white font-bold rounded focus:outline-none focus:shadow-outline ${
                isLoading ? 'opacity-50 cursor-not-allowed' : ''
              }`}
            >
              {isLoading ? 'Signing up…' : 'Sign Up'}
            </button>
          </form>
        )}
      </div>
    </div>
  );
};

export default Signup; 