import React, { useState, useEffect } from 'react';
import { <PERSON>, Calculator, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>Cir<PERSON>, Loader } from 'lucide-react';
import { supabase } from '../lib/supabaseClient';
import { generateFamilyTypePrices, QuoteGeneratorData } from '../utils/familyTypeQuoteGenerator';
import { loadFamilyTypePricesFromDatabase } from '../utils/familyTypeQuoteGenerator';
import { getCrmClient, getQuoteClient } from '../../lib/supabaseManager';

// Family Type interface matching CRM database
interface FamilyType {
  family_id: string;
  family_type: string;
  no_of_adults: number;
  no_of_infants: number;
  no_of_child: number;
  no_of_children: number;
  family_count: number;
  cab_type: string;
  cab_capacity: number;
  rooms_need: number;
}

// Baseline Quote interface
interface BaselineQuote {
  id: string;
  package_name: string;
  customer_name: string;
  destination: string;
  family_type: string;
  total_cost: number;
  no_of_persons: number;
  children: number;
  infants: number;
  extra_adults: number;
  is_draft: boolean;
}

// Family Type Price Result interface  
interface FamilyTypePriceResult {
  familyType: FamilyType;
  calculatedPrice: number;
  breakdown: {
    hotelCost: number;
    vehicleCost: number;
    additionalCosts: number;
    rooms: number;
    grandTotal: number;
    discount: number;
  };
}

// Props interface for receiving live quote data from parent
interface LiveQuoteData {
  packageName: string;
  customerName: string;
  destination: string;
  quoteDate: string;
  validityDate: string;
  noOfPersons: number;
  extraAdults: number;
  children: number;
  infants: number;
  hotelRows: any[];
  costs: any;
  commission: number;
  discountValue: number;
  discountType: string;
}

interface FamilyTypeTabProps {
  currentQuoteId?: string | null;
  liveQuoteData?: LiveQuoteData;
}

function FamilyTypeTab({ currentQuoteId, liveQuoteData }: FamilyTypeTabProps) {
  // State management
  const [familyTypes, setFamilyTypes] = useState<FamilyType[]>([]);
  const [baselineQuotes, setBaselineQuotes] = useState<BaselineQuote[]>([]);
  const [selectedBaselineQuote, setSelectedBaselineQuote] = useState('');
  const [familyTypePricesResults, setFamilyTypePricesResults] = useState<FamilyTypePriceResult[]>([]);
  const [savedFamilyTypePrices, setSavedFamilyTypePrices] = useState<FamilyTypePriceResult[]>([]);
  
  // Loading states
  const [isLoadingFamilyTypes, setIsLoadingFamilyTypes] = useState(false);
  const [isLoadingQuotes, setIsLoadingQuotes] = useState(false);
  const [isGeneratingPrices, setIsGeneratingPrices] = useState(false);
  const [isLoadingSavedPrices, setIsLoadingSavedPrices] = useState(false);
  
  // Messages
  const [message, setMessage] = useState('');
  const [showSavedPrices, setShowSavedPrices] = useState(false);

  // Include drafts toggle
  const [includeDrafts, setIncludeDrafts] = useState(false);

  // Initialize data on component mount
  useEffect(() => {
    fetchFamilyTypes();
    fetchBaselineQuotes();
  }, [includeDrafts]);

  // Fetch family types from CRM database
  const fetchFamilyTypes = async () => {
    setIsLoadingFamilyTypes(true);
    try {
      console.log('🔍 Fetching family types from CRM database...');

      const crmClient = getCrmClient();
      const { data, error } = await crmClient
        .from('family_type')
        .select('*')
        .order('family_id');

      if (error) {
        console.error('❌ Error fetching family types:', error);
        setMessage(`❌ Error fetching family types: ${error.message}`);
        return;
      }

      if (data && data.length > 0) {
        console.log(`✅ Successfully loaded ${data.length} family types from CRM database`);
        setFamilyTypes(data);
        setMessage(`✅ Loaded ${data.length} family types from database`);
      } else {
        setMessage('⚠️ No family types found in database');
      }
    } catch (error) {
      console.error('❌ Exception while fetching family types:', error);
      setMessage(`❌ Error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsLoadingFamilyTypes(false);
    }
  };

  // Fetch baseline quotes
  const fetchBaselineQuotes = async () => {
    setIsLoadingQuotes(true);
    try {
      console.log('🔍 Fetching quotes from Quote database...');

      const quoteClient = await getQuoteClient();
      let query = quoteClient
        .from('quotes')
        .select('id, package_name, customer_name, destination, family_type, total_cost, no_of_persons, children, infants, extra_adults, is_draft')
        .not('total_cost', 'is', null)
        .gt('total_cost', 0)
        .order('created_at', { ascending: false })
        .limit(20);

      if (!includeDrafts) {
        query = query.eq('is_draft', false);
      }

      const { data, error } = await query;

      if (error) {
        console.error('❌ Error fetching baseline quotes:', error);
        setMessage(`❌ Error fetching quotes: ${error.message}`);
        return;
      }

      console.log(`✅ Successfully fetched ${data?.length || 0} quotes`);
      setBaselineQuotes(data || []);
    } catch (error) {
      console.error('❌ Exception fetching baseline quotes:', error);
      setMessage(`❌ Error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsLoadingQuotes(false);
    }
  };

  // Generate family type prices using Quote Generator logic
  const handleGenerateFamilyTypePrices = async () => {
    if (!selectedBaselineQuote) {
      setMessage('❌ Please select a baseline quote first.');
      return;
    }

    setIsGeneratingPrices(true);
    setMessage('');
    
    try {
      console.log('🎯 Starting family type price generation...');
      
      // Get the baseline quote data
      const baselineQuote = baselineQuotes.find(q => q.id === selectedBaselineQuote);
      if (!baselineQuote) {
        setMessage('❌ Selected baseline quote not found.');
        return;
      }

      let quoteGeneratorData: QuoteGeneratorData;

      // Check if we're working with the current active quote and have live data
      if (currentQuoteId && selectedBaselineQuote === currentQuoteId && liveQuoteData) {
        console.log('🔥 Using LIVE form data for current active quote');
        
        // Use live data from the form (current state)
        quoteGeneratorData = {
          packageName: liveQuoteData.packageName,
          customerName: liveQuoteData.customerName,
          destination: liveQuoteData.destination,
          quoteDate: liveQuoteData.quoteDate,
          validityDate: liveQuoteData.validityDate,
          noOfPersons: liveQuoteData.noOfPersons,
          extraAdults: liveQuoteData.extraAdults,
          children: liveQuoteData.children,
          infants: liveQuoteData.infants,
          hotelRows: liveQuoteData.hotelRows,
          costs: liveQuoteData.costs,
          commission: liveQuoteData.commission,
          discountValue: liveQuoteData.discountValue,
          discountType: liveQuoteData.discountType,
        };

        console.log('✅ Live quote data prepared for family type generation');
      } else {
        // Fetch complete quote details from database
        console.log('📖 Fetching complete quote details from database...');

        const quoteClient = await getQuoteClient();
        const { data: fullQuoteData, error: quoteError } = await quoteClient
          .from('quotes')
          .select('*')
          .eq('id', selectedBaselineQuote)
          .single();

        if (quoteError || !fullQuoteData) {
          console.error('❌ Error fetching full quote details:', quoteError);
          setMessage('❌ Could not fetch complete quote details from database. Ensure the quote has Hotel Rows and Quote Mapping data.');
          return;
        }

        console.log('✅ Full quote data loaded from database');

        // Parse hotel rows and costs from the database
        let hotelRows = [];
        let costs = {
          basicCosts: { meals: 0, transportation: 0, cabSightseeing: 0, trainCost: 0, ferryCost: 0, parkingToll: 0 },
          addOnCosts: { addOnActivity: 0, marketing: 0, addOn: 0 },
          optionalCosts: { flightTicket: 0, guideWages: 0 },
        };

        try {
          // Parse hotel rows if available
          if (fullQuoteData.hotel_rows && typeof fullQuoteData.hotel_rows === 'string') {
            hotelRows = JSON.parse(fullQuoteData.hotel_rows);
          } else if (Array.isArray(fullQuoteData.hotel_rows)) {
            hotelRows = fullQuoteData.hotel_rows;
          }

          // Parse costs if available
          if (fullQuoteData.costs && typeof fullQuoteData.costs === 'string') {
            const parsedCosts = JSON.parse(fullQuoteData.costs);
            costs = { ...costs, ...parsedCosts };
          } else if (typeof fullQuoteData.costs === 'object' && fullQuoteData.costs !== null) {
            costs = { ...costs, ...fullQuoteData.costs };
          }
        } catch (parseError) {
          console.warn('⚠️ Could not parse hotel rows or costs, using defaults:', parseError);
        }

        // Create complete QuoteGeneratorData from database
        quoteGeneratorData = {
          packageName: fullQuoteData.package_name || baselineQuote.package_name,
          customerName: fullQuoteData.customer_name || baselineQuote.customer_name,
          destination: fullQuoteData.destination || baselineQuote.destination,
          quoteDate: fullQuoteData.quote_date || new Date().toISOString().split('T')[0],
          validityDate: fullQuoteData.validity_date || new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
          noOfPersons: fullQuoteData.no_of_persons || baselineQuote.no_of_persons,
          extraAdults: fullQuoteData.extra_adults || baselineQuote.extra_adults,
          children: fullQuoteData.children || baselineQuote.children,
          infants: fullQuoteData.infants || baselineQuote.infants,
          hotelRows: hotelRows,
          costs: costs,
          commission: fullQuoteData.commission || 10,
          discountValue: fullQuoteData.discount_value || 0,
          discountType: fullQuoteData.discount_type || 'percentage',
        };

        console.log('🚀 QuoteGeneratorData prepared with database hotel and cost data');
      }

      // Generate family type prices using the Quote Generator function
      const result = await generateFamilyTypePrices(quoteGeneratorData, selectedBaselineQuote);
      
      if (result.success && result.results) {
        setFamilyTypePricesResults(result.results);
        setSavedFamilyTypePrices(result.results);
        const dataSource = (currentQuoteId && selectedBaselineQuote === currentQuoteId && liveQuoteData) ? 'live form data' : 'saved database data';
        setMessage(`✅ Successfully generated exact prices for ${result.results.length} family types using ${dataSource} and saved to database.`);
        setShowSavedPrices(false); // Show generated results instead of saved
        console.log('✅ Generated and saved family type prices:', result.results);
      } else {
        setMessage(result.message);
      }
    } catch (error) {
      console.error('❌ Error generating family type prices:', error);
      setMessage(`❌ Error generating prices: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsGeneratingPrices(false);
    }
  };

  // Load saved family type prices from database
  const handleLoadSavedPrices = async () => {
    if (!selectedBaselineQuote) {
      setMessage('❌ Please select a baseline quote first.');
      return;
    }

    setIsLoadingSavedPrices(true);
    setMessage('');
    
    try {
      console.log('📖 Loading saved family type prices...');
      
      const result = await loadFamilyTypePricesFromDatabase(selectedBaselineQuote);
      
      if (result.success && result.results) {
        setSavedFamilyTypePrices(result.results);
        setFamilyTypePricesResults([]); // Clear generated results
        setMessage(`✅ Loaded ${result.results.length} saved family type prices from database.`);
        setShowSavedPrices(true);
        console.log('✅ Loaded saved family type prices:', result.results);
      } else {
        setSavedFamilyTypePrices([]);
        setMessage(result.message);
        setShowSavedPrices(true);
      }
    } catch (error) {
      console.error('❌ Error loading saved family type prices:', error);
      setMessage(`❌ Error loading saved prices: ${error instanceof Error ? error.message : 'Unknown error'}`);
      setShowSavedPrices(true);
    } finally {
      setIsLoadingSavedPrices(false);
    }
  };

  // Format price helper
  const formatPrice = (price: number): string => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(price);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white rounded-lg shadow">
        <div className="p-6 border-b border-gray-200 bg-purple-50">
          <div className="flex items-center gap-3">
            <Users className="h-6 w-6 text-purple-600" />
            <div>
              <h3 className="text-lg font-semibold text-gray-800">Family Type Price Generator</h3>
              <p className="text-sm text-gray-600 mt-1">
                Generate accurate prices for all family types using Quote Generator logic with Quote Mapping data
              </p>
            </div>
          </div>
        </div>

        <div className="p-6">
          {/* Status Message */}
          {message && (
            <div className={`mb-4 p-3 rounded-md text-sm ${
              message.includes('✅') 
                ? 'bg-green-50 border border-green-200 text-green-800' 
                : message.includes('⚠️')
                ? 'bg-yellow-50 border border-yellow-200 text-yellow-800'
                : 'bg-red-50 border border-red-200 text-red-800'
            }`}>
              {message}
            </div>
          )}

          {/* Main Controls */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Quote Selection */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Select Baseline Quote
              </label>
              
              {/* Include Drafts Toggle */}
              <div className="mb-3">
                <label className="flex items-center text-sm text-gray-600">
                  <input
                    type="checkbox"
                    checked={includeDrafts}
                    onChange={(e) => {
                      setIncludeDrafts(e.target.checked);
                      setSelectedBaselineQuote('');
                    }}
                    className="mr-2 h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"
                  />
                  Include draft quotes
                </label>
              </div>
              
              <select
                value={selectedBaselineQuote}
                onChange={(e) => setSelectedBaselineQuote(e.target.value)}
                className="w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:ring-purple-500 focus:border-purple-500"
                disabled={isLoadingQuotes}
              >
                <option value="">Choose a customer's baseline quote...</option>
                {baselineQuotes.map(quote => (
                  <option key={quote.id} value={quote.id}>
                    {quote.customer_name} - {quote.destination} ({quote.family_type}) - {formatPrice(quote.total_cost)} {quote.is_draft ? '(Draft)' : ''}
                  </option>
                ))}
              </select>
              
              {isLoadingQuotes && (
                <p className="text-sm text-gray-500 mt-1 flex items-center gap-2">
                  <Loader className="w-4 h-4 animate-spin" />
                  Loading quotes...
                </p>
              )}

              {/* Selected Quote Details */}
              {selectedBaselineQuote && (
                <div className="mt-3 p-3 bg-purple-50 border border-purple-200 rounded-md">
                  {(() => {
                    const selectedQuote = baselineQuotes.find(q => q.id === selectedBaselineQuote);
                    if (!selectedQuote) return null;
                    
                    return (
                      <div className="text-sm">
                        <p className="font-medium text-purple-800">Selected Baseline Quote:</p>
                        <div className="mt-1 space-y-1 text-purple-700">
                          <p><span className="font-medium">Customer:</span> {selectedQuote.customer_name}</p>
                          <p><span className="font-medium">Destination:</span> {selectedQuote.destination}</p>
                          <p><span className="font-medium">Total Cost:</span> {formatPrice(selectedQuote.total_cost)}</p>
                          <p><span className="font-medium">Composition:</span> {(selectedQuote.no_of_persons + selectedQuote.extra_adults)} Adults, {selectedQuote.children} Children, {selectedQuote.infants} Infants</p>
                        </div>
                      </div>
                    );
                  })()}
                </div>
              )}
            </div>
            
            {/* Action Buttons */}
            <div className="flex items-end">
              <div className="w-full space-y-3">
                <button
                  onClick={handleGenerateFamilyTypePrices}
                  disabled={!selectedBaselineQuote || isGeneratingPrices}
                  className="w-full px-6 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors flex items-center justify-center gap-2 font-semibold"
                >
                  {isGeneratingPrices ? (
                    <>
                      <Loader className="w-5 h-5 animate-spin" />
                      Generating Prices...
                    </>
                  ) : (
                    <>
                      <Calculator className="w-5 h-5" />
                      Generate Family Type Prices
                    </>
                  )}
                </button>

                <button
                  onClick={handleLoadSavedPrices}
                  disabled={!selectedBaselineQuote || isLoadingSavedPrices}
                  className="w-full px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors flex items-center justify-center gap-2"
                >
                  {isLoadingSavedPrices ? (
                    <>
                      <Loader className="w-5 h-5 animate-spin" />
                      Loading...
                    </>
                  ) : (
                    <>
                      <BarChart3 className="w-5 h-5" />
                      View Saved Prices
                    </>
                  )}
                </button>

                <div className="text-xs text-gray-500 text-center mt-2">
                  Uses exact Quote Generator calculation logic with Quote Mapping data
                </div>
              </div>
            </div>
          </div>

          {/* Quick Stats */}
          <div className="mt-6 grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="bg-blue-50 p-3 rounded-lg">
              <div className="text-sm font-medium text-blue-800">Family Types</div>
              <div className="text-2xl font-bold text-blue-900">{familyTypes.length}</div>
            </div>
            <div className="bg-green-50 p-3 rounded-lg">
              <div className="text-sm font-medium text-green-800">Baseline Quotes</div>
              <div className="text-2xl font-bold text-green-900">{baselineQuotes.length}</div>
            </div>
            <div className="bg-purple-50 p-3 rounded-lg">
              <div className="text-sm font-medium text-purple-800">Generated Prices</div>
              <div className="text-2xl font-bold text-purple-900">{familyTypePricesResults.length}</div>
            </div>
            <div className="bg-orange-50 p-3 rounded-lg">
              <div className="text-sm font-medium text-orange-800">Saved Prices</div>
              <div className="text-2xl font-bold text-orange-900">{showSavedPrices ? savedFamilyTypePrices.length : 0}</div>
            </div>
          </div>
        </div>
      </div>

      {/* Results Section - Generated Prices */}
      {(familyTypePricesResults.length > 0 && !showSavedPrices) && (
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center gap-2">
            <Calculator className="w-5 h-5 text-purple-600" />
            Family Type Prices Results
          </h3>
          
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Family Type</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Members</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Rooms</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Vehicle</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Price</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Details</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {familyTypePricesResults.map((result, index) => (
                  <tr key={result.familyType.family_id} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      {result.familyType.family_type}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {result.familyType.no_of_adults}A
                      {result.familyType.no_of_children > 0 && ` + ${result.familyType.no_of_children}C`}
                      {result.familyType.no_of_infants > 0 && ` + ${result.familyType.no_of_infants}I`}
                      {result.familyType.no_of_child > 0 && ` + ${result.familyType.no_of_child}K`}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {result.breakdown.rooms} rooms
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {result.familyType.cab_type}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-semibold text-green-600">
                      {formatPrice(result.calculatedPrice)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      <div className="text-xs">
                        Hotel: {formatPrice(result.breakdown.hotelCost)}<br/>
                        Vehicle: {formatPrice(result.breakdown.vehicleCost)}<br/>
                        Others: {formatPrice(result.breakdown.additionalCosts)}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}

      {/* Results Section - Saved Prices */}
      {(savedFamilyTypePrices.length > 0 && showSavedPrices) && (
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center gap-2">
            <BarChart3 className="w-5 h-5 text-green-600" />
            Saved Family Type Prices
          </h3>
          
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Family Type</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Members</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Rooms</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Vehicle</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Price</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Details</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {savedFamilyTypePrices
                  .sort((a, b) => a.calculatedPrice - b.calculatedPrice)
                  .map((result, index) => (
                  <tr key={`${result.familyType.family_id}-${index}`} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      {result.familyType.family_type}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {result.familyType.no_of_adults}A
                      {result.familyType.no_of_children > 0 && ` + ${result.familyType.no_of_children}C`}
                      {result.familyType.no_of_infants > 0 && ` + ${result.familyType.no_of_infants}I`}
                      {result.familyType.no_of_child > 0 && ` + ${result.familyType.no_of_child}K`}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {result.breakdown.rooms} rooms
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {result.familyType.cab_type}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-semibold text-green-600">
                      {formatPrice(result.calculatedPrice)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      <div className="text-xs">
                        Hotel: {formatPrice(result.breakdown.hotelCost)}<br/>
                        Vehicle: {formatPrice(result.breakdown.vehicleCost)}<br/>
                        Others: {formatPrice(result.breakdown.additionalCosts)}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          <div className="mt-4 p-3 bg-gray-50 rounded text-sm text-gray-600">
            <p className="font-medium mb-2">Calculation Details:</p>
            <ul className="text-xs space-y-1">
              <li>• <strong>Hotel Cost:</strong> Based on room requirements and Quote Mapping rates</li>
              <li>• <strong>Vehicle Cost:</strong> Based on family size and vehicle type capacity</li>
              <li>• <strong>Others:</strong> Includes basic costs, add-ons, commission, and discounts</li>
              <li>• <strong>Total Price:</strong> Exact same calculation as Quote Generator</li>
            </ul>
          </div>
        </div>
      )}

      {/* No Results Message */}
      {baselineQuotes.length === 0 && !isLoadingQuotes && (
        <div className="bg-white rounded-lg shadow p-6">
          <div className="text-center py-8">
            <AlertCircle className="w-12 h-12 text-orange-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No Baseline Quotes Available</h3>
            <p className="text-gray-600 mb-4">
              To generate family type prices, you need at least one saved quote to use as a baseline.
            </p>
            <div className="text-sm text-gray-500">
              <p className="mb-2">Steps to get started:</p>
              <ol className="text-left inline-block space-y-1">
                <li>1. Go to the <strong>Quote Generator</strong> tab</li>
                <li>2. Create a complete quote with hotel and cost data</li>
                <li>3. <strong>Save the quote</strong> to the database</li>
                <li>4. Return to this Family Type tab</li>
                <li>5. Select your saved quote and generate prices</li>
              </ol>
            </div>
          </div>
        </div>
      )}

      {/* Footer Info */}
      <div className="bg-gray-50 rounded-lg p-4">
        <div className="flex items-center justify-between text-sm text-gray-600">
          <div className="flex items-center gap-4">
            <span>Family Types: {familyTypes.length}</span>
            <span>Baseline Quotes: {baselineQuotes.length}</span>
            {familyTypePricesResults.length > 0 && <span>Generated: {familyTypePricesResults.length}</span>}
          </div>
          <div className="flex items-center gap-2">
            <CheckCircle className="w-4 h-4 text-green-500" />
            <span>Uses Quote Generator Logic</span>
          </div>
        </div>
      </div>
    </div>
  );
}

export default FamilyTypeTab; 