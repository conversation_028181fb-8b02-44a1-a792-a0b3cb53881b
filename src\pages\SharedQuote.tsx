import React, { useEffect, useState, useRef } from 'react';
import { useParams } from 'react-router-dom';
import { createClient } from '@supabase/supabase-js';
import whatsappLogo from '../assets/whatsapp-logo.png';
import { getQuoteSupabaseConfig, getCrmSupabaseConfig } from '../config/env';
//import jsPDF from 'jspdf';
//import html2canvas from 'html2canvas';

interface HotelRow {
  hotelName: string;
  roomType: string;
  mealPlan: string;
  noOfRooms: number;
  stayNights: number;
  price: number;
  extraAdultCost: number;
  childrenCost: number;
  infantCost: number;
  stayPrice: number;
  gstAmount: number;
  currency: string;
}

interface Quote {
  id: string;
  package_name: string;
  customer_name: string;
  customer_email?: string;
  customer_phone?: string;
  destination: string;
  trip_duration: string;
  family_type: string;
  validity_date: string;
  no_of_persons: number;
  extra_adults: number;
  children: number;
  infants: number;
  total_cost: number;
  package_type: string;
  plan: string;
  season: string;
  quote_type: string;
  currency: string;
  created_at: string;
  updated_at: string;
  itinerary: string;
  travel_date: string;
}

interface HotelRowData {
  id: string;
  quote_id: string;
  hotel_name: string;
  room_type: string;
  meal_plan: string;
  no_of_rooms: number;
  stay_nights: number;
  price: number;
  extra_adult_cost: number;
  children_cost: number;
  infant_cost: number;
  stay_price: number;
  gst_amount: number;
  currency: string;
}

interface EnquiryPopupProps {
  onClose: () => void;
  quote: Quote | null;
  quoteId: string | undefined;
}

const EnquiryPopup: React.FC<EnquiryPopupProps> = ({ onClose, quote, quoteId }) => {
  const [step, setStep] = useState('initial'); // initial, form, schedule, schedule-details, success
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    noOfPersons: '',
    travelDate: '',
    departureCity: '',
  });
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);
  const [selectedTime, setSelectedTime] = useState<string | null>(null);
  const [scheduleName, setScheduleName] = useState('');
  const [schedulePhone, setSchedulePhone] = useState('');
  const [isEditingDestination, setIsEditingDestination] = useState(false);
  const [destination, setDestination] = useState(quote?.destination || '');

  const handleFormChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { id, value } = e.target;
    setFormData(prev => ({ ...prev, [id]: value }));
  };

  const handleFormSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!quote) return;

    try {
      const config = getCrmSupabaseConfig();
      const supabase = createClient(config.url, config.anonKey);
       const nights = parseInt(quote.trip_duration.split(' ')[0], 10) || null;
      const { error } = await supabase
        .from('leads')
        .insert([
          {
            customer_name: formData.name,
            email: formData.email,
            phone: formData.phone,
            adults: quote.no_of_persons + quote.extra_adults,
            children: quote.children,
            infants: quote.infants,
            travel_date: formData.travelDate,
            destination: destination,
            departure_city: formData.departureCity,
            nights: nights,
          },
        ]);

      if (error) throw error;

        // Also update the shared_quotes table to disable the popup
        const { error: updateError } = await supabase
        .from('shared_quotes')
        .update({ is_popup_active: false })
        .eq('quote_hash', quoteId);

      if (updateError) {
        console.error('Error updating shared_quote:', updateError);
        // Decide if you want to proceed even if this fails
      }

      setStep('success');
      setTimeout(() => {
        onClose();
      }, 5000);
    } catch (error) {
      console.error('Error submitting form:', error);
      // Optionally, set an error step
    }
  };

  const handleFillOutDetails = () => {
    setStep('form');
  };

  const handleScheduleCall = () => {
    setStep('schedule');
  };

  const handleDateSelect = (date: Date) => {
    setSelectedDate(date);
  };

  const handleTimeSelect = (time: string) => {
    setSelectedTime(time);
  };

  const handleBookingConfirm = () => {
    setStep('schedule-details');
  };

  const handleScheduleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!quote || !selectedDate || !selectedTime) return;

    try {
      const config = getCrmSupabaseConfig();
      const supabase = createClient(config.url, config.anonKey);

      // Combine date and time
      const timePart = selectedTime.split(' ')[0]; // e.g., "10:00"
      const hours = parseInt(timePart.split(':')[0], 10);
      const minutes = parseInt(timePart.split(':')[1], 10);
      const scheduledDateTime = new Date(selectedDate);
      scheduledDateTime.setHours(hours, minutes, 0, 0);

      const nights = parseInt(quote.trip_duration.split(' ')[0], 10) || null;
      const { error } = await supabase
        .from('leads')
        .insert([
          {
            customer_name: scheduleName,
            phone: schedulePhone,
            adults: quote.no_of_persons + quote.extra_adults,
            children: quote.children,
            infants: quote.infants,
            destination: quote.destination,
            nights: nights,
            status: 'CALL CUSTOMER',
            scheduled_to: scheduledDateTime.toISOString(),
          },
        ]);

      if (error) throw error;

      // Also update the shared_quotes table to disable the popup
      const { error: updateError } = await supabase
        .from('shared_quotes')
        .update({ is_popup_active: false })
        .eq('quote_hash', quoteId);

      if (updateError) {
        console.error('Error updating shared_quote:', updateError);
      }

      setStep('success');
      setTimeout(() => {
        onClose();
      }, 5000);
    } catch (error) {
      console.error('Error submitting scheduled call:', error);
      // Optionally, set an error step
    }
  };

  const renderInitial = () => (
    <div className="text-center">
      <h2 className="text-xl sm:text-2xl font-bold text-gray-800 mb-2">✨Get Your Custom Quote✨</h2>
      <p className="text-gray-600 font-semibold mt-5 mb-3 text-sm sm:text-base">Need Offer? Fill your Travel Details 🎉</p>
      <div className="space-y-3 sm:space-y-4">
        <button 
          onClick={handleFillOutDetails} 
          className="w-full bg-gradient-to-r from-blue-500 to-indigo-500 text-white font-bold py-3 px-4 rounded-lg transition-transform transform hover:scale-105 duration-300 shadow-lg focus:outline-none focus:ring-4 focus:ring-blue-300 text-sm sm:text-base"
        >
          <i className="fas fa-edit mr-2"></i> Fill Out Details
        </button>
        <p className="text-gray-600 font-semibold mb-6 text-sm sm:text-base">Schedule a Call for More Info ⚡</p>
        <button 
          onClick={handleScheduleCall} 
          className="w-full bg-gradient-to-r from-green-500 to-teal-500 text-white font-bold py-3 px-4 rounded-lg transition-transform transform hover:scale-105 duration-300 shadow-lg focus:outline-none focus:ring-4 focus:ring-green-300 text-sm sm:text-base"
        >
          <i className="fas fa-phone-alt mr-2"></i> Schedule a Call
        </button>
      </div>
    </div>
  );

  const renderForm = () => (
    <>
      <button onClick={() => setStep('initial')} className="absolute top-3 left-3 text-gray-500 hover:text-gray-800">
        <i className="fas fa-arrow-left"></i> Back
      </button>
      <h2 className="text-xl sm:text-2xl font-bold mb-4 text-center">Your Details</h2>
      <form onSubmit={handleFormSubmit} className="space-y-3">
        <div>
          <label htmlFor="name" className="block mb-1 font-semibold text-gray-700 text-sm">Name</label>
          <input type="text" id="name" value={formData.name} onChange={handleFormChange} className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 text-sm" required />
        </div>
        <div>
          <label htmlFor="email" className="block mb-1 font-semibold text-gray-700 text-sm">Email (Optional)</label>
          <input type="email" id="email" value={formData.email} onChange={handleFormChange} className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 text-sm" />
        </div>
        <div>
          <label htmlFor="phone" className="block mb-1 font-semibold text-gray-700 text-sm">Phone Number</label>
          <input type="tel" id="phone" value={formData.phone} onChange={handleFormChange} className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 text-sm" required />
        </div>
        <div>
          <label htmlFor="departureCity" className="block mb-1 font-semibold text-gray-700 text-sm">Departure City</label>
          <input type="text" id="departureCity" value={formData.departureCity} onChange={handleFormChange} className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 text-sm" required />
        </div>
        <div>
          <label htmlFor="travelDate" className="block mb-1 font-semibold text-gray-700 text-sm">Travel Date</label>
          <input type="date" id="travelDate" value={formData.travelDate} onChange={handleFormChange} className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 text-sm" />
        </div>
        <div>
          <label htmlFor="destination" className="block mb-1 font-semibold text-gray-700 text-sm">Destination</label>
          {isEditingDestination ? (
            <input
              type="text"
              id="destination"
              value={destination}
              onChange={(e) => setDestination(e.target.value)}
              onBlur={() => setIsEditingDestination(false)}
              className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 text-sm"
              autoFocus
            />
          ) : (
            <div className="flex items-center">
              <span className="font-bold text-gray-900">{destination}</span>
              <button
                type="button"
                className="ml-2 text-green-600 text-xs font-semibold"
                onClick={() => setIsEditingDestination(true)}
              >
                (Change Destination)
              </button>
            </div>
          )}
        </div>
        <button type="submit" className="w-full bg-blue-500 hover:bg-blue-600 text-white font-bold py-2.5 rounded-lg transition-colors duration-300 text-sm sm:text-base">Submit</button>
      </form>
    </>
  );

  const renderSchedule = () => {
    const today = new Date();
    const dates = [today, new Date(today.getTime() + 86400000), new Date(today.getTime() + 2 * 86400000)];
    const timeSlots = ['10:00 AM - 10:30 AM', '11:00 AM - 12:00 PM', '01:00 PM - 02:00 PM', '03:00 PM - 04:00 PM'];

    return (
      <>
        <button onClick={() => setStep('initial')} className="absolute top-3 left-3 text-gray-500 hover:text-gray-800">
          <i className="fas fa-arrow-left"></i> Back
        </button>
        <h2 className="text-xl sm:text-2xl font-bold mb-4 text-center">Schedule a Call</h2>
        
        <div className="mb-4">
          <h3 className="font-semibold text-md sm:text-lg mb-2 text-gray-700">Select a Date</h3>
          <div className="flex justify-around">
            {dates.map(date => (
              <button 
                key={date.toISOString()} 
                onClick={() => handleDateSelect(date)} 
                className={`px-3 py-2 sm:px-4 rounded-lg border-2 transition-all duration-300 ${selectedDate?.toDateString() === date.toDateString() ? 'bg-green-500 text-white border-green-600' : 'bg-gray-100 border-gray-300 hover:bg-gray-200'}`}
              >
                <div className="font-bold text-sm sm:text-base">{date.toLocaleDateString('en-US', { weekday: 'short' })}</div>
                <div className="text-xs sm:text-sm">{date.toLocaleDateString('en-US', { day: 'numeric' })}</div>
              </button>
            ))}
          </div>
        </div>

        {selectedDate && (
          <div className="mb-4">
            <h3 className="font-semibold text-md sm:text-lg mb-2 text-gray-700">Select a Time Slot <span className="text-xs sm:text-sm text-gray-500">(IST)</span></h3>
            <div className="grid grid-cols-2 gap-2">
              {timeSlots.map(time => (
                <button 
                  key={time} 
                  onClick={() => handleTimeSelect(time)} 
                  className={`px-3 py-2 rounded-lg border-2 transition-all duration-300 text-xs sm:text-sm ${selectedTime === time ? 'bg-green-500 text-white border-green-600' : 'bg-gray-100 border-gray-300 hover:bg-gray-200'}`}
                >
                  {time}
                </button>
              ))}
            </div>
          </div>
        )}

        {selectedDate && selectedTime && (
          <div className="mt-4 text-center">
             <p className="mb-3 text-center text-gray-700 text-sm sm:text-base">You've selected a call for <strong>{selectedDate?.toLocaleDateString('en-US', { weekday: 'long', month: 'long', day: 'numeric' })}</strong> at <strong>{selectedTime}</strong>.</p>
            <button 
              onClick={handleBookingConfirm} 
              className="w-full bg-green-500 hover:bg-green-600 text-white font-bold py-2.5 rounded-lg transition-colors duration-300 text-sm sm:text-base"
            >
              Confirm My Call Schedule
            </button>
          </div>
        )}
      </>
    );
  };

  const renderSuccess = () => (
    <div className="text-center">
      <div className="w-16 h-16 sm:w-20 sm:h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
        <i className="fas fa-check text-4xl sm:text-5xl text-green-500"></i>
      </div>
      <h2 className="text-2xl sm:text-3xl font-bold text-green-600 mb-2">Confirmed!</h2>
      <p className="text-gray-700 text-sm sm:text-base">Your call is scheduled. We'll be in touch shortly!</p>
    </div>
  );

  const renderScheduleDetails = () => (
    <>
      <button onClick={() => setStep('schedule')} className="absolute top-3 left-3 text-gray-500 hover:text-gray-800">
        <i className="fas fa-arrow-left"></i> Back
      </button>
      <h2 className="text-xl sm:text-2xl font-bold mb-4 text-center">Your Details</h2>
      <form onSubmit={handleScheduleSubmit} className="space-y-3">
        <div>
          <label htmlFor="scheduleName" className="block mb-1 font-semibold text-gray-700 text-sm">Name</label>
          <input type="text" id="scheduleName" value={scheduleName} onChange={(e) => setScheduleName(e.target.value)} className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 text-sm" required />
        </div>
        <div>
          <label htmlFor="schedulePhone" className="block mb-1 font-semibold text-gray-700 text-sm">Phone Number</label>
          <input type="tel" id="schedulePhone" value={schedulePhone} onChange={(e) => setSchedulePhone(e.target.value)} className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 text-sm" required />
        </div>
        <button type="submit" className="w-full bg-blue-500 hover:bg-blue-600 text-white font-bold py-2.5 rounded-lg transition-colors duration-300 text-sm sm:text-base">Confirm</button>
      </form>
    </>
  );

  const renderStep = () => {
    switch (step) {
      case 'form':
        return renderForm();
      case 'schedule':
        return renderSchedule();
      case 'schedule-details':
        return renderScheduleDetails();
      case 'success':
        return renderSuccess();
      case 'initial':
      default:
        return renderInitial();
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-60 flex items-center justify-center z-50 p-4">
      <div className="bg-white p-6 sm:p-8 rounded-2xl shadow-2xl w-full max-w-sm sm:max-w-md relative transform transition-all duration-300 scale-95 animate-in fade-in-0 zoom-in-95">
        <button onClick={onClose} className="absolute top-3 right-3 sm:top-4 sm:right-4 text-gray-400 hover:text-gray-800 transition-colors">
          <i className="fas fa-times text-xl sm:text-2xl"></i>
        </button>
        {renderStep()}
      </div>
    </div>
  );
};

const FloatingWhatsAppButton: React.FC<{ quote: Quote | null }> = ({ quote }) => {
  if (!quote) return null;

  const handleWhatsAppClick = () => {
    const phoneNumber = "919442424492";
    const message = `Hello! 🎉 I'm excited to proceed with the *${quote.package_name}* 🏝️💕.\n\nThe quote for a *${quote.trip_duration}* trip for *${quote.no_of_persons}* adults at *${new Intl.NumberFormat('en-IN', { style: 'currency', currency: 'INR', maximumFractionDigits: 0 }).format(quote.total_cost)}* is approved! ✅\n\nPlease let me know the next steps to confirm the booking ✈️ and the available payment options.`;
    const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);

    let whatsappUrl;

    if (isMobile) {
      // On mobile, use the universal link (which is the best practice for all devices anyway)
      whatsappUrl = `https://wa.me/${phoneNumber}?text=${encodeURIComponent(message)}`;
    } else {
      // On desktop, you could force the web.whatsapp.com link
      whatsappUrl = `https://web.whatsapp.com/send?phone=${phoneNumber}&text=${encodeURIComponent(message)}`;
    }
    // --- End of unnecessary logic ---

    window.open(whatsappUrl, '_blank');
  };

  return (
    <div className="group fixed bottom-6 right-6 sm:bottom-8 sm:right-8 flex items-center z-50">
      <span className="bg-green-500 text-white text-sm rounded-md px-3 py-2 transition-transform-opacity duration-300 transform-gpu scale-x-0 group-hover:scale-x-100 origin-right mr-2">
        Contact Us
      </span>
      <button
        onClick={handleWhatsAppClick}
        className="bg-green-500 text-white w-14 h-14 sm:w-16 sm:h-16 rounded-full shadow-lg flex items-center justify-center transition-transform transform hover:scale-110"
        aria-label="Contact on WhatsApp"
      >
        <img src={whatsappLogo} alt="WhatsApp" className="w-11 h-11" />
      </button>
    </div>
  );
};

const ApproveButton: React.FC<{ quote: Quote | null }> = ({ quote }) => {
  if (!quote) return null;

  const handleApproveClick = () => {
    const phoneNumber = "919442424492";
    const message = `Looks good! 👍 We confirm the *${quote.package_name}* for *${quote.no_of_persons}* adults at *${new Intl.NumberFormat('en-IN', { style: 'currency', currency: 'INR', maximumFractionDigits: 0 }).format(quote.total_cost)}*. ✅\n\nPlease let me know the next steps to confirm the booking ✈️ and the available payment options.`;
    const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    let whatsappUrl;
    if (isMobile) {
      // On mobile, use the universal link (which is the best practice for all devices anyway)
      whatsappUrl = `https://wa.me/${phoneNumber}?text=${encodeURIComponent(message)}`;
    } else {
      // On desktop, you could force the web.whatsapp.com link
      whatsappUrl = `https://web.whatsapp.com/send?phone=${phoneNumber}&text=${encodeURIComponent(message)}`;
    }
    // --- End of unnecessary logic ---
    window.open(whatsappUrl, '_blank');
  };

  return (
    <div className="flex justify-center">
      <button
        onClick={handleApproveClick}
        className="bg-green-500 text-white font-bold py-3 px-6 rounded-full shadow-lg flex items-center justify-center transition-transform transform hover:scale-110"
        aria-label="Approve Quote"
      >
        <i className="fas fa-check mr-2"></i> Approve
      </button>
    </div>
  );
};

/*const FloatingDownloadButton: React.FC<{ onClick: () => void; isVisible: boolean }> = ({ onClick, isVisible }) => {
  if (!isVisible) return null;

  return (
    <div className="group fixed top-6 right-6 sm:top-8 sm:right-8 flex items-center z-50">
      <button
        onClick={onClick}
        className="bg-blue-400/20 backdrop-blur-md text-blue-700 py-2 px-4 rounded-full text-xs sm:text-sm font-semibold shadow-lg z-10 border-white/30 uppercase tracking-wider"
        aria-label="Download PDF"
      >
        <i className="fas fa-download text-lg"></i>
      </button>
    </div>
  );
}; */

const Logo: React.FC = () => (
    <div className="flex justify-center items-center flex-col mb-4">
        <div className="relative z-10">
            <img 
                src="https://tripemilestone.in-maa-1.linodeobjects.com/logo%2Ftripxplo-logo-crop.png" 
                alt="TripXplo" 
                className="logo-image" 
                style={{ height: '80px'}} 
            />
        </div>
        <p className="md:text-sm lg:text-xl font-semibold text-black text-center">Your Happiness & Memories 😊</p>
    </div>
);

// Helper function to get meal plan description
const getMealPlanDescription = (code: string) => {
if (!code) return 'N/A';

switch (code.toUpperCase()) {
    case 'EP': return 'Room Only';
    case 'CP': return 'Breakfast Included';
    case 'MAP': return 'Breakfast and Dinner Included';
    case 'AP': return 'All Meals Included';
    default: return code;
    }
  };

const ItineraryDisplay = ({ itinerary, destination, trip_duration }: { itinerary: string | null, destination: string, trip_duration: string }) => {
  if (!itinerary) {
    return null; // Don't render anything if no itinerary is provided
  }

  const parsedItinerary = itinerary.split('\n\n').map((dayBlock, index) => {
    const lines = dayBlock.trim().split('\n');
    const title = lines[0];
    const description = lines.slice(1).join('\n');
    // Extract day number from title if possible, e.g., "Day 1: ..."
    const dayMatch = title.match(/Day\s*(\d+)/i);
    const dayNumber = dayMatch ? parseInt(dayMatch[1], 10) : index + 1;

    return { day: dayNumber, title, description };
  });

  return (
    <section className="mt-8 sm:mt-12 bg-white/80 backdrop-blur-sm p-4 sm:p-8 rounded-2xl shadow-lg border border-gray-200/50">
      <div className="itinerary-content">
        <div className="itinerary-header mb-4 sm:mb-6">
          <h4 className="text-2xl sm:text-3xl font-bold text-gray-800">
            <i className="fas fa-route mr-2 sm:mr-3 text-cyan-500"></i> Detailed Itinerary
          </h4>
          <p className="text-sm sm:text-base text-gray-600 mt-2">
            Day-wise breakdown of your {trip_duration} journey to {destination}
          </p>
        </div>

        <div className="itinerary-timeline relative border-l-2 border-cyan-200 pl-6 sm:pl-8">
          {parsedItinerary.map((day, index, arr) => (
            <div key={index} className={`day-item mb-8 sm:mb-10 relative ${index === arr.length - 1 ? 'last-day' : ''}`}>
              <div className="day-marker absolute -left-10 sm:-left-11 top-1 flex items-center justify-center w-7 h-7 sm:w-8 sm:h-8 rounded-full bg-cyan-500 text-white font-bold border-2 sm:border-4 border-white shadow-md">
                {day.day}
              </div>
              <div className="day-content bg-cyan-50 p-4 sm:p-6 rounded-xl shadow-sm">
                <div className="day-header flex justify-between items-center mb-2">
                  <h5 className="text-lg sm:text-xl font-bold text-cyan-800">{day.title}</h5>
                </div>
                <p className="day-description text-sm sm:text-base text-gray-700 leading-relaxed whitespace-pre-wrap">{day.description}</p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

const SharedQuote: React.FC = () => {
  const { quoteId } = useParams<{ quoteId: string }>();
  const [quote, setQuote] = useState<Quote | null>(null);
  const [hotelRows, setHotelRows] = useState<HotelRow[]>([]);
  const [inclusions, setInclusions] = useState<string[]>([]);
  const [exclusions, setExclusions] = useState<string[]>([]);
  const [itinerary, setItinerary] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isItineraryVisible, setIsItineraryVisible] = useState(false);
  const [isTermsVisible, setIsTermsVisible] = useState(false);
//  const [isPrinting, setIsPrinting] = useState(false);
  const [grandTotal, setGrandTotal] = useState<number>(0);
  const [showPopup, setShowPopup] = useState(false);
  const [isPopupActive, setIsPopupActive] = useState(false);
  //const [isHeaderVisible, setIsHeaderVisible] = useState(true);
  const grandTotalRef = useRef<HTMLDivElement>(null);
  const headerRef = useRef<HTMLElement>(null);

  /*const handleDownloadPdf = () => {
    setIsPrinting(true);
    setTimeout(() => {
      const input = document.getElementById('pdf-content');
      if (input) {
        html2canvas(input, {
          useCORS: true,
          scale: 2,
          windowWidth: input.scrollWidth,
          windowHeight: input.scrollHeight,
        }).then(canvas => {
          const imgData = canvas.toDataURL('image/jpeg', 0.7);
          const pdf = new jsPDF('p', 'mm', 'a4');
          const pdfWidth = pdf.internal.pageSize.getWidth();
          const pdfHeight = pdf.internal.pageSize.getHeight();
          const canvasWidth = canvas.width;
          const canvasHeight = canvas.height;
          const ratio = canvasWidth / canvasHeight;
          const width = pdfWidth;
          const height = width / ratio;

          let position = 0;
          let heightLeft = height;

          pdf.addImage(imgData, 'JPEG', 0, position, width, height);
          heightLeft -= pdfHeight;

          while (heightLeft >= 0) {
            position = heightLeft - height;
            pdf.addPage();
            pdf.addImage(imgData, 'JPEG', 0, position, width, height);
            heightLeft -= pdfHeight;
          }
          pdf.save(`quote-${quote?.id}.pdf`);
        }).finally(() => {
          setIsPrinting(false);
        });
      } else {
        setIsPrinting(false);
      }
    }, 500);
  }; */

  const getPlanStyle = (plan: string) => {
    const planName = plan || 'Standard';
    let color = 'gold'; // Default
    if (planName.toLowerCase().includes('platinum')) {
      color = '#E5E4E2';
    } else if (planName.toLowerCase().includes('silver')) {
      color = '#C0C0C0';
    }
    return { color, name: planName };
  };

  function getOnlineImageUrl(destination: string) {
    // Extract destination for image search
    const searchTerm = destination || 'travel destination';
    const cleanTerm = searchTerm.toLowerCase().replace(/[^a-z\s]/g, '').trim();

    // Map common destinations to trending open source images
    const imageMap = {
        'Goa': 'https://images.unsplash.com/photo-1512343879784-a960bf40e7f2?w=800&h=400&fit=crop',  
        'Munnar': 'https://images.unsplash.com/photo-1602216056096-3b40cc0c9944?w=800&h=400&fit=crop', 
        'Ooty': 'https://images.unsplash.com/photo-1580979222901-12a0e56abf7d?w=800&h=400&fit=crop',  
        'Kodaikanal': 'https://images.unsplash.com/photo-1593692716621-1e228b0a9224?w=800&h=400&fit=crop',  
        'Delhi': 'https://images.unsplash.com/photo-1576519465852-4d119fcccf33?w=800&h=400&fit=crop',  
        'Kerala': 'https://images.unsplash.com/photo-1519125323398-675f0ddb6308?w=800&h=400&fit=crop',  
        'Manali': 'https://images.unsplash.com/photo-1626621341517-bbf3d9990a23?w=800&h=400&fit=crop',  
        'Yercaud': 'https://images.unsplash.com/photo-1470770841072-f978cf4d019e?w=800&h=400&fit=crop',  
        'Andaman': 'https://images.unsplash.com/photo-1640718835374-6116a99c6e6c?w=800&h=400&fit=crop',  
        'Varkala': 'https://images.unsplash.com/photo-1697193375091-f9bfe3d7ed7b?w=800&h=400&fit=crop',  
        'Bali': 'https://images.unsplash.com/photo-1506744038136-46273834b3fb?w=800&h=400&fit=crop',  
        'Wayanad': 'https://images.unsplash.com/photo-1464983953574-0892a716854b?w=800&h=400&fit=crop', 
        'Coorg': 'https://images.unsplash.com/photo-1602216056096-3b40cc0c9944?w=800&h=400&fit=crop'   
    };

    // Find matching destination
    for (const [key, url] of Object.entries(imageMap)) {
      if (cleanTerm.includes(key.toLowerCase())) {
        return url;
      }
    }

    // Default fallback image
    return 'https://images.unsplash.com/photo-1470770841072-f978cf4d019e?w=800&h=400&fit=crop';
  }


  // Update the useEffect to include security checks
  useEffect(() => {
    const fetchQuote = async () => {
      try {
        setLoading(true);
        
        // Initialize Supabase client
        const config = getQuoteSupabaseConfig();
        const supabase = createClient(config.url, config.anonKey);
        
        // First, fetch the shared quote record
        const { data: sharedQuoteData, error: sharedQuoteError } = await supabase
          .from('shared_quotes')
          .select('*')
          .eq('quote_hash', quoteId) // quoteId parameter is actually the hash
          .single();
        
        if (sharedQuoteError) throw new Error('Link not found');
        if (!sharedQuoteData) throw new Error('Quote not found');
        
        // Security checks
        if (!sharedQuoteData.is_active) {
          throw new Error('This link has been disabled');
        }

        // Set inclusions and exclusions directly from shared_quotes
        if (sharedQuoteData.inclusions) {
          setInclusions(sharedQuoteData.inclusions);
        }
        
        if (sharedQuoteData.exclusions) {
          setExclusions(sharedQuoteData.exclusions);
        }

        // Now fetch the actual quote data
        const { data: quoteData, error: quoteError } = await supabase
          .from('quotes')
          .select('*')
          .eq('id', sharedQuoteData.quote_id)
          .single();

        if (sharedQuoteData.itinerary) {
          setItinerary(sharedQuoteData.itinerary);
        } else if (quoteData && quoteData.itinerary) {
          setItinerary(quoteData.itinerary);
        }
        
        if (quoteError) throw quoteError;
        if (!quoteData) throw new Error('Quote not found');
        
        setQuote(quoteData);
        setIsPopupActive(sharedQuoteData.is_popup_active);


        // Set grand total from the quote's total_cost
        if (quoteData.total_cost) {
            setGrandTotal(quoteData.total_cost);
        }
        
        // Fetch hotel rows - Use sharedQuoteData.quote_id instead of quoteId
        const { data: hotelRowsData, error: hotelRowsError } = await supabase
          .from('hotel_rows')
          .select('*')
          .eq('quote_id', sharedQuoteData.quote_id);
        
        if (hotelRowsError) throw hotelRowsError;
        
        // Transform hotel rows data
        const transformedHotelRows = hotelRowsData.map((row: HotelRowData) => ({
          hotelName: row.hotel_name,
          roomType: row.room_type,
          mealPlan: row.meal_plan,
          noOfRooms: row.no_of_rooms,
          stayNights: row.stay_nights,
          price: row.price,
          extraAdultCost: row.extra_adult_cost,
          childrenCost: row.children_cost,
          infantCost: row.infant_cost,
          stayPrice: row.stay_price,
          gstAmount: row.gst_amount,
          currency: row.currency
        }));
        
        setHotelRows(transformedHotelRows);
        
      } catch (err) {
        console.error('Error fetching quote:', err);
        setError((err as Error).message);
      } finally {
        setLoading(false);
      }
    };
    
    if (quoteId) {
      fetchQuote();
    }
  }, [quoteId]);


  useEffect(() => {
    if (loading || !isPopupActive || !grandTotalRef.current) {
      return;
    }

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setTimeout(() => {
            setShowPopup(true);
          }, 2000);
          observer.disconnect();
        }
      },
      {
        threshold: 0.5,
      }
    );

    if (grandTotalRef.current) {
      observer.observe(grandTotalRef.current);
    }

    return () => observer.disconnect();
  }, [loading, isPopupActive]);

  /*useEffect(() => {
    const handleScroll = () => {
      if (headerRef.current) {
        const { bottom } = headerRef.current.getBoundingClientRect();
        setIsHeaderVisible(bottom > 0);
      }
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    handleScroll(); // Initial check

    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);*/


  // Format currency
  const formatCurrency = (amount: number, currency: string = 'INR') => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: currency,
      maximumFractionDigits: 0
    }).format(amount);
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-[#d0ffe1]">
        <div className="loader"></div>
      </div>
    );
  }

  if (error || !quote) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-[#d0ffe1]">
        <div className="max-w-md w-full bg-white shadow-2xl rounded-2xl p-10">
          <div className="text-center">
            <svg className="w-20 h-20 text-red-500 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <h2 className="mt-6 text-2xl font-bold text-gray-800">Quote Not Found</h2>
            <p className="mt-3 text-gray-600">{error || 'The requested quote could not be found.'}</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-[#d0ffe1] p-4 sm:p-8 md:p-14 font-sans">
      <style>
        {`
          @media print, .print-mode {
            body {
              -webkit-print-color-adjust: exact;
              print-color-adjust: exact;
            }
            .hotel-container {
              page-break-inside: avoid !important;
            }
            .hotel-container:not(:first-child) {
               page-break-before: always !important;
            }
          }
        `}
      </style>
      <div id="pdf-content" className="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <header
          ref={headerRef}
          className="relative text-white shadow-2xl p-8 sm:p-12 md:p-14 rounded-3xl flex flex-col justify-center items-center min-h-[250px] sm:min-h-[300px] md:min-h-[350px] overflow-hidden"
        >
          <img
            src={getOnlineImageUrl(quote.destination)}
            crossOrigin="anonymous"
            alt="Destination"
            className="absolute inset-0 w-full h-full object-cover"
          />
          <div className="absolute inset-0 bg-black bg-opacity-40 rounded-3xl" />
          <div className="absolute top-4 left-4 right-4 flex justify-between items-center">
            <div className="bg-white/20 backdrop-blur-md text-white py-2 px-4 rounded-full text-xs sm:text-sm font-semibold shadow-lg z-10 border-2 border-white/30 uppercase tracking-wider">
                <svg className="crown-icon" fill="currentColor" viewBox="0 0 24 24" style={{ width: '18px', height: '18px', marginRight: '6px', display: 'inline-block', verticalAlign: 'middle' }}>
                    <path d="M5 16L3 6l5.5 4L12 4l3.5 6L21 6l-2 10H5zm2.7-2h8.6l.9-4.4L14 12l-2-3.4L10 12l-3.2-2.4L7.7 14z"></path>
                  </svg>
                  <span style={{ color: getPlanStyle(quote.plan).color, verticalAlign: 'middle' }}>
                  {getPlanStyle(quote.plan).name} Plan
                </span>
              </div>
              <div className="flex items-center gap-4">
                <div className="bg-white/20 backdrop-blur-md text-white py-2 px-4 rounded-full text-xs sm:text-sm font-semibold shadow-lg z-10 border-2 border-white/30 uppercase tracking-wider">
                  <svg className="duration-icon mr-2" fill="currentColor" viewBox="0 0 24 24" style={{ width: '18px', height: '18px', display: 'inline-block', verticalAlign: 'middle' }}>
                    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                  </svg>
                  <span style={{ color: '#00f4ff', verticalAlign: 'middle' }}>
                    {quote.trip_duration}
                  </span>
                </div>
              </div>
            </div>
          <div className="relative z-10 text-center mt-5 ">
            <h1 className="text-2xl sm:text-3xl md:text-5xl font-bold tracking-tight text-white drop-shadow-xl break-words">
              {quote.package_name}
            </h1>
            <p className="mt-2 sm:mt-4 text-lg sm:text-xl md:text-2xl text-gray-100 drop-shadow-lg break-words">
              Your Customised Trip to {quote.destination}
            </p>
          </div>
        </header>

        <main className="py-8">
          <div className="flex flex-col lg:flex-row lg:gap-10">
            {/* Left Column */}
            <div className="lg:w-2/3 space-y-8">
              {/* Customer & Trip Details */}
              <section className="bg-white/80 backdrop-blur-sm p-6 rounded-2xl shadow-lg border border-gray-200/50 transition-all duration-300 hover:shadow-xl hover:border-gray-400">
                <Logo />
                <h2 className="text-xl sm:text-2xl font-bold text-gray-800 mb-6 text-center sm:text-left">
                  <i className="fas fa-info-circle mr-3 text-blue-500"></i> Your Trip Details
                </h2>
                <div className="grid grid-cols-2 md:grid-cols-6 gap-6 mb-6">
                  <div className="col-span-1 md:col-span-3 text-center bg-gradient-to-br from-teal-50 to-green-100 p-4 rounded-2xl shadow-md border border-teal-200/70 transition-all duration-300 hover:shadow-xl hover:border-teal-400">
                    <p className="text-xs sm:text-sm text-green-800 font-semibold">Customer Name</p>
                    <p className="font-bold text-lg sm:text-xl text-green-900">
                      {quote.customer_name}
                    </p>
                  </div>
                  <div className="col-span-1 md:col-span-3 text-center bg-gradient-to-br from-teal-50 to-green-100 p-4 rounded-2xl shadow-md border border-teal-200/70 transition-all duration-300 hover:shadow-xl hover:border-teal-400">
                    <p className="text-xs sm:text-sm text-green-800 font-semibold">Destination</p>
                    <p className="font-bold text-lg sm:text-xl text-green-900">
                      {quote.destination}
                    </p>
                  </div>
                  <div className="col-span-1 md:col-span-2 text-center bg-gradient-to-br from-teal-50 to-green-100 p-4 rounded-2xl shadow-md border border-teal-200/70 transition-all duration-300 hover:shadow-xl hover:border-teal-400">
                    <p className="text-xs sm:text-sm text-green-800 font-semibold">Nights</p>
                    <p className="font-bold text-lg sm:text-xl text-green-900">
                      {quote.trip_duration}
                    </p>
                  </div>
                 {quote.travel_date && (
                    <div className="md:col-span-2 text-center bg-gradient-to-br from-teal-50 to-green-100 p-4 rounded-2xl shadow-md border border-teal-200/70 transition-all duration-300 hover:shadow-xl hover:border-teal-400">
                      <p className="text-xs sm:text-sm text-green-800 font-semibold"> Tenetative Travel Date</p>
                      <p className="font-bold text-lg sm:text-xl text-green-900">
                        {new Date(quote.travel_date).toLocaleDateString('en-GB', { day: 'numeric', month: 'short', year: 'numeric' })}
                      </p>
                    </div>
                  )}
                  <div className="col-span-1 md:col-span-2 text-center bg-gradient-to-br from-teal-50 to-green-100 p-4 rounded-2xl shadow-md border border-teal-200/70 transition-all duration-300 hover:shadow-xl hover:border-teal-400">
                    <p className="text-xs sm:text-sm text-green-800 font-semibold">Package Type</p>
                    <p className="font-bold text-base sm:text-lg text-green-900">
                      {quote.package_type || "Standard"}
                    </p>
                  </div>
                  <div className="col-span-1 md:col-span-2 text-center bg-gradient-to-br from-teal-50 to-green-100 p-4 rounded-2xl shadow-md border border-teal-200/70 transition-all duration-300 hover:shadow-xl hover:border-teal-400">
                    <p className="text-xs sm:text-sm text-green-800 font-semibold">Family Type</p>
                    <p className="font-bold text-lg sm:text-xl text-green-900">
                      {quote.family_type.split(' - ')[0]}
                    </p>
                  </div>
                  {quote.customer_phone && (
                  <div className="md:col-span-2 text-center bg-gradient-to-br from-teal-50 to-green-100 p-4 rounded-2xl shadow-md border border-teal-200/70 transition-all duration-300 hover:shadow-xl hover:border-teal-400">
                    <p className="text-xs sm:text-sm text-green-800 font-semibold">Phone Number</p>
                    <p className="font-bold text-base sm:text-lg text-green-900">
                        {typeof quote.customer_phone === 'string' && quote.customer_phone.length > 6
                            ? `XXXXXX${quote.customer_phone.slice(6)}`
                            : quote.customer_phone}
                      </p>
                  </div>
                  )}
                  
                  <div className="col-span-1 md:col-span-2 text-center bg-gradient-to-br from-teal-50 to-green-100 p-4 rounded-2xl shadow-md border border-teal-200/70 transition-all duration-300 hover:shadow-xl hover:border-teal-400">
                    <p className="text-xs sm:text-sm text-green-800 font-semibold">Travelers</p>
                    <p className="font-bold text-base sm:text-lg text-green-900">
                      {(quote.no_of_persons || 0) + (quote.extra_adults || 0)} Adults
                      {quote.children > 0 && `, ${quote.children} Children`}
                      {quote.infants > 0 && `, ${quote.infants} Infants`}
                    </p>
                  </div>
                </div>
                <div className="mt-6">
                  <h3 className="text-lg sm:text-xl font-semibold text-gray-800 mb-3">
                    <i className="fas fa-box-open mr-3 text-yellow-500"></i> About This Package
                  </h3>
                  <p className="text-gray-700 leading-relaxed">
                    experience the beauty and charm of <strong>{quote.destination}</strong> with our specially crafted <strong>{quote.trip_duration}</strong> family package. This package offers a blend of comfort, adventure, 
                    and relaxation. Immerse your family in lush landscapes and vibrant local culture, while enjoying 
                    guided sightseeing, cozy accommodations, and authentic cuisine. Enjoy your trip with us! 🚀
                  </p>
                </div>
              </section>

              {/* Hotel & Cost Details */}
              <section className="bg-white/80 backdrop-blur-sm p-6 rounded-2xl shadow-lg border border-gray-200/50 transition-all duration-300 hover:shadow-xl hover:border-gray-400">
                <h2 className="text-2xl sm:text-3xl font-bold text-gray-800 mb-6 text-center sm:text-left">
                  <i className="fas fa-hotel mr-3 text-teal-500"></i> Accommodation Details
                </h2>
                <div className="space-y-6">
                  {hotelRows.map((hotel, index) => (
                    <div key={index} className="hotel-container bg-gradient-to-br from-teal-50 to-green-100 p-4 sm:p-6 rounded-2xl shadow-md border border-teal-200/70 transition-all duration-300 hover:shadow-xl hover:border-teal-400">
                      <div className="flex-grow text-center">
                        <h3 className="text-xl sm:text-2xl font-bold text-teal-800">{hotel.hotelName}</h3>
                        <p className="text-teal-600 font-semibold my-2 text-sm sm:text-base">{hotel.roomType}</p>
                        <div className="mt-4 pt-4 border-t border-teal-200">
                          {/* Desktop view */}
                          <div className="hidden sm:grid sm:grid-cols-3 sm:gap-4 text-center">
                            <div>
                              <i className="fas fa-utensils text-lg sm:text-xl text-teal-500 mb-1"></i>
                              <p className="text-xs sm:text-sm text-gray-600 font-semibold">Meal Plan</p>
                              <p className="font-bold text-sm sm:text-base text-teal-700">{getMealPlanDescription(hotel.mealPlan)}</p>
                            </div>
                            <div>
                              <i className="fas fa-door-open text-lg sm:text-xl text-teal-500 mb-1"></i>
                              <p className="text-xs sm:text-sm text-gray-600 font-semibold">Rooms</p>
                              <p className="font-bold text-sm sm:text-base text-teal-700">{hotel.noOfRooms}</p>
                            </div>
                            <div>
                              <i className="fas fa-moon text-lg sm:text-xl text-teal-500 mb-1"></i>
                              <p className="text-xs sm:text-sm text-gray-600 font-semibold">Nights</p>
                              <p className="font-bold text-sm sm:text-base text-teal-700">{hotel.stayNights}</p>
                            </div>
                          </div>
                          {/* Mobile view */}
                          <div className="sm:hidden space-y-3">
                            <div className="bg-[#e3fcee] p-3 rounded-lg flex items-center justify-between">
                              <div className="flex items-center text-sm text-gray-600 font-semibold">
                                <i className="fas fa-utensils text-teal-500 w-6 text-center mr-2"></i>
                                <span>Meal Plan</span>
                              </div>
                              <p className="font-bold text-sm text-teal-700">{getMealPlanDescription(hotel.mealPlan)}</p>
                            </div>
                            <div className="bg-[#e3fcee] p-3 gap-12 rounded-lg flex items-center">
                              <div className="flex items-center text-sm text-gray-600 font-semibold">
                                <i className="fas fa-door-open text-teal-500 w-6 text-center mr-2"></i>
                                <span>Rooms</span>
                              </div>
                              <p className="font-bold text-sm text-teal-700 ml-2">{hotel.noOfRooms}</p>
                            </div>
                            <div className="bg-[#e3fcee] p-3 gap-12 rounded-lg flex items-center">
                              <div className="flex items-center text-sm text-gray-600 font-semibold">
                                <i className="fas fa-moon text-teal-500 w-6 text-center mr-2"></i>
                                <span>Nights</span>
                              </div>
                              <p className="font-bold text-sm text-teal-700 ml-2">{hotel.stayNights}</p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
                <div className="mt-8 pt-6 border-t-2 border-dashed border-gray-300" ref={grandTotalRef}>
                  <div className="flex flex-col sm:flex-row justify-between items-center text-center sm:text-left">
                    <span className="text-xl sm:text-2xl font-bold text-gray-900 mb-2 sm:mb-0">
                      Grand Total:
                    </span>
                    <div className="flex items-baseline justify-end space-x-2">
                      <span className="text-3xl sm:text-4xl font-extrabold text-green-600">
                        {formatCurrency(grandTotal, quote.currency)}
                      </span>
                      <span className="text-xs text-gray-600">(inc. GST)</span>
                    </div>
                  </div>
                  {(quote.no_of_persons + quote.extra_adults + quote.children) > 0 && (
                    <div className="text-center sm:text-right mt-2 text-sm sm:text-lg font-semibold text-gray-700">
                      (Per Person: {formatCurrency(grandTotal / (quote.no_of_persons + quote.extra_adults), quote.currency)})
                    </div>
                  )}
                </div>
              </section>
              {/* View Itinerary Section - Desktop */}
              <section className="hidden lg:block bg-white/80 backdrop-blur-sm p-6 rounded-2xl shadow-lg border border-gray-200/50 transition-all duration-300 hover:shadow-xl hover:border-gray-400">
                <h2 className="text-xl sm:text-2xl font-bold text-gray-800 cursor-pointer flex justify-between items-center" onClick={() => setIsItineraryVisible(!isItineraryVisible)}>
                  <span>
                    <i className="fas fa-route mr-3 text-cyan-500"></i> View Itinerary
                  </span>
                  <i className={`fas ${isItineraryVisible ? 'fa-chevron-up' : 'fa-chevron-down'} text-cyan-500 transition-transform duration-300`}></i>
                </h2>
                {isItineraryVisible && (
                  <ItineraryDisplay itinerary={itinerary} destination={quote.destination} trip_duration={quote.trip_duration} />
                )}
              </section>
            </div>

            {/* Right Column (Sticky) */}
            <div className="lg:w-1/3 space-y-8 mt-8 lg:mt-0">
              <div className="lg:sticky top-8">
                {/* Inclusions & Exclusions */}
                <section className="bg-white/80 backdrop-blur-sm p-6 rounded-2xl shadow-lg border border-gray-200/50 transition-all duration-300 hover:shadow-xl hover:border-gray-400">
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-1 gap-6">
                      <div>
                        <h2 className="text-xl sm:text-2xl font-bold text-gray-800 mb-4 flex items-center">
                          <i className="fas fa-check-circle mr-3 text-green-500"></i> What's Included
                        </h2>
                        <div className="bg-green-50 border border-green-200 rounded-lg p-4 flex items-center transition-all duration-300 hover:shadow-md hover:bg-green-100">
                          <ul className="space-y-2">
                            {inclusions.length > 0 ? (
                              inclusions.map((item, index) => (
                                <li key={index} className="flex items-center text-emerald-950 font-semibold text-sm sm:text-base">
                                  <span>{item.replace('•', '').trim()}</span>
                                </li>
                              ))
                            ) : (
                              <p className="text-gray-500">No inclusions specified.</p>
                            )}
                          </ul>
                        </div>
                      </div>
                      <div>
                        <h2 className="text-xl sm:text-2xl font-bold text-gray-800 mb-4 flex items-center">
                          <i className="fas fa-times-circle mr-3 text-red-500"></i> What's Not Included
                        </h2>
                        <div className="bg-red-50 border border-red-200 rounded-lg p-4 flex items-center transition-all duration-300 hover:shadow-md hover:bg-red-100">
                          <ul className="space-y-2">
                            {exclusions.length > 0 ? (
                              exclusions.map((item, index) => (
                                <li key={index} className="flex items-center font-semibold text-red-950 text-sm sm:text-base">
                                  <span>{item.replace('•', '').trim()}</span>
                                </li>
                              ))
                            ) : (
                              <p className="text-gray-500">No exclusions specified.</p>
                            )}
                          </ul>
                        </div>
                      </div>
                      </div>
                </section>
              {/* View Itinerary Section - Mobile */}
              <section className="lg:hidden bg-white/80 mt-8 backdrop-blur-sm p-6 rounded-2xl shadow-lg border border-gray-200/50 transition-all duration-300 hover:shadow-xl hover:border-gray-400">
                <h2 className="text-xl sm:text-2xl font-bold text-gray-800 cursor-pointer flex justify-between items-center" onClick={() => setIsItineraryVisible(!isItineraryVisible)}>
                  <span>
                    <i className="fas fa-route mr-3 text-cyan-500"></i> View Itinerary
                  </span>
                  <i className={`fas ${isItineraryVisible ? 'fa-chevron-up' : 'fa-chevron-down'} text-cyan-500 transition-transform duration-300`}></i>
                </h2>
                {isItineraryVisible && (
                  <ItineraryDisplay itinerary={itinerary} destination={quote.destination} trip_duration={quote.trip_duration} />
                )}
              </section>
              <section className="bg-white/80 mt-8 backdrop-blur-sm p-6 rounded-2xl shadow-lg border border-gray-200/50 transition-all duration-300 hover:shadow-xl hover:border-gray-400">
                <h2 className="text-xl sm:text-2xl font-bold text-gray-800 cursor-pointer flex justify-between items-center" onClick={() => setIsTermsVisible(!isTermsVisible)}>
                  <span>
                    <i className="fas fa-file-contract mr-3 text-cyan-500"></i> Terms & Conditions
                  </span>
                  <i className={`fas ${isTermsVisible ? 'fa-chevron-up' : 'fa-chevron-down'} text-cyan-500 transition-transform duration-300`}></i>
                </h2>
                {isTermsVisible && (
                  <div className="space-y-6">
                    <p className="text-gray-700 leading-relaxed bg-gray-100 p-4 rounded-lg text-sm sm:text-base">
                      All prices are subject to change without prior notice. The booking is not confirmed until a confirmation voucher is issued. All bookings are subject to availability.
                    </p>
                    {/* Payment Policy Card */}
                    <div className="bg-gradient-to-br from-blue-50 to-indigo-100 p-4 sm:p-6 rounded-xl shadow-md border border-blue-200">
                      <h3 className="text-lg sm:text-xl font-bold text-blue-800 mb-4">
                        <i className="fas fa-credit-card mr-3"></i> Payment Policy
                      </h3>
                      <ul className="space-y-2 text-sm sm:text-base">
                        <li className="flex items-start">
                          <i className="fas fa-check-circle text-blue-500 mt-1 mr-3"></i>
                          <span><strong>50%</strong> of the total amount to be paid at the time of booking.</span>
                        </li>
                        <li className="flex items-start">
                          <i className="fas fa-check-circle text-blue-500 mt-1 mr-3"></i>
                          <span>Remaining <strong>50%</strong> to be paid 15 days prior to the travel date.</span>
                        </li>
                      </ul>
                    </div>
                    {/* Cancellation Policy Card */}
                    <div className="bg-gradient-to-br from-red-50 to-pink-100 p-4 sm:p-6 rounded-xl shadow-md border border-red-200">
                      <h3 className="text-lg sm:text-xl font-bold text-red-800 mb-4">
                        <i className="fas fa-exclamation-triangle mr-3"></i> Cancellation Policy
                      </h3>
                      <ul className="space-y-2 text-sm sm:text-base">
                        <li className="flex items-start">
                          <i className="fas fa-times-circle text-red-500 mt-1 mr-3"></i>
                          <span>Cancellations made <strong>30 days before</strong> travel will incur a <strong>25%</strong> cancellation fee.</span>
                        </li>
                        <li className="flex items-start">
                          <i className="fas fa-times-circle text-red-500 mt-1 mr-3"></i>
                          <span>Cancellations made between <strong>15-30 days</strong> will incur a <strong>50%</strong> cancellation fee.</span>
                        </li>
                        <li className="flex items-start">
                          <i className="fas fa-times-circle text-red-500 mt-1 mr-3"></i>
                          <span>Cancellations made within <strong>15 days</strong> of travel are <strong>non-refundable</strong>.</span>
                        </li>
                      </ul>
                    </div>
                  </div>
                )}
              </section>
              </div>
            </div>
          </div>
          <div className="mt-12 text-center">
            <ApproveButton quote={quote} />
          </div>
        </main>

        <footer className="mt-12 text-center pb-8">
          <p className="text-xs sm:text-sm text-gray-700">
            For assistance, please contact <a href="https://tripxplo.com" className="text-blue-500 hover:underline">TripXplo</a>.
          </p>
          <p className="mt-2 text-xs text-gray-600">
            © {new Date().getFullYear()} TripXplo. All rights reserved.
          </p>
        </footer>
      </div>
      <FloatingWhatsAppButton quote={quote} />
      {/* <FloatingDownloadButton onClick={handleDownloadPdf} isVisible={!isHeaderVisible} /> */}
      {showPopup && <EnquiryPopup onClose={() => setShowPopup(false)} quote={quote} quoteId={quoteId} />}
    </div>
  );
};

export default SharedQuote;
