export interface TravelData {
  title: string
  date: string
  destination: string
  packageDetails: string
  itinerary: string
  plan: string
  inclusions: string
  exclusions: string
  price: string
  overlays: string
  currency?: string
}

export interface ElementStyle {
  fontFamily: string
  fontSize: number
  fontWeight: string
  color: string
  backgroundColor: string
  padding: number
  borderRadius: number
  textAlign: "left" | "center" | "right"
  alignItems?: "flex-start" | "center" | "flex-end"
  opacity: number
  textTransform?: string
  lineHeight?: string | number
  border?: string
  letterSpacing?: string
  textShadow?: string
  boxShadow?: string
}

export interface TemplateElement {
  id: string
  type: "text" | "price" | "image" | "badge" | "divider"
  content: string
  position: { x: number; y: number }
  size: { width: number; height: number }
  style: ElementStyle
  field?: keyof TravelData
  locked?: boolean
}

export interface TemplateLayout {
  id: string
  name: string
  dimensions: { width: number; height: number }
  displaySize: { width: number; height: number }
  elements: TemplateElement[]
  backgroundStyle: {
    backgroundColor: string
    backgroundImage: string
    overlay: boolean
    overlayOpacity: number
  }
}

export interface BrandColor {
  id: string
  name: string
  hex: string
  type: "primary" | "secondary" | "accent" | "custom"
}

export interface BrandLogo {
  id: string
  name: string
  url: string
  file: File | null
  type: "full-color" | "monochrome" | "white" | "custom"
  isPrimary?: boolean
}

export interface BrandFont {
  id: string
  name: string
  file: File | null
  url?: string
  fontFamily: string
  type: "headline" | "body" | "custom"
  isDefault?: boolean
}

export interface BrandKit {
  id: string
  name: string
  colors: BrandColor[]
  logos: BrandLogo[]
  fonts: BrandFont[]
  createdAt: Date
  updatedAt: Date
}
