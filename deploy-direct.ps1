# Direct Deployment Script for TripXplo CRM
Write-Host "🚀 Direct Deployment to crm.tripxplo.com" -ForegroundColor Green
Write-Host "=========================================" -ForegroundColor Green

$SERVER_IP = "*************"
$SERVER_USER = "root"
$DOMAIN = "crm.tripxplo.com"

Write-Host "📦 Uploading built files to server..." -ForegroundColor Cyan

# Create deployment directory on server
& ssh "$SERVER_USER@$SERVER_IP" "mkdir -p /var/www/crm && rm -rf /var/www/crm/*"

if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Failed to prepare server directory" -ForegroundColor Red
    exit 1
}

# Upload all files from dist folder
Write-Host "📤 Uploading application files..." -ForegroundColor Cyan
& scp -r "dist/*" "$SERVER_USER@$SERVER_IP`:/var/www/crm/"

if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Failed to upload files" -ForegroundColor Red
    exit 1
}

Write-Host "✅ Files uploaded successfully" -ForegroundColor Green

# Create and upload Nginx configuration
$nginxConfig = @'
server {
    listen 80;
    server_name crm.tripxplo.com www.crm.tripxplo.com;
    
    root /var/www/crm;
    index index.html index.htm;
    
    # Handle React Router (SPA routing)
    location / {
        try_files $uri $uri/ /index.html;
    }
    
    # Cache static assets
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot|map)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    
    # CORS headers for Supabase
    add_header Access-Control-Allow-Origin "*" always;
    add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS" always;
    add_header Access-Control-Allow-Headers "DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization" always;
    
    # Handle preflight OPTIONS requests
    if ($request_method = 'OPTIONS') {
        add_header Access-Control-Allow-Origin "*";
        add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS";
        add_header Access-Control-Allow-Headers "DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization";
        add_header Access-Control-Max-Age 1728000;
        add_header Content-Type "text/plain charset=UTF-8";
        add_header Content-Length 0;
        return 204;
    }
}
'@

$nginxConfig | Out-File -FilePath "nginx-crm.conf" -Encoding UTF8

Write-Host "🌐 Configuring Nginx..." -ForegroundColor Cyan

# Upload Nginx config
& scp "nginx-crm.conf" "$SERVER_USER@$SERVER_IP`:/etc/nginx/sites-available/$DOMAIN"

# Configure server
$serverCommands = @"
# Set permissions
chown -R www-data:www-data /var/www/crm
chmod -R 755 /var/www/crm

# Enable site
ln -sf /etc/nginx/sites-available/$DOMAIN /etc/nginx/sites-enabled/
rm -f /etc/nginx/sites-enabled/default

# Test and reload Nginx
nginx -t && systemctl reload nginx

echo "✅ Nginx configured and reloaded"

# Setup SSL if certbot is available
if command -v certbot > /dev/null 2>&1; then
    echo "🔒 Setting up SSL certificate..."
    certbot --nginx -d $DOMAIN -d www.$DOMAIN --non-interactive --agree-tos --email <EMAIL> --redirect || echo "⚠️ SSL setup failed"
else
    echo "📦 Installing Certbot..."
    apt update && apt install -y certbot python3-certbot-nginx
    echo "🔒 Setting up SSL certificate..."
    certbot --nginx -d $DOMAIN -d www.$DOMAIN --non-interactive --agree-tos --email <EMAIL> --redirect || echo "⚠️ SSL setup failed"
fi

echo "🎉 Deployment completed!"
echo "🌐 Visit: https://$DOMAIN"
"@

Write-Host "⚙️ Configuring server..." -ForegroundColor Cyan

# Execute server configuration
& ssh "$SERVER_USER@$SERVER_IP" $serverCommands

if ($LASTEXITCODE -eq 0) {
    Write-Host ""
    Write-Host "🎉 Deployment completed successfully!" -ForegroundColor Green
    Write-Host "🌐 Your CRM is now live at: https://crm.tripxplo.com" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "✅ What was deployed:" -ForegroundColor Yellow
    Write-Host "• Enhanced lead editing functionality" -ForegroundColor White
    Write-Host "• Material Design Trello-like Kanban board" -ForegroundColor White
    Write-Host "• Priority management system" -ForegroundColor White
    Write-Host "• Real-time updates and activity logging" -ForegroundColor White
    Write-Host "• Smart auto-loading (once per session)" -ForegroundColor White
    Write-Host ""
    Write-Host "🧪 Testing deployment..." -ForegroundColor Cyan
    
    # Test the deployment
    try {
        $response = Invoke-WebRequest -Uri "http://crm.tripxplo.com" -Method Head -TimeoutSec 10
        Write-Host "✅ HTTP test passed (Status: $($response.StatusCode))" -ForegroundColor Green
    } catch {
        Write-Host "⚠️ HTTP test failed - DNS may still be propagating" -ForegroundColor Yellow
    }
    
} else {
    Write-Host "❌ Server configuration failed" -ForegroundColor Red
}

# Clean up local files
Remove-Item "nginx-crm.conf" -ErrorAction SilentlyContinue

Write-Host ""
Write-Host "📋 Next steps:" -ForegroundColor Green
Write-Host "1. Test your CRM at https://crm.tripxplo.com" -ForegroundColor White
Write-Host "2. Verify all lead editing features work" -ForegroundColor White
Write-Host "3. For future updates: npm run build + run this script" -ForegroundColor White 