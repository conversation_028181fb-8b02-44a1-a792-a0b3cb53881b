import React, { useState, useEffect, useRef } from 'react';

const FAMILY_TYPES = [
    { id: "BB-1", name: "Baby Bliss - 2 Adults + 1 Infant (Below 2 yrs)" },
    { id: "TD-2", name: "Tiny Delight - 2 Adults + 1 Child (Below 5 yrs)" },
    { id: "FN-3", name: "Family Nest - 2 Adults + 2 Child (Below 5 yrs)" },
    { id: "NN-4", name: "Nebula Nest - 2 Adults + 1 Children (5 yrs to 11 yrs)" },
    { id: "SD-5", name: "Stellar Duo - 2 Adults + 1 Child (2 to 5 yrs) + 1 Children (5 to 11 yrs)" },
    { id: "ST-6", name: "Stellar Teen Duo - 2 Adults + 1 Child (2 to 5 yrs) + 1 Children (5 to 11 yrs) + 1 Teenager (Above 11 yrs)" },
    { id: "OD-7", name: "Orbiting Duo - 1 Adult + 1 Child (Below 5 yrs)" },
    { id: "OD-8", name: "Orbiting Duo+ - 1 Adult + 1 Children (5 yrs to 11 yrs)" },
    { id: "FF-9", name: "Fantastic Four - 2 Adults + 2 Children (5 yrs to 11 yrs)" },
    { id: "PP-10", name: "Parent Plus Two - 1 Adult + 2 Children (Below 5 yrs)" },
    { id: "TT-11", name: "Teen Trek - 2 Adults + 1 Teenager (Above 11 yrs)" },
    { id: "CC-12", name: "Cosmic Combo - 2 Adults + 1 Child (Below 5 yrs) + 1 Teenager (Above 11 yrs)" },
    { id: "DF-13", name: "Dynamic Family Duo+ - 2 Adults + 2 Teenagers (Above 11 yrs)" },
    { id: "MM-14", name: "Mix Match Clan - 2 Adults + 3 Children (Mix of age groups)" },
    { id: "GB-15", name: "Grand Bliss - 2 Adults + 1 Infant (Below 2 yrs) + 2 Grandparents" },
    { id: "GD-16", name: "Grand Delight - 2 Adults + 1 Child (Below 5 yrs) + 2 Grandparents" },
    { id: "GF-17", name: "Grand Family Nest - 2 Adults + 2 Child (Below 5 yrs) + 2 Grandparents" },
    { id: "GN-18", name: "Grand Nebula - 2 Adults + 1 Child (5 yrs to 11 yrs) + 2 Grandparents" },
    { id: "SG-19", name: "Stellar Grand Duo - 2 Adults + 1 Child (2 to 5 yrs) + 1 Children (5 to 11 yrs) + 2 Grandparents" },
    { id: "ST-20", name: "Stellar Teen Duo - 2 Adults + 1 Child (2 to 5 yrs) + 1 Children (5 to 11 yrs) + 1 Teenager (Above 11 yrs) + 2 Grandparents" },
    { id: "OG-21", name: "Orbiting Grand Duo - 1 Adult + (Below 5 yrs) + 2 Grandparents" },
    { id: "OG-22", name: "Orbiting Grand Duo+ - 1 Adult + 1 Children (5 yrs to 11 yrs) + 2 Grandparents" },
    { id: "GF-23", name: "Grand Fantastic Four - 2 Adults + 2 Children (5 yrs to 11 yrs) + 2 Grandparents" },
    { id: "GP-24", name: "Grandparent Plus One - 1 Adult + 2 Children (Below 5 yrs) + 2 Grandparents" },
    { id: "TT-25", name: "Teen Trek with Grand - 2 Adults + 1 Teenager (Above 11 yrs) + 2 Grandparents" },
    { id: "CG-26", name: "Cosmic Grand Combo - 2 Adults + 1 Child (Below 5 yrs) + 1 Teenager (Above 11 yrs) + 2 Grandparents" },
    { id: "DG-27", name: "Dynamic Grand Duo+ - 2 Adults + 2 Teenagers (Above 11 yrs) + 2 Grandparents" },
    { id: "GM-28", name: "Grand Mix Match Clan - 2 Adults + 3 Children (Mix of age groups) + 2 Grandparents" },
    { id: "D-29", name: "Duo - 2 Adults" },
    { id: "CD-30", name: "Cosmic Duo+ - 2 Adults + 1 Extra Adult (Above 11 yrs)" },
    { id: "EC-31", name: "Extended Cosmic Duo+ - 2 Adults + 2 Extra Adult (Above 11 yrs)" },
    { id: "DC-32", name: "Dynamic Cosmic Duo - 5 Adults" },
    { id: "DC-33", name: "Dynamic Cosmic Duo+ - 5 Adults + 1 Extra Adult (Above 11 yrs)" },
    { id: "DC-34", name: "Honeymoon Couple" },
    { id: "DC-35", name: "Couple Trip" },
    { id: "DC-36", name: "Family Trip" },
    { id: "DC-37", name: "Solo Trip" },
    { id: "DC-38", name: "Friends Trip" },
    { id: "DC-39", name: "Group Trip" }    
];

interface Props {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
}

export const FamilyTypeAutocomplete: React.FC<Props> = ({
  value,
  onChange,
  placeholder = "Enter family type"
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [suggestions, setSuggestions] = useState<string[]>([]);
  const wrapperRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (wrapperRef.current && !wrapperRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleInputChange = (inputValue: string) => {
    onChange(inputValue);
    if (inputValue.trim() === '') {
      setSuggestions(FAMILY_TYPES.map(type => type.name));
    } else {
      const filtered = FAMILY_TYPES.filter(type =>
        type.name.toLowerCase().includes(inputValue.toLowerCase())
      ).map(type => type.name);
      setSuggestions(filtered);
    }
    setIsOpen(true);
  };

  const handleFocus = () => {
    setSuggestions(FAMILY_TYPES.map(type => type.name));
    setIsOpen(true);
  };

  return (
    <div ref={wrapperRef} className="relative">
      <input
        type="text"
        value={value}
        onChange={(e) => handleInputChange(e.target.value)}
        onFocus={handleFocus}
        placeholder={placeholder}
        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
      />
      {isOpen && (
        <ul className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-auto">
          {suggestions.map((suggestion, index) => (
            <li
              key={index}
              onClick={() => {
                onChange(suggestion);
                setIsOpen(false);
              }}
              className="px-4 py-2 hover:bg-blue-50 cursor-pointer"
            >
              {suggestion}
            </li>
          ))}
        </ul>
      )}
    </div>
  );
};
