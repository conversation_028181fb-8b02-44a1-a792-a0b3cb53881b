# Auto-Fix TripXplo CRM Deployment
# This script will fix both current 500 error and setup automated deployment

Write-Host "🚀 TripXplo CRM - Auto Deployment Fix" -ForegroundColor Green
Write-Host "======================================" -ForegroundColor Green

$SERVER_IP = "*************"
$SERVER_USER = "root"
$KEY_NAME = "tripxplo_crm_fix"

# Step 1: Generate SSH Key for automated access
Write-Host "🔐 Step 1: Setting up SSH key authentication..." -ForegroundColor Yellow

$sshDir = "$env:USERPROFILE\.ssh"
$privateKey = "$sshDir\$KEY_NAME"
$publicKey = "$sshDir\$KEY_NAME.pub"

# Create .ssh directory if it doesn't exist
if (-not (Test-Path $sshDir)) {
    New-Item -ItemType Directory -Path $sshDir -Force | Out-Null
}

# Generate SSH key pair if it doesn't exist
if (-not (Test-Path $privateKey)) {
    Write-Host "Generating new SSH key..." -ForegroundColor Gray
    & ssh-keygen -t rsa -b 4096 -f $privateKey -N '""' -q
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ SSH key generated!" -ForegroundColor Green
    } else {
        Write-Host "❌ Failed to generate SSH key" -ForegroundColor Red
        exit 1
    }
} else {
    Write-Host "✅ SSH key already exists" -ForegroundColor Green
}

# Step 2: Copy public key to server (requires password once)
Write-Host ""
Write-Host "🔑 Step 2: Setting up passwordless access..." -ForegroundColor Yellow
Write-Host "You'll need to enter your server password ONE TIME to enable automated access:" -ForegroundColor Cyan

$publicKeyContent = Get-Content $publicKey -Raw
& ssh $SERVER_USER@$SERVER_IP "mkdir -p ~/.ssh; echo '$publicKeyContent' >> ~/.ssh/authorized_keys; chmod 600 ~/.ssh/authorized_keys; chmod 700 ~/.ssh"

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ SSH key authentication enabled!" -ForegroundColor Green
} else {
    Write-Host "❌ Failed to setup SSH key. Please check your password and try again." -ForegroundColor Red
    exit 1
}

# Step 3: Test SSH connection without password
Write-Host ""
Write-Host "🧪 Step 3: Testing passwordless connection..." -ForegroundColor Yellow

& ssh -i $privateKey -o StrictHostKeyChecking=no $SERVER_USER@$SERVER_IP "echo 'SSH key authentication working!'"

if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ SSH key authentication failed" -ForegroundColor Red
    exit 1
}

Write-Host "✅ Passwordless SSH connection successful!" -ForegroundColor Green

# Step 4: Create the server fix script
Write-Host ""
Write-Host "🔧 Step 4: Creating server fix script..." -ForegroundColor Yellow

$serverFixScript = @'
#!/bin/bash
set -e

echo "🔧 Fixing TripXplo CRM deployment..."

# Navigate to CRM directory
cd /var/www/crm

# Check current structure
echo "📁 Current structure:"
ls -la

# Create backup
BACKUP_DIR="/var/www/crm.backup.$(date +%Y%m%d_%H%M%S)"
cp -r /var/www/crm $BACKUP_DIR
echo "💾 Backup created: $BACKUP_DIR"

# Move files from nested directory if it exists
if [ -d "TripXplo-CRM" ]; then
    echo "📁 Moving files from TripXplo-CRM to root..."
    cp -r TripXplo-CRM/* .
    rm -rf TripXplo-CRM
    echo "✅ Files moved successfully"
else
    echo "ℹ️ TripXplo-CRM directory not found - files may already be in correct location"
fi

# Set proper permissions
echo "🔐 Setting file permissions..."
chown -R www-data:www-data /var/www/crm
chmod -R 755 /var/www/crm
find /var/www/crm -name "*.html" -exec chmod 644 {} \;
find /var/www/crm -name "*.css" -exec chmod 644 {} \;
find /var/www/crm -name "*.js" -exec chmod 644 {} \;

# Verify index.html exists
if [ -f "/var/www/crm/index.html" ]; then
    echo "✅ index.html found in correct location"
else
    echo "❌ index.html not found in /var/www/crm/"
    echo "Current contents:"
    ls -la /var/www/crm/
    exit 1
fi

# Update Nginx configuration
echo "🌐 Updating Nginx configuration..."
cat > /etc/nginx/sites-available/crm.tripxplo.com << 'NGINXEOF'
server {
    listen 80;
    server_name crm.tripxplo.com www.crm.tripxplo.com;
    root /var/www/crm;
    index index.html;
    
    # Enable error logging
    error_log /var/log/nginx/crm_error.log;
    access_log /var/log/nginx/crm_access.log;
    
    # Handle React Router (SPA routing)
    location / {
        try_files $uri $uri/ /index.html;
        add_header Cache-Control "no-cache, no-store, must-revalidate" always;
    }
    
    # Static assets with proper caching
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot|map)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header Access-Control-Allow-Origin "*";
    }
    
    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    
    # CORS headers for Supabase
    add_header Access-Control-Allow-Origin "*" always;
    add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS" always;
    add_header Access-Control-Allow-Headers "DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization" always;
    
    # Handle preflight OPTIONS requests
    if ($request_method = 'OPTIONS') {
        add_header Access-Control-Allow-Origin "*";
        add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS";
        add_header Access-Control-Allow-Headers "DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization";
        add_header Access-Control-Max-Age 1728000;
        add_header Content-Type "text/plain charset=UTF-8";
        add_header Content-Length 0;
        return 204;
    }
    
    # Block access to sensitive files
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }
}
NGINXEOF

# Enable the site
ln -sf /etc/nginx/sites-available/crm.tripxplo.com /etc/nginx/sites-enabled/

# Test and reload Nginx
echo "🧪 Testing Nginx configuration..."
if nginx -t; then
    echo "✅ Nginx configuration is valid"
    systemctl reload nginx
    echo "✅ Nginx reloaded successfully"
else
    echo "❌ Nginx configuration test failed"
    nginx -t 2>&1
    exit 1
fi

echo ""
echo "✅ Server fix completed!"
echo "🌐 Testing site..."

# Test the site
sleep 2
HTTP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" http://crm.tripxplo.com || echo "000")
echo "HTTP Status: $HTTP_STATUS"

if [ "$HTTP_STATUS" = "200" ]; then
    echo "🎉 Site is working! http://crm.tripxplo.com"
else
    echo "⚠️ Site may need a moment to fully load. Try: http://crm.tripxplo.com"
fi

echo ""
echo "📊 Final structure:"
ls -la /var/www/crm/ | head -10

echo ""
echo "✅ TripXplo CRM deployment fix complete!"
'@

# Save the script to a temporary file
$serverFixScript | Out-File -FilePath "temp-server-fix.sh" -Encoding UTF8

# Step 5: Upload and execute the fix script
Write-Host ""
Write-Host "🚀 Step 5: Uploading and executing fix script..." -ForegroundColor Yellow

# Upload the script
& scp -i $privateKey -o StrictHostKeyChecking=no "temp-server-fix.sh" "${SERVER_USER}@${SERVER_IP}:/tmp/fix-deployment.sh"

if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Failed to upload fix script" -ForegroundColor Red
    exit 1
}

# Execute the script
Write-Host "Executing fix script on server..." -ForegroundColor Gray
& ssh -i $privateKey -o StrictHostKeyChecking=no $SERVER_USER@$SERVER_IP "chmod +x /tmp/fix-deployment.sh && /tmp/fix-deployment.sh"

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ Server fix completed successfully!" -ForegroundColor Green
} else {
    Write-Host "❌ Server fix encountered issues" -ForegroundColor Red
}

# Clean up local temporary file
Remove-Item "temp-server-fix.sh" -ErrorAction SilentlyContinue

# Step 6: Test the website
Write-Host ""
Write-Host "🧪 Step 6: Testing the website..." -ForegroundColor Yellow

try {
    $response = Invoke-WebRequest -Uri "http://crm.tripxplo.com" -Method Head -ErrorAction SilentlyContinue
    if ($response.StatusCode -eq 200) {
        Write-Host "🎉 SUCCESS! Site is working!" -ForegroundColor Green
        Write-Host "🌐 Visit: https://crm.tripxplo.com" -ForegroundColor Cyan
    } else {
        Write-Host "⚠️ Site returned status: $($response.StatusCode)" -ForegroundColor Yellow
    }
} catch {
    Write-Host "⚠️ Could not test site from here, but it should be working on the server" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "🎉 Deployment fix complete!" -ForegroundColor Green
Write-Host ""
Write-Host "📋 What was fixed:" -ForegroundColor Cyan
Write-Host "✅ SSH key authentication setup (no more passwords needed)" -ForegroundColor White
Write-Host "✅ Files moved from /var/www/crm/TripXplo-CRM/ to /var/www/crm/" -ForegroundColor White
Write-Host "✅ Proper file permissions set" -ForegroundColor White
Write-Host "✅ Nginx configuration updated" -ForegroundColor White
Write-Host "✅ Server reloaded" -ForegroundColor White
Write-Host ""
Write-Host "🌐 Your CRM should now be working at: https://crm.tripxplo.com" -ForegroundColor Green
Write-Host ""

# Step 7: Show next steps for GitHub Actions
Write-Host "🔧 Next: Fix GitHub Actions deployment..." -ForegroundColor Yellow
Write-Host "The SSH key for automated deployment is saved at: $privateKey" -ForegroundColor Gray
Write-Host ""
Write-Host "To update GitHub Actions, add this SSH private key as a secret:" -ForegroundColor White
Write-Host "1. Go to GitHub → Settings → Secrets and variables → Actions" -ForegroundColor Gray
Write-Host "2. Add secret: SSH_PRIVATE_KEY" -ForegroundColor Gray
Write-Host "3. Copy the content of: $privateKey" -ForegroundColor Gray
Write-Host ""
Write-Host "Private key content:" -ForegroundColor Yellow
Write-Host "===================" -ForegroundColor Yellow
Get-Content $privateKey
Write-Host "===================" -ForegroundColor Yellow

Write-Host ""
Write-Host "🚀 Ready for automated deployments!" -ForegroundColor Green 