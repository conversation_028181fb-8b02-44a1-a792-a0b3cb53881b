import React from 'react';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { useNavigate } from 'react-router-dom';

// Use the same Lead interface as in LeadsKanban
export interface Lead {
  id: string;
  customer_name: string;
  email: string;
  phone?: string;
  destination?: string;
  departure_city?: string;
  travel_date?: string;
  adults?: number;
  children?: number;
  infants?: number;
  nights?: number;
  budget_range?: string;
  lead_source?: string;
  status: string;
  priority?: string;
  notes?: string;
  special_requests?: string;
  assigned_to: string;
  created_at: string;
  updated_at?: string;
}

interface LeadCardProps {
  lead: Lead;
  isUpdating?: boolean;
  onViewDetails?: (leadId: string) => void;
  onCreateQuote?: (lead: Lead) => void;
}

const LeadCard: React.FC<LeadCardProps> = ({ lead, isUpdating = false, onViewDetails, onCreateQuote: _onCreateQuote }) => {
  const navigate = useNavigate();

  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: lead.id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.7 : 1,
    zIndex: isDragging ? 9999 : 'auto',
    boxShadow: isDragging ? '0 5px 15px rgba(0,0,0,0.2)' : 'none',
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return 'Not set';

    try {
      const date = new Date(dateString);
      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      });
    } catch (e) {
      return 'Invalid date';
    }
  };

  const getPriorityClass = (priority?: string) => {
    switch(priority?.toLowerCase()) {
      case 'high':
        return 'bg-red-100 text-red-800';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-green-100 text-green-800';
    }
  };

  // Return border color class based on priority (for accent border)
  const getPriorityBorderClass = (priority?: string) => {
    switch(priority?.toLowerCase()) {
      case 'high':
        return 'border-red-400';
      case 'medium':
        return 'border-yellow-400';
      default:
        return 'border-green-400';
    }
  };

  /* Example for dynamic background based on status:
  const statusBgColor: string = {
    'NEW LEAD': 'bg-blue-50',
    'CONTACTED': 'bg-green-50',
    // add more mappings as needed
  }[lead.status] || 'bg-white';
  */

  const handleCreateQuote = (e: React.MouseEvent) => {
    // Stop propagation to prevent drag handlers from being triggered
    e.stopPropagation();

    // Prepare lead data for auto-filling the quote form
    const leadDataForQuote = {
      customerName: lead.customer_name,
      destination: lead.destination || '',
      travelDate: lead.travel_date || '',
      source: lead.lead_source || '',
      email: lead.email,
      phone: lead.phone || '',
      // Travel group information
      adults: lead.adults || 1,
      children: lead.children || 0,
      infants: lead.infants || 0,
      nights: lead.nights || 1,
      budgetRange: lead.budget_range || '',
      notes: lead.notes || '',
      specialRequests: lead.special_requests || '',
    };

    // Store lead data in sessionStorage for auto-filling the quote form
    sessionStorage.setItem('leadDataForQuote', JSON.stringify(leadDataForQuote));

    // Navigate to the quotes page
    navigate('/quotes/new');
  };

  const handleViewDetails = (e: React.MouseEvent) => {
    // Stop propagation to prevent drag handlers from being triggered
    e.stopPropagation();
    e.preventDefault();

    console.log('[LeadCard] View Details clicked for lead:', lead.id, lead.customer_name);

    // Call the onViewDetails prop with the lead ID
    if (onViewDetails) {
      onViewDetails(lead.id);
    } else {
      console.warn('[LeadCard] onViewDetails prop is not provided');
    }
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      {...attributes}
      className={`relative bg-white p-4 rounded-lg shadow mb-4 hover:shadow-lg transition-all overflow-visible border-l-4 ${getPriorityBorderClass(lead.priority)} ${isDragging ? 'opacity-70 z-50' : 'z-auto'}`}
    >
      {isUpdating && (
        <div className="absolute inset-0 flex items-center justify-center bg-white bg-opacity-50 z-10 rounded">
          <div className="w-6 h-6 border-2 border-gray-300 border-t-primary rounded-full animate-spin"></div>
        </div>
      )}

      {/* Drag handle area - excludes buttons */}
      <div
        {...listeners}
        className="cursor-move"
      >
        {/* Header with name and priority accent */}
        <div className="flex justify-between items-center mb-3 pb-2 border-b border-gray-100">
          <h3 className="text-lg font-semibold text-gray-800">
            {lead.customer_name}
          </h3>
          <span className={`text-xs font-medium rounded-full px-2 py-1 ${getPriorityClass(lead.priority)}`}>
            {lead.priority || 'Low'}
          </span>
        </div>

        {/* Lead details */}
        <div className="mb-4 space-y-2 text-sm text-gray-700">
          <div className="flex">
            <span className="text-gray-500 w-24 text-sm">Destination:</span>
            <span className="text-gray-700 font-medium text-sm">{lead.destination || 'Not specified'}</span>
          </div>

          <div className="flex">
            <span className="text-gray-500 w-24 text-sm">Travel Date:</span>
            <span className="text-gray-700 font-medium text-sm">{formatDate(lead.travel_date)}</span>
          </div>

          <div className="flex">
            <span className="text-gray-500 w-24 text-sm">Source:</span>
            <span className="text-gray-700 font-medium text-sm">{lead.lead_source || 'Not specified'}</span>
          </div>

          <div className="flex text-xs text-gray-400 mt-1">
            <span>Created: {formatDate(lead.created_at)}</span>
          </div>
        </div>
      </div>

      {/* Action buttons - outside drag area */}
      <div className="flex space-x-3 mt-4 pt-3 border-t border-gray-200">
        <button
          type="button"
          onClick={handleViewDetails}
          className="px-4 py-2 text-sm bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-lg transition-colors cursor-pointer z-10 relative"
          style={{ pointerEvents: 'auto' }}
        >
          View Details
        </button>
        <button
          type="button"
          onClick={handleCreateQuote}
          className="px-4 py-2 text-sm bg-primary text-white hover:bg-primary-dark rounded-lg transition-colors cursor-pointer z-10 relative"
          style={{ pointerEvents: 'auto' }}
        >
          Create Quote
        </button>
      </div>
    </div>
  );
};

export default LeadCard;