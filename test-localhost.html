<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Localhost API</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            box-sizing: border-box;
        }
        button {
            background: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            display: none;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background: #e2e3e5;
            border: 1px solid #d6d8db;
            color: #383d41;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Test Localhost API Connection</h1>
        
        <div class="result info" style="display: block;">
            <strong>Environment Detection:</strong><br>
            <span id="environmentInfo">Loading...</span>
        </div>

        <h2>Test Contact Form Submission</h2>
        <form id="testForm">
            <div class="form-group">
                <label for="firstName">First Name:</label>
                <input type="text" id="firstName" value="John" required>
            </div>
            
            <div class="form-group">
                <label for="lastName">Last Name:</label>
                <input type="text" id="lastName" value="Doe" required>
            </div>
            
            <div class="form-group">
                <label for="mobileNumber">Mobile Number:</label>
                <input type="text" id="mobileNumber" value="9876543210" required>
            </div>
            
            <div class="form-group">
                <label for="emailId">Email:</label>
                <input type="email" id="emailId" value="<EMAIL>" required>
            </div>
            
            <div class="form-group">
                <label for="packageId">Package ID:</label>
                <input type="text" id="packageId" value="test-package-123" required>
            </div>
            
            <button type="submit">🚀 Test API Connection</button>
        </form>

        <div class="result" id="result"></div>
    </div>

    <script>
        // API Configuration - Same as main site
        const API_CONFIG = {
            getBaseUrl: () => {
                const hostname = window.location.hostname;
                const protocol = window.location.protocol;
                
                // Check if running on localhost
                if (hostname === 'localhost' || hostname === '127.0.0.1' || hostname.includes('localhost')) {
                    return 'http://localhost:3001';
                }
                
                // Check if running on file:// protocol (local HTML file)
                if (protocol === 'file:') {
                    return 'http://localhost:3001';
                }
                
                // Production environment
                return 'https://family.tripxplo.com';
            },
            
            getApiUrl: (endpoint) => {
                return `${API_CONFIG.getBaseUrl()}/api/${endpoint}`;
            }
        };

        // Display environment info
        document.addEventListener('DOMContentLoaded', function() {
            const envInfo = document.getElementById('environmentInfo');
            envInfo.innerHTML = `
                <strong>Hostname:</strong> ${window.location.hostname}<br>
                <strong>Protocol:</strong> ${window.location.protocol}<br>
                <strong>API Base URL:</strong> ${API_CONFIG.getBaseUrl()}<br>
                <strong>Contact API URL:</strong> ${API_CONFIG.getApiUrl('submit-contact-details')}
            `;
        });

        // Handle form submission
        document.getElementById('testForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const resultDiv = document.getElementById('result');
            const button = e.target.querySelector('button');
            
            // Show loading state
            button.disabled = true;
            button.textContent = '⏳ Testing...';
            resultDiv.style.display = 'none';
            
            try {
                // Collect form data
                const formData = {
                    firstName: document.getElementById('firstName').value,
                    lastName: document.getElementById('lastName').value,
                    mobileNumber: document.getElementById('mobileNumber').value,
                    emailId: document.getElementById('emailId').value,
                    packageId: document.getElementById('packageId').value,
                    packageData: {
                        title: 'Test Package',
                        destination: 'Test Destination',
                        price: 25000
                    },
                    searchParams: {
                        destination: 'Test Destination',
                        adults: 2,
                        children: 0,
                        infants: 0
                    },
                    selectedEmiPlan: {
                        months: 6,
                        monthly_amount: 4167,
                        processing_fee: 500
                    },
                    utm_source: 'localhost_test',
                    session_id: `test_${Date.now()}`
                };

                console.log('🧪 Testing API with data:', formData);

                // Make API call
                const apiUrl = API_CONFIG.getApiUrl('submit-contact-details');
                const response = await fetch(apiUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(formData)
                });

                const result = await response.json();

                if (response.ok && result.success) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `
                        <strong>✅ Success!</strong><br>
                        Quote ID: ${result.quote_id}<br>
                        Customer: ${result.customer_name}<br>
                        Message: ${result.message}
                    `;
                } else {
                    throw new Error(result.error || 'API call failed');
                }

            } catch (error) {
                console.error('❌ API test failed:', error);
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `
                    <strong>❌ Error!</strong><br>
                    ${error.message}<br><br>
                    <strong>Troubleshooting:</strong><br>
                    1. Make sure the API server is running on port 3001<br>
                    2. Check the browser console for more details<br>
                    3. Verify CORS configuration allows localhost
                `;
            } finally {
                // Reset button
                button.disabled = false;
                button.textContent = '🚀 Test API Connection';
                resultDiv.style.display = 'block';
            }
        });
    </script>
</body>
</html>
