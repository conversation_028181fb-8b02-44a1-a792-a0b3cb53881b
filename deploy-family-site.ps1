# Deploy Family Site to Linode
# This script will upload the family-tripxplo-production files to the server

Write-Host "Starting deployment of family.tripxplo.com..." -ForegroundColor Green

# Check if family-tripxplo-production directory exists
if (-not (Test-Path "family-tripxplo-production")) {
    Write-Host "Error: family-tripxplo-production directory not found!" -ForegroundColor Red
    exit 1
}

Write-Host "Found production files. Proceeding with deployment..." -ForegroundColor Yellow

# Step 1: Create temporary directory on server and upload files
Write-Host "Step 1: Uploading files to server..." -ForegroundColor Cyan
$scpCommand = "scp -r family-tripxplo-production/* root@*************:/tmp/family-new/"
Write-Host "Running: $scpCommand" -ForegroundColor Gray
Invoke-Expression $scpCommand

if ($LASTEXITCODE -ne 0) {
    Write-Host "Error: Failed to upload files to server!" -ForegroundColor Red
    exit 1
}

Write-Host "Files uploaded successfully!" -ForegroundColor Green

# Step 2: Connect to server and deploy
Write-Host "Step 2: Connecting to server to complete deployment..." -ForegroundColor Cyan

$sshCommands = @"
# Create backup of existing files
echo "Creating backup of existing family site..."
if [ -d "/var/www/family" ]; then
    cp -r /var/www/family /var/www/family.backup.$(date +%Y%m%d_%H%M%S)
    echo "Backup created successfully"
fi

# Create new directory structure
echo "Setting up new directory structure..."
mkdir -p /tmp/family-new
mkdir -p /var/www/family

# Clear existing files (but keep subdomain separate from main domain)
echo "Clearing existing family subdomain files..."
rm -rf /var/www/family/*

# Copy new files
echo "Copying new production files..."
cp -r /tmp/family-new/* /var/www/family/

# Set proper permissions
echo "Setting proper permissions..."
chown -R www-data:www-data /var/www/family
chmod -R 755 /var/www/family

# Update nginx configuration to point to correct directory
echo "Updating nginx configuration..."
cat > /etc/nginx/sites-available/family.tripxplo.com << 'EOF'
server {
    listen 80;
    server_name family.tripxplo.com www.family.tripxplo.com;
    root /var/www/family;
    index index.html;

    # Enable gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/css application/javascript application/json image/svg+xml text/plain text/xml;

    # Cache static assets
    location ~* \.(css|js|png|jpg|jpeg|gif|svg|ico)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # Main application
    location / {
        try_files \$uri \$uri/ /index.html;
    }

    # Security headers
    add_header X-Content-Type-Options nosniff;
    add_header X-Frame-Options DENY;
    add_header X-XSS-Protection "1; mode=block";
}
EOF

# Test nginx configuration
echo "Testing nginx configuration..."
nginx -t

if [ \$? -eq 0 ]; then
    echo "Nginx configuration is valid. Reloading..."
    systemctl reload nginx
    echo "Nginx reloaded successfully!"
else
    echo "Error: Nginx configuration is invalid!"
    exit 1
fi

# Clean up temporary files
echo "Cleaning up temporary files..."
rm -rf /tmp/family-new

# Test the site
echo "Testing the deployed site..."
curl -I http://family.tripxplo.com

echo "Deployment completed successfully!"
echo "Site should now be available at: http://family.tripxplo.com"
"@

# Write SSH commands to temporary file
$sshCommands | Out-File -FilePath "deploy-commands.sh" -Encoding UTF8

# Execute SSH commands
Write-Host "Executing deployment commands on server..." -ForegroundColor Cyan
$sshCommand = "ssh root@************* 'bash -s' < deploy-commands.sh"
Write-Host "Running: $sshCommand" -ForegroundColor Gray
Invoke-Expression $sshCommand

if ($LASTEXITCODE -ne 0) {
    Write-Host "Error: Deployment failed!" -ForegroundColor Red
    exit 1
}

# Clean up local temporary file
Remove-Item "deploy-commands.sh" -Force

Write-Host "Deployment completed successfully!" -ForegroundColor Green
Write-Host "Your site should now be available at: http://family.tripxplo.com" -ForegroundColor Yellow
Write-Host "Please test the site to ensure everything is working correctly." -ForegroundColor Cyan
