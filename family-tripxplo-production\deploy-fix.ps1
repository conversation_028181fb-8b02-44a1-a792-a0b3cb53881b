#!/usr/bin/env pwsh
# PowerShell script to deploy the database authentication fix to production server

Write-Host "🚀 Deploying database authentication fix to production server..." -ForegroundColor Green

# Production server details
$SERVER = "root@*************"
$REMOTE_PATH = "/var/www/family"
$LOCAL_PATH = "family-tripxplo-production"

try {
    Write-Host "📦 Preparing deployment files..." -ForegroundColor Yellow
    
    # Create temporary deployment directory
    $tempDir = "temp-deploy-$(Get-Date -Format 'yyyyMMdd-HHmmss')"
    New-Item -ItemType Directory -Path $tempDir -Force | Out-Null
    
    # Copy API files with fixes
    Copy-Item -Path "$LOCAL_PATH/api" -Destination "$tempDir/" -Recurse -Force
    
    # Copy updated JavaScript files
    Copy-Item -Path "$LOCAL_PATH/js" -Destination "$tempDir/" -Recurse -Force
    
    Write-Host "🔧 Uploading fixed files to production server..." -ForegroundColor Yellow
    
    # Upload API directory with fixed environment configuration
    scp -r "$tempDir/api" "${SERVER}:${REMOTE_PATH}/"
    if ($LASTEXITCODE -ne 0) {
        throw "Failed to upload API files"
    }
    
    # Upload JavaScript files
    scp -r "$tempDir/js" "${SERVER}:${REMOTE_PATH}/"
    if ($LASTEXITCODE -ne 0) {
        throw "Failed to upload JS files"
    }
    
    Write-Host "⚙️ Configuring production environment..." -ForegroundColor Yellow
    
    # SSH commands to configure the server
    $sshCommands = @"
cd $REMOTE_PATH/api

# Backup existing .env if it exists
if [ -f .env ]; then
    cp .env .env.backup.$(date +%Y%m%d-%H%M%S)
fi

# Copy the production environment file to .env
cp .env.production .env

# Install/update dependencies
npm install

# Restart the API service (assuming PM2 or similar)
if command -v pm2 &> /dev/null; then
    echo "🔄 Restarting API service with PM2..."
    pm2 restart family-api || pm2 start server.js --name family-api
elif command -v systemctl &> /dev/null; then
    echo "🔄 Restarting API service with systemctl..."
    systemctl restart family-api || echo "⚠️ Service restart failed - may need manual restart"
else
    echo "⚠️ No process manager found. Please manually restart the API service."
fi

# Test database connections
echo "🧪 Testing database connections..."
curl -s http://localhost:3000/api/test-db | jq '.' || echo "Database test endpoint not responding"

echo "✅ Deployment completed!"
"@
    
    # Execute SSH commands
    $sshCommands | ssh $SERVER "bash -s"
    if ($LASTEXITCODE -ne 0) {
        Write-Host "⚠️ Some SSH commands may have failed, but deployment files were uploaded" -ForegroundColor Yellow
    }
    
    Write-Host "🧪 Testing production database connection..." -ForegroundColor Yellow
    
    # Test the production endpoint
    try {
        $testResult = Invoke-RestMethod -Uri "https://family.tripxplo.com/api/test-db" -Method Get -TimeoutSec 30
        Write-Host "✅ Database connection test results:" -ForegroundColor Green
        $testResult | ConvertTo-Json -Depth 3 | Write-Host
    } catch {
        Write-Host "⚠️ Could not test production endpoint: $($_.Exception.Message)" -ForegroundColor Yellow
        Write-Host "Please manually test: https://family.tripxplo.com/api/test-db" -ForegroundColor Yellow
    }
    
    Write-Host "🎉 Deployment completed successfully!" -ForegroundColor Green
    Write-Host ""
    Write-Host "Next steps:" -ForegroundColor Cyan
    Write-Host "1. Test the website: https://family.tripxplo.com" -ForegroundColor White
    Write-Host "2. Check database connection: https://family.tripxplo.com/api/test-db" -ForegroundColor White
    Write-Host "3. Monitor console logs for any remaining issues" -ForegroundColor White
    
} catch {
    Write-Host "❌ Deployment failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
} finally {
    # Cleanup temporary directory
    if (Test-Path $tempDir) {
        Remove-Item -Path $tempDir -Recurse -Force
    }
}
