import { getQuoteClient } from '../lib/supabaseManager';

export interface ItineraryTemplate {
  id: string;
  title: string;
  destination: string;
  nights_days: string;
  content?: string;
  created_at: string;
  updated_at: string;
}

export interface CreateItineraryTemplate {
  title: string;
  destination: string;
  nights_days: string;
  content?: string;
}

export interface UpdateItineraryTemplate {
  title?: string;
  destination?: string;
  nights_days?: string;
  content?: string;
}

/**
 * Fetch all itinerary templates
 */
export const fetchItineraryTemplates = async (): Promise<ItineraryTemplate[]> => {
  try {
    const supabase = await getQuoteClient();
    
    const { data, error } = await supabase
      .from('itinerary_templates')
      .select('*')
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching itinerary templates:', error);
      throw error;
    }

    return data || [];
  } catch (error) {
    console.error('Failed to fetch itinerary templates:', error);
    throw error;
  }
};

/**
 * Fetch a single itinerary template by ID
 */
export const fetchItineraryTemplate = async (id: string): Promise<ItineraryTemplate | null> => {
  try {
    const supabase = await getQuoteClient();
    
    const { data, error } = await supabase
      .from('itinerary_templates')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      console.error('Error fetching itinerary template:', error);
      throw error;
    }

    return data;
  } catch (error) {
    console.error('Failed to fetch itinerary template:', error);
    throw error;
  }
};

/**
 * Create a new itinerary template
 */
export const createItineraryTemplate = async (template: CreateItineraryTemplate): Promise<ItineraryTemplate> => {
  try {
    const supabase = await getQuoteClient();
    
    const { data, error } = await supabase
      .from('itinerary_templates')
      .insert([template])
      .select()
      .single();

    if (error) {
      console.error('Error creating itinerary template:', error);
      throw error;
    }

    return data;
  } catch (error) {
    console.error('Failed to create itinerary template:', error);
    throw error;
  }
};

/**
 * Update an existing itinerary template
 */
export const updateItineraryTemplate = async (
  id: string, 
  updates: UpdateItineraryTemplate
): Promise<ItineraryTemplate> => {
  try {
    const supabase = await getQuoteClient();
    
    const { data, error } = await supabase
      .from('itinerary_templates')
      .update(updates)
      .eq('id', id)
      .select()
      .single();

    if (error) {
      console.error('Error updating itinerary template:', error);
      throw error;
    }

    return data;
  } catch (error) {
    console.error('Failed to update itinerary template:', error);
    throw error;
  }
};

/**
 * Delete an itinerary template
 */
export const deleteItineraryTemplate = async (id: string): Promise<void> => {
  try {
    const supabase = await getQuoteClient();
    
    const { error } = await supabase
      .from('itinerary_templates')
      .delete()
      .eq('id', id);

    if (error) {
      console.error('Error deleting itinerary template:', error);
      throw error;
    }
  } catch (error) {
    console.error('Failed to delete itinerary template:', error);
    throw error;
  }
};

/**
 * Fetch unique destinations from quotes table
 */
export const fetchDestinations = async (): Promise<string[]> => {
  try {
    const supabase = await getQuoteClient();
    
    const { data, error } = await supabase
      .from('quotes')
      .select('destination')
      .not('destination', 'is', null);

    if (error) {
      console.error('Error fetching destinations:', error);
      // Return fallback destinations if fetch fails
      return [
        'Kerala - Gods Own Country',
        'Goa - Beach Paradise', 
        'Rajasthan - Royal Heritage',
        'Himachal - Mountain Paradise',
        'Karnataka - Cultural Haven',
        'Andaman - Island Paradise',
        'Sikkim - Northeast Wonder',
        'Kashmir - Heaven on Earth'
      ];
    }
    
    const uniqueDestinations = [...new Set(data?.map(item => item.destination) || [])]
      .filter(dest => dest && dest.trim() !== '')
      .sort();
    
    return uniqueDestinations;
  } catch (error) {
    console.error('Failed to fetch destinations:', error);
    // Return fallback destinations if fetch fails
    return [
    'Manali', 'Ooty', 'Munnar', 'Andaman', 'Kodaikanal', 'Coorg', 'Alleppey',
    'Kochi', 'Shimla', 'Yelagiri', 'Wayanad', 'Meghalaya', 'Darjeeling', 'Sikkim',
    'Delhi', 'Agra', 'Pondicherry', 'Madurai', 'Rameswaram', 'Ladakh', 'Mysore',
    'Bali', 'Maldives', 'Europe', 'Thailand', 'Singapore', 'Abu Dhabi', 'Vietnam',
    'Dubai', 'Australia'
    ];
  }
};

/**
 * Search itinerary templates by title, destination, or duration
 */
export const searchItineraryTemplates = async (searchTerm: string): Promise<ItineraryTemplate[]> => {
  try {
    const supabase = await getQuoteClient();
    
    const { data, error } = await supabase
      .from('itinerary_templates')
      .select('*')
      .or(`title.ilike.%${searchTerm}%,destination.ilike.%${searchTerm}%,nights_days.ilike.%${searchTerm}%`)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error searching itinerary templates:', error);
      throw error;
    }

    return data || [];
  } catch (error) {
    console.error('Failed to search itinerary templates:', error);
    throw error;
  }
};

/**
 * Get templates by destination
 */
export const getTemplatesByDestination = async (destination: string): Promise<ItineraryTemplate[]> => {
  try {
    const supabase = await getQuoteClient();
    
    const { data, error } = await supabase
      .from('itinerary_templates')
      .select('*')
      .eq('destination', destination)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching templates by destination:', error);
      throw error;
    }

    return data || [];
  } catch (error) {
    console.error('Failed to fetch templates by destination:', error);
    throw error;
  }
};

/**
 * Get templates by duration
 */
export const getTemplatesByDuration = async (nightsDays: string): Promise<ItineraryTemplate[]> => {
  try {
    const supabase = await getQuoteClient();
    
    const { data, error } = await supabase
      .from('itinerary_templates')
      .select('*')
      .eq('nights_days', nightsDays)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching templates by duration:', error);
      throw error;
    }

    return data || [];
  } catch (error) {
    console.error('Failed to fetch templates by duration:', error);
    throw error;
  }
};
