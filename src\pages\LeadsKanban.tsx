import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import LeadCard, { Lead } from '../components/LeadCard';
import LeadDetailModal from '../components/LeadDetailModal';
import {
  DndContext,
  DragEndEvent,
  DragOverlay,
  DragStartEvent,
  MouseSensor,
  TouchSensor,
  useSensor,
  useSensors,
  useDroppable,
  UniqueIdentifier,
  rectIntersection,
} from '@dnd-kit/core';
import { SortableContext, verticalListSortingStrategy } from '@dnd-kit/sortable';
import { toast } from 'react-hot-toast';
import { Spinner } from '../components/Spinner';
// Use Supabase API directly for leads
import { fetchLeadsByUser, updateLeadStatus } from '../lib/leadService';

// Material Design Color Palette - More attractive and vibrant
const statusConfig = {
  'NEW LEAD': { 
    color: 'bg-blue-600', 
    lightColor: 'bg-blue-50', 
    borderColor: 'border-blue-300',
    textColor: 'text-blue-800',
    cardColor: '#E3F2FD',
    icon: '🆕'
  },
  'CALL CUSTOMER': { 
    color: 'bg-purple-600', 
    lightColor: 'bg-purple-50', 
    borderColor: 'border-purple-300',
    textColor: 'text-purple-800',
    cardColor: '#F3E5F5',
    icon: '📞'
  },
  'CONTACTED': { 
    color: 'bg-green-600', 
    lightColor: 'bg-green-50', 
    borderColor: 'border-green-300',
    textColor: 'text-green-800',
    cardColor: '#E8F5E8',
    icon: '✅'
  },
  'CALL NOT ANSWERED': { 
    color: 'bg-orange-600', 
    lightColor: 'bg-orange-50', 
    borderColor: 'border-orange-300',
    textColor: 'text-orange-800',
    cardColor: '#FFF3E0',
    icon: '📵'
  },
  'NO RESPONSE': { 
    color: 'bg-red-600', 
    lightColor: 'bg-red-50', 
    borderColor: 'border-red-300',
    textColor: 'text-red-800',
    cardColor: '#FFEBEE',
    icon: '🚫'
  },
  'MORE INFO': { 
    color: 'bg-amber-600', 
    lightColor: 'bg-amber-50', 
    borderColor: 'border-amber-300',
    textColor: 'text-amber-800',
    cardColor: '#FFFBF0',
    icon: 'ℹ️'
  },
  'FOLLOW-UP': { 
    color: 'bg-indigo-600', 
    lightColor: 'bg-indigo-50', 
    borderColor: 'border-indigo-300',
    textColor: 'text-indigo-800',
    cardColor: '#E8EAF6',
    icon: '🔄'
  },
  'QUOTE SENT': { 
    color: 'bg-teal-600', 
    lightColor: 'bg-teal-50', 
    borderColor: 'border-teal-300',
    textColor: 'text-teal-800',
    cardColor: '#E0F2F1',
    icon: '📋'
  },
  'NEGOTIATION': { 
    color: 'bg-pink-600', 
    lightColor: 'bg-pink-50', 
    borderColor: 'border-pink-300',
    textColor: 'text-pink-800',
    cardColor: '#FCE4EC',
    icon: '🤝'
  },
  'APPROVED': { 
    color: 'bg-emerald-600', 
    lightColor: 'bg-emerald-50', 
    borderColor: 'border-emerald-300',
    textColor: 'text-emerald-800',
    cardColor: '#ECFDF5',
    icon: '✔️'
  },
  'BOOKED WITH US': { 
    color: 'bg-cyan-600', 
    lightColor: 'bg-cyan-50', 
    borderColor: 'border-cyan-300',
    textColor: 'text-cyan-800',
    cardColor: '#E0F7FA',
    icon: '🎉'
  },
  'TRIP COMPLETED': { 
    color: 'bg-lime-600', 
    lightColor: 'bg-lime-50', 
    borderColor: 'border-lime-300',
    textColor: 'text-lime-800',
    cardColor: '#F9FBE7',
    icon: '🏆'
  },
  'ON-HOLD': { 
    color: 'bg-yellow-600', 
    lightColor: 'bg-yellow-50', 
    borderColor: 'border-yellow-300',
    textColor: 'text-yellow-800',
    cardColor: '#FFFDE7',
    icon: '⏸️'
  },
  'POSTPONE TRIP': { 
    color: 'bg-violet-600', 
    lightColor: 'bg-violet-50', 
    borderColor: 'border-violet-300',
    textColor: 'text-violet-800',
    cardColor: '#F3E5F5',
    icon: '⏰'
  },
  'NOT INTERESTED': { 
    color: 'bg-gray-600', 
    lightColor: 'bg-gray-50', 
    borderColor: 'border-gray-300',
    textColor: 'text-gray-800',
    cardColor: '#FAFAFA',
    icon: '❌'
  },
  'BOOKING - OTHERS': { 
    color: 'bg-slate-600', 
    lightColor: 'bg-slate-50', 
    borderColor: 'border-slate-300',
    textColor: 'text-slate-800',
    cardColor: '#F8FAFC',
    icon: '🏢'
  },
  'TRIP CANCELLED': { 
    color: 'bg-rose-600', 
    lightColor: 'bg-rose-50', 
    borderColor: 'border-rose-300',
    textColor: 'text-rose-800',
    cardColor: '#FFF1F2',
    icon: '🚮'
  }
};

const statuses = Object.keys(statusConfig);

// Compact Kanban column with Material Design
interface KanbanColumnProps {
  status: string;
  leads: Lead[];
  updatingLeadId: string | null;
  onViewDetails: (id: string) => void;
}

const KanbanColumn: React.FC<KanbanColumnProps> = ({ status, leads, updatingLeadId, onViewDetails }) => {
  const { setNodeRef } = useDroppable({ id: `column-${status}` });
  const config = statusConfig[status as keyof typeof statusConfig];
  
  return (
    <div className="flex-shrink-0 w-full sm:w-72 mx-auto sm:mx-1 mb-4 sm:mb-0">
      {/* Compact Column Header */}
      <div className={`${config.color} text-white p-3 rounded-t-lg shadow-md`}>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <span className="text-sm">{config.icon}</span>
            <h3 className="font-semibold text-xs uppercase tracking-wide truncate">
              {status}
            </h3>
          </div>
          <div className="bg-white bg-opacity-25 text-white text-xs font-bold py-0.5 px-2 rounded-full">
            {leads.length}
          </div>
        </div>
      </div>
      
      {/* Compact Column Body with better spacing */}
      <div
        ref={setNodeRef}
        id={`column-${status}`}
        className={`${config.lightColor} ${config.borderColor} border-l border-r border-b rounded-b-lg p-2 sm:p-3 min-h-[300px] sm:min-h-[400px] max-h-[calc(100vh-250px)] sm:max-h-[calc(100vh-200px)] overflow-y-auto shadow-md`}
        data-status={status}
        style={{
          backgroundColor: config.cardColor,
        }}
      >
        <div
          id={`column-content-${status}`}
          className="space-y-3"
          data-status={status}
        >
          {leads.length === 0 ? (
            <div className={`${config.textColor} text-center py-6 opacity-50`}>
              <div className="text-2xl mb-1">{config.icon}</div>
              <p className="text-xs font-medium">No leads</p>
              <p className="text-xs mt-1 opacity-75">Drop here</p>
            </div>
          ) : (
            leads.map((lead) => (
              <LeadCard
                key={lead.id}
                lead={lead}
                isUpdating={updatingLeadId === lead.id}
                onViewDetails={onViewDetails}
                statusConfig={config}
              />
            ))
          )}
        </div>
      </div>
    </div>
  );
};

const LeadsKanban: React.FC = () => {
  console.log('[KANBAN] Component rendering / re-rendering...');
  
  const { user } = useAuth();
  const [leads, setLeads] = useState<Lead[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true); // Auto-load on first visit
  const [error, setError] = useState<string | null>(null);
  const [updatingLeadId, setUpdatingLeadId] = useState<string | null>(null);
  const [activeLead, setActiveLead] = useState<Lead | null>(null);
  const [viewingLeadId, setViewingLeadId] = useState<string | null>(null);
  const [selectedStatuses, setSelectedStatuses] = useState<string[]>(statuses);
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [hasInitialLoad, setHasInitialLoad] = useState<boolean>(false);

  // Configure sensors for mouse and touch interactions
  const sensors = useSensors(
    useSensor(MouseSensor, {
      activationConstraint: {
        distance: 10,
      },
    }),
    useSensor(TouchSensor, {
      activationConstraint: {
        delay: 250,
        tolerance: 5,
      },
    })
  );

  // Smart auto-load: only on first visit, not on every screen switch
  useEffect(() => {
    if (user?.id && !hasInitialLoad) {
      fetchLeads();
    }
  }, [user?.id, hasInitialLoad]);

  const fetchLeads = async () => {
    if (!user?.id) {
      setIsLoading(false);
      return;
    }

    console.log('[KANBAN] Fetching leads for user:', user.id);
    setIsLoading(true);
    setError(null);

    try {
      const result = await fetchLeadsByUser(user.id);
      console.log('[KANBAN] fetchLeadsByUser result:', result);

      if (result.success && result.data) {
        setLeads(result.data);
        setHasInitialLoad(true);
        console.log('[KANBAN] Successfully loaded', result.data.length, 'leads');
      } else {
        throw new Error(result.error || 'Failed to fetch leads');
      }
    } catch (error: any) {
      console.error('[KANBAN] Error fetching leads:', error);
      setError(error.message || 'Failed to load leads');
    } finally {
      setIsLoading(false);
    }
  };

  // Manual refresh function
  const handleRefresh = async () => {
    if (!user?.id) {
      toast.error('Please login to load leads');
      return;
    }
    
    toast.success('Refreshing leads...');
    await fetchLeads();
  };

  const getLeadsByStatus = (status: string) => {
    return leads.filter(lead => lead.status === status && 
      (searchTerm === '' || 
       lead.customer_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
       lead.destination?.toLowerCase().includes(searchTerm.toLowerCase()) ||
       lead.email.toLowerCase().includes(searchTerm.toLowerCase())
      )
    );
  };

  const handleViewDetails = (leadId: string) => {
    console.log('[KANBAN] View Details requested for lead:', leadId);
    setViewingLeadId(leadId);
  };

  const handleDragStart = (event: DragStartEvent) => {
    console.log('[KANBAN] Drag started:', event.active.id);
    const lead = leads.find(l => l.id === event.active.id);
    setActiveLead(lead || null);
  };

  const handleDragOver = () => {
    // Optional: Add visual feedback during drag over
  };

  const findColumnFromTarget = (targetId: UniqueIdentifier): string | null => {
    const targetStr = String(targetId);
    console.log('[KANBAN] Finding column for target:', targetStr);

    // Direct column match
    if (targetStr.startsWith('column-')) {
      const status = targetStr.replace('column-', '');
      console.log('[KANBAN] Direct column match:', status);
      return status;
    }

    // Column content match
    if (targetStr.startsWith('column-content-')) {
      const status = targetStr.replace('column-content-', '');
      console.log('[KANBAN] Column content match:', status);
      return status;
    }

    // Lead card match - find which column it belongs to
    const leadId = targetStr;
    const lead = leads.find(l => l.id === leadId);
    if (lead) {
      console.log('[KANBAN] Lead match, status:', lead.status);
      return lead.status;
    }

    // DOM traversal fallback
    const element = document.getElementById(targetStr);
    if (element) {
      const columnElement = element.closest('[data-status]');
      if (columnElement) {
        const status = columnElement.getAttribute('data-status');
        console.log('[KANBAN] DOM traversal match:', status);
        return status;
      }
    }

    console.log('[KANBAN] No column found for target:', targetStr);
    return null;
  };

  const handleDragEnd = async (event: DragEndEvent) => {
    console.log('[KANBAN] Drag ended:', event);
    setActiveLead(null);

    const { active, over } = event;
    if (!over) {
      console.log('[KANBAN] No drop target');
      return;
    }

    const leadId = String(active.id);
    const newStatus = findColumnFromTarget(over.id);

    console.log('[KANBAN] Drag end details:', {
      leadId,
      overId: over.id,
      newStatus
    });

    if (newStatus && statuses.includes(newStatus)) {
      const leadIndex = leads.findIndex(lead => lead.id === leadId);
      if (leadIndex === -1) {
        console.error('[KANBAN] Lead not found:', leadId);
        return;
      }

      const originalStatus = leads[leadIndex].status;
      if (originalStatus === newStatus) {
        console.log('[KANBAN] No status change needed');
        return;
      }

      console.log('[KANBAN] Updating lead status:', {
        leadId,
        from: originalStatus,
        to: newStatus
      });

      // Optimistic update
      const updatedLeads = [...leads];
      updatedLeads[leadIndex] = {
        ...updatedLeads[leadIndex],
        status: newStatus
      };
      setLeads(updatedLeads);
      setUpdatingLeadId(leadId);

      // API update with improved error handling
      try {
        if (user?.id) {
          console.log("Calling updateLeadStatus service with:", {
            leadId: leadId,
            newStatus: newStatus,
            userId: user.id
          });

          const result = await updateLeadStatus(leadId, newStatus);

          console.log("Status update response:", result);

          if (!result.success) {
            throw new Error(result.error || 'Failed to update lead status');
          }

          console.log("Database update successful");
          toast.success(`Lead moved to ${newStatus}`);
        } else {
          console.log("No user ID, skipping database update");
          toast.success(`Lead moved to ${newStatus} (local only)`);
        }
      } catch (error: any) {
        console.error('Error updating lead status:', error);

        // Revert the change in UI if the database update fails
        const revertedLeads = [...updatedLeads];
        revertedLeads[leadIndex] = {
          ...revertedLeads[leadIndex],
          status: originalStatus
        };

        setLeads(revertedLeads);
        console.log("Reverted to original status due to error");
        toast.error(error.message || 'Failed to update lead status. Please try again.');
      } finally {
        setUpdatingLeadId(null);
      }
    } else {
      console.log("Dropped on an invalid target");
    }
  };

  // Toggle a status in the filter menu
  const handleToggleStatus = (status: string) => {
    setSelectedStatuses(prev =>
      prev.includes(status)
        ? prev.filter(s => s !== status)
        : [...prev, status]
    );
  };

  // Show loading state only on initial load
  if (isLoading && !hasInitialLoad) {
    return (
      <div className="flex flex-col justify-center items-center h-64 space-y-4">
        <Spinner size="xl" />
        <p className="text-gray-500">Loading leads...</p>
        {error && (
          <div className="p-4 my-3 bg-red-50 text-red-600 rounded-lg">
            <h2 className="text-lg font-semibold">Error</h2>
            <p>{error}</p>
          </div>
        )}
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-blue-50">
      {/* Compact Header */}
      <div className="bg-white shadow-md border-b-2 border-blue-500">
        <div className="max-w-7xl mx-auto px-4 py-4">
          <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4 sm:gap-0">
            <div>
              <h1 className="text-xl sm:text-2xl font-bold text-gray-900 flex items-center">
                <span className="text-lg sm:text-xl mr-2">📋</span>
                Leads Kanban
              </h1>
              <p className="text-gray-600 text-sm">
                {error ? 'Error loading data' : 'Manage your travel leads'}
              </p>
            </div>
            <div className="flex items-center space-x-2 sm:space-x-4 self-end sm:self-auto">
              <div className="text-right">
                <div className="text-lg sm:text-xl font-bold text-blue-600">{leads.length}</div>
                <div className="text-xs text-gray-500">Total</div>
              </div>
              <button
                onClick={handleRefresh}
                disabled={isLoading}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors text-sm font-medium flex items-center space-x-2"
              >
                {isLoading ? (
                  <>
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                    <span>Loading...</span>
                  </>
                ) : (
                  <>
                    <span>🔄</span>
                    <span>Refresh</span>
                  </>
                )}
              </button>
            </div>
          </div>
          
          {error && (
            <div className="mt-3 p-3 bg-red-50 text-red-700 rounded-lg border border-red-200 text-sm">
              <span className="font-medium">⚠️ Error:</span> {error}
            </div>
          )}
        </div>
      </div>

      {/* Compact Search and Filter */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-2 sm:px-4 py-3">
          {/* Compact Search */}
          <div className="mb-3">
            <div className="relative max-w-full sm:max-w-sm">
              <input
                type="text"
                placeholder="Search leads..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-8 pr-3 py-1.5 text-sm border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
              <span className="absolute left-2.5 top-2 text-gray-400 text-sm">🔍</span>
            </div>
          </div>

          {/* Compact Filter Pills */}
          <div className="flex flex-wrap gap-1">
            <button
              onClick={() => setSelectedStatuses(statuses)}
              className="px-2 py-1 text-xs bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
            >
              All
            </button>
            <button
              onClick={() => setSelectedStatuses([])}
              className="px-2 py-1 text-xs bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors"
            >
              None
            </button>
            {statuses.map(status => {
              const config = statusConfig[status as keyof typeof statusConfig];
              const leadsCount = getLeadsByStatus(status).length;
              const isSelected = selectedStatuses.includes(status);
              
              return (
                <button
                  key={status}
                  onClick={() => handleToggleStatus(status)}
                  className={`px-2 py-1 text-xs rounded-md transition-colors flex items-center space-x-1 ${
                    isSelected
                      ? `${config.color} text-white`
                      : `${config.lightColor} ${config.textColor} border ${config.borderColor}`
                  }`}
                >
                  <span className="text-xs">{config.icon}</span>
                  <span className="font-medium truncate max-w-20 sm:max-w-full">{status.split(' ')[0]}</span>
                  <span className={`text-xs px-1 rounded ${
                    isSelected ? 'bg-white bg-opacity-20' : 'bg-white bg-opacity-60'
                  }`}>
                    {leadsCount}
                  </span>
                </button>
              );
            })}
          </div>
        </div>
      </div>

      {/* Compact Kanban Board */}
      <DndContext
        sensors={sensors}
        collisionDetection={rectIntersection}
        onDragStart={handleDragStart}
        onDragOver={handleDragOver}
        onDragEnd={handleDragEnd}
      >
        <div className="px-2 sm:px-4 pb-6">
          <div className="flex flex-col md:flex-row md:space-x-2 overflow-x-auto min-h-[500px] bg-gradient-to-r from-blue-50 via-purple-50 to-pink-50 rounded-lg p-2 sm:p-4 shadow-inner">
            {statuses.filter(status => selectedStatuses.includes(status)).map((status) => (
              <SortableContext
                key={status}
                items={getLeadsByStatus(status).map((l) => l.id)}
                strategy={verticalListSortingStrategy}
              >
                <KanbanColumn
                  status={status}
                  leads={getLeadsByStatus(status)}
                  updatingLeadId={updatingLeadId}
                  onViewDetails={handleViewDetails}
                />
              </SortableContext>
            ))}
          </div>
        </div>

        <DragOverlay>
          {activeLead && (
            <div className="transform rotate-2 scale-105">
              <LeadCard
                lead={activeLead}
                onViewDetails={handleViewDetails}
                statusConfig={statusConfig[activeLead.status as keyof typeof statusConfig]}
              />
            </div>
          )}
        </DragOverlay>
      </DndContext>

      {/* Lead Detail Modal */}
      <LeadDetailModal
        leadId={viewingLeadId}
        onClose={() => setViewingLeadId(null)}
        leads={leads}
        onLeadUpdated={(updatedLead) => {
          // Update the lead in the local state
          setLeads(prevLeads => 
            prevLeads.map(lead => 
              lead.id === updatedLead.id ? updatedLead : lead
            )
          );
        }}
      />
    </div>
  );
};

export default LeadsKanban;
