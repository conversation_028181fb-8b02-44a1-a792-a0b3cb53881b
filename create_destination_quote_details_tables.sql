CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

CREATE TABLE public.destination_quote_hotel_rows (
  id uuid NOT NULL DEFAULT uuid_generate_v4(),
  destination_quote_id uuid NULL,
  hotel_name text NULL,
  room_type text NULL DEFAULT 'Standard'::text,
  price double precision NULL DEFAULT 0,
  meal_plan text NULL DEFAULT 'MAP'::text,
  no_of_rooms integer NULL DEFAULT 0,
  stay_nights integer NULL DEFAULT 0,
  extra_adult_cost double precision NULL DEFAULT 0,
  children_cost double precision NULL DEFAULT 0,
  infant_cost double precision NULL DEFAULT 0,
  gst_type text NULL DEFAULT '0'::text,
  tac_percentage double precision NULL DEFAULT 0,
  tac_amount double precision NULL DEFAULT 0,
  info text NULL,
  stay_price double precision NULL DEFAULT 0,
  gst_amount double precision NULL DEFAULT 0,
  currency text NULL DEFAULT 'INR'::text,
  created_at timestamp with time zone NULL DEFAULT now(),
  room_capacity integer NULL,
  CONSTRAINT destination_quote_hotel_rows_pkey PRIMARY KEY (id),
  CONSTRAINT destination_quote_hotel_rows_destination_quote_id_fkey FOREIGN KEY (destination_quote_id) REFERENCES destination_quotes (id) ON DELETE CASCADE
) TABLESPACE pg_default;

CREATE INDEX IF NOT EXISTS idx_destination_quote_hotel_rows_destination_quote_id ON public.destination_quote_hotel_rows USING btree (destination_quote_id) TABLESPACE pg_default;

CREATE TABLE public.destination_quote_costs (
  id uuid NOT NULL DEFAULT uuid_generate_v4(),
  destination_quote_id uuid NULL,
  meals double precision NULL DEFAULT 0,
  transportation double precision NULL DEFAULT 0,
  cab_sightseeing double precision NULL DEFAULT 0,
  train_cost double precision NULL DEFAULT 0,
  ferry_cost double precision NULL DEFAULT 0,
  parking_toll double precision NULL DEFAULT 0,
  add_on_activity double precision NULL DEFAULT 0,
  marketing double precision NULL DEFAULT 0,
  add_on double precision NULL DEFAULT 0,
  flight_ticket double precision NULL DEFAULT 0,
  guide_wages double precision NULL DEFAULT 0,
  created_at timestamp with time zone NULL DEFAULT now(),
  CONSTRAINT destination_quote_costs_pkey PRIMARY KEY (id),
  CONSTRAINT destination_quote_costs_destination_quote_id_fkey FOREIGN KEY (destination_quote_id) REFERENCES destination_quotes (id) ON DELETE CASCADE
) TABLESPACE pg_default;

CREATE INDEX IF NOT EXISTS idx_destination_quote_costs_destination_quote_id ON public.destination_quote_costs USING btree (destination_quote_id) TABLESPACE pg_default;
