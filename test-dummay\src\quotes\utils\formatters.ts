// Define types for currency objects
export interface Currency {
  symbol: string;
  name: string;
  rate: number;
  code: string;
}

// Currency definitions with symbols and exchange rates (relative to INR)
export const CURRENCIES: Record<string, Currency> = {
  INR: {
    symbol: '₹',
    name: 'Indian Rupee',
    rate: 1, // Base currency
    code: 'INR'
  },
  USD: {
    symbol: '$',
    name: 'US Dollar',
    rate: 0.012, // 1 INR = 0.012 USD (approx)
    code: 'USD'
  },
  EUR: {
    symbol: '€',
    name: 'Euro',
    rate: 0.011, // 1 INR = 0.011 EUR (approx)
    code: 'EUR'
  },
  GBP: {
    symbol: '£',
    name: 'British Pound',
    rate: 0.0095, // 1 INR = 0.0095 GBP (approx)
    code: 'GBP'
  },
  IDR: {
    symbol: 'Rp',
    name: 'Indonesian Rupiah',
    rate: 189.71, // 1 INR = 189.71 IDR (approx)
    code: 'IDR'
  },
  AED: {
    symbol: 'د.إ',
    name: 'UAE Dirham',
    rate: 0.044, // 1 INR = 0.044 AED (approx)
    code: 'AED'
  }
};

// Store the active currency (default to INR)
let activeCurrency = 'INR';

// Get the active currency configuration
export const getActiveCurrency = () => {
  // Force a fresh read from localStorage to prevent stale data
  try {
    const storedCurrency = localStorage.getItem('activeCurrency');
    if (storedCurrency && CURRENCIES[storedCurrency]) {
      activeCurrency = storedCurrency;
    }
  } catch (e) {
    console.error('Error reading currency from localStorage', e);
  }
  
  return CURRENCIES[activeCurrency];
};

// Get active currency code only
export const getActiveCurrencyCode = () => {
  // Force a fresh read
  getActiveCurrency();
  return activeCurrency;
};

// Set the active currency
export const setActiveCurrency = (currencyCode: string) => {
  if (CURRENCIES[currencyCode]) {
    const previousCurrency = activeCurrency;
    activeCurrency = currencyCode;
    
    // Save to localStorage for persistence
    try {
      localStorage.setItem('activeCurrency', currencyCode);
    } catch (e) {
      console.error('Failed to save currency to localStorage', e);
    }

    // Log the currency change for debugging
    console.log(`Currency changed from ${previousCurrency} to ${currencyCode}`);
    
    return true;
  }
  return false;
};

// Initialize from localStorage if available
export const initCurrency = () => {
  try {
    const savedCurrency = localStorage.getItem('activeCurrency');
    if (savedCurrency && CURRENCIES[savedCurrency]) {
      activeCurrency = savedCurrency;
      console.log(`Initialized currency from localStorage: ${activeCurrency}`);
    }
  } catch (e) {
    console.error('Failed to load currency from localStorage', e);
  }
};

/**
 * Convert amount from one currency to another using direct cross-rate calculation
 * @param amount The amount to convert
 * @param fromCurrency The source currency code
 * @param toCurrency The target currency code
 * @returns The converted amount
 */
export const convertCurrency = (
  amount: number,
  fromCurrency: string = 'INR',
  toCurrency: string = getActiveCurrencyCode() // Use function to always get fresh value
): number => {
  // Handle invalid input
  if (amount === undefined || amount === null || isNaN(amount)) {
    return 0;
  }

  // Normalize currency codes to valid currencies
  const sourceCurrency = CURRENCIES[fromCurrency] ? fromCurrency : 'INR';
  const targetCurrency = CURRENCIES[toCurrency] ? toCurrency : getActiveCurrencyCode();

  // If same currency, no conversion needed
  if (sourceCurrency === targetCurrency) {
    return amount;
  }

  // Get exchange rates
  const fromRate = CURRENCIES[sourceCurrency]?.rate || 1;
  const toRate = CURRENCIES[targetCurrency]?.rate || 1;

  // Convert to base currency (INR) first, then to target currency
  let convertedAmount = (amount / fromRate) * toRate;

  // Special case for IDR which has a very different scale
  if (sourceCurrency === 'IDR' && targetCurrency !== 'IDR') {
    convertedAmount = amount / CURRENCIES['IDR'].rate;
    convertedAmount = convertedAmount * CURRENCIES[targetCurrency].rate;
  } else if (sourceCurrency !== 'IDR' && targetCurrency === 'IDR') {
    convertedAmount = amount / CURRENCIES[sourceCurrency].rate;
    convertedAmount = convertedAmount * CURRENCIES['IDR'].rate;
  }

  // Round to 2 decimal places to avoid floating point issues
  return Math.round(convertedAmount * 100) / 100;
};

/**
 * Format a price in the active currency
 * @param amount The amount to format
 * @param fromCurrency The source currency code
 * @param toCurrency The target currency code
 * @returns Formatted price string with symbol
 */
export const formatPrice = (
  amount: number,
  fromCurrency: string = 'INR',
  toCurrency: string = getActiveCurrencyCode() // Use function to always get fresh value
): string => {
  if (amount === undefined || amount === null || isNaN(amount)) {
    const symbol = CURRENCIES[toCurrency]?.symbol || '₹';
    return `${symbol}0`;
  }

  // Ensure we have valid currency codes
  const sourceCurrency = CURRENCIES[fromCurrency] ? fromCurrency : 'INR';
  const targetCurrency = CURRENCIES[toCurrency] ? toCurrency : getActiveCurrencyCode();

  // Convert the amount if needed
  const convertedAmount = convertCurrency(amount, sourceCurrency, targetCurrency);
  
  // Get the symbol for the target currency
  const symbol = CURRENCIES[targetCurrency]?.symbol || '₹';

  // Format the amount with thousand separators
  const formatter = new Intl.NumberFormat('en-IN', {
    minimumFractionDigits: 0,
    maximumFractionDigits: 2,
  });
  
  return `${symbol}${formatter.format(convertedAmount)}`;
};

/**
 * Format a price specifically for PDF output
 * This version is optimized for use in the PDF generator
 * @param amount The amount to format
 * @param fromCurrency The source currency code
 * @param toCurrency The target currency code
 * @returns Formatted price string with symbol
 */
export const formatPriceForPDF = (
  amount: number,
  fromCurrency: string = 'INR',
  toCurrency: string = getActiveCurrencyCode() // Use function to always get fresh value
): string => {
  if (amount === undefined || amount === null || isNaN(amount)) {
    const symbol = CURRENCIES[toCurrency]?.symbol || '₹';
    return `${symbol}0`;
  }

  // Ensure we have valid currency codes
  const sourceCurrency = CURRENCIES[fromCurrency] ? fromCurrency : 'INR';
  const targetCurrency = CURRENCIES[toCurrency] ? toCurrency : getActiveCurrencyCode();

  // Convert the amount if needed
  const convertedAmount = convertCurrency(amount, sourceCurrency, targetCurrency);
  
  // Get the symbol for the target currency
  const symbol = CURRENCIES[targetCurrency]?.symbol || '₹';
  
  // Format with commas for thousands
  const parts = convertedAmount.toFixed(0).toString().split('.');
  parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  
  return `${symbol}${parts.join('.')}`;
};

// Initialize currency from localStorage on module load
initCurrency();

/**
 * Format a date string to a more readable format
 * @param dateString Date string in 'YYYY-MM-DD' format
 * @returns Formatted date string (e.g., "Jan 1, 2023")
 */
export const formatDate = (dateString: string): string => {
  if (!dateString) return '';
  
  try {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  } catch (e) {
    console.error('Invalid date format', e);
    return dateString;
  }
};

/**
 * Get the current date in YYYY-MM-DD format
 * @returns Current date string in YYYY-MM-DD format
 */
export const getCurrentDate = (): string => {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0');
  const day = String(now.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
};
