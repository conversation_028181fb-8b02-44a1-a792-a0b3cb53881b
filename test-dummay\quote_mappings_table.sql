-- Create quote_mappings table for storing enhanced quote data
CREATE TABLE IF NOT EXISTS quote_mappings (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    quote_id UUID NOT NULL REFERENCES quotes(id) ON DELETE CASCADE,
    quote_name TEXT NOT NULL,
    customer_name TEXT NOT NULL,
    destination TEXT NOT NULL,
    
    -- JSONB fields for flexible data storage
    hotel_mappings JSONB DEFAULT '[]'::jsonb,
    vehicle_mappings JSONB DEFAULT '[]'::jsonb,
    additional_costs JSONB DEFAULT '{}'::jsonb,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Unique constraint to prevent duplicate mappings for same quote
    UNIQUE(quote_id)
);

-- Create index for faster lookups
CREATE INDEX IF NOT EXISTS idx_quote_mappings_quote_id ON quote_mappings(quote_id);
CREATE INDEX IF NOT EXISTS idx_quote_mappings_destination ON quote_mappings(destination);

-- Enable RLS (Row Level Security)
ALTER TABLE quote_mappings ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
CREATE POLICY "Users can view all quote mappings" ON quote_mappings
    FOR SELECT USING (true);

CREATE POLICY "Users can insert quote mappings" ON quote_mappings
    FOR INSERT WITH CHECK (true);

CREATE POLICY "Users can update quote mappings" ON quote_mappings
    FOR UPDATE USING (true);

CREATE POLICY "Users can delete quote mappings" ON quote_mappings
    FOR DELETE USING (true);

-- Add comments for documentation
COMMENT ON TABLE quote_mappings IS 'Enhanced quote data for family type pricing calculations';
COMMENT ON COLUMN quote_mappings.hotel_mappings IS 'Array of hotel cost mappings with extra adult, children, infant costs';
COMMENT ON COLUMN quote_mappings.vehicle_mappings IS 'Array of vehicle cost mappings with pricing models and multipliers';
COMMENT ON COLUMN quote_mappings.additional_costs IS 'Additional cost mappings for meals, ferry, activities, guide, parking/toll';

-- Example of expected JSONB structure:

-- hotel_mappings format:
-- [
--   {
--     "hotel_name": "Hotel ABC",
--     "extra_adult_cost": 2000,
--     "children_cost": 1400,
--     "infant_cost": 0,
--     "grandparent_discount": 10
--   }
-- ]

-- vehicle_mappings format:
-- [
--   {
--     "vehicle_type": "Sedan/Dzire",
--     "pricing_type": "multiplier",
--     "base_cost": 8500,
--     "cost_multiplier": 1.0,
--     "max_capacity": 4,
--     "is_active": true
--   }
-- ]

-- additional_costs format:
-- {
--   "meal_cost_per_person": 500,
--   "ferry_cost": 1000,
--   "activity_cost_per_person": 800,
--   "guide_cost_per_day": 2000,
--   "parking_toll_multiplier": 1.0
-- } 