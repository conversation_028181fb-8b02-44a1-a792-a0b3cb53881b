import { jsPDF } from 'jspdf';
import 'jspdf-autotable';
import { formatPriceForPDF, getActiveCurrency, formatDate, getCurrentDate } from './formatters';

// --- Asset Imports ---
// Assuming your build tool handles these imports (e.g., Vite, Webpack)
import bgPage1 from '../assets/tripxplo-pdf-bg-page-1.jpg';
import bgPage2 from '../assets/tripxplo-pdf-bg-page-2.jpg'; // Background for page 2+ (except last)
import bgPage3 from '../assets/tripxplo-pdf-bg-page-3.jpg'; // Background for last page
import logoImage from '../assets/tripxplo.com.png';
import optionIcon from '../assets/option-icon.png';
import hotelIcon from '../assets/hotel-icon.png';
import starIcon from '../assets/star-icon.png';
import startupIndiaLogo from '../assets/startupindia-logo.png';
import startupTNLogo from '../assets/startupTN-logo.png';

// Type definitions
declare module 'jspdf' {
  interface jsPDF {
    autoTable: {
      (options: any): any;
      previous: any;
    }
  }
}

// Enhanced jsPDF type
export interface EnhancedPDF extends jsPDF {
  getFilename?: () => string;
}

// Interfaces (Keep existing definitions)
interface HotelRow {
  hotelName: string;
  roomType: string;
  noOfRooms: number;
  mealPlan: string;
  stayNights?: number;
  price?: number;
  currency?: string; // Added currency field for the hotel price
}
export interface TripDetails {
  customerName: string;
  destination: string;
  packageName: string;
  quoteDate: string;
  validityDate: string;
  noOfPersons: number;
  children: number;
  hotelRows: HotelRow[];
  calculateTotals: () => { 
    grandTotal: number; 
    perPersonCost: number; 
    gst: number; 
    discount?: number;
    totalHotelCost: number;
    subtotal: number;
  };
  familyType?: string;
  packageId?: string;
  tripDuration?: string;
  packageType?: string;
  tripType?: string;
  travelDate?: string;
  cabDetails?: {
    type: string;
    seats: string;
  };
  currency?: string; // Currency code (e.g., 'INR', 'USD')
  inclusions?: string[];
  exclusions?: string[];
  paymentOptions?: string[];
  paymentPolicies?: string[][];
  refundPolicies?: string[][];
  terms?: string[];
}
interface TextStyle { size: number; color: string; font?: string; }
interface TextSegment { text: string; style: TextStyle; }
interface ContentItem { segments?: TextSegment[]; text?: string; style?: TextStyle; }
interface Line { segments: TextSegment[]; width: number; }

// --- Font Constants (Registered Names) ---
const FONTS = {
  MONTSERRAT: 'Montserrat'
};

// --- Font Style Constants ---
const FONT_STYLE_NORMAL = 'normal';
const FONT_STYLE_BOLD = 'bold';
const FONT_STYLE_ITALIC = 'italic';

// --- Theme Colors ---
const THEME = {
  primary: '#15ae8b',
  primaryDark: '#0e5c57',
  primaryLight: '#eaf7f4',
  secondary: '#FFFFFF',
  accent: '#FFD700',
  text: {
    dark: '#0e5c57',
    light: '#FFFFFF',
    muted: '#7F8C8D',
    about: '#1b6560',
  },
  background: {
    light: '#F9F9F9',
    highlight: '#E8F8F5'
  },
  border: '#0e5c57'
};

// --- ACTION NEEDED: Provide ACTUAL Base64 Data for FONTS ---
const montserratRegularBase64 = 'PASTE_MONTSERRAT_REGULAR_BASE64_HERE';
const montserratBoldBase64 = 'PASTE_MONTSERRAT_BOLD_BASE64_HERE';
const montserratItalicBase64 = 'PASTE_MONTSERRAT_ITALIC_BASE64_HERE';

// Use imported variables directly
const bgPage1Base64 = bgPage1;
const bgPage2Base64 = bgPage2;
const bgPage3Base64 = bgPage3;

// --- Helper Function: Hex to RGB ---
const hexToRgb = (hex: string): [number, number, number] => {
    const fallback: [number, number, number] = [0, 0, 0];
    if (!hex) return fallback;
    const shorthandRegex = /^#?([a-f\d])([a-f\d])([a-f\d])$/i;
    hex = hex.replace(shorthandRegex, (m, r, g, b) => r + r + g + g + b + b);
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    return result ? [parseInt(result[1], 16), parseInt(result[2], 16), parseInt(result[3], 16)] : fallback;
};

// Helper function to map meal plan codes to customer-friendly descriptions
const getMealPlanDescription = (code: string | undefined): string => {
  if (!code) return 'N/A';
  
  switch (code.toUpperCase()) {
    case 'EP': return 'Room Only';
    case 'CP': return 'Breakfast Included';
    case 'MAP': return 'Breakfast and Dinner Included'; 
    case 'AP': return 'All Meals Included';
    default: return code;
  }
};

/**
 * Generates the Trip Quotation PDF document.
 */
export const generatePDF = (details: TripDetails): EnhancedPDF => {
  const {
    customerName, destination, packageName, quoteDate, validityDate,
    noOfPersons, children, hotelRows, calculateTotals, familyType,
    packageId, tripDuration, packageType, travelDate, cabDetails, tripType,
    currency = 'INR', // Default to INR if not specified
    inclusions = [], exclusions = [], paymentOptions = [],
    paymentPolicies = [], refundPolicies = [], terms = []
  } = details;
  
  // Get currency information
  const tripCurrency = currency;
  
  // We're no longer destructuring totals here, we'll use calculateTotals() directly in the Package Cost section
  // const { grandTotal, perPersonCost, gst, discount, totalHotelCost, subtotal } = calculateTotals();

  // --- Document Setup ---
  const doc = new jsPDF({ orientation: 'portrait', unit: 'mm', format: 'a4' });
  const pageWidth = doc.internal.pageSize.width;
  const pageHeight = doc.internal.pageSize.height;
  const margin = 15;
  const contentWidth = pageWidth - (margin * 2);
  let lastY = 0;
  const footerHeight = 20;
  const pageContentStartY = margin; // Content starts at margin on pages 2+
  let currentPageNumber = 1;

  // --- Color Conversion ---
  const themeRgb = {
    primary: hexToRgb('#00B69B'),        // Main green color
    primaryDark: hexToRgb('#009B84'),    // Darker green for emphasis
    primaryLight: hexToRgb('#E7F9F7'),   // Light green for backgrounds
    textDark: hexToRgb('#2D3748'),       // Dark text
    textLight: hexToRgb('#FFFFFF'),      // White text
  };

  // --- Register Custom Fonts ---
  try {
    // Only register Montserrat Regular font
    if (montserratRegularBase64 && montserratRegularBase64 !== 'PASTE_MONTSERRAT_REGULAR_BASE64_HERE') {
        doc.addFileToVFS('Montserrat-Regular.ttf', montserratRegularBase64);
        doc.addFont('Montserrat-Regular.ttf', FONTS.MONTSERRAT, FONT_STYLE_NORMAL);
        doc.addFont('Montserrat-Regular.ttf', FONTS.MONTSERRAT, FONT_STYLE_BOLD); // Use regular as fallback for bold
        doc.addFont('Montserrat-Regular.ttf', FONTS.MONTSERRAT, FONT_STYLE_ITALIC); // Use regular as fallback for italic
    } else { throw new Error('Montserrat Regular Base64 missing'); }
    console.log('Custom font registered.');
  } catch (e) {
    console.error("CRITICAL: Error registering fonts:", e);
    alert("Error loading custom fonts. PDF text will not use the correct fonts.");
    doc.setFont('helvetica', 'normal'); // Fallback
  }

  // --- Helper: Add Background Image ---
  const addBackgroundImage = (pageNumber: number, totalPages: number | null = null) => {
      let bgData: string | null = null;
      let bgFormat = 'JPEG';
      if (pageNumber === 1) {
          bgData = bgPage1Base64;
      } else if (totalPages !== null && pageNumber === totalPages && bgPage3Base64 && !bgPage3Base64.includes('PASTE_')) {
          bgData = bgPage3Base64;
      } else if (bgPage2Base64 && !bgPage2Base64.includes('PASTE_')) {
          bgData = bgPage2Base64;
      }
      if (bgData) {
          try {
              if (bgData.startsWith('data:image/png')) bgFormat = 'PNG';
              else if (bgData.startsWith('data:image/jpeg')) bgFormat = 'JPEG';
              doc.addImage(bgData, bgFormat, 0, 0, pageWidth, pageHeight, `bg_page_${pageNumber}`, 'NONE');
          } catch (e) { console.error(`Error adding background image for page ${pageNumber}:`, e); }
      } else { console.warn(`Background image data missing or placeholder used for page ${pageNumber} or dependent pages.`); }
  };

  // --- Helper: Add Page with Background (No Header for Pages 2+) ---
   const addNewPage = (): number => {
       doc.addPage();
       currentPageNumber++;
       addBackgroundImage(currentPageNumber, null);
       return pageContentStartY; // Return new starting Y (top margin)
   };

  // --- Helper: Center Text ---
   const centerText = ( text: string, y: number, fontSize?: number, fontStyle = FONT_STYLE_NORMAL ) => {
    if (fontSize) doc.setFontSize(fontSize);
     try { doc.setFont(FONTS.MONTSERRAT, fontStyle); }
     catch (e) { console.warn(`Font ${FONTS.MONTSERRAT} (${fontStyle}) not found, falling back.`); doc.setFont('helvetica', fontStyle); }
    const textWidth = doc.getStringUnitWidth(text) * doc.getFontSize() / doc.internal.scaleFactor;
    const x = (pageWidth - textWidth) / 2;
    const finalX = Math.max(margin, x);
    doc.text(text, finalX, y);
  };

  // --- Helper: Add Section Title ---
  const addSectionTitle = (title: string, y: number): number => {
    const titleSize = 16;
    const titleColor = hexToRgb('#0e5c57');
    try { doc.setFont(FONTS.MONTSERRAT, FONT_STYLE_BOLD); }
    catch (e) { doc.setFont('helvetica', FONT_STYLE_BOLD); }
    doc.setFontSize(titleSize);
    doc.setTextColor(...titleColor);
    doc.text(title, margin, y);
    return y + 8;
  };

  // --- Helper: Draw Text Block ---
  const drawTextBlock = ( textLines: string[], startY: number, fontSize: number, lineHeightFactor: number = 1.5, indent: number = 0, color = themeRgb.textDark ): number => {
    try { doc.setFont(FONTS.MONTSERRAT, FONT_STYLE_NORMAL); }
    catch (e) { doc.setFont('helvetica', FONT_STYLE_NORMAL); }
    doc.setFontSize(fontSize);
    doc.setTextColor(...color);
    let currentY = startY;
    const lineSpacing = fontSize * lineHeightFactor / doc.internal.scaleFactor;
    textLines.forEach(line => {
        const splitLines = doc.splitTextToSize(line, contentWidth - indent);
        const neededHeight = splitLines.length * lineSpacing;
        if (currentY + neededHeight > pageHeight - footerHeight - margin) {
            currentY = addNewPage();
            try { doc.setFont(FONTS.MONTSERRAT, FONT_STYLE_NORMAL); }
            catch (e) { doc.setFont('helvetica', FONT_STYLE_NORMAL); }
            doc.setFontSize(fontSize); 
            doc.setTextColor(...color);
        }
        doc.text(splitLines, margin + indent, currentY);
        currentY += neededHeight;
    });
    return currentY;
  };

  // --- Helper: Check Space and Add Page ---
  const checkAndAddPage = (currentY: number, estimatedHeight: number): number => {
    // Use original snippet's logic (adds spacing if no new page)
    if (currentY + estimatedHeight + footerHeight > pageHeight - margin) {
        return addNewPage();
    }
    return currentY + 8; // Add consistent spacing if not adding page
  };

  // =============================================
  // START PDF GENERATION - PAGE 1
  // (Keep the existing Page 1 logic as finalized previously)
  // =============================================

  addBackgroundImage(1, null);
  lastY = margin + 35;

  // Title
  try { doc.setFont(FONTS.MONTSERRAT, FONT_STYLE_ITALIC); doc.setFontSize(14); doc.setTextColor(...hexToRgb(THEME.text.about)); doc.setCharSpace(0.3); const titleText = "Customize & Book Your Holiday!"; centerText(titleText, lastY, 14, FONT_STYLE_ITALIC); doc.setCharSpace(0); lastY += 6; } catch (e) { console.error("Error rendering Page 1 title:", e); lastY += 6; }

  // Logo
  try { const logoWidth = 40; const logoHeight = 10; const logoX = (pageWidth - logoWidth) / 2; const format = logoImage.startsWith('data:image/png') ? 'PNG' : 'JPEG'; doc.addImage(logoImage, format, logoX, lastY, logoWidth, logoHeight, 'logo', 'FAST'); lastY += logoHeight + 15; } catch (e) { console.error("Error loading/adding logo image:", e); lastY += 15; }

  // "Why TripXplo?" Content Array
  const whyTripXploContent = [
    { segments: [ { text: "TripXplo's user-friendly web app", style: { size: 18, color: THEME.text.about, font: FONT_STYLE_BOLD } }, { text: " offers travellers an all-in-one solution to plan and book their dream vacations. Our innovative web app simplifies the entire travel experience, from destination selection to receiving ", style: { size: 18, color: THEME.text.about, font: FONT_STYLE_NORMAL } }, { text: "personalized trip recommendations,", style: { size: 18, color: THEME.text.about, font: FONT_STYLE_BOLD } }, { text: " all while prioritizing traveller safety and security.", style: { size: 18, color: THEME.text.about, font: FONT_STYLE_NORMAL } } ] },
    { segments: [ { text: "Tripmilestone Tours Pvt Limited", style: { size: 18, color: THEME.text.about, font: FONT_STYLE_BOLD } }, { text: ", the parent company of TripXplo, is recognized by ", style: { size: 18, color: THEME.text.about, font: FONT_STYLE_NORMAL } }, { text: "Startup India", style: { size: 18, color: THEME.text.about, font: FONT_STYLE_BOLD } }, { text: " and the ", style: { size: 18, color: THEME.text.about, font: FONT_STYLE_NORMAL } }, { text: "Department for Promotion of Industry and Internal Trade (DPIIT) under the Ministry of Commerce & Industry.", style: { size: 18, color: THEME.text.about, font: FONT_STYLE_BOLD } } ] },
    { segments: [ { text: "TripXplo Online Platform Officially launched by", style: { size: 18, color: THEME.text.about, font: FONT_STYLE_NORMAL } }, { text: " StartupTN,", style: { size: 18, color: THEME.text.about, font: FONT_STYLE_BOLD } }, { text: " Govt. of Tamil Nadu.", style: { size: 18, color: THEME.text.about, font: FONT_STYLE_BOLD } } ] },
    { text: "Transparent Pricing | No Hidden Costs", style: { size: 16, color: THEME.primary } },
    { text: "Customize: Choose from 100+ Hotels/Resorts", style: { size: 16, color: THEME.primary } },
    { text: "4.9 Star ratings on Google with 100+ reviews", style: { size: 16, color: THEME.primary } }
  ];

  // Render Paragraphs 1, 2, and 3
  for (let i = 0; i < 3; i++) {
      const content = whyTripXploContent[i];
      if (content.segments) {
          let currentY = lastY; let lines: Line[] = []; let currentLine: Line = { segments: [], width: 0 };
          content.segments.forEach(segment => { const fontStyle = segment.style.font || FONT_STYLE_NORMAL; const fontSize = segment.style.size; try { doc.setFont(FONTS.MONTSERRAT, fontStyle); } catch (e) { doc.setFont('helvetica', fontStyle); } doc.setFontSize(fontSize); const words = segment.text.trim().split(/ +/); words.forEach(word => { if (!word) return; const wordWithSpace = word + ' '; const wordWidth = doc.getStringUnitWidth(wordWithSpace) * fontSize / doc.internal.scaleFactor; if (currentLine.width + wordWidth > contentWidth && currentLine.segments.length > 0) { lines.push(currentLine); currentLine = { segments: [], width: 0 }; } currentLine.segments.push({ text: word, style: segment.style }); currentLine.width += wordWidth; }); });
          if (currentLine.segments.length > 0) lines.push(currentLine);
          const lineHeight = (18 * 1.4) / doc.internal.scaleFactor;
          lines.forEach((line, lineIndex) => { let x = margin; line.segments.forEach((seg, segIndex) => { const textToDraw = segIndex === 0 ? seg.text : ' ' + seg.text; const style = seg.style.font || FONT_STYLE_NORMAL; const size = seg.style.size; try { doc.setFont(FONTS.MONTSERRAT, style); } catch(e){ doc.setFont('helvetica', style); } doc.setFontSize(size); doc.setTextColor(...hexToRgb(seg.style.color)); doc.text(textToDraw, x, currentY + (lineIndex * lineHeight)); x += doc.getStringUnitWidth(textToDraw) * size / doc.internal.scaleFactor; }); });
          lastY += (lines.length * lineHeight) + 5;
      }
  }

  lastY += 12; // Vertical space before icon points

  // Render Icon Points (Center Block, Align Left Edges)
  const pointIconSize = 6; const pointIconPadding = 4; const pointFontSize = 16;
  let maxBlockWidth = 0;
  try { doc.setFont(FONTS.MONTSERRAT, FONT_STYLE_NORMAL); } catch(e){ doc.setFont('helvetica'); } doc.setFontSize(pointFontSize);
  for (let i = 3; i < whyTripXploContent.length; i++) { const content = whyTripXploContent[i]; if (content.text) { const text = content.text; const tempTextAvailableWidth = contentWidth - pointIconSize - pointIconPadding; const splitTempText = doc.splitTextToSize(text, tempTextAvailableWidth); const textDims = doc.getTextDimensions(splitTempText); const itemWidth = pointIconSize + pointIconPadding + textDims.w; maxBlockWidth = Math.max(maxBlockWidth, itemWidth); } }
  maxBlockWidth = Math.min(maxBlockWidth, contentWidth); const blockStartX = Math.max(margin, (pageWidth - maxBlockWidth) / 2);

  for (let i = 3; i < whyTripXploContent.length; i++) {
       const content = whyTripXploContent[i];
        if (content.text && content.style?.color) {
            let iconData: string | null = null; if (i === 3) iconData = optionIcon; else if (i === 4) iconData = hotelIcon; else if (i === 5) iconData = starIcon;
            const text = content.text; const pointLineHeight = (pointFontSize * 1.4) / doc.internal.scaleFactor; const iconX = blockStartX; const textX = blockStartX + pointIconSize + pointIconPadding; const textAvailableWidth = pageWidth - margin - textX; const iconY = lastY + pointLineHeight / 2 - pointIconSize / 2 ; const textBaselineY = lastY + pointLineHeight * 0.8;
            if (iconData) { try { let iconFormat = 'PNG'; if (iconData.startsWith('data:image/jpeg')) iconFormat = 'JPEG'; doc.addImage(iconData, iconFormat, iconX, iconY, pointIconSize, pointIconSize); } catch (e) { console.error("Error adding icon:", i, e); doc.text("•", iconX, textBaselineY); } } else { doc.text("•", iconX, textBaselineY); console.warn(`Icon data missing: ${i}`); }
            try { doc.setFont(FONTS.MONTSERRAT, FONT_STYLE_NORMAL); } catch(e){ doc.setFont('helvetica'); } doc.setFontSize(pointFontSize); doc.setTextColor(...hexToRgb(THEME.primary)); const splitText = doc.splitTextToSize(text, textAvailableWidth); doc.text(splitText, textX, textBaselineY);
            const textHeight = doc.getTextDimensions(splitText).h; lastY += Math.max(textHeight, pointIconSize) + 6;
        }
  }
  lastY += 8;

   // Add Footer Logos to Page 1 Bottom (Above Footer Bar Area)
    const footerLogoH = 12; const footerLogoW = 36; const footerLogoSpacing = 4; const footerLogosY = pageHeight - footerHeight - footerLogoH - 5; const totalFooterLogosWidth = (footerLogoW * 2) + footerLogoSpacing; let footerLogoX = Math.max(margin, (pageWidth - totalFooterLogosWidth) / 2);
    if (startupIndiaLogo) { try { let format = 'PNG'; if (startupIndiaLogo.startsWith('data:image/jpeg')) format = 'JPEG'; doc.addImage(startupIndiaLogo, format, footerLogoX, footerLogosY, footerLogoW, footerLogoH); } catch (e) { console.error("Error adding startupindia logo:", e); } } else { console.warn("Startup India Logo not imported."); }
    footerLogoX += footerLogoW + footerLogoSpacing; if (startupTNLogo) { try { if (footerLogoX + footerLogoW <= pageWidth - margin) { let format = 'PNG'; if (startupTNLogo.startsWith('data:image/jpeg')) format = 'JPEG'; doc.addImage(startupTNLogo, format, footerLogoX, footerLogosY, footerLogoW, footerLogoH); } else { console.warn("Second footer logo would exceed page width."); } } catch (e) { console.error("Error adding startupTN logo:", e); } } else { console.warn("StartupTN Logo not imported."); }

  // --- Draw Footer (Only on First Page) ---
  const totalPages = doc.getNumberOfPages();
  const footerStartY = pageHeight - footerHeight;
  
  // Go to first page
  doc.setPage(1);
  
  // Draw footer on first page
  doc.setFillColor(...themeRgb.primaryDark);
  doc.rect(0, footerStartY, pageWidth, footerHeight, 'F');
  doc.setTextColor(...themeRgb.textLight);
  
  try { doc.setFont(FONTS.MONTSERRAT, FONT_STYLE_NORMAL); }
  catch(e) { doc.setFont('helvetica', FONT_STYLE_NORMAL); }

  doc.setFontSize(8);
  centerText("TripXplo | Book Smart, Travel Easy | <EMAIL>", footerStartY + 5, 8, FONT_STYLE_NORMAL);
  centerText("WhatsApp: 9442424492, 9444041468 | Phone: 7695993808 | Web: tripxplo.com", footerStartY + 10, 8, FONT_STYLE_NORMAL);

  doc.setFontSize(7);
  centerText("TripXplo by Tripmilestone (P) Ltd", footerStartY + 15, 7, FONT_STYLE_NORMAL);

  try { doc.setFont(FONTS.MONTSERRAT, FONT_STYLE_NORMAL); }
  catch(e) { doc.setFont('helvetica', FONT_STYLE_NORMAL); }
  
  doc.setFontSize(8);
  const pageNumText = `Page 1 of ${totalPages}`;
  const pageNumWidth = doc.getStringUnitWidth(pageNumText) * doc.getFontSize() / doc.internal.scaleFactor;
  doc.text(pageNumText, pageWidth - margin - pageNumWidth, footerStartY + 17);

  // =============================================
  // START PDF GENERATION - PAGE 2 (Quote Details)
  // =============================================
  lastY = addNewPage();
  lastY = 30; // Set top margin to 30px

  // --- Main Title ---
  doc.setTextColor(...hexToRgb('#0e5c57'));
  try { doc.setFont(FONTS.MONTSERRAT, FONT_STYLE_BOLD); }
  catch(e){ doc.setFont('helvetica', FONT_STYLE_BOLD); }
  doc.setFontSize(20);
  centerText("TripXplo Package Quote", lastY);
  lastY += 12; // Reduced spacing for subtitle

  // Add Package Name as subtitle
  doc.setFontSize(16);
  centerText(packageName || '-', lastY);
  lastY += 8; // Consistent 20px spacing

  // --- Package Details Box ---
  const boxHeight = 45; // Keep increased height
  
  // Only apply background color to the header - use Primary color
  const headerHeight = 10;
  doc.setFillColor(...themeRgb.primary);
  doc.roundedRect(margin, lastY, contentWidth, headerHeight, 3, 3, 'F');
  
  // No background for content area, just a border
  doc.setDrawColor(...themeRgb.primary);
  doc.setFillColor(255, 255, 255); // White background
  doc.roundedRect(margin, lastY + headerHeight, contentWidth, boxHeight - headerHeight, 3, 3, 'S');
  
  // Header text - use Primary Light for text
  doc.setTextColor(...themeRgb.textLight);
  try { doc.setFont(FONTS.MONTSERRAT, FONT_STYLE_BOLD); }
  catch(e){ doc.setFont('helvetica', FONT_STYLE_BOLD); }
  doc.setFontSize(14);
  doc.text("Package Details", margin + 5, lastY + 7);
  
  // Content text - use Primary Dark for text
  doc.setTextColor(...themeRgb.primaryDark);
  try { doc.setFont(FONTS.MONTSERRAT, FONT_STYLE_NORMAL); }
  catch(e){ doc.setFont('helvetica'); }
  doc.setFontSize(12);
  
  const detailsStartY = lastY + headerHeight + 8;
  const colWidth = contentWidth / 2;
  
  // Left column
  doc.text(`Package ID: ${packageId || '-'}`, margin + 5, detailsStartY);
  doc.text(`Customer Name: ${customerName || '-'}`, margin + 5, detailsStartY + 8);
  doc.text(`Package Type: ${packageType || '-'}`, margin + 5, detailsStartY + 16);
  
  // Right column
  doc.text(`No of Adults: ${noOfPersons || '-'}, Children: ${children || '-'}`, margin + colWidth, detailsStartY);
  doc.text(`Quote Date: ${quoteDate || '-'}`, margin + colWidth, detailsStartY + 8);
  doc.text(`Destination: ${destination || '-'}`, margin + colWidth, detailsStartY + 16);
  
  // Full row (Family Type)
  doc.text(`Family Type: ${familyType || '-'}`, margin + 5, detailsStartY + 24);
  
  lastY += boxHeight + 6; // Consistent 20px spacing

  // --- Trip Details Box (Modified) ---
  const tripBoxHeight = 40; // Increased height for additional content
  
  // Only apply background color to the header - use Primary color
  doc.setFillColor(...themeRgb.primary);
  doc.roundedRect(margin, lastY, contentWidth, headerHeight, 3, 3, 'F');
  
  // No background for content area, just a border
  doc.setDrawColor(...themeRgb.primary);
  doc.setFillColor(255, 255, 255); // White background
  doc.roundedRect(margin, lastY + headerHeight, contentWidth, tripBoxHeight - headerHeight, 3, 3, 'S');
  
  // Fetch total nights from hotel rows
  const totalNights = hotelRows.reduce((sum, row) => sum + (row.stayNights || 0), 0);
  
  // Determine trip duration to display
  const tripDurationDisplay = tripDuration || 
                       (totalNights > 0 ? `${totalNights}N/${totalNights + 1}D` : "N/A");
  
  // Cache these values for later use
  const tripDurationText = tripDurationDisplay;
  const destinationText = destination || "N/A";
  
  // Centered Trip Duration + Destination Header
  doc.setTextColor(...themeRgb.textLight);
  try { doc.setFont(FONTS.MONTSERRAT, FONT_STYLE_BOLD); }
  catch(e){ doc.setFont('helvetica', FONT_STYLE_BOLD); }
  doc.setFontSize(12);
  
  // Create combined header text - fix duration display correctly
  const tripHeaderText = `${tripDurationDisplay} - ${destination || "N/A"}`;
  
  // Center the header
  const tripHeaderWidth = doc.getStringUnitWidth(tripHeaderText) * doc.getFontSize() / doc.internal.scaleFactor;
  const tripHeaderX = margin + (contentWidth - tripHeaderWidth) / 2;
  doc.text(tripHeaderText, tripHeaderX, lastY + 7);
  
  // Trip Details Content - ensure consistent use of primaryDark color for text
  doc.setTextColor(...themeRgb.primaryDark);
  try { doc.setFont(FONTS.MONTSERRAT, FONT_STYLE_NORMAL); }
  catch(e){ doc.setFont('helvetica'); }
  doc.setFontSize(12);
  
  const tripContentStartY = lastY + headerHeight + 8;
  
  // Format travel date to DD-MM-YYYY with suffix
  const formatTravelDate = (date: string) => {
    if (!date) return '-';
    const d = new Date(date);
    const day = d.getDate();
    const month = d.toLocaleString('default', { month: 'long' });
    const year = d.getFullYear();
    const suffix = getDaySuffix(day);
    return `${day}${suffix} ${month} ${year}`;
  };
  
  // Travel Date with proper formatting - LINE 1
  const formattedTravelDate = formatTravelDate(travelDate || '');
  doc.text(`Travel Date: ${formattedTravelDate}`, margin + 5, tripContentStartY);
  
  // Combined Trip Duration and Trip Type (simplified format) - LINE 2
  const combinedDurationType = `${tripDurationDisplay} - ${tripType || 'Family Trip'}`;
  doc.text(combinedDurationType, margin + 5, tripContentStartY + 8);
  
  // Cab Details - LINE 3
  const cabType = cabDetails?.type || '-';
  const cabSeats = cabDetails?.seats || '-';
  const cabDetailsText = `Cab: ${cabType || '-'}, ${cabSeats} Seater`;
  doc.text(cabDetailsText, margin + 5, tripContentStartY + 16);
  
  // Use appropriate spacing to avoid overlap
  lastY += tripBoxHeight + 4;

  // --- Hotel details Table with rounded rectangle ---
  const hotelTableStartY = lastY;
  
  // Calculate estimated hotel table height based on rows
  const estimatedRowHeight = 5; // Approximate height per row
  const estimatedHotelTableHeight = (hotelRows.length + 1) * estimatedRowHeight + 5; // Add padding
  
  // Draw rounded rectangle for hotel table - exact same size as the table
  doc.setFillColor(...themeRgb.primaryLight);
  doc.setDrawColor(...themeRgb.primary);
  doc.roundedRect(margin, hotelTableStartY, contentWidth, estimatedHotelTableHeight, 3, 3, 'F');

  // Improved table styling with Primary color header background
  (doc as any).autoTable({
    startY: hotelTableStartY,
    head: [["Accommodation Hotel/Resort", "Room Type", "Rooms", "Stay Nights", "Meal Plan"]],
    body: hotelRows.map(row => [
      row.hotelName || 'N/A',
      row.roomType || 'N/A',
      `${row.noOfRooms || 1} Room${(row.noOfRooms || 1) > 1 ? 's' : ''}`,
      `${row.stayNights || 0} Night${(row.stayNights || 0) > 1 ? 's' : ''}`,
      getMealPlanDescription(row.mealPlan)      
    ]),
    theme: 'grid',
    headStyles: {
      fillColor: themeRgb.primary, 
      textColor: themeRgb.textLight, 
      font: FONTS.MONTSERRAT, 
      fontStyle: FONT_STYLE_BOLD, 
      fontSize: 11,
      halign: 'center',
      cellPadding: { top: 3, right: 3, bottom: 3, left: 3 },
      overflow: 'linebreak',
      valign: 'middle'
    },
    styles: {
      fontSize: 9,
      font: FONTS.MONTSERRAT, 
      fontStyle: FONT_STYLE_NORMAL,
      textColor: hexToRgb(THEME.primaryDark),
      lineColor: [200, 200, 200], 
      lineWidth: 0.1, 
      halign: 'left', 
      valign: 'middle',
      cellPadding: { top: 3, right: 3, bottom: 3, left: 3 },
      overflow: 'linebreak'
    },
    columnStyles: {
      0: { cellWidth: contentWidth * 0.30 },
      1: { cellWidth: contentWidth * 0.25 },
      2: { cellWidth: contentWidth * 0.15, halign: 'center' },
      3: { cellWidth: contentWidth * 0.15, halign: 'center' },
      4: { cellWidth: contentWidth * 0.15 }
    },
    margin: { left: margin, right: margin }
  });

  // Update lastY after hotel table
  lastY = (doc as any).lastAutoTable.finalY + 4;

  // --- Improved Smart Page Break Logic ---
  // Calculate remaining space on page
  const remainingSpace = pageHeight - lastY - footerHeight;
  
  // Determine space needs for sections
  const minimumSpaceForInclusions = 20; // Space for inclusions section
  const minimumSpaceForPricing = 20; // Space for pricing box
  
  // We want to keep package cost on page 2 if possible
  let shouldAddNewPage = false;
  let inclusionsWillFit = remainingSpace >= minimumSpaceForInclusions;
  let bothSectionsWillFit = remainingSpace >= (minimumSpaceForInclusions + minimumSpaceForPricing + 10); // 10px padding
  
  // Only add a new page if we really need to, prioritizing keeping Package Cost on page 2
  // For 3 or fewer hotels, we try to keep everything on page 2
  if (hotelRows.length > 3 && !inclusionsWillFit) {
    // Too many hotels and not enough space for inclusions - start page 3
    doc.addPage();
    addBackgroundImage(currentPageNumber + 1, null);
    currentPageNumber++;
    lastY = margin;
    shouldAddNewPage = true;
  }

  // --- Package Inclusions Section ---
  // Now add the Package Inclusions header
  doc.setFillColor(...themeRgb.primary);
  doc.setTextColor(...themeRgb.textLight);
  try { doc.setFont(FONTS.MONTSERRAT, FONT_STYLE_BOLD); }
  catch(e){ doc.setFont('helvetica', FONT_STYLE_BOLD); }
  doc.setFontSize(13);
  
  // Draw the header as a rounded rectangle with text
  doc.roundedRect(margin, lastY, contentWidth, 10, 3, 3, 'F');
  doc.text("Package Inclusions", margin + 5, lastY + 7);
  lastY += 15;

  // Content - use Primary Dark for text
  doc.setTextColor(...themeRgb.primaryDark);
  try { doc.setFont(FONTS.MONTSERRAT, FONT_STYLE_NORMAL); }
  catch(e){ doc.setFont('helvetica'); }
  doc.setFontSize(11);
  
  // Use inclusions if provided, otherwise use default inclusions
  const inclusionList = Array.isArray(inclusions) && inclusions.length > 0 
    ? inclusions 
    : [
        "• Welcome Drink on Arrival",
        "• Daily Breakfast & Dinner Included",
        "• Private Cab for Transfer + Sightseeing",
        "• Free ICICI Lombard - 10 Days Domestic Travel Insurance"
      ];
  
  // Draw each inclusion item
  inclusionList.forEach((item, index) => {
    // Ensure item has a bullet point at the beginning
    const itemText = item.trim().startsWith('•') ? item : `• ${item}`;
    doc.text(itemText, margin + 10, lastY + (index * 6));
  });
  
  // Add spacing after inclusions
  lastY += (inclusionList.length * 5) + 5;

  // --- Package Cost Section ---
  // Check if we need a new page for pricing section
  // Only add a new page if both sections won't fit and we haven't already added a page
  if (!shouldAddNewPage && !bothSectionsWillFit) {
    doc.addPage();
    addBackgroundImage(currentPageNumber + 1, null);
    currentPageNumber++;
    lastY = margin;
  }
  
  // Header with Primary color background
  doc.setFillColor(...themeRgb.primary);
  doc.roundedRect(margin, lastY, contentWidth, 10, 3, 3, 'F');
  
  // No background for content area, fill AND draw border (FD mode)
  doc.setDrawColor(...themeRgb.primary);
  doc.setFillColor(...themeRgb.primaryLight);
  doc.roundedRect(margin, lastY + 10, contentWidth, 40, 3, 3, 'FD'); // Use FD mode for fill and draw
  
  // Header Text - use Primary Light for text
  doc.setTextColor(...themeRgb.textLight);
  try { doc.setFont(FONTS.MONTSERRAT, FONT_STYLE_BOLD); }
  catch(e){ doc.setFont('helvetica', FONT_STYLE_BOLD); }
  doc.setFontSize(12);
  
  // Create the title using fields from dashboard
  const packageTitle = `Package Cost for ${tripDurationDisplay} - ${destination || "N/A"}${packageType ? ` - ${packageType}` : ''} Trip`;
  doc.text(packageTitle, margin + 5, lastY + 7);
  
  // Content - use Primary Dark for text from THEME
  doc.setTextColor(...hexToRgb(THEME.primaryDark));
  try { doc.setFont(FONTS.MONTSERRAT, FONT_STYLE_NORMAL); }
  catch(e){ doc.setFont('helvetica'); }
  doc.setFontSize(12);
  
  // Ensure totals exist before accessing
  const totals = calculateTotals ? calculateTotals() : { perPersonCost: 0, gst: 0, discount: 0, grandTotal: 0, totalHotelCost: 0, subtotal: 0 };
  
  // Round the totals
  const roundedPerPersonCost = roundTo(totals.perPersonCost);
  const roundedGst = roundTo(totals.gst);
  const roundedDiscount = roundTo(totals.discount);
  const roundedGrandTotal = roundTo(totals.grandTotal);
  const roundedTotalHotelCost = roundTo(totals.totalHotelCost);
  
  // Position variables for better alignment
  const labelX = margin + 10;
  const valueX = margin + 110; // Increased spacing
  const lineHeight = 8; // Line height between items
  let itemY = lastY + 20; // Starting Y position for first item
  
  // Use the correct currency symbol based on currency
  const currencySymbol = tripCurrency === 'INR' ? '₹' : getActiveCurrency().symbol || '₹';
  
  // Format price with proper currency symbol
  const formatPrice = (amount: number): string => {
    return `${currencySymbol} ${amount.toLocaleString('en-IN')}`;
  };

  // No of Adults
  doc.text(`No of Adults:`, labelX, itemY);
  doc.text(`${noOfPersons || 0}`, valueX, itemY);
  itemY += lineHeight;
  
  // Per Person Cost
  doc.text(`Per Person Cost:`, labelX, itemY);
  doc.text(`${formatPrice(roundedPerPersonCost)}`, valueX, itemY);
  itemY += lineHeight;
  
  // GST
  doc.text(`GST:`, labelX, itemY);
  doc.text(`${formatPrice(roundedGst)}`, valueX, itemY);
  itemY += lineHeight;
  
  // Discount (only if present)
  if (roundedDiscount && roundedDiscount > 0) {
    doc.text(`Discount:`, labelX, itemY);
    doc.text(`${formatPrice(roundedDiscount)}`, valueX, itemY);
    itemY += lineHeight;
  }
  
  // Total Package Cost with emphasis
  doc.setTextColor(...hexToRgb(THEME.primaryDark)); 
  try { doc.setFont(FONTS.MONTSERRAT, FONT_STYLE_BOLD); }
  catch(e){ doc.setFont('helvetica', FONT_STYLE_BOLD); }
  doc.setFontSize(13);
  doc.text(`Final Package Cost:`, labelX, itemY);
  doc.text(`${formatPrice(roundedGrandTotal)}`, valueX, itemY);
  
  // Add currency note if not INR
  if (tripCurrency !== 'INR') {
    doc.setFontSize(9);
    try { doc.setFont(FONTS.MONTSERRAT, FONT_STYLE_ITALIC); }
    catch(e){ doc.setFont('helvetica', FONT_STYLE_ITALIC); }
    doc.text(`*All prices shown in ${getActiveCurrency().name}`, labelX, itemY + 8);
  }
  
  // Update lastY for next section - add proper spacing after the package cost box
  lastY += 50 + 10; // Box height (40) + header (10) + additional spacing (10)

  // =============================================
  // START PDF GENERATION - PAGE 3 (Package Details)
  // =============================================
  
  // Check if we need a new page before Exclusions
  if (pageHeight - lastY - footerHeight < 50) {
    doc.addPage();
    addBackgroundImage(currentPageNumber + 1, null);
    currentPageNumber++;
    lastY = 30; // Top margin for new page
  }

  // --- Exclusions ---
  doc.setTextColor(...hexToRgb('#0e5c57'));
  lastY = addSectionTitle("Exclusions", lastY);
  doc.setTextColor(...hexToRgb('#039674'));
  
  // Use exclusions if provided, otherwise use default exclusions
  const exclusionList = Array.isArray(exclusions) && exclusions.length > 0
    ? exclusions
    : [
        "• Train or airfare.",
        "• Personal expenses (entry charges, telephone, internet, laundry, etc.).",
        "• Adventure activities unless specified.",
        "• Additional sightseeing.",
        "• Museum/park entry fees, Jeep safari, room heater charges.",
        "• Snow vehicle fare.",
        "• Travel insurance unless specified.",
        "• Anything not mentioned in inclusion"
      ];
  
  lastY = drawTextBlock(exclusionList, lastY + 2, 12, 1.5, 5);
  
  // Check if we need a new page before Payment Options
  if (pageHeight - lastY - footerHeight < 50) {
    doc.addPage();
    addBackgroundImage(currentPageNumber + 1, null);
    currentPageNumber++;
    lastY = 30; // Top margin for new page
  } else {
    lastY += 6; // Add spacing only if not adding page
  }

  // --- Payment Options ---
  doc.setTextColor(...hexToRgb('#0e5c57'));
  lastY = addSectionTitle("Payment Options", lastY);
  doc.setTextColor(...hexToRgb('#039674'));
  
  // Use payment options if provided, otherwise use default options
  const paymentOptionsList = Array.isArray(paymentOptions) && paymentOptions.length > 0
    ? paymentOptions
    : [
        "• 50% advance payment at the time of booking",
        "• Balance 50% payment 15 days before travel",
        "• Payment can be made via bank transfer, UPI, or credit card"
      ];
  
  lastY = drawTextBlock(paymentOptionsList, lastY + 2, 12, 1.5, 5);
  
  // Check if we need a new page before Payment Policy
  if (pageHeight - lastY - footerHeight < 50) {
    doc.addPage();
    addBackgroundImage(currentPageNumber + 1, null);
    currentPageNumber++;
    lastY = 30; // Top margin for new page
  } else {
    lastY += 6; // Add spacing only if not adding page
  }

  // --- Payment Policy Table ---
  doc.setTextColor(...hexToRgb('#0e5c57'));
  lastY = addSectionTitle("Payment Policy", lastY);
  doc.setTextColor(...hexToRgb('#039674'));
  const paymentPoliciesBody = [
    ["Cancellation", "100%", "50%", "25%", "No Refund"],
    ["Refund", "Full", "50%", "25%", "No Refund"]
  ];
  (doc as any).autoTable({
    startY: lastY + 2,
    head: [["", "Hotel & Other Confirmation", "30 Days Before", "21 Days Before", "7 Days Before"]],
    body: paymentPoliciesBody,
    theme: 'grid',
    headStyles: { 
      fillColor: themeRgb.primary, 
      textColor: themeRgb.textLight, 
      font: FONTS.MONTSERRAT, 
      fontStyle: FONT_STYLE_BOLD, 
      fontSize: 8, 
      halign: 'center',
      cellPadding: 2 
    },
    styles: { 
      fontSize: 7, 
      font: FONTS.MONTSERRAT, 
      textColor: hexToRgb('#039674'), 
      lineColor: hexToRgb(THEME.border),
      lineWidth: 0.1, 
      halign: 'center', 
      valign: 'middle' 
    },
    alternateRowStyles: { fillColor: themeRgb.primaryLight },
    columnStyles: { 0: { halign: 'left', fontStyle: FONT_STYLE_BOLD, cellWidth: contentWidth * 0.25 } },
    margin: { left: margin, right: margin }
  });
  lastY = (doc as any).autoTable.previous.finalY + 10;

  // Check if we need a new page before Refund Policy
  if (pageHeight - lastY - footerHeight < 50) {
    doc.addPage();
    addBackgroundImage(currentPageNumber + 1, null);
    currentPageNumber++;
    lastY = 30; // Top margin for new page
  }

  // --- Refund Policy Table ---
  doc.setTextColor(...hexToRgb('#0e5c57'));
  lastY = addSectionTitle("Refund Policy", lastY);
  doc.setTextColor(...hexToRgb('#039674'));
  const refundPoliciesBody = [
    ["More than 30 days before trip", "Full Refund"],
    ["15-30 days before trip", "50% Refund"],
    ["Within 15 days before trip", "No Refund"]
  ];
  (doc as any).autoTable({
    startY: lastY + 2,
    head: [["Time Before Trip", "Refund Eligibility"]],
    body: refundPoliciesBody,
    theme: 'grid',
    headStyles: { 
      fillColor: themeRgb.primary, 
      textColor: themeRgb.textLight, 
      font: FONTS.MONTSERRAT, 
      fontStyle: FONT_STYLE_BOLD, 
      fontSize: 8, 
      halign: 'center', 
      cellPadding: 2 
    },
    styles: { 
      fontSize: 8, 
      font: FONTS.MONTSERRAT, 
      textColor: hexToRgb('#039674'), 
      lineColor: hexToRgb(THEME.border),
      lineWidth: 0.1, 
      halign: 'left', 
      valign: 'middle' 
    },
    alternateRowStyles: { fillColor: themeRgb.primaryLight },
    columnStyles: {
      0: { cellWidth: contentWidth * 0.5 }, 
      1: { cellWidth: contentWidth * 0.5, fontStyle: FONT_STYLE_BOLD } 
    },
    margin: { left: margin, right: margin }
  });
  lastY = (doc as any).autoTable.previous.finalY + 10;

  // Check if we need a new page before Terms & Conditions
  if (pageHeight - lastY - footerHeight < 40) {
    doc.addPage();
    addBackgroundImage(currentPageNumber + 1, null);
    currentPageNumber++;
    lastY = 30; // Top margin for new page
  }

  // --- General Terms & Conditions ---
  doc.setTextColor(...hexToRgb('#0e5c57'));
  lastY = addSectionTitle("General Terms & Conditions", lastY);
  doc.setTextColor(...hexToRgb('#039674'));
  
  // Use terms if provided, otherwise use default terms
  const termsList = Array.isArray(terms) && terms.length > 0
    ? terms
    : [
        "• Prices are subject to change without prior notice",
        "• Hotel check-in/check-out times are as per hotel policy",
        "• Rates are valid for the mentioned dates only"
      ];
  
  lastY = drawTextBlock(termsList, lastY + 2, 12, 1.5, 5);

  // Check if we need a new page before Final Price
  if (pageHeight - lastY - footerHeight < 20) {
    doc.addPage();
    addBackgroundImage(currentPageNumber + 1, null);
    currentPageNumber++;
    lastY = 30; // Top margin for new page
  }

  // --- Final Price Section ---
  const finalPriceBoxHeight = 15;
  doc.setFillColor(themeRgb.primary[0], themeRgb.primary[1], themeRgb.primary[2]);
  doc.roundedRect(margin, lastY, contentWidth, finalPriceBoxHeight, 3, 3, 'F');
  doc.setTextColor(...themeRgb.textLight);
  try { doc.setFont(FONTS.MONTSERRAT, FONT_STYLE_BOLD); } catch(e){ doc.setFont('helvetica', FONT_STYLE_BOLD); }
  doc.setFontSize(11);
  const labelY = lastY + (finalPriceBoxHeight / 2) + (doc.getFontSize() / 4);
  doc.text(`FINAL PRICE (Incl. GST) - ${getActiveCurrency().code}`, margin + 5, labelY);
  doc.setFontSize(16);
  const totalPrice = formatPriceForPDF(totals.grandTotal, tripCurrency);
  const priceTextWidth = doc.getStringUnitWidth(totalPrice) * doc.getFontSize() / doc.internal.scaleFactor;
  doc.text(totalPrice, pageWidth - margin - 20 - priceTextWidth, labelY);
  lastY += finalPriceBoxHeight + 5;

   // --- Filename Generation ---
   const dateSegments = quoteDate?.split('-') || ['', '', ''];
   const month = quoteDate ? new Date(quoteDate).toLocaleString('default', { month: 'short' }) : 'Mon';
   const day = quoteDate ? parseInt(dateSegments[2]) : 1;
   const daySuffix = getDaySuffix(day);
   return Object.assign(doc, {
     getFilename: () => {
       const cleanDestination = destination?.split('-')[0].trim().replace(/ /g, '_') || 'Destination';
       const cleanCustomerName = customerName?.trim().replace(/ /g, '_') || 'Customer';
       const familySegment = familyType ? `-${familyType.replace(/ /g, '_')}` : '';
       return `${cleanCustomerName}-${cleanDestination}-Quote-${day}${daySuffix}-${month}${familySegment}.pdf`;
     }
   });
 };
 
 // Helper function to get day suffix (th, st, nd, rd)
 function getDaySuffix(day: number): string { if (!day) return ''; if (day > 3 && day < 21) return 'th'; switch (day % 10) { case 1: return 'st'; case 2: return 'nd'; case 3: return 'rd'; default: return 'th'; } }
 
 // Function to handle empty or N/A values
 const formatValue = (value: string | number | undefined): string => {
   if (value === undefined || value === null || value === '' || value === 'N/A') {
     return '-';
   }
   return value.toString();
 };
 

// Helper function to round a number to a specified decimal place
const roundTo = (value: number, decimals: number = 0): number => {
  const factor = Math.pow(10, decimals);
  return Math.round(value * factor) / factor;
};
