import React, { useState, useEffect } from 'react';
import { 
  Package, 
  Users, 
  CreditCard, 
  Star,
  Car,
  Bed,
  Eye,
  Search,
  Filter,
  Calendar,
  DollarSign,
  TrendingUp,
  AlertCircle
} from 'lucide-react';
import { supabase } from '../lib/supabaseClient';

interface FamilyEMIPackage {
  id: string;
  quote_id: string;
  family_type_id: string;
  family_type_name: string;
  no_of_adults: number;
  no_of_children: number;
  no_of_child: number;
  no_of_infants: number;
  family_count: number;
  rooms_need: number;
  cab_type: string;
  cab_capacity: number;
  destination_category: string;
  season_category: string;
  package_duration_days: number;
  hotel_cost: number;
  vehicle_cost: number;
  additional_costs: number;
  subtotal: number;
  total_price: number;
  discount_amount: number;
  emi_enabled: boolean;
  min_emi_months: number;
  max_emi_months: number;
  emi_processing_fee_percent: number;
  emi_interest_rate_percent: number;
  is_public_visible: boolean;
  public_display_order: number;
  created_at: string;
  updated_at: string;
  emi_plans: EMIPlan[];
}

interface EMIPlan {
  id: string;
  family_price_id: string;
  emi_months: number;
  monthly_amount: number;
  total_amount: number;
  processing_fee: number;
  total_interest: number;
  first_payment_amount?: number;
  subsequent_payment_amount?: number;
  final_payment_amount?: number;
  savings_vs_full_payment: number;
  effective_annual_rate: number;
  is_featured: boolean;
  marketing_label?: string;
  created_at: string;
  updated_at: string;
}

const FamilyEMIPackages: React.FC = () => {
  const [packages, setPackages] = useState<FamilyEMIPackage[]>([]);
  const [filteredPackages, setFilteredPackages] = useState<FamilyEMIPackage[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [destinationFilter, setDestinationFilter] = useState('all');
  const [familyTypeFilter, setFamilyTypeFilter] = useState('all');
  const [selectedPackage, setSelectedPackage] = useState<FamilyEMIPackage | null>(null);
  const [showEMIModal, setShowEMIModal] = useState(false);
  const [availableDestinations, setAvailableDestinations] = useState<string[]>([]);
  const [availableFamilyTypes, setAvailableFamilyTypes] = useState<{id: string, name: string}[]>([]);

  const loadFamilyEMIPackages = async () => {
    try {
      setLoading(true);
      setError(null);
      
      // Query family_type_prices with EMI plans
      const { data: familyPricesData, error: familyPricesError } = await supabase
        .from('family_type_prices')
        .select(`
          id,
          quote_id,
          family_type_id,
          family_type_name,
          no_of_adults,
          no_of_children,
          no_of_child,
          no_of_infants,
          family_count,
          rooms_need,
          cab_type,
          cab_capacity,
          destination_category,
          season_category,
          package_duration_days,
          hotel_cost,
          vehicle_cost,
          additional_costs,
          subtotal,
          total_price,
          discount_amount,
          emi_enabled,
          min_emi_months,
          max_emi_months,
          emi_processing_fee_percent,
          emi_interest_rate_percent,
          is_public_visible,
          public_display_order,
          created_at,
          updated_at
        `)
        .eq('emi_enabled', true)
        .eq('is_public_visible', true)
        .order('public_display_order', { ascending: true })
        .order('created_at', { ascending: false });

      if (familyPricesError) {
        throw familyPricesError;
      }

      if (!familyPricesData || familyPricesData.length === 0) {
        console.log('No family EMI packages found in database');
        setPackages([]);
        return;
      }

      // Get EMI plans for all packages
      const packageIds = familyPricesData.map(pkg => pkg.id);
      const { data: emiPlansData, error: emiPlansError } = await supabase
        .from('family_type_emi_plans')
        .select(`
          id,
          family_price_id,
          emi_months,
          monthly_amount,
          total_amount,
          processing_fee,
          total_interest,
          first_payment_amount,
          subsequent_payment_amount,
          final_payment_amount,
          savings_vs_full_payment,
          effective_annual_rate,
          is_featured,
          marketing_label,
          created_at,
          updated_at
        `)
        .in('family_price_id', packageIds)
        .order('emi_months', { ascending: true });

      if (emiPlansError) {
        throw emiPlansError;
      }

      // Combine packages with their EMI plans
      const packagesWithEMI: FamilyEMIPackage[] = familyPricesData.map(pkg => ({
        ...pkg,
        emi_plans: emiPlansData?.filter(plan => plan.family_price_id === pkg.id) || []
      }));

      console.log(`Loaded ${packagesWithEMI.length} family EMI packages from database`);
      setPackages(packagesWithEMI);
      
    } catch (error: any) {
      console.error('Error loading family EMI packages:', error);
      setError(error.message || 'Failed to load family EMI packages');
    } finally {
      setLoading(false);
    }
  };

  const loadFilterOptions = async () => {
    try {
      // Get unique destinations
      const { data: destinationsData, error: destinationsError } = await supabase
        .from('family_type_prices')
        .select('destination_category')
        .eq('emi_enabled', true)
        .eq('is_public_visible', true)
        .not('destination_category', 'is', null);

      if (destinationsError) {
        console.error('Error loading destinations:', destinationsError);
      } else {
        const uniqueDestinations = [...new Set(destinationsData?.map(d => d.destination_category).filter(Boolean))];
        setAvailableDestinations(uniqueDestinations);
      }

      // Get unique family types
      const { data: familyTypesData, error: familyTypesError } = await supabase
        .from('family_type_prices')
        .select('family_type_id, family_type_name')
        .eq('emi_enabled', true)
        .eq('is_public_visible', true);

      if (familyTypesError) {
        console.error('Error loading family types:', familyTypesError);
      } else {
        const uniqueFamilyTypes = familyTypesData?.reduce((acc: {id: string, name: string}[], curr) => {
          if (!acc.find(item => item.id === curr.family_type_id)) {
            acc.push({ id: curr.family_type_id, name: curr.family_type_name });
          }
          return acc;
        }, []) || [];
        setAvailableFamilyTypes(uniqueFamilyTypes);
      }
    } catch (error) {
      console.error('Error loading filter options:', error);
    }
  };

  const refreshData = async () => {
    await loadFamilyEMIPackages();
    await loadFilterOptions();
  };

  useEffect(() => {
    const fetchData = async () => {
      await loadFamilyEMIPackages();
      await loadFilterOptions();
    };
    fetchData();
  }, []);

  useEffect(() => {
    let filtered = packages;

    if (searchQuery) {
      filtered = filtered.filter(
        pkg => 
          pkg.family_type_name.toLowerCase().includes(searchQuery.toLowerCase()) ||
          pkg.destination_category.toLowerCase().includes(searchQuery.toLowerCase()) ||
          pkg.family_type_id.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    if (destinationFilter !== 'all') {
      filtered = filtered.filter(pkg => pkg.destination_category === destinationFilter);
    }

    if (familyTypeFilter !== 'all') {
      filtered = filtered.filter(pkg => pkg.family_type_id === familyTypeFilter);
    }

    setFilteredPackages(filtered);
  }, [packages, searchQuery, destinationFilter, familyTypeFilter]);

  const formatCurrency = (amount: number) => {
    return `₹${amount.toLocaleString()}`;
  };

  const getFamilyComposition = (pkg: FamilyEMIPackage) => {
    const parts = [];
    if (pkg.no_of_adults > 0) parts.push(`${pkg.no_of_adults}A`);
    if (pkg.no_of_children > 0) parts.push(`${pkg.no_of_children}C`);
    if (pkg.no_of_child > 0) parts.push(`${pkg.no_of_child}K`);
    if (pkg.no_of_infants > 0) parts.push(`${pkg.no_of_infants}I`);
    return parts.join(' + ');
  };

  const getDestinationIcon = (category: string) => {
    if (!category) return '📍';
    switch (category.toLowerCase()) {
      case 'beach': return '🏖️';
      case 'hill station': return '⛰️';
      case 'adventure': return '🏔️';
      case 'heritage': return '🏛️';
      case 'wildlife': return '🦁';
      case 'city': return '🏙️';
      case 'religious': return '🕉️';
      default: return '📍';
    }
  };

  const getSeasonBadgeColor = (season: string) => {
    if (!season) return 'bg-gray-100 text-gray-800';
    switch (season.toLowerCase()) {
      case 'peak': return 'bg-red-100 text-red-800';
      case 'off-peak': return 'bg-green-100 text-green-800';
      case 'normal': return 'bg-blue-100 text-blue-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        <span className="ml-3 text-gray-600">Loading family EMI packages...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="max-w-7xl mx-auto">
        <div className="bg-red-50 border border-red-200 rounded-lg p-6">
          <div className="flex items-center">
            <AlertCircle className="w-6 h-6 text-red-600 mr-3" />
            <div>
              <h3 className="text-lg font-medium text-red-800">Error Loading Data</h3>
              <p className="text-red-700 mt-1">{error}</p>
              <button
                onClick={refreshData}
                className="mt-3 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
              >
                Retry
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto">
      {/* Header */}
      <div className="flex justify-between items-center mb-6">
        <div>
          <h2 className="text-xl font-bold text-gray-800">Available Family EMI Packages</h2>
          <p className="text-gray-600">Browse family vacation packages with EMI options from database</p>
        </div>
        <div className="flex items-center gap-4">
          <button
            onClick={refreshData}
            className="px-3 py-2 text-sm bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
          >
            Refresh
          </button>
          <div className="text-sm text-gray-500">
            {filteredPackages.length} of {packages.length} packages
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Total Packages</p>
              <p className="text-xl font-bold text-blue-600">{packages.length}</p>
            </div>
            <Package className="w-6 h-6 text-blue-600" />
          </div>
        </div>

        <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">EMI Enabled</p>
              <p className="text-xl font-bold text-green-600">{packages.filter(p => p.emi_enabled).length}</p>
            </div>
            <CreditCard className="w-6 h-6 text-green-600" />
          </div>
        </div>

        <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Featured Plans</p>
              <p className="text-xl font-bold text-yellow-600">
                {packages.reduce((count, pkg) => count + pkg.emi_plans.filter(plan => plan.is_featured).length, 0)}
              </p>
            </div>
            <Star className="w-6 h-6 text-yellow-600" />
          </div>
        </div>

        <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Avg. Package Value</p>
              <p className="text-xl font-bold text-purple-600">
                {packages.length > 0 ? formatCurrency(packages.reduce((sum, pkg) => sum + pkg.total_price, 0) / packages.length) : '₹0'}
              </p>
            </div>
            <TrendingUp className="w-6 h-6 text-purple-600" />
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200 mb-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              placeholder="Search packages..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
            />
          </div>

          <select
            value={destinationFilter}
            onChange={(e) => setDestinationFilter(e.target.value)}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
          >
            <option value="all">All Destinations</option>
            {availableDestinations.map(dest => (
              <option key={dest} value={dest}>{dest}</option>
            ))}
          </select>

          <select
            value={familyTypeFilter}
            onChange={(e) => setFamilyTypeFilter(e.target.value)}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
          >
            <option value="all">All Family Types</option>
            {availableFamilyTypes.map(ft => (
              <option key={ft.id} value={ft.id}>{ft.name}</option>
            ))}
          </select>

          <div className="flex items-center text-sm text-gray-600">
            <Filter className="w-4 h-4 mr-2" />
            Filtered Results: {filteredPackages.length}
          </div>
        </div>
      </div>

      {/* Packages Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
        {filteredPackages.map((pkg) => (
          <div key={pkg.id} className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow">
            {/* Package Header */}
            <div className="p-6 border-b border-gray-100">
              <div className="flex justify-between items-start mb-4">
                <div className="flex items-center gap-2">
                  <span className="text-2xl">{getDestinationIcon(pkg.destination_category)}</span>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-800">{pkg.family_type_name}</h3>
                    <p className="text-sm text-gray-500">
                      {pkg.destination_category} • {pkg.package_duration_days ? `${pkg.package_duration_days} Days` : 'Duration TBD'}
                    </p>
                  </div>
                </div>
                <div className="flex flex-col items-end gap-1">
                  {pkg.season_category && (
                    <span className={`px-2 py-1 text-xs font-medium rounded-full ${getSeasonBadgeColor(pkg.season_category)}`}>
                      {pkg.season_category}
                    </span>
                  )}
                  {pkg.emi_plans.some(plan => plan.is_featured) && (
                    <span className="px-2 py-1 text-xs font-medium rounded-full bg-yellow-100 text-yellow-800 flex items-center gap-1">
                      <Star className="w-3 h-3" />
                      Featured EMI
                    </span>
                  )}
                </div>
              </div>

              {/* Family Composition */}
              <div className="flex items-center justify-between text-sm text-gray-600 mb-4">
                <div className="flex items-center gap-4">
                  <div className="flex items-center gap-1">
                    <Users className="w-4 h-4" />
                    <span>{getFamilyComposition(pkg)}</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Bed className="w-4 h-4" />
                    <span>{pkg.rooms_need} Room{pkg.rooms_need > 1 ? 's' : ''}</span>
                  </div>
                  {pkg.cab_type && (
                    <div className="flex items-center gap-1">
                      <Car className="w-4 h-4" />
                      <span>{pkg.cab_type}</span>
                    </div>
                  )}
                </div>
              </div>

              {/* Pricing */}
              <div className="mb-4">
                <div className="flex items-center justify-between">
                  <div>
                    {pkg.discount_amount > 0 && (
                      <span className="text-sm text-gray-500 line-through">
                        {formatCurrency(pkg.subtotal)}
                      </span>
                    )}
                    <div className="text-2xl font-bold text-gray-800">{formatCurrency(pkg.total_price)}</div>
                  </div>
                  {pkg.discount_amount > 0 && (
                    <div className="text-right">
                      <span className="bg-green-100 text-green-800 text-sm font-medium px-2 py-1 rounded">
                        Save {formatCurrency(pkg.discount_amount)}
                      </span>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* EMI Plans Preview */}
            <div className="p-6">
              <h4 className="text-sm font-semibold text-gray-800 mb-3 flex items-center gap-2">
                <CreditCard className="w-4 h-4" />
                EMI Options ({pkg.emi_plans.length} available)
              </h4>
              
              {pkg.emi_plans.length > 0 ? (
                <>
                  <div className="space-y-2">
                    {pkg.emi_plans.slice(0, 2).map((plan) => (
                      <div key={plan.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div className="flex items-center gap-3">
                          <div className="text-center">
                            <div className="text-lg font-bold text-primary">{plan.emi_months}</div>
                            <div className="text-xs text-gray-500">months</div>
                          </div>
                          <div>
                            <div className="font-semibold text-gray-800">{formatCurrency(plan.monthly_amount)}/month</div>
                            <div className="text-xs text-gray-500">
                              Total: {formatCurrency(plan.total_amount)}
                            </div>
                          </div>
                        </div>
                        <div className="text-right">
                          {plan.is_featured && plan.marketing_label && (
                            <span className="bg-blue-100 text-blue-800 text-xs font-medium px-2 py-1 rounded">
                              {plan.marketing_label}
                            </span>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>

                  <button
                    onClick={() => {
                      setSelectedPackage(pkg);
                      setShowEMIModal(true);
                    }}
                    className="w-full mt-4 px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-dark transition-colors text-sm font-medium flex items-center justify-center gap-2"
                  >
                    <Eye className="w-4 h-4" />
                    View All EMI Plans
                  </button>
                </>
              ) : (
                <div className="text-center py-4 text-gray-500">
                  <CreditCard className="w-8 h-8 mx-auto mb-2 text-gray-400" />
                  <p className="text-sm">No EMI plans configured</p>
                </div>
              )}
            </div>
          </div>
        ))}
      </div>

      {/* EMI Plans Modal */}
      {showEMIModal && selectedPackage && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex justify-between items-center mb-6">
                <div>
                  <h2 className="text-xl font-bold text-gray-800">{selectedPackage.family_type_name} - EMI Plans</h2>
                  <p className="text-gray-600">{selectedPackage.destination_category} • Total: {formatCurrency(selectedPackage.total_price)}</p>
                </div>
                <button
                  onClick={() => setShowEMIModal(false)}
                  className="text-gray-400 hover:text-gray-600 text-2xl"
                >
                  ×
                </button>
              </div>

              {selectedPackage.emi_plans.length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {selectedPackage.emi_plans.map((plan) => (
                    <div key={plan.id} className={`border rounded-lg p-4 ${plan.is_featured ? 'border-primary bg-primary-light' : 'border-gray-200'}`}>
                      <div className="flex justify-between items-start mb-3">
                        <div>
                          <div className="text-lg font-bold text-gray-800">{plan.emi_months} Months Plan</div>
                          {plan.marketing_label && (
                            <span className="text-sm font-medium text-primary">{plan.marketing_label}</span>
                          )}
                        </div>
                        {plan.is_featured && (
                          <Star className="w-5 h-5 text-yellow-500 fill-current" />
                        )}
                      </div>

                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span className="text-gray-600">Monthly EMI:</span>
                          <span className="font-semibold">{formatCurrency(plan.monthly_amount)}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Processing Fee:</span>
                          <span>{formatCurrency(plan.processing_fee)}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Total Interest:</span>
                          <span>{formatCurrency(plan.total_interest)}</span>
                        </div>
                        <div className="flex justify-between font-semibold border-t pt-2">
                          <span>Total Amount:</span>
                          <span>{formatCurrency(plan.total_amount)}</span>
                        </div>
                        {plan.effective_annual_rate && (
                          <div className="flex justify-between text-xs text-gray-500">
                            <span>Effective APR:</span>
                            <span>{plan.effective_annual_rate}%</span>
                          </div>
                        )}
                      </div>

                      <button className="w-full mt-4 px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-dark transition-colors text-sm font-medium">
                        Select This Plan
                      </button>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <CreditCard className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-800 mb-2">No EMI Plans Available</h3>
                  <p className="text-gray-600">EMI plans have not been configured for this package yet.</p>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Empty State */}
      {!loading && !error && filteredPackages.length === 0 && (
        <div className="text-center py-12">
          <Package className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-800 mb-2">No EMI Packages Found</h3>
          <p className="text-gray-600">
            {packages.length === 0 
              ? 'No family EMI packages are available in the database.' 
              : 'Try adjusting your search or filter criteria.'}
          </p>
          {packages.length === 0 && (
            <button
              onClick={refreshData}
              className="mt-4 px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-dark transition-colors"
            >
              Refresh Data
            </button>
          )}
        </div>
      )}
    </div>
  );
};

export default FamilyEMIPackages; 