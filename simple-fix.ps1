# Simple Fix for TripXplo CRM Deployment
Write-Host "🚀 TripXplo CRM - Simple Deployment Fix" -ForegroundColor Green

$ServerIP = "*************"
$ServerUser = "root"

# Create SSH key
$keyPath = "$env:USERPROFILE\.ssh\tripxplo_fix"
$pubKeyPath = "$keyPath.pub"

if (-not (Test-Path "$env:USERPROFILE\.ssh")) {
    New-Item -ItemType Directory -Path "$env:USERPROFILE\.ssh" -Force | Out-Null
}

if (-not (Test-Path $keyPath)) {
    Write-Host "🔐 Generating SSH key..." -ForegroundColor Yellow
    ssh-keygen -t rsa -b 4096 -f $keyPath -N '""' -q
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ SSH key generated!" -ForegroundColor Green
    }
}

# Setup SSH key authentication
Write-Host "🔑 Setting up SSH authentication (enter password once)..." -ForegroundColor Yellow
if (Test-Path $pubKeyPath) {
    $pubKey = Get-Content $pubKeyPath -Raw
    ssh $ServerUser@$ServerIP "mkdir -p ~/.ssh; echo '$pubKey' >> ~/.ssh/authorized_keys; chmod 600 ~/.ssh/authorized_keys; chmod 700 ~/.ssh"
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ SSH key setup complete!" -ForegroundColor Green
    }
}

# Create bash fix script
Write-Host "🔧 Creating server fix script..." -ForegroundColor Yellow

$bashScript = @"
#!/bin/bash
set -e
echo "Fixing TripXplo CRM deployment..."
cd /var/www/crm
ls -la
BACKUP_DIR="/var/www/crm.backup.`$(date +%Y%m%d_%H%M%S)"
cp -r /var/www/crm `$BACKUP_DIR
echo "Backup created: `$BACKUP_DIR"
if [ -d "TripXplo-CRM" ]; then
  echo "Moving files from TripXplo-CRM to root..."
  cp -r TripXplo-CRM/* .
  rm -rf TripXplo-CRM
  echo "Files moved successfully"
fi
chown -R www-data:www-data /var/www/crm
chmod -R 755 /var/www/crm
if [ -f "index.html" ]; then
  echo "index.html found in correct location"
else
  echo "index.html not found"
  ls -la
fi
cat > /etc/nginx/sites-available/crm.tripxplo.com << 'ENDCONF'
server {
    listen 80;
    server_name crm.tripxplo.com www.crm.tripxplo.com;
    root /var/www/crm;
    index index.html;
    error_log /var/log/nginx/crm_error.log;
    access_log /var/log/nginx/crm_access.log;
    location / {
        try_files `$uri `$uri/ /index.html;
    }
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot|map)`$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
ENDCONF
ln -sf /etc/nginx/sites-available/crm.tripxplo.com /etc/nginx/sites-enabled/
if nginx -t; then
  systemctl reload nginx
  echo "Nginx reloaded successfully"
else
  echo "Nginx config error"
  exit 1
fi
echo "Testing site..."
sleep 2
curl -I http://crm.tripxplo.com
echo "Fix completed!"
"@

# Save script
$bashScript | Out-File -FilePath "server-fix.sh" -Encoding UTF8

# Upload and execute
Write-Host "📤 Uploading and executing fix..." -ForegroundColor Yellow
scp -i $keyPath -o StrictHostKeyChecking=no "server-fix.sh" "${ServerUser}@${ServerIP}:/tmp/fix.sh"

if ($LASTEXITCODE -eq 0) {
    ssh -i $keyPath -o StrictHostKeyChecking=no $ServerUser@$ServerIP "chmod +x /tmp/fix.sh && /tmp/fix.sh"
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Server fix completed!" -ForegroundColor Green
    }
}

# Clean up
Remove-Item "server-fix.sh" -ErrorAction SilentlyContinue

# Test website
Write-Host "🧪 Testing website..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "http://crm.tripxplo.com" -Method Head -TimeoutSec 10
    Write-Host "🎉 SUCCESS! Website is working! Status: $($response.StatusCode)" -ForegroundColor Green
} catch {
    Write-Host "⚠️ Test from local failed, but check: http://crm.tripxplo.com" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "🎉 Deployment fix complete!" -ForegroundColor Green
Write-Host "🌐 Visit: https://crm.tripxplo.com" -ForegroundColor Cyan
Write-Host ""
Write-Host "📋 SSH Key for GitHub Actions (add as SSH_PRIVATE_KEY secret):" -ForegroundColor Yellow
Write-Host "=" * 60 -ForegroundColor Gray
Get-Content $keyPath
Write-Host "=" * 60 -ForegroundColor Gray 