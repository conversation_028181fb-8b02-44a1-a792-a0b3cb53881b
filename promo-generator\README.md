# TripXplo Promo Generator

A modern, interactive web app to create, customize, and export stunning Instagram travel posts and stories. Drag-and-drop elements, edit styles, and export your designs as images for social media.

## Features

- **Drag-and-drop template designer** for Instagram posts, stories, reels, and more
- **Customizable travel package data** (title, destination, price, dates, inclusions, exclusions, etc.)
- **Professional template formats** with modern layouts and visual hierarchy
- **Element style editing** (font, color, size, alignment, etc.)
- **Background customization** (color, image, overlay)
- **Brand Kit Management** - Centralized brand colors, logos, and fonts with one-click application
- **Export as PNG** for easy sharing
- **Live preview** in a new window

### Professional Template Designs

The application includes professionally designed templates optimized for travel promotion:

#### Square Format (1080x1080px)
- **Modern overlay design** with gradient price banners
- **Hero destination typography** with shadow effects
- **Color-coded badges** for package details and dates
- **Semi-transparent cards** for better readability
- **Professional visual hierarchy** for maximum impact

#### Story Format (1080x1920px)
- **Mobile-optimized layout** for Instagram stories
- **Top badge system** for destination and pricing
- **Card-based information** with high contrast
- **Gradient backgrounds** and modern styling
- **Space-efficient design** for vertical viewing

#### Portrait Format (1080x1350px)
- **Clean, magazine-style layout** for detailed promotions
- **Color-coded sections** with rounded corners
- **Professional typography** with proper spacing
- **Card-based inclusions/exclusions** with visual distinction
- **Balanced composition** for Instagram feed posts

## Getting Started

### Prerequisites
- Node.js (v16+ recommended)
- npm/yarn/pnpm

### Installation

1. Navigate to the promo-generator directory:
   ```bash
   cd promo-generator
   ```

2. Install dependencies:
   ```bash
   npm install
   # or
   yarn install
   # or
   pnpm install
   ```

3. Start the development server:
   ```bash
   npm run dev
   # or
   yarn dev
   # or
   pnpm dev
   ```

4. Open [http://localhost:3000](http://localhost:3000) in your browser

## Usage

1. **Select a Template**: Choose from Square, Story, or Portrait formats
2. **Customize Content**: Edit travel package details, titles, prices, and descriptions
3. **Style Elements**: Adjust fonts, colors, sizes, and alignment
4. **Add Branding**: Use the Brand Kit to apply consistent colors and logos
5. **Export**: Download your design as a high-quality PNG image

## Brand Kit Features

- **Color Management**: Extract and manage brand colors from logos
- **Font Integration**: Upload and apply custom fonts
- **Logo Management**: Store and apply brand logos to templates
- **One-Click Application**: Apply entire brand kit to any template

## Project Structure

```
promo-generator/
├── app/                    # Next.js app directory
├── components/             # UI components
│   ├── template/          # Template designer components
│   ├── ui/               # Reusable UI components
│   └── brand-kit-modal.tsx
├── hooks/                 # Custom React hooks
├── lib/                   # Utility functions
├── public/                # Static assets
└── styles/                # Global styles
```

## Integration with TripXplo CRM

This promo generator is designed to integrate seamlessly with the TripXplo CRM system, allowing travel agents to:

- Create promotional materials directly from CRM data
- Maintain brand consistency across all marketing materials
- Export designs for social media campaigns
- Streamline the content creation workflow

## License

MIT License - see LICENSE file for details 