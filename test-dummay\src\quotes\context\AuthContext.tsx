import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { SupabaseClient, Session, User } from '@supabase/supabase-js';
import { getQuoteClient } from '../../lib/supabaseManager';

// Define context value types
interface AuthContextType {
  supabase: SupabaseClient | null;
  session: Session | null;
  user: User | null;
  isLoading: boolean;
}

// Create AuthContext
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// AuthProvider component
export const AuthProvider = ({ children }: { children: ReactNode }) => {
  const [supabase, setSupabase] = useState<SupabaseClient | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Initialize Supabase client
  useEffect(() => {
    const initSupabase = async () => {
      try {
        setIsLoading(true);

        // Set a timeout for the client initialization with increased duration
        const clientPromise = getQuoteClient();
        const timeoutPromise = new Promise<null>((resolve) => {
          setTimeout(() => {
            console.log('[QuoteAuthContext] Client initialization timed out, continuing with null client');
            resolve(null);
          }, 12000); // 12 second timeout (increased from 8)
        });

        // Race the client initialization against the timeout
        const client = await Promise.race([clientPromise, timeoutPromise]);

        if (!client) {
          console.error('[QuoteAuthContext] Failed to initialize Supabase client within timeout');
          setSupabase(null);
          setSession(null);
          return;
        }

        setSupabase(client);

        // Set a timeout for the session check with increased duration
        const sessionPromise = client.auth.getSession();
        const sessionTimeoutPromise = new Promise<{data: {session: null}}>((resolve) => {
          setTimeout(() => {
            console.log('[QuoteAuthContext] Session check timed out, continuing with null session');

            // Try to get session from localStorage as fallback
            try {
              const storageKey = 'tripxplo-quote-supabase-auth'; // Must match the key in quotes/lib/supabaseClient.ts
              const sessionStr = localStorage.getItem(storageKey);

              if (sessionStr) {
                try {
                  const sessionData = JSON.parse(sessionStr);
                  if (sessionData?.user) {
                    console.log('[QuoteAuthContext] Found user data in localStorage after timeout');
                    return resolve({data: {session: { user: sessionData.user } as any}});
                  }
                } catch (parseError) {
                  console.error('[QuoteAuthContext] Error parsing localStorage session:', parseError);
                }
              }
            } catch (storageError) {
              console.error('[QuoteAuthContext] Error accessing localStorage:', storageError);
            }

            resolve({data: {session: null}});
          }, 10000); // 10 second timeout (increased from 5)
        });

        // Race the session check against the timeout
        let { data } = await Promise.race([sessionPromise, sessionTimeoutPromise]);

        // If session is null but we have a client, try one more time with a short delay
        if (!data.session && client) {
          console.log('[QuoteAuthContext] First session check failed, retrying after delay...');

          // Wait a moment before retrying
          await new Promise(resolve => setTimeout(resolve, 1000));

          try {
            // Try to refresh the session first
            const refreshResult = await client.auth.refreshSession();
            if (refreshResult.data.session) {
              console.log('[QuoteAuthContext] Session refresh successful on retry');
              data = refreshResult.data;
            } else {
              // If refresh fails, try getSession again
              const retryResult = await client.auth.getSession();
              if (retryResult.data.session) {
                console.log('[QuoteAuthContext] Session retry successful');
                data = retryResult.data;
              }
            }
          } catch (retryError) {
            console.error('[QuoteAuthContext] Error during session retry:', retryError);
          }
        }

        setSession(data.session);

        // Listen for auth state changes
        const { data: { subscription } } = client.auth.onAuthStateChange((_event, session) => {
          setSession(session);
        });

        // Cleanup subscription on unmount
        return () => {
          subscription.unsubscribe();
        };
      } catch (error) {
        console.error('[QuoteAuthContext] Error initializing Supabase client:', error);
        // Continue with null client and session on error
        setSupabase(null);
        setSession(null);
      } finally {
        setIsLoading(false);
      }
    };

    initSupabase();
  }, []);

  const user = session?.user ?? null;

  return (
    <AuthContext.Provider value={{ supabase, session, user, isLoading }}>
      {children}
    </AuthContext.Provider>
  );
};

// Hook for consuming AuthContext
export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

// Loading component for use with AuthProvider
export const AuthLoading = ({ children }: { children: ReactNode }) => {
  const { isLoading } = useAuth();

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center p-8 max-w-md">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#00B69B] mx-auto mb-4"></div>
          <h2 className="text-xl font-semibold text-gray-800 mb-2">Loading Quote System</h2>
          <p className="text-gray-600">Please wait while we connect to the database...</p>
        </div>
      </div>
    );
  }

  return <>{children}</>;
};