import React, { useState, useCallback } from 'react';
import { Calculator, IndianRupee, Calendar, Percent } from 'lucide-react';

const EMI_OPTIONS = {
  loanAmounts: [
    100000, 200000, 300000, 500000, 750000, 
    1000000, 1500000, 2000000, 2500000, 3000000
  ],
  interestRates: [
    8.5, 9.0, 9.5, 10.0, 10.5, 11.0, 11.5, 12.0, 12.5, 13.0
  ],
  tenures: [
    { months: 3, label: '3 Months' },
    { months: 6, label: '6 Months' },
    { months: 12, label: '1 Year' },
    { months: 24, label: '2 Years' },
    { months: 36, label: '3 Years' },
    { months: 48, label: '4 Years' },
    { months: 60, label: '5 Years' }
  ]
};

interface EMICalculation {
  emi: number;
  totalInterest: number;
  totalPayment: number;
  breakup: {
    month: number;
    emi: number;
    principal: number;
    interest: number;
    balance: number;
  }[];
}

const EmiCalculator: React.FC = () => {
  const [loanAmount, setLoanAmount] = useState<number>(EMI_OPTIONS.loanAmounts[0]);
  const [interestRate, setInterestRate] = useState<number>(EMI_OPTIONS.interestRates[2]);
  const [tenure, setTenure] = useState<number>(EMI_OPTIONS.tenures[2].months);
  const [isCustomAmount, setIsCustomAmount] = useState(false);
  const [isCustomRate, setIsCustomRate] = useState(false);
  const [isCustomTenure, setIsCustomTenure] = useState(false);
  const [calculation, setCalculation] = useState<EMICalculation | null>(null);

  const calculateEMI = useCallback(() => {
    const p = loanAmount;
    const r = interestRate / 12 / 100;
    const n = tenure;
    
    const emi = p * r * Math.pow(1 + r, n) / (Math.pow(1 + r, n) - 1);
    const totalPayment = emi * n;
    const totalInterest = totalPayment - p;

    // Calculate monthly breakup
    let balance = p;
    const breakup = [];
    
    for (let month = 1; month <= n; month++) {
      const interest = balance * r;
      const principal = emi - interest;
      balance = balance - principal;
      
      breakup.push({
        month,
        emi,
        principal,
        interest,
        balance: balance < 0 ? 0 : balance
      });
    }

    setCalculation({
      emi,
      totalInterest,
      totalPayment,
      breakup
    });
  }, [loanAmount, interestRate, tenure]);

  return (
    <div className="max-w-7xl mx-auto p-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        {/* Input Section */}
        <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
          <h2 className="text-xl font-semibold mb-6 flex items-center gap-2">
            <Calculator className="w-5 h-5 text-blue-600" />
            EMI Calculator
          </h2>
          
          <div className="space-y-6">
            {/* Loan Amount Input */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Loan Amount (₹)
              </label>
              <div className="flex gap-4">
                <select
                  value={isCustomAmount ? "custom" : loanAmount}
                  onChange={(e) => {
                    if (e.target.value === "custom") {
                      setIsCustomAmount(true);
                    } else {
                      setIsCustomAmount(false);
                      setLoanAmount(Number(e.target.value));
                    }
                  }}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  {EMI_OPTIONS.loanAmounts.map((amount) => (
                    <option key={amount} value={amount}>
                      ₹{amount.toLocaleString()}
                    </option>
                  ))}
                  <option value="custom">Custom Amount</option>
                </select>
                {isCustomAmount && (
                  <div className="relative flex-1">
                    <IndianRupee className="absolute left-3 top-1/2 -translate-y-1/2 w-4 h-4 text-gray-400" />
                    <input
                      type="number"
                      value={loanAmount}
                      onChange={(e) => setLoanAmount(Number(e.target.value))}
                      className="pl-10 w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="Enter custom amount"
                    />
                  </div>
                )}
              </div>
            </div>

            {/* Interest Rate Input */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Interest Rate (% per annum)
              </label>
              <div className="flex gap-4">
                <select
                  value={isCustomRate ? "custom" : interestRate}
                  onChange={(e) => {
                    if (e.target.value === "custom") {
                      setIsCustomRate(true);
                    } else {
                      setIsCustomRate(false);
                      setInterestRate(Number(e.target.value));
                    }
                  }}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  {EMI_OPTIONS.interestRates.map((rate) => (
                    <option key={rate} value={rate}>
                      {rate}%
                    </option>
                  ))}
                  <option value="custom">Custom Rate</option>
                </select>
                {isCustomRate && (
                  <div className="relative flex-1">
                    <Percent className="absolute left-3 top-1/2 -translate-y-1/2 w-4 h-4 text-gray-400" />
                    <input
                      type="number"
                      value={interestRate}
                      onChange={(e) => setInterestRate(Number(e.target.value))}
                      className="pl-10 w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="Enter custom rate"
                      step="0.1"
                    />
                  </div>
                )}
              </div>
            </div>

            {/* Tenure Input */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Tenure
              </label>
              <div className="flex gap-4">
                <select
                  value={isCustomTenure ? "custom" : tenure}
                  onChange={(e) => {
                    if (e.target.value === "custom") {
                      setIsCustomTenure(true);
                    } else {
                      setIsCustomTenure(false);
                      setTenure(Number(e.target.value));
                    }
                  }}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  {EMI_OPTIONS.tenures.map((option) => (
                    <option key={option.months} value={option.months}>
                      {option.label}
                    </option>
                  ))}
                  <option value="custom">Custom Tenure</option>
                </select>
                {isCustomTenure && (
                  <div className="relative flex-1">
                    <Calendar className="absolute left-3 top-1/2 -translate-y-1/2 w-4 h-4 text-gray-400" />
                    <input
                      type="number"
                      value={tenure}
                      onChange={(e) => setTenure(Number(e.target.value))}
                      className="pl-10 w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="Enter months"
                    />
                  </div>
                )}
              </div>
            </div>

            <button
              onClick={calculateEMI}
              className="w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
            >
              Calculate EMI
            </button>
          </div>
        </div>

        {/* Results Section */}
        {calculation && (
          <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <h2 className="text-xl font-semibold mb-6">EMI Details</h2>
            
            <div className="space-y-6">
              <div className="bg-blue-50 p-4 rounded-lg">
                <p className="text-sm text-gray-600">Monthly EMI</p>
                <p className="text-2xl font-bold text-blue-600">
                  ₹{calculation.emi.toFixed(2)}
                </p>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="bg-gray-50 p-4 rounded-lg">
                  <p className="text-sm text-gray-600">Total Interest</p>
                  <p className="text-lg font-semibold text-gray-800">
                    ₹{calculation.totalInterest.toFixed(2)}
                  </p>
                </div>
                <div className="bg-gray-50 p-4 rounded-lg">
                  <p className="text-sm text-gray-600">Total Payment</p>
                  <p className="text-lg font-semibold text-gray-800">
                    ₹{calculation.totalPayment.toFixed(2)}
                  </p>
                </div>
              </div>

              {/* EMI Breakup Table */}
              <div className="mt-6">
                <h3 className="text-lg font-semibold mb-4">Monthly Breakup</h3>
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Month</th>
                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">EMI</th>
                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Principal</th>
                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Interest</th>
                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Balance</th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {calculation.breakup.map((row) => (
                        <tr key={row.month}>
                          <td className="px-4 py-2 text-sm text-gray-900">{row.month}</td>
                          <td className="px-4 py-2 text-sm text-gray-900">₹{row.emi.toFixed(2)}</td>
                          <td className="px-4 py-2 text-sm text-gray-900">₹{row.principal.toFixed(2)}</td>
                          <td className="px-4 py-2 text-sm text-gray-900">₹{row.interest.toFixed(2)}</td>
                          <td className="px-4 py-2 text-sm text-gray-900">₹{row.balance.toFixed(2)}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default EmiCalculator;
