<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EMI Fix Verification</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        .status { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        button { background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; margin: 5px; }
        .log { background: #f8f9fa; border: 1px solid #dee2e6; padding: 10px; font-family: monospace; font-size: 12px; max-height: 300px; overflow-y: auto; }
    </style>
</head>
<body>
    <h1>🔧 EMI Fix Deployment Verification</h1>
    
    <div class="info">
        <h3>Deployment Status:</h3>
        <ul>
            <li>✅ Updated databaseService.js with EMI recalculation logic</li>
            <li>✅ Created cache-busting version: databaseService.v2.js</li>
            <li>✅ Updated test-emi-calculation.html to use v2</li>
            <li>✅ Updated main index.html to use v2</li>
            <li>✅ Reloaded nginx web server</li>
            <li>✅ Set correct file permissions</li>
        </ul>
    </div>

    <button onclick="testLiveSite()">🧪 Test Live Site EMI Fix</button>
    <button onclick="clearLog()">🗑️ Clear Log</button>
    
    <div id="results"></div>
    <div id="log" class="log" style="display: none;"></div>

    <script>
        let logMessages = [];
        
        function log(message) {
            console.log(message);
            logMessages.push(new Date().toLocaleTimeString() + ': ' + message);
            updateLogDisplay();
        }

        function updateLogDisplay() {
            const logEl = document.getElementById('log');
            logEl.innerHTML = logMessages.slice(-20).join('<br>');
            logEl.scrollTop = logEl.scrollHeight;
        }

        function showLog() {
            const logEl = document.getElementById('log');
            logEl.style.display = logEl.style.display === 'none' ? 'block' : 'none';
        }

        async function testLiveSite() {
            const resultsEl = document.getElementById('results');
            resultsEl.innerHTML = '<div class="info">🔄 Testing live site...</div>';
            logMessages = [];
            
            try {
                log('🔍 Testing live site EMI calculation...');
                
                // Test the live site
                const response = await fetch('https://family.tripxplo.com/test-emi-calculation.html');
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                log('✅ Live test page is accessible');
                
                // Test if the new JS file is loaded
                const jsResponse = await fetch('https://family.tripxplo.com/js/databaseService.v2.js');
                if (!jsResponse.ok) {
                    throw new Error(`JS file not accessible: HTTP ${jsResponse.status}`);
                }
                
                log('✅ New databaseService.v2.js is accessible');
                
                resultsEl.innerHTML = `
                    <div class="success">
                        <h3>✅ Deployment Successful!</h3>
                        <p>The EMI fix has been deployed successfully. Please:</p>
                        <ol>
                            <li>Visit <a href="https://family.tripxplo.com/test-emi-calculation.html" target="_blank">the test page</a></li>
                            <li>Hard refresh the page (Ctrl+F5) to clear browser cache</li>
                            <li>Check the console for "📊 Recalculated EMI plan" messages</li>
                            <li>Verify EMI amounts match package prices exactly</li>
                        </ol>
                    </div>
                    <button onclick="showLog()">📋 Show/Hide Log</button>
                `;
                
            } catch (error) {
                log('❌ Test failed: ' + error.message);
                resultsEl.innerHTML = `
                    <div class="error">
                        <h3>❌ Deployment Issue Detected</h3>
                        <p>Error: ${error.message}</p>
                        <p>Please check server connectivity and file permissions.</p>
                    </div>
                    <button onclick="showLog()">📋 Show/Hide Log</button>
                `;
            }
        }

        function clearLog() {
            logMessages = [];
            updateLogDisplay();
            document.getElementById('results').innerHTML = '';
        }

        // Auto-test on load
        window.addEventListener('load', () => {
            setTimeout(testLiveSite, 1000);
        });
    </script>
</body>
</html> 