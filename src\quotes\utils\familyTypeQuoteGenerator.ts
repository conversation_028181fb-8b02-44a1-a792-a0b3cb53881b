// Family Type Price Generator for Quote Generator Integration
import { getQuoteClient, getCrmClient } from '../../lib/supabaseManager';

// =============================================================================
// INTERFACES - Using exact Quote Generator data structures
// =============================================================================

export interface FamilyTypeDB {
  family_id: string;
  family_type: string;
  no_of_adults: number;
  no_of_infants: number;     // Below 2 yrs - Free
  no_of_child: number;       // Below 5 yrs - Free  
  no_of_children: number;    // 6-12 yrs - Charged
  family_count: number;      // Total family members
  cab_type: string;          // Required vehicle type
  cab_capacity: number;      // Vehicle capacity
  rooms_need: number;        // Rooms required
}

export interface QuoteGeneratorData {
  // Basic quote info
  packageName: string;
  customerName: string;
  destination: string;
  quoteDate: string;
  validityDate: string;
  noOfPersons: number;
  extraAdults: number;
  children: number;
  infants: number;
  
  // Hotel data
  hotelRows: HotelRow[];
  
  // Costs
  costs: {
    basicCosts: {
      meals: number;
      transportation: number;
      cabSightseeing: number;
      trainCost: number;
      ferryCost: number;
      parkingToll: number;
    };
    addOnCosts: {
      addOnActivity: number;
      marketing: number;
      addOn: number;
    };
    optionalCosts: {
      flightTicket: number;
      guideWages: number;
    };
  };
  
  // Quote settings
  commission: number;
  discountValue: number;
  discountType: string;
}

export interface HotelRow {
  hotelName: string;
  roomType: string;
  price: number;
  mealPlan: string;
  noOfRooms: number;
  stayNights: number;
  extraAdultCost: number;
  childrenCost: number;
  infantCost: number;
  gstType: string;
  tacPercentage: number;
  room_capacity?: number;     // snake_case (database field)
  roomCapacity?: number;      // camelCase (JavaScript field)
  capacity?: number;          // alternative field name
  room_size?: number;         // alternative field name
  occupancy?: number;         // alternative field name
}

export interface QuoteMappingData {
  id: string;
  quote_id: string;
  hotel_mappings: HotelCostMapping[];
  vehicle_mappings: VehicleCostMapping[];
  additional_costs: {
    meal_cost_per_person: number;
    ferry_cost: number;
    activity_cost_per_person: number;
    guide_cost_per_day: number;
    parking_toll_multiplier: number;
  };
}

export interface HotelCostMapping {
  hotel_name: string;
  extra_adult_cost: number;
  children_cost: number;
  infant_cost: number;
  grandparent_discount: number;
}

export interface VehicleCostMapping {
  vehicle_type: string;
  pricing_type: 'multiplier' | 'actual_cost';
  base_cost: number;
  cost_multiplier: number;
  max_capacity: number;
  is_active: boolean;
}

export interface FamilyTypePriceResult {
  familyType: FamilyTypeDB;
  calculatedPrice: number;
  breakdown: {
    hotelCost: number;
    vehicleCost: number;
    additionalCosts: number;
    basicCosts: number;
    addOnCosts: number;
    optionalCosts: number;
    subtotal: number;
    discount: number;
    commission: number;
    grandTotal: number;
    rooms: number;
    extraAdults: number;
    childrenCharged: number;
    infantsFree: number;
    roomType?: string;
    notes: string[];
  };
}

// =============================================================================
// CORE FUNCTIONS
// =============================================================================

// Step 1: Validate Quote Mapping Data
export const validateQuoteMappingData = async (quoteId?: string): Promise<{ isValid: boolean; data?: QuoteMappingData; message: string }> => {
  try {
    if (!quoteId) {
      return { isValid: false, message: 'No quote ID provided. Please save the quote first.' };
    }

    const supabase = await getQuoteClient();
    if (!supabase) {
      return { isValid: false, message: 'Database connection not available.' };
    }

    // Check if Quote Mapping exists
    const { data, error } = await supabase
      .from('quote_mappings')
      .select('*')
      .eq('quote_id', quoteId)
      .single();

    if (error || !data) {
      return { 
        isValid: false, 
        message: 'Quote Mapping data not found. Please go to Quote Mapping tab and configure hotel rates, vehicle rates, and additional costs first.' 
      };
    }

    // Validate required mapping data
    const issues: string[] = [];

    if (!data.hotel_mappings || data.hotel_mappings.length === 0) {
      issues.push('Hotel cost mappings are missing');
    } else {
      // Check if hotel mappings have valid cost values
      const invalidHotelMappings = data.hotel_mappings.filter((hm: HotelCostMapping) => 
        (!hm.extra_adult_cost || hm.extra_adult_cost <= 0) && 
        (!hm.children_cost || hm.children_cost <= 0)
      );
      if (invalidHotelMappings.length === data.hotel_mappings.length) {
        issues.push('Hotel cost mappings have invalid or zero cost values');
      }
    }

    if (!data.vehicle_mappings || data.vehicle_mappings.length === 0) {
      issues.push('Vehicle cost mappings are missing');
    } else {
      // Check if vehicle mappings have valid values
      const activeVehicleMappings = data.vehicle_mappings.filter((vm: VehicleCostMapping) => vm.is_active);
      if (activeVehicleMappings.length === 0) {
        issues.push('No active vehicle cost mappings found');
      }
    }

    if (!data.additional_costs) {
      issues.push('Additional cost configurations are missing');
    }

    if (issues.length > 0) {
      return {
        isValid: false,
        message: `Quote Mapping incomplete or has invalid values:\n• ${issues.join('\n• ')}\n\nPlease complete the Quote Mapping configuration with proper cost values.`
      };
    }

    return { isValid: true, data, message: 'Quote Mapping data is valid and complete.' };

  } catch (error) {
    return { 
      isValid: false, 
      message: `Error validating Quote Mapping: ${error instanceof Error ? error.message : 'Unknown error'}` 
    };
  }
};

// Step 2: Get Family Types from Database
export const getFamilyTypesFromDatabase = async (): Promise<FamilyTypeDB[]> => {
  try {
    const supabase = await getCrmClient();
    if (!supabase) {
      console.error('❌ CRM client not available');
      return [];
    }

    const { data, error } = await supabase
      .from('family_type')
      .select('*')
      .order('family_id');

    if (error) {
      console.error('❌ Error fetching family types:', error);
      return [];
    }

    console.log(`✅ Fetched ${data?.length || 0} family types from database`);
    return data || [];
  } catch (error) {
    console.error('❌ Exception getting family types:', error);
    return [];
  }
};

// =============================================================================
// MAIN FUNCTION: Generate Family Type Prices
// =============================================================================

// Save Family Type Prices to Database
export const saveFamilyTypePricesToDatabase = async (
  quoteId: string, // This should be UUID string from the quotes table
  results: FamilyTypePriceResult[],
  quoteGeneratorData: QuoteGeneratorData,
  quoteMappingData: QuoteMappingData
): Promise<{ success: boolean; message: string }> => {
  try {
    const supabase = await getQuoteClient();
    if (!supabase) {
      return { success: false, message: 'Database connection not available.' };
    }

    console.log(`💾 Saving ${results.length} family type prices to database...`);

    // Prepare data for insertion
    const familyTypePricesData = results.map(result => ({
      quote_id: quoteId,
      family_type_id: result.familyType.family_id,
      family_type_name: result.familyType.family_type,
      
      // Family Composition
      no_of_adults: result.familyType.no_of_adults,
      no_of_children: result.familyType.no_of_children,
      no_of_child: result.familyType.no_of_child,
      no_of_infants: result.familyType.no_of_infants,
      family_count: result.familyType.family_count,
      
      // Room and Vehicle Info
      rooms_need: result.familyType.rooms_need,
      cab_type: result.familyType.cab_type,
      cab_capacity: result.familyType.cab_capacity,
      
      // Calculated Costs
      hotel_cost: result.breakdown.hotelCost,
      vehicle_cost: result.breakdown.vehicleCost,
      additional_costs: result.breakdown.additionalCosts,
      basic_costs: result.breakdown.basicCosts,
      addon_costs: result.breakdown.addOnCosts,
      optional_costs: result.breakdown.optionalCosts,
      
      // Final Pricing
      subtotal: result.breakdown.subtotal,
      discount_amount: result.breakdown.discount,
      commission_amount: result.breakdown.commission,
      total_price: result.breakdown.grandTotal,
      
      // Room Calculation Details
      extra_adults: result.breakdown.extraAdults,
      children_charged: result.breakdown.childrenCharged,
      infants_free: result.breakdown.infantsFree,
      room_type: result.breakdown.roomType || null,
      
      // Metadata
      baseline_quote_data: {
        packageName: quoteGeneratorData.packageName,
        customerName: quoteGeneratorData.customerName,
        destination: quoteGeneratorData.destination,
        quoteDate: quoteGeneratorData.quoteDate,
        validityDate: quoteGeneratorData.validityDate,
        noOfPersons: quoteGeneratorData.noOfPersons,
        extraAdults: quoteGeneratorData.extraAdults,
        children: quoteGeneratorData.children,
        infants: quoteGeneratorData.infants,
        commission: quoteGeneratorData.commission,
        discountValue: quoteGeneratorData.discountValue,
        discountType: quoteGeneratorData.discountType
      },
      quote_mapping_data: quoteMappingData,
      calculation_notes: result.breakdown.notes || []
    }));

    // Delete existing records for this quote (to replace with new calculations)
    const { error: deleteError } = await supabase
      .from('family_type_prices')
      .delete()
      .eq('quote_id', quoteId);

    if (deleteError) {
      console.warn('⚠️ Warning: Could not delete existing family type prices:', deleteError);
      // Continue anyway - might be first time saving
    }

    // Insert new records
    const { error: insertError } = await supabase
      .from('family_type_prices')
      .insert(familyTypePricesData);

    if (insertError) {
      console.error('❌ Error saving family type prices:', insertError);
      return { success: false, message: `Failed to save family type prices: ${insertError.message}` };
    }

    console.log(`✅ Successfully saved ${results.length} family type prices to database`);
    return { success: true, message: `Successfully saved ${results.length} family type prices to database.` };

  } catch (error) {
    console.error('❌ Exception saving family type prices:', error);
    return { success: false, message: `Error saving family type prices: ${error instanceof Error ? error.message : 'Unknown error'}` };
  }
};

// Load Family Type Prices from Database
export const loadFamilyTypePricesFromDatabase = async (
  quoteId: string
): Promise<{ success: boolean; results?: FamilyTypePriceResult[]; message: string }> => {
  try {
    const supabase = await getQuoteClient();
    if (!supabase) {
      return { success: false, message: 'Database connection not available.' };
    }

    console.log(`📖 Loading family type prices for quote: ${quoteId}`);

    const { data, error } = await supabase
      .from('family_type_prices')
      .select('*')
      .eq('quote_id', quoteId)
      .order('family_type_name');

    if (error) {
      console.error('❌ Error loading family type prices:', error);
      return { success: false, message: `Failed to load family type prices: ${error.message}` };
    }

    if (!data || data.length === 0) {
      return { success: false, message: 'No saved family type prices found for this quote.' };
    }

    // Convert database data back to FamilyTypePriceResult format
    const results: FamilyTypePriceResult[] = data.map(row => ({
      familyType: {
        family_id: row.family_type_id,
        family_type: row.family_type_name,
        no_of_adults: row.no_of_adults,
        no_of_children: row.no_of_children,
        no_of_child: row.no_of_child,
        no_of_infants: row.no_of_infants,
        family_count: row.family_count,
        rooms_need: row.rooms_need,
        cab_type: row.cab_type,
        cab_capacity: row.cab_capacity
      },
      calculatedPrice: row.total_price,
      breakdown: {
        hotelCost: row.hotel_cost,
        vehicleCost: row.vehicle_cost,
        additionalCosts: row.additional_costs,
        basicCosts: row.basic_costs,
        addOnCosts: row.addon_costs,
        optionalCosts: row.optional_costs,
        subtotal: row.subtotal,
        discount: row.discount_amount,
        commission: row.commission_amount,
        grandTotal: row.total_price,
        rooms: row.rooms_need,
        extraAdults: row.extra_adults,
        childrenCharged: row.children_charged,
        infantsFree: row.infants_free,
        roomType: row.room_type,
        notes: row.calculation_notes || []
      }
    }));

    console.log(`✅ Loaded ${results.length} family type prices from database`);
    return { success: true, results, message: `Successfully loaded ${results.length} family type prices from database.` };

  } catch (error) {
    console.error('❌ Exception loading family type prices:', error);
    return { success: false, message: `Error loading family type prices: ${error instanceof Error ? error.message : 'Unknown error'}` };
  }
};

export const generateFamilyTypePrices = async (
  quoteGeneratorData: QuoteGeneratorData,
  currentQuoteId?: string
): Promise<{ success: boolean; results?: FamilyTypePriceResult[]; message: string }> => {
  
  console.log('\n🎯 === FAMILY TYPE PRICE GENERATION ===');
  console.log(`📊 Quote: ${quoteGeneratorData.customerName} - ${quoteGeneratorData.destination}`);

  try {
    // Step 1: Validate Quote Mapping (but allow fallback to Quote Generator data)
    const validation = await validateQuoteMappingData(currentQuoteId);
    
    let quoteMappingData: QuoteMappingData | null = null;
    let useQuoteGeneratorFallback = false;
    
    if (!validation.isValid) {
      console.log('⚠️ Quote Mapping validation failed:', validation.message);
      
      // If no Quote Mapping exists, offer to proceed with Quote Generator data
      if (validation.message.includes('not found')) {
        useQuoteGeneratorFallback = true;
        console.log('📋 Will use Quote Generator hotel/cost data as fallback');
        
        // Create minimal Quote Mapping structure for consistency
        quoteMappingData = {
          id: 'fallback',
          quote_id: currentQuoteId || 'unknown',
          hotel_mappings: [],
          vehicle_mappings: [],
          additional_costs: {
            meal_cost_per_person: 0,
            ferry_cost: 0,
            activity_cost_per_person: 0,
            guide_cost_per_day: 0,
            parking_toll_multiplier: 1
          }
        };
      } else {
        // Quote Mapping exists but has invalid/incomplete data
        return { 
          success: false, 
          message: `${validation.message}\n\n⚠️ Please configure Quote Mapping properly before generating family type prices.` 
        };
      }
    } else {
      quoteMappingData = validation.data!;
    console.log('✅ Quote Mapping validation passed');
    }

    // Step 2: Get Family Types
    const familyTypes = await getFamilyTypesFromDatabase();
    if (familyTypes.length === 0) {
      return { success: false, message: 'No family types found in database.' };
    }

    console.log(`✅ Processing ${familyTypes.length} family types`);
    if (useQuoteGeneratorFallback) {
      console.log('📋 Using Quote Generator data as cost fallback');
    }

    // Step 3: Calculate prices for each family type
    const results: FamilyTypePriceResult[] = [];

    for (const familyType of familyTypes) {
      try {
        console.log(`\n💰 Calculating: ${familyType.family_type}`);
        console.log(`👥 Family: ${familyType.no_of_adults}A + ${familyType.no_of_children}C + ${familyType.no_of_infants}I + ${familyType.no_of_child}K = ${familyType.family_count} people`);
        
        // DEBUG: Show exactly what hotel data we're receiving
        console.log(`🔍 DEBUG - Quote Generator Hotel Data:`);
        if (quoteGeneratorData.hotelRows && quoteGeneratorData.hotelRows.length > 0) {
          quoteGeneratorData.hotelRows.forEach((hotel, index) => {
            console.log(`   Hotel ${index + 1}:`);
            console.log(`      Name: ${hotel.hotelName}`);
            console.log(`      Room Type: ${hotel.roomType}`);
            console.log(`      Price: ₹${hotel.price}`);
            console.log(`      Room Capacity: ${hotel.room_capacity} (${typeof hotel.room_capacity})`);
            console.log(`      Extra Adult Cost: ₹${hotel.extraAdultCost}`);
            console.log(`      Children Cost: ₹${hotel.childrenCost}`);
            console.log(`      All Hotel Fields:`, Object.keys(hotel));
            console.log(`      🔍 DETAILED FIELD CHECK:`);
            Object.keys(hotel).forEach(key => {
              console.log(`         ${key}: ${(hotel as any)[key]} (${typeof (hotel as any)[key]})`);
            });
            console.log(`      Full Hotel Object:`, hotel);
          });
        } else {
          console.log(`   ⚠️ No hotel data found in Quote Generator!`);
        }
        
        // Determine the best room capacity to use based on family composition and available hotels
        let effectiveRoomCapacity = 2; // Default to standard room
        let effectiveRoomType = "Standard Room";
        
        if (quoteGeneratorData.hotelRows && quoteGeneratorData.hotelRows.length > 0) {
          // Analyze all hotels to find the most suitable room capacity
          const familyAdults = familyType.no_of_adults;
          const familyChildren = familyType.no_of_children; // 6-11 years (charged)
          const totalOccupancy = familyAdults + familyChildren; // Count only paying occupants for room capacity
          
          console.log(`🏨 Analyzing hotels for family: ${familyAdults} adults + ${familyChildren} children (6-11) = ${totalOccupancy} occupancy`);
          
          // Find the best room capacity that can accommodate the family
          for (const hotel of quoteGeneratorData.hotelRows) {
            // Try different possible field names for room capacity
            const hotelCapacity = hotel.room_capacity || 
                                 (hotel as any).roomCapacity || 
                                 (hotel as any).capacity || 
                                 (hotel as any).room_size || 
                                 (hotel as any).occupancy || 
                                 2; // Default fallback
            
            console.log(`   Hotel: ${hotel.hotelName}, Room: ${hotel.roomType}, Capacity: ${hotelCapacity}`);
            console.log(`   🔍 Capacity Debug: room_capacity=${hotel.room_capacity}, roomCapacity=${(hotel as any).roomCapacity}, capacity=${(hotel as any).capacity}`);
            
            // Determine max occupancy for this room type based on your logic
            let maxOccupancy = 0;
            switch (hotelCapacity) {
              case 2: // Standard/Deluxe Room: 3 Adults + 2 Children
                maxOccupancy = 3 + 2; // 5 total
                break;
              case 3: // Triple Room: 4 Adults + 2 Children  
                maxOccupancy = 4 + 2; // 6 total
                break;
              case 4: // Family Room: 5 Adults + 4 Children
                maxOccupancy = 5 + 4; // 9 total
                break;
              case 6: // Six Occupancy Room: 7 Adults + 6 Children
                maxOccupancy = 7 + 6; // 13 total
                break;
              default:
                maxOccupancy = hotelCapacity + 1 + Math.min(hotelCapacity, 6); // Dynamic calculation
                break;
            }
            
            console.log(`   Max occupancy for this room: ${maxOccupancy}`);
            
            // If this room can accommodate the family better, use it
            if (totalOccupancy <= maxOccupancy && hotelCapacity > effectiveRoomCapacity) {
              effectiveRoomCapacity = hotelCapacity;
              effectiveRoomType = hotel.roomType || "Standard Room";
              console.log(`   ✅ Better room found: ${effectiveRoomType} (capacity ${effectiveRoomCapacity})`);
            }
          }
          
          // If no room can accommodate the family in one room, use the largest available
          if (effectiveRoomCapacity === 2 && totalOccupancy > 5) {
            const largestRoom = quoteGeneratorData.hotelRows.reduce((largest, hotel) => {
              const currentCapacity = hotel.room_capacity || 
                                    (hotel as any).roomCapacity || 
                                    (hotel as any).capacity || 
                                    (hotel as any).room_size || 
                                    (hotel as any).occupancy || 
                                    2;
              const largestCapacity = largest.room_capacity || 
                                    (largest as any).roomCapacity || 
                                    (largest as any).capacity || 
                                    (largest as any).room_size || 
                                    (largest as any).occupancy || 
                                    2;
              return currentCapacity > largestCapacity ? hotel : largest;
            });
            effectiveRoomCapacity = largestRoom.room_capacity || 
                                  (largestRoom as any).roomCapacity || 
                                  (largestRoom as any).capacity || 
                                  (largestRoom as any).room_size || 
                                  (largestRoom as any).occupancy || 
                                  2;
            effectiveRoomType = largestRoom.roomType || "Standard Room";
            console.log(`   📏 Using largest available room: ${effectiveRoomType} (capacity ${effectiveRoomCapacity})`);
          }
        }

        // Create effective hotel row for room calculation
        const effectiveHotelRow = quoteGeneratorData.hotelRows && quoteGeneratorData.hotelRows.length > 0 
          ? {
              ...quoteGeneratorData.hotelRows[0], // Use first hotel as base
              roomType: effectiveRoomType,
              room_capacity: effectiveRoomCapacity
            }
          : {
              hotelName: "Default Hotel",
              roomType: effectiveRoomType,
              price: 2000,
              mealPlan: "MAP",
              noOfRooms: 1,
              stayNights: 3,
              extraAdultCost: 500,
              childrenCost: 300,
              infantCost: 0,
              gstType: "12%",
              tacPercentage: 5,
              room_capacity: effectiveRoomCapacity
            };

        console.log(`🏨 Using effective room: ${effectiveHotelRow.roomType}, Capacity: ${effectiveHotelRow.room_capacity}`);
        
        // Calculate room requirements using the determined room capacity
        const roomReq = calculateRoomRequirements(familyType, effectiveHotelRow);
        console.log(`🏨 Room calculation result: ${roomReq.roomsNeeded} ${roomReq.roomCategory}, Extra Adults: ${roomReq.extraAdults}`);
        
        // Calculate costs
        const hotelCost = calculateHotelCosts(familyType, roomReq, quoteGeneratorData, quoteMappingData);
        const vehicleCost = calculateVehicleCosts(familyType, quoteGeneratorData, quoteMappingData);
        const additionalCosts = calculateAdditionalCosts(familyType, quoteGeneratorData, quoteMappingData);
        
        console.log(`💰 Hotel: ₹${hotelCost}, Vehicle: ₹${vehicleCost}, Additional: ₹${additionalCosts}`);
        
        // Scale add-on and optional costs based on family size (using old logic)
        const baselinePeople = quoteGeneratorData.noOfPersons + quoteGeneratorData.extraAdults + 
                              quoteGeneratorData.children + quoteGeneratorData.infants;
        const peopleRatio = familyType.family_count / Math.max(baselinePeople, 1);
        
        console.log(`👥 People scaling: ${familyType.family_count}/${baselinePeople} = ${peopleRatio.toFixed(2)}x`);
        
        const basicCosts = Math.round(Object.values(quoteGeneratorData.costs.basicCosts).reduce((sum, cost) => sum + cost, 0) * peopleRatio);
        const addOnCosts = Math.round(Object.values(quoteGeneratorData.costs.addOnCosts).reduce((sum, cost) => sum + cost, 0) * peopleRatio);
        const optionalCosts = Math.round(Object.values(quoteGeneratorData.costs.optionalCosts).reduce((sum, cost) => sum + cost, 0) * peopleRatio);
        
        console.log(`💰 Scaled costs - Basic: ₹${basicCosts}, AddOn: ₹${addOnCosts}, Optional: ₹${optionalCosts}`);
        
        // Calculate subtotal with detailed breakdown (matching old logic)
        const subtotal = hotelCost + vehicleCost + additionalCosts + addOnCosts + optionalCosts;
        
        console.log(`\n💵 === FINAL CALCULATION BREAKDOWN ===`);
        console.log(`   Hotel Cost:      ₹${hotelCost.toLocaleString()}`);
        console.log(`   Vehicle Cost:    ₹${vehicleCost.toLocaleString()}`);
        console.log(`   Additional:      ₹${additionalCosts.toLocaleString()}`);
        console.log(`   Basic Costs:     ₹${basicCosts.toLocaleString()}`);
        console.log(`   Add-On Costs:    ₹${addOnCosts.toLocaleString()}`);
        console.log(`   Optional Costs:  ₹${optionalCosts.toLocaleString()}`);
        console.log(`   ----------------------------------------`);
        console.log(`   SUBTOTAL:        ₹${subtotal.toLocaleString()}`);
        
        // Apply discount
        let discount = 0;
        if (quoteGeneratorData.discountType === 'percentage') {
          discount = subtotal * (quoteGeneratorData.discountValue / 100);
          console.log(`   Discount (${quoteGeneratorData.discountValue}%): -₹${discount.toLocaleString()}`);
        } else {
          discount = quoteGeneratorData.discountValue;
          console.log(`   Discount (fixed): -₹${discount.toLocaleString()}`);
        }
        
        const afterDiscount = Math.max(0, subtotal - discount);
        console.log(`   After Discount:  ₹${afterDiscount.toLocaleString()}`);
        
        // Apply commission
        const commission = afterDiscount * (quoteGeneratorData.commission / 100);
        console.log(`   Commission (${quoteGeneratorData.commission}%): +₹${commission.toLocaleString()}`);
        
        const grandTotal = afterDiscount + commission;
        console.log(`   ========================================`);
        console.log(`   GRAND TOTAL:     ₹${grandTotal.toLocaleString()}`);
        console.log(`   ========================================\n`);
        
        // Enhanced notes with room capacity details
        const enhancedNotes = [
          ...roomReq.notes,
          `Total occupancy: ${roomReq.totalOccupancy} persons`,
          `Room configuration optimized for ${familyType.family_type}`,
          `Scaled costs for ${familyType.family_count} family members`,
          `Using ${effectiveRoomType} (capacity ${effectiveRoomCapacity}) for calculations`
        ];
        
        const result: FamilyTypePriceResult = {
          familyType,
          calculatedPrice: grandTotal,
          breakdown: {
            hotelCost,
            vehicleCost,
            additionalCosts,
            basicCosts,
            addOnCosts,
            optionalCosts,
            subtotal: Math.round(subtotal),
            discount: Math.round(discount),
            commission: Math.round(commission),
            grandTotal: Math.round(grandTotal),
            rooms: roomReq.roomsNeeded,
            extraAdults: roomReq.extraAdults,
            childrenCharged: roomReq.childrenCharged,
            infantsFree: roomReq.infantsFree,
            roomType: roomReq.roomType,
            notes: enhancedNotes
          }
        };

        results.push(result);
        console.log(`✅ ${familyType.family_type}: ₹${grandTotal.toLocaleString()}`);

      } catch (error) {
        console.error(`❌ Error calculating ${familyType.family_type}:`, error);
      }
    }

    console.log(`\n🎉 === GENERATION COMPLETE ===`);
    console.log(`✅ Generated prices for ${results.length}/${familyTypes.length} family types`);

    // Step 4: Save to Database (if quote ID is available)
    if (currentQuoteId && results.length > 0 && quoteMappingData) {
      const saveResult = await saveFamilyTypePricesToDatabase(currentQuoteId, results, quoteGeneratorData, quoteMappingData);
      if (saveResult.success) {
        console.log('✅ Family type prices saved to database');
      } else {
        console.warn('⚠️ Could not save to database:', saveResult.message);
      }
    }

    const message = useQuoteGeneratorFallback 
      ? `Successfully generated family type prices using Quote Generator data. For more accurate pricing, please configure Quote Mapping.`
      : `Successfully generated exact prices for ${results.length} family types using Quote Mapping data.`;

    return { 
      success: true, 
      results, 
      message
    };

  } catch (error) {
    console.error('❌ Error in family type price generation:', error);
    return { 
      success: false, 
      message: `Error generating family type prices: ${error instanceof Error ? error.message : 'Unknown error'}` 
    };
  }
};

// Calculate Room Requirements following hotel industry standards with proper room capacity logic
const calculateRoomRequirements = (familyType: FamilyTypeDB, hotelRow?: HotelRow) => {
  let adults = familyType.no_of_adults;
  let childrenFree = familyType.no_of_child;      // Below 5 yrs - Free
  let childrenCharged = familyType.no_of_children; // 6-12 yrs - Charged
  let infantsFree = familyType.no_of_infants;     // Below 2 yrs - Free

  const totalAdults = adults;
  const totalChildrenCharged = childrenCharged; // 6-11 years (charged but don't count as adults)
  const totalChildrenFree = childrenFree + infantsFree; // ≤5 years + infants (free, don't count)

  console.log(`Room Calculation: ${familyType.family_type}`);
  console.log(`  Adults: ${totalAdults}`);
  console.log(`  Children (6-11, charged): ${totalChildrenCharged}`);
  console.log(`  Children/Infants (≤5, free): ${totalChildrenFree}`);

  // Get room capacity from hotel data or use default - check multiple field names
  const roomCapacity = hotelRow?.room_capacity || 
                      (hotelRow as any)?.roomCapacity || 
                      (hotelRow as any)?.capacity || 
                      (hotelRow as any)?.room_size || 
                      (hotelRow as any)?.occupancy || 
                      2; // Default to standard room capacity
  const roomType = hotelRow?.roomType || "Standard Room";
  
  console.log(`  🔍 Room Capacity Detection: room_capacity=${hotelRow?.room_capacity}, roomCapacity=${(hotelRow as any)?.roomCapacity}, final=${roomCapacity}`);

  console.log(`  Hotel Room Type: ${roomType}, Base Capacity: ${roomCapacity}`);

  let maxAdultsPerRoom: number;
  let maxChildrenPerRoom: number;
  let roomCategory: string;

  // Determine room capacity rules based on room capacity or room type
  switch (roomCapacity) {
    case 2: // Standard/Deluxe Room
      maxAdultsPerRoom = 3; // 2 Adults + 1 Extra Adult
      maxChildrenPerRoom = 2; // Can accommodate 2 children (6-11)
      roomCategory = "Standard/Deluxe Room";
      break;
    
    case 3: // Triple Room
      maxAdultsPerRoom = 4; // 3 Adults + 1 Extra Adult
      maxChildrenPerRoom = 2; // Can accommodate 2 children (6-11)
      roomCategory = "Triple Room";
      break;
    
    case 4: // Family Room
      maxAdultsPerRoom = 5; // 4 Adults + 1 Extra Adult
      maxChildrenPerRoom = 4; // Can accommodate 4 children (6-11)
      roomCategory = "Family Room";
      break;
    
    case 6: // Six Occupancy Room
      maxAdultsPerRoom = 7; // 6 Adults + 1 Extra Adult
      maxChildrenPerRoom = 6; // Can accommodate 6 children (6-11)
      roomCategory = "Six Occupancy Room";
      break;
    
    default:
      // For any other capacity, calculate dynamically
      maxAdultsPerRoom = roomCapacity + 1; // Base capacity + 1 extra adult
      maxChildrenPerRoom = Math.min(roomCapacity, 6); // Max 6 children per room
      roomCategory = `${roomCapacity}-Capacity Room`;
      break;
  }

  console.log(`  Room Category: ${roomCategory}`);
  console.log(`  Max Adults per room: ${maxAdultsPerRoom}`);
  console.log(`  Max Children (6-11) per room: ${maxChildrenPerRoom}`);

  // Calculate rooms needed for adults
  const roomsNeededForAdults = Math.ceil(totalAdults / maxAdultsPerRoom);
  
  // Calculate rooms needed for children (6-11) - they need bed space but don't count as adults
  const roomsNeededForChildren = Math.ceil(totalChildrenCharged / maxChildrenPerRoom);
  
  // Total rooms needed is the maximum of adult rooms and children rooms
  const roomsNeeded = Math.max(roomsNeededForAdults, roomsNeededForChildren);

  console.log(`  Rooms needed for adults: ${roomsNeededForAdults}`);
  console.log(`  Rooms needed for children (6-11): ${roomsNeededForChildren}`);
  console.log(`  Total rooms needed: ${roomsNeeded}`);

  // Calculate extra adults properly - adults beyond the base capacity of allocated rooms
  // Base capacity = number of rooms × (base capacity per room, excluding extra adult slot)
  const baseCapacityPerRoom = maxAdultsPerRoom - 1; // Subtract 1 for the extra adult slot
  const baseAdultCapacity = roomsNeeded * baseCapacityPerRoom;
  let extraAdults = Math.max(0, totalAdults - baseAdultCapacity);
  extraAdults = Math.min(extraAdults, roomsNeeded); // Cap at number of rooms (1 extra per room max)

  console.log(`  Base capacity per room (without extra): ${baseCapacityPerRoom}`);
  console.log(`  Base adult capacity (${roomsNeeded} rooms): ${baseAdultCapacity}`);
  console.log(`  Extra adults: ${extraAdults}`);

  // Calculate total room capacity with children
  const totalRoomOccupancy = totalAdults + totalChildrenCharged;
  const totalRoomCapacity = roomsNeeded * (maxAdultsPerRoom + maxChildrenPerRoom);

  console.log(`  Total occupancy: ${totalRoomOccupancy}`);
  console.log(`  Total room capacity: ${totalRoomCapacity}`);

  // Determine final room type description
  let finalRoomType: string;
  if (roomsNeeded === 1) {
    if (extraAdults > 0) {
      finalRoomType = `${roomCategory} (with ${extraAdults} extra adult${extraAdults > 1 ? 's' : ''})`;
    } else {
      finalRoomType = roomCategory;
    }
  } else {
    finalRoomType = `${roomsNeeded} × ${roomCategory}`;
    if (extraAdults > 0) {
      finalRoomType += ` (with ${extraAdults} extra adult${extraAdults > 1 ? 's' : ''})`;
    }
  }

  const notes: string[] = [];
  notes.push(`${roomsNeeded} rooms (${finalRoomType})`);
  notes.push(`${totalAdults} adults total`);
  if (totalChildrenCharged > 0) {
    notes.push(`${totalChildrenCharged} children (6-11 yrs, charged)`);
  }
  if (totalChildrenFree > 0) {
    notes.push(`${totalChildrenFree} children/infants (≤5 yrs, free)`);
  }
  notes.push(`Room capacity: ${maxAdultsPerRoom} adults + ${maxChildrenPerRoom} children per room`);
  if (extraAdults > 0) {
    notes.push(`${extraAdults} extra adult charge${extraAdults > 1 ? 's' : ''}`);
  }
  
  // Add occupancy summary
  const occupancyPerRoom = Math.ceil(totalRoomOccupancy / roomsNeeded);
  notes.push(`Average occupancy: ${occupancyPerRoom} persons per room`);

  return {
    roomsNeeded,
    extraAdults,
    childrenCharged: totalChildrenCharged,
    infantsFree: totalChildrenFree,
    roomType: finalRoomType,
    notes,
    roomCapacity: maxAdultsPerRoom, // Max adults per room
    maxChildrenPerRoom,
    roomCategory,
    totalOccupancy: totalRoomOccupancy
  };
};

// Calculate Hotel Costs using Quote Mapping data and proper room logic
const calculateHotelCosts = (
  familyType: FamilyTypeDB,
  roomReq: any,
  quoteData: QuoteGeneratorData,
  mappingData: QuoteMappingData
) => {
  console.log(`🏨 Hotel Cost Calculation for ${familyType.family_type}:`);
  console.log(`   🏠 Room Requirement: ${roomReq.roomType}`);
  console.log(`   👥 Extra Adults: ${roomReq.extraAdults}, Children (6-12): ${roomReq.childrenCharged}, Free Children/Infants: ${roomReq.infantsFree}`);
  console.log(`   🏨 Room Category: ${roomReq.roomCategory}, Capacity: ${roomReq.roomCapacity} adults + ${roomReq.maxChildrenPerRoom} children`);
  
  // Debug Quote Mapping data
  console.log(`   🔍 Quote Mapping Debug:`);
  console.log(`      Hotel mappings count: ${mappingData.hotel_mappings?.length || 0}`);
  if (mappingData.hotel_mappings && mappingData.hotel_mappings.length > 0) {
    mappingData.hotel_mappings.forEach((hm, index) => {
      console.log(`      [${index}] ${hm.hotel_name}: Extra Adult ₹${hm.extra_adult_cost}, Children ₹${hm.children_cost}, Infant ₹${hm.infant_cost}`);
    });
  } else {
    console.log(`      ⚠️ No hotel mappings found in Quote Mapping data`);
  }

  // Check if we have hotel rows data
  if (!quoteData.hotelRows || quoteData.hotelRows.length === 0) {
    console.log(`   ⚠️ No hotel rows found, using fallback calculation`);

    // Fallback calculation when no hotel data is available
    const totalTransportCost = quoteData.costs.basicCosts.transportation + quoteData.costs.basicCosts.cabSightseeing;
    const estimatedHotelBudget = totalTransportCost * 1.6; // Hotel ~1.6x transportation
    const nights = 3; // Default nights
    
    // Base room cost per night based on room category
    let baseRoomCostPerNight: number;
    switch (roomReq.roomCategory) {
      case "Standard/Deluxe Room":
        baseRoomCostPerNight = Math.max(1500, estimatedHotelBudget / (roomReq.roomsNeeded * nights));
        break;
      case "Triple Room":
        baseRoomCostPerNight = Math.max(2000, estimatedHotelBudget / (roomReq.roomsNeeded * nights) * 1.2);
        break;
      case "Family Room":
        baseRoomCostPerNight = Math.max(3000, estimatedHotelBudget / (roomReq.roomsNeeded * nights) * 1.5);
        break;
      case "Six Occupancy Room":
        baseRoomCostPerNight = Math.max(4500, estimatedHotelBudget / (roomReq.roomsNeeded * nights) * 2.0);
        break;
      default:
        baseRoomCostPerNight = Math.max(1500, estimatedHotelBudget / (roomReq.roomsNeeded * nights));
        break;
    }

    console.log(`   💰 Estimated hotel rate: ₹${baseRoomCostPerNight}/night (${roomReq.roomCategory})`);

    const baseRoomCost = baseRoomCostPerNight * roomReq.roomsNeeded * nights;

    // Extra costs using Quote Mapping data or Quote Generator data as fallback
    const hotelMapping = mappingData.hotel_mappings?.[0];
    
    // Use Quote Generator hotel row data as fallback if no valid Quote Mapping
    const extraAdultCost = hotelMapping?.extra_adult_cost || 500;    // Standard fallback
    const childrenCost = hotelMapping?.children_cost || 300;         // Standard fallback  
    const infantCost = hotelMapping?.infant_cost || 0;               // Free for infants/children ≤5

    const extraAdultTotal = extraAdultCost * roomReq.extraAdults * nights;
    const childrenTotal = childrenCost * roomReq.childrenCharged * nights;
    const infantTotal = infantCost * roomReq.infantsFree * nights;

    const totalHotelCost = baseRoomCost + extraAdultTotal + childrenTotal + infantTotal;
    
    console.log(`   💰 Breakdown:`);
    console.log(`      Base rooms: ₹${baseRoomCost} (${roomReq.roomsNeeded} × ₹${baseRoomCostPerNight} × ${nights} nights)`);
    console.log(`      Extra adults: ₹${extraAdultTotal} (${roomReq.extraAdults} × ₹${extraAdultCost} × ${nights} nights)`);
    console.log(`      Children (6-11): ₹${childrenTotal} (${roomReq.childrenCharged} × ₹${childrenCost} × ${nights} nights)`);
    console.log(`      Children/Infants (≤5): ₹${infantTotal} (${roomReq.infantsFree} × ₹${infantCost} × ${nights} nights)`);
    console.log(`   ✅ Fallback Total Hotel Cost: ₹${totalHotelCost}`);

    return Math.round(totalHotelCost);
  }

  console.log(`   🏨 Processing ${quoteData.hotelRows.length} hotels from Quote Generator`);

  let totalHotelCost = 0;
  let totalNights = 0;

  // Process each hotel row from Quote Generator
  for (let i = 0; i < quoteData.hotelRows.length; i++) {
    const hotelRow = quoteData.hotelRows[i];
    const hotelNights = hotelRow.stayNights || 0;
    const hotelRooms = hotelRow.noOfRooms || 0;
    const hotelPrice = hotelRow.price || 0;

    // Get the actual room capacity using the same detection logic
    const actualRoomCapacity = hotelRow.room_capacity || 
                              (hotelRow as any).roomCapacity || 
                              (hotelRow as any).capacity || 
                              (hotelRow as any).room_size || 
                              (hotelRow as any).occupancy || 
                              2;

    console.log(`   🏨 Hotel ${i + 1}: ${hotelRow.hotelName || 'Unnamed'}`);
    console.log(`      💰 Rate: ₹${hotelPrice}/night, Quoted Rooms: ${hotelRooms}, Nights: ${hotelNights}`);
    console.log(`      🏠 Hotel Room Capacity: ${actualRoomCapacity} (detected from ${hotelRow.room_capacity ? 'room_capacity' : (hotelRow as any).roomCapacity ? 'roomCapacity' : 'fallback'}), Type: ${hotelRow.roomType || 'Standard'}`);
    console.log(`      💵 Hotel Row Extra Costs: Extra Adult ₹${hotelRow.extraAdultCost || 0}, Children ₹${hotelRow.childrenCost || 0}, Infant ₹${hotelRow.infantCost || 0}`);

    // Skip hotels with no rooms or nights
    if (hotelRooms === 0 || hotelNights === 0) {
      console.log(`      ⏭️ Skipping hotel with 0 rooms or nights`);
      continue;
    }

    // Use family's room requirement - this is the key change
    // We use the family's calculated room needs, not the hotel's quoted rooms
    const familyRoomsForThisHotel = roomReq.roomsNeeded;
    
    // Adjust base room price based on room category difference
    let adjustedRoomPrice = hotelPrice;
    // Use the same logic as in room detection to get the actual capacity
    const hotelRoomCapacity = hotelRow.room_capacity || 
                             (hotelRow as any).roomCapacity || 
                             (hotelRow as any).capacity || 
                             (hotelRow as any).room_size || 
                             (hotelRow as any).occupancy || 
                             2; // Default fallback
    const quotedRoomCategory = getRoomCategoryFromCapacity(hotelRoomCapacity);
    
    console.log(`      🔍 Hotel Capacity Detection: room_capacity=${hotelRow.room_capacity}, roomCapacity=${(hotelRow as any).roomCapacity}, final=${hotelRoomCapacity}`);
    
    console.log(`      🔄 Hotel room category: ${quotedRoomCategory}, Family needs: ${roomReq.roomCategory}`);
    
    // If family needs different room category, adjust price
    if (roomReq.roomCategory !== quotedRoomCategory) {
      const priceMultiplier = getRoomPriceMultiplier(roomReq.roomCategory, quotedRoomCategory);
      adjustedRoomPrice = hotelPrice * priceMultiplier;
      console.log(`      💰 Price adjusted: ₹${hotelPrice} → ₹${adjustedRoomPrice} (${priceMultiplier}x for ${roomReq.roomCategory})`);
    }

    const baseRoomCostForHotel = adjustedRoomPrice * familyRoomsForThisHotel * hotelNights;

    console.log(`      🏠 Base cost: ₹${adjustedRoomPrice} × ${familyRoomsForThisHotel} rooms × ${hotelNights} nights = ₹${baseRoomCostForHotel}`);

    // Find hotel-specific mapping or use Quote Generator hotel data as fallback  
    const hotelMapping = mappingData.hotel_mappings?.find((hm: HotelCostMapping) =>
      hm.hotel_name.toLowerCase().includes(hotelRow.hotelName?.toLowerCase() || '')
    ) || mappingData.hotel_mappings?.[0];

    console.log(`      🔍 Hotel Mapping Debug:`);
    if (hotelMapping) {
      console.log(`         Found Quote Mapping: Extra Adult ₹${hotelMapping.extra_adult_cost}, Children ₹${hotelMapping.children_cost}, Infant ₹${hotelMapping.infant_cost}`);
    } else {
      console.log(`         No Quote Mapping found, using Quote Generator hotel data`);
    }

    // Use Quote Mapping data if available, otherwise use Quote Generator hotel data, with proper fallbacks
    const quoteMappingExtraAdult = hotelMapping?.extra_adult_cost || 0;
    const quoteMappingChildren = hotelMapping?.children_cost || 0;
    const quoteMappingInfant = hotelMapping?.infant_cost || 0;
    
    const quoteGenExtraAdult = hotelRow.extraAdultCost || 0;
    const quoteGenChildren = hotelRow.childrenCost || 0;
    const quoteGenInfant = hotelRow.infantCost || 0;
    
    // Use standard fallback values if both Quote Mapping and Quote Generator have zero costs
    const effectiveHotelMapping = {
      extra_adult_cost: quoteMappingExtraAdult || quoteGenExtraAdult || 500,  // Standard fallback ₹500
      children_cost: quoteMappingChildren || quoteGenChildren || 300,         // Standard fallback ₹300
      infant_cost: quoteMappingInfant || quoteGenInfant || 0,                 // Infants remain free
      grandparent_discount: hotelMapping?.grandparent_discount || 0
    };
    
    console.log(`         🔍 Cost Source Analysis:`);
    console.log(`            Quote Mapping: Extra Adult ₹${quoteMappingExtraAdult}, Children ₹${quoteMappingChildren}, Infant ₹${quoteMappingInfant}`);
    console.log(`            Quote Generator: Extra Adult ₹${quoteGenExtraAdult}, Children ₹${quoteGenChildren}, Infant ₹${quoteGenInfant}`);
    console.log(`            Using Fallbacks: ${quoteMappingExtraAdult === 0 && quoteGenExtraAdult === 0 ? 'YES (₹500/₹300)' : 'NO'}`);

    console.log(`         Effective mapping: Extra Adult ₹${effectiveHotelMapping.extra_adult_cost}, Children ₹${effectiveHotelMapping.children_cost}, Infant ₹${effectiveHotelMapping.infant_cost}`);
    console.log(`      💰 Extra costs: Adults ₹${effectiveHotelMapping.extra_adult_cost}/night, Children ₹${effectiveHotelMapping.children_cost}/night`);

    // Calculate extra costs for this hotel using effective mapping
    const extraAdultCostForHotel = effectiveHotelMapping.extra_adult_cost * roomReq.extraAdults * hotelNights;
    const childrenCostForHotel = effectiveHotelMapping.children_cost * roomReq.childrenCharged * hotelNights;
    const infantCostForHotel = effectiveHotelMapping.infant_cost * roomReq.infantsFree * hotelNights;

    const hotelTotalCost = baseRoomCostForHotel + extraAdultCostForHotel + childrenCostForHotel + infantCostForHotel;

    console.log(`      💰 Hotel ${i + 1} Breakdown:`);
    console.log(`         Base rooms: ₹${baseRoomCostForHotel}`);
    console.log(`         Extra adults: ₹${extraAdultCostForHotel} (${roomReq.extraAdults} × ₹${effectiveHotelMapping.extra_adult_cost} × ${hotelNights})`);
    console.log(`         Children (6-11): ₹${childrenCostForHotel} (${roomReq.childrenCharged} × ₹${effectiveHotelMapping.children_cost} × ${hotelNights})`);
    console.log(`         Children/Infants (≤5): ₹${infantCostForHotel} (${roomReq.infantsFree} × ₹${effectiveHotelMapping.infant_cost} × ${hotelNights})`);
    console.log(`      ✅ Hotel ${i + 1} Total: ₹${hotelTotalCost}`);

    totalHotelCost += hotelTotalCost;
    totalNights += hotelNights;
  }

  console.log(`   🌙 Total nights across all hotels: ${totalNights}`);
  console.log(`   ✅ Grand Total Hotel Cost: ₹${totalHotelCost}`);

  return Math.round(totalHotelCost);
};

// Helper function to get room category from capacity
const getRoomCategoryFromCapacity = (capacity: number): string => {
  switch (capacity) {
    case 2: return "Standard/Deluxe Room";
    case 3: return "Triple Room";
    case 4: return "Family Room";
    case 6: return "Six Occupancy Room";
    default: return `${capacity}-Capacity Room`;
  }
};

// Helper function to get price multiplier when changing room categories
const getRoomPriceMultiplier = (neededCategory: string, quotedCategory: string): number => {
  const categoryValues: { [key: string]: number } = {
    "Standard/Deluxe Room": 1.0,
    "Triple Room": 1.2,
    "Family Room": 1.5,
    "Six Occupancy Room": 2.0
  };
  
  const neededValue = categoryValues[neededCategory] || 1.0;
  const quotedValue = categoryValues[quotedCategory] || 1.0;
  
  return neededValue / quotedValue;
};

// Calculate Vehicle Costs using Database cab_type and Quote Mapping data
const calculateVehicleCosts = (
  familyType: FamilyTypeDB,
  quoteData: QuoteGeneratorData,
  mappingData: QuoteMappingData
) => {
  const cabType = familyType.cab_type; // Use exact DB value
  const cabCapacity = familyType.cab_capacity;
  const familyCount = familyType.family_count;
  
  console.log(`🚗 Vehicle Calculation for ${familyType.family_type}:`);
  console.log(`   🚙 Required: ${cabType} (${cabCapacity} capacity)`);
  console.log(`   👥 Family Count: ${familyCount} people`);
  
  // Base transportation cost from Quote Generator
  const baseTransportationCost = quoteData.costs.basicCosts.transportation + 
                                 quoteData.costs.basicCosts.cabSightseeing;
  
  console.log(`   💰 Base Transportation Cost: ₹${baseTransportationCost}`);
  
  // Find matching vehicle from Quote Mapping using exact cab_type
  const vehicleMapping = mappingData.vehicle_mappings?.find(vm => {
    if (!vm.is_active) return false;
    
    // Match by vehicle type name
    const mappingType = vm.vehicle_type.toLowerCase();
    const familyCabType = cabType.toLowerCase();
    
    // Direct name matching
    if (mappingType.includes('sedan') && familyCabType.includes('sedan')) return true;
    if (mappingType.includes('innova') && familyCabType.includes('innova')) return true;
    if (mappingType.includes('crysta') && familyCabType.includes('innova')) return true;
    if (mappingType.includes('suv') && familyCabType.includes('suv')) return true;
    if (mappingType.includes('scorpio') && familyCabType.includes('suv')) return true;
    if (mappingType.includes('tempo') && familyCabType.includes('tempo')) return true;
    if (mappingType.includes('bus') && familyCabType.includes('bus')) return true;
    
    // Capacity matching as fallback
    return vm.max_capacity >= familyCount;
  });
  
  let vehicleCost = baseTransportationCost;
  let vehicleMultiplier = 1.0;
  
  if (vehicleMapping) {
    console.log(`   ✅ Found Quote Mapping: ${vehicleMapping.vehicle_type} (${vehicleMapping.max_capacity} capacity)`);
    
    if (vehicleMapping.pricing_type === 'actual_cost') {
      vehicleCost = vehicleMapping.base_cost;
      console.log(`   💰 Using Actual Cost: ₹${vehicleCost}`);
    } else {
      vehicleMultiplier = vehicleMapping.cost_multiplier;
      vehicleCost = baseTransportationCost * vehicleMultiplier;
      console.log(`   💰 Using Multiplier: ${vehicleMultiplier}x = ₹${vehicleCost}`);
    }
  } else {
    console.log(`   ⚠️ No Quote Mapping found, using fallback multipliers`);
    
    // Fallback multipliers based on database cab_type
    const cabTypeLower = cabType.toLowerCase();
    if (cabTypeLower.includes('sedan')) {
      vehicleMultiplier = 1.0; // Base rate
    } else if (cabTypeLower.includes('innova') || cabTypeLower.includes('crysta')) {
      vehicleMultiplier = 1.2; // 20% more than sedan
    } else if (cabTypeLower.includes('suv') || cabTypeLower.includes('scorpio')) {
      vehicleMultiplier = 1.25; // 25% more than sedan
    } else if (cabTypeLower.includes('tempo')) {
      vehicleMultiplier = 1.4; // 40% more than sedan
    } else if (cabTypeLower.includes('bus')) {
      vehicleMultiplier = 1.6; // 60% more than sedan
    }
    
    vehicleCost = baseTransportationCost * vehicleMultiplier;
    console.log(`   💰 Fallback Multiplier: ${vehicleMultiplier}x = ₹${vehicleCost}`);
  }
  
  // Validate vehicle capacity vs family count
  const capacityWarning = cabCapacity < familyCount;
  if (capacityWarning) {
    console.log(`   ⚠️ WARNING: Vehicle capacity (${cabCapacity}) < Family count (${familyCount})`);
    // Increase cost for capacity mismatch (may need larger vehicle or multiple vehicles)
    vehicleCost *= 1.2;
    console.log(`   💰 Adjusted for capacity mismatch: ₹${vehicleCost}`);
  }
  
  console.log(`   ✅ Final Vehicle Cost: ₹${Math.round(vehicleCost)}`);
  
  return Math.round(vehicleCost);
};

// Calculate Additional Costs using Quote Mapping data
const calculateAdditionalCosts = (
  familyType: FamilyTypeDB,
  quoteData: QuoteGeneratorData,
  mappingData: QuoteMappingData
) => {
  const familyCount = familyType.family_count;
  const baselinePeople = quoteData.noOfPersons + quoteData.extraAdults + 
                        quoteData.children + quoteData.infants;
  
  // Scale by people ratio
  const peopleRatio = familyCount / Math.max(baselinePeople, 1);
  
  // Use Quote Mapping additional costs if available
  let additionalCosts = 0;
  if (mappingData.additional_costs) {
    // Meal cost per person (all family members)
    const mealCosts = mappingData.additional_costs.meal_cost_per_person * familyCount;

    // Ferry cost: per-person cost * eligible persons (adults + children + child, excluding infants)
    const ferryEligiblePersons = familyType.no_of_adults + 
                                (familyType.no_of_children || 0) + 
                                (familyType.no_of_child || 0);
    const ferryCosts = (mappingData.additional_costs.ferry_cost || 0) * ferryEligiblePersons;

    // Activity cost per person (all family members)
    const activityCosts = mappingData.additional_costs.activity_cost_per_person * familyCount;

    // Guide cost per day (fixed cost, assume 3 days)
    const guideCosts = mappingData.additional_costs.guide_cost_per_day * 3;

    console.log(`   🍽️ Meal costs: ₹${mappingData.additional_costs.meal_cost_per_person} × ${familyCount} = ₹${mealCosts}`);
    console.log(`   ⛴️ Ferry costs: ₹${mappingData.additional_costs.ferry_cost} × ${ferryEligiblePersons} persons (excluding ${familyType.no_of_infants} infants) = ₹${ferryCosts}`);
    console.log(`   🎯 Activity costs: ₹${mappingData.additional_costs.activity_cost_per_person} × ${familyCount} = ₹${activityCosts}`);
    console.log(`   👨‍🏫 Guide costs: ₹${mappingData.additional_costs.guide_cost_per_day} × 3 days = ₹${guideCosts}`);

    additionalCosts = mealCosts + ferryCosts + activityCosts + guideCosts;
  } else {
    console.log(`   ⚠️ No Quote Mapping data, using scaled costs without ferry`);

    // Fallback: scale existing costs (excluding ferry)
    const scaledMeals = quoteData.costs.basicCosts.meals * peopleRatio;
    const scaledTrain = quoteData.costs.basicCosts.trainCost * peopleRatio;
    const scaledParking = quoteData.costs.basicCosts.parkingToll;
    additionalCosts = scaledMeals + scaledTrain + scaledParking;

    console.log(`   🍽️ Scaled meals: ₹${scaledMeals}`);
    console.log(`   🚂 Scaled train: ₹${scaledTrain}`);
    console.log(`   🅿️ Parking: ₹${scaledParking}`);
  }

  console.log(`   ✅ Total Additional Costs: ₹${additionalCosts}`);

  return Math.round(additionalCosts);
};

// Main calculation function with proper logic
// function calculatePriceForFamilyType(
//   familyType: FamilyTypeDB,
//   quoteData: QuoteGeneratorData, 
//   mappingData: QuoteMappingData
// ): any {
//   console.log(`\n💰 Calculating: ${familyType.family_type}`);
//   console.log(`👥 Family: ${familyType.no_of_adults}A + ${familyType.no_of_children}C + ${familyType.no_of_infants}I + ${familyType.no_of_child}K = ${familyType.family_count} people`);
//   
//   // DEBUG: Show exactly what hotel data we're receiving
//   console.log(`🔍 DEBUG - Quote Generator Hotel Data:`);
//   if (quoteData.hotelRows && quoteData.hotelRows.length > 0) {
//     quoteData.hotelRows.forEach((hotel, index) => {
//       console.log(`   Hotel ${index + 1}:`);
//       // Detect the actual room capacity for debug output
//       const debugRoomCapacity = hotel.room_capacity || 
//                                (hotel as any).roomCapacity || 
//                                (hotel as any).capacity || 
//                                (hotel as any).room_size || 
//                                (hotel as any).occupancy;
//       
//       console.log(`      Name: ${hotel.hotelName}`);
//       console.log(`      Room Type: ${hotel.roomType}`);
//       console.log(`      Price: ₹${hotel.price}`);
//       console.log(`      Room Capacity: ${debugRoomCapacity} (${typeof debugRoomCapacity}) - detected from ${hotel.room_capacity !== undefined ? 'room_capacity' : (hotel as any).roomCapacity !== undefined ? 'roomCapacity' : 'other field'}`);
//       console.log(`      Extra Adult Cost: ₹${hotel.extraAdultCost}`);
//       console.log(`      Children Cost: ₹${hotel.childrenCost}`);
//       console.log(`      All Hotel Fields:`, Object.keys(hotel));
//       console.log(`      🔍 DETAILED FIELD CHECK:`);
//       Object.keys(hotel).forEach(key => {
//         console.log(`         ${key}: ${(hotel as any)[key]} (${typeof (hotel as any)[key]})`);
//       });
//       console.log(`      Full Hotel Object:`, hotel);
//     });
//   } else {
//     console.log(`   ⚠️ No hotel data found in Quote Generator!`);
//   }
//   
//   // Determine the best room capacity to use based on family composition and available hotels
//   let effectiveRoomCapacity = 2; // Default to standard room
//   let effectiveRoomType = "Standard Room";
//   
//   if (quoteData.hotelRows && quoteData.hotelRows.length > 0) {
//     // Analyze all hotels to find the most suitable room capacity
//     const familyAdults = familyType.no_of_adults;
//     const familyChildren = familyType.no_of_children; // 6-11 years (charged)
//     const totalOccupancy = familyAdults + familyChildren; // Count only paying occupants for room capacity
//     
//     console.log(`🏨 Analyzing hotels for family: ${familyAdults} adults + ${familyChildren} children (6-11) = ${totalOccupancy} occupancy`);
//     
//     // Find the best room capacity that can accommodate the family
//     for (const hotel of quoteData.hotelRows) {
//       // Try different possible field names for room capacity
//       const hotelCapacity = hotel.room_capacity || 
//                            (hotel as any).roomCapacity || 
//                            (hotel as any).capacity || 
//                            (hotel as any).room_size || 
//                            (hotel as any).occupancy || 
//                            2; // Default fallback
//       
//       console.log(`   Hotel: ${hotel.hotelName}, Room: ${hotel.roomType}, Capacity: ${hotelCapacity}`);
//       console.log(`   🔍 Capacity Debug: room_capacity=${hotel.room_capacity}, roomCapacity=${(hotel as any).roomCapacity}, capacity=${(hotel as any).capacity}`);
//       
//       // Determine max occupancy for this room type based on your logic
//       let maxOccupancy = 0;
//       switch (hotelCapacity) {
//         case 2: // Standard/Deluxe Room: 3 Adults + 2 Children
//           maxOccupancy = 3 + 2; // 5 total
//           break;
//         case 3: // Triple Room: 4 Adults + 2 Children  
//           maxOccupancy = 4 + 2; // 6 total
//           break;
//         case 4: // Family Room: 5 Adults + 4 Children
//           maxOccupancy = 5 + 4; // 9 total
//           break;
//         case 6: // Six Occupancy Room: 7 Adults + 6 Children
//           maxOccupancy = 7 + 6; // 13 total
//           break;
//         default:
//           maxOccupancy = hotelCapacity + 1 + Math.min(hotelCapacity, 6); // Dynamic calculation
//           break;
//       }
//       
//       console.log(`   Max occupancy for this room: ${maxOccupancy}`);
//       
//       // If this room can accommodate the family better, use it
//       if (totalOccupancy <= maxOccupancy && hotelCapacity > effectiveRoomCapacity) {
//         effectiveRoomCapacity = hotelCapacity;
//         effectiveRoomType = hotel.roomType || "Standard Room";
//         console.log(`   ✅ Better room found: ${effectiveRoomType} (capacity ${effectiveRoomCapacity})`);
//       }
//     }
//     
//     // If no room can accommodate the family in one room, use the largest available
//     if (effectiveRoomCapacity === 2 && totalOccupancy > 5) {
//       const largestRoom = quoteData.hotelRows.reduce((largest, hotel) => {
//         const currentCapacity = hotel.room_capacity || 
//                               (hotel as any).roomCapacity || 
//                               (hotel as any).capacity || 
//                               (hotel as any).room_size || 
//                               (hotel as any).occupancy || 
//                               2;
//         const largestCapacity = largest.room_capacity || 
//                               (largest as any).roomCapacity || 
//                               (largest as any).capacity || 
//                               (largest as any).room_size || 
//                               (largest as any).occupancy || 
//                               2;
//         return currentCapacity > largestCapacity ? hotel : largest;
//       });
//       effectiveRoomCapacity = largestRoom.room_capacity || 
//                             (largestRoom as any).roomCapacity || 
//                             (largestRoom as any).capacity || 
//                             (largestRoom as any).room_size || 
//                             (largestRoom as any).occupancy || 
//                             2;
//       effectiveRoomType = largestRoom.roomType || "Standard Room";
//       console.log(`   📏 Using largest available room: ${effectiveRoomType} (capacity ${effectiveRoomCapacity})`);
//     }
//   }
//
//   // Create effective hotel row for room calculation
//   const effectiveHotelRow = quoteData.hotelRows && quoteData.hotelRows.length > 0 
//     ? {
//         ...quoteData.hotelRows[0], // Use first hotel as base
//         roomType: effectiveRoomType,
//         room_capacity: effectiveRoomCapacity
//       }
//     : {
//         hotelName: "Default Hotel",
//         roomType: effectiveRoomType,
//         price: 2000,
//         mealPlan: "MAP",
//         noOfRooms: 1,
//         stayNights: 3,
//         extraAdultCost: 500,
//         childrenCost: 300,
//         infantCost: 0,
//         gstType: "12%",
//         tacPercentage: 5,
//         room_capacity: effectiveRoomCapacity
//       };
//
//   console.log(`🏨 Using effective room: ${effectiveHotelRow.roomType}, Capacity: ${effectiveHotelRow.room_capacity}`);
//   
//   // Calculate room requirements using the determined room capacity
//   const roomReq = calculateRoomRequirements(familyType, effectiveHotelRow);
//   console.log(`🏨 Room calculation result: ${roomReq.roomsNeeded} ${roomReq.roomCategory}, Extra Adults: ${roomReq.extraAdults}`);
//   
//   // Calculate costs
//   const hotelCost = calculateHotelCosts(familyType, roomReq, quoteData, mappingData);
//   const vehicleCost = calculateVehicleCosts(familyType, quoteData, mappingData);
//   const additionalCosts = calculateAdditionalCosts(familyType, quoteData, mappingData);
//   
//   console.log(`💰 Hotel: ₹${hotelCost}, Vehicle: ₹${vehicleCost}, Additional: ₹${additionalCosts}`);
//   
//   // Scale add-on and optional costs based on family size
//   const baselinePeople = quoteData.noOfPersons + quoteData.extraAdults + 
//                         quoteData.children + quoteData.infants;
//   const peopleRatio = familyType.family_count / Math.max(baselinePeople, 1);
//   
//   console.log(`👥 People scaling: ${familyType.family_count}/${baselinePeople} = ${peopleRatio.toFixed(2)}x`);
//   
//   const basicCosts = Math.round(Object.values(quoteData.costs.basicCosts).reduce((sum, cost) => sum + cost, 0) * peopleRatio);
//   const addOnCosts = Math.round(Object.values(quoteData.costs.addOnCosts).reduce((sum, cost) => sum + cost, 0) * peopleRatio);
//   const optionalCosts = Math.round(Object.values(quoteData.costs.optionalCosts).reduce((sum, cost) => sum + cost, 0) * peopleRatio);
//   
//   console.log(`💰 Scaled costs - Basic: ₹${basicCosts}, AddOn: ₹${addOnCosts}, Optional: ₹${optionalCosts}`);
//   
//   // Calculate subtotal
//   const subtotal = hotelCost + vehicleCost + additionalCosts + basicCosts + addOnCosts + optionalCosts;
//   
//   // Apply discount
//   let discount = 0;
//   if (quoteData.discountType === 'percentage') {
//     discount = subtotal * (quoteData.discountValue / 100);
//   } else {
//     discount = quoteData.discountValue;
//   }
//   
//   const afterDiscount = Math.max(0, subtotal - discount);
//   
//   // Apply commission
//   const commission = afterDiscount * (quoteData.commission / 100);
//   const grandTotal = afterDiscount + commission;
//   
//   console.log(`💵 Subtotal: ₹${subtotal}, Discount: ₹${discount}, Commission: ₹${commission}, Total: ₹${grandTotal}`);
//   
//   // Enhanced notes with room capacity details
//   const enhancedNotes = [
//     ...roomReq.notes,
//     `Total occupancy: ${roomReq.totalOccupancy} persons`,
//     `Room configuration optimized for ${familyType.family_type}`,
//     `Scaled costs for ${familyType.family_count} family members`,
//     `Using ${effectiveRoomType} (capacity ${effectiveRoomCapacity}) for calculations`
//   ];
//   
//   return {
//     hotelCost,
//     vehicleCost,
//     additionalCosts,
//     basicCosts,
//     addOnCosts,
//     optionalCosts,
//     subtotal: Math.round(subtotal),
//     discount: Math.round(discount),
//     commission: Math.round(commission),
//     grandTotal: Math.round(grandTotal),
//     rooms: roomReq.roomsNeeded,
//     extraAdults: roomReq.extraAdults,
//     childrenCharged: roomReq.childrenCharged,
//     infantsFree: roomReq.infantsFree,
//     roomType: roomReq.roomType,
//     notes: enhancedNotes
//   };
// }

export const formatPrice = (price: number) => {
  return new Intl.NumberFormat('en-IN', {
    style: 'currency',
    currency: 'INR',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(price);
}; 