# TripXplo CRM Deployment Fix Guide

## Issues Identified

### 1. **500 Internal Server Error**
- **Cause**: Files are located in `/var/www/crm/TripXplo-CRM/` instead of `/var/www/crm/`
- **Effect**: <PERSON><PERSON><PERSON> can't find the `index.html` file in the expected location

### 2. **GitHub Actions Deployment Failure**
- **Error**: `:/tmp/: No such file or directory`
- **Cause**: Path issues in the deployment script and insufficient error handling

### 3. **File Structure Mismatch**
- Expected: `/var/www/crm/index.html`
- Actual: `/var/www/crm/TripXplo-CRM/index.html`

## Immediate Fix (For Current 500 Error)

Run this command on your server to fix the immediate issue:

```bash
# SSH into your server
ssh root@*************

# Upload and run the fix script
cd /tmp
curl -s https://raw.githubusercontent.com/your-repo/TripXplo-CRM/master/fix-current-deployment.sh -o fix-current-deployment.sh
chmod +x fix-current-deployment.sh
./fix-current-deployment.sh
```

## Complete Fix Steps

### Step 1: Fix Current Server Deployment

```bash
# SSH into server
ssh root@*************

# Run the fix script manually
cd /var/www/crm
ls -la  # Check current structure

# If TripXplo-CRM directory exists, move files
if [ -d "TripXplo-CRM" ]; then
  # Create backup
  cp -r /var/www/crm /var/www/crm.backup.$(date +%Y%m%d_%H%M%S)
  
  # Move files to correct location
  cp -r TripXplo-CRM/* .
  rm -rf TripXplo-CRM
  
  # Set permissions
  chown -R www-data:www-data /var/www/crm
  chmod -R 755 /var/www/crm
  
  # Reload nginx
  systemctl reload nginx
fi
```

### Step 2: Test the Fix

```bash
# Check if index.html is in the right place
ls -la /var/www/crm/index.html

# Test the site
curl -I http://crm.tripxplo.com
curl -I https://crm.tripxplo.com
```

### Step 3: Fix GitHub Actions (Already Done)

The deployment script `scripts/deploy-server.sh` has been updated with:
- Better error handling
- Path validation
- Improved extraction process
- Enhanced logging

### Step 4: Rebuild and Deploy

From your local machine:

```bash
# Build the project
npm run build

# Test auto-deployment
git add .
git commit -m "Fix deployment issues"
git push origin master
```

## Verification Checklist

- [ ] Site loads at https://crm.tripxplo.com without 500 error
- [ ] Static assets (CSS, JS) load properly
- [ ] React Router navigation works
- [ ] Authentication with Supabase works
- [ ] GitHub Actions deployment succeeds

## Files Modified

1. `scripts/deploy-server.sh` - Enhanced deployment script
2. `fix-current-deployment.sh` - Immediate fix script
3. `DEPLOYMENT_FIX_GUIDE.md` - This guide

## Common Issues and Solutions

### Issue: Still getting 500 error after fix
```bash
# Check nginx error logs
tail -f /var/log/nginx/crm_error.log

# Check file permissions
ls -la /var/www/crm/
```

### Issue: Static assets not loading
```bash
# Check if assets directory exists
ls -la /var/www/crm/assets/

# Check nginx configuration
nginx -t
```

### Issue: GitHub Actions still failing
```bash
# Check if secrets are properly set:
# - SERVER_IP
# - SSH_PRIVATE_KEY
# - All VITE_SUPABASE_* variables
```

## Server Configuration Details

### Nginx Configuration Location
- Config file: `/etc/nginx/sites-available/crm.tripxplo.com`
- Enabled: `/etc/nginx/sites-enabled/crm.tripxplo.com`

### SSL Certificate
- Location: `/etc/letsencrypt/live/crm.tripxplo.com/`
- Auto-renewal via certbot

### Log Locations
- Error log: `/var/log/nginx/crm_error.log`
- Access log: `/var/log/nginx/crm_access.log`

## Emergency Rollback

If anything goes wrong, restore from backup:

```bash
# Find latest backup
ls -la /var/www/crm.backup.*

# Restore (replace timestamp with actual backup)
rm -rf /var/www/crm/*
cp -r /var/www/crm.backup.YYYYMMDD_HHMMSS/* /var/www/crm/
chown -R www-data:www-data /var/www/crm
systemctl reload nginx
```

## Contact Information

For deployment issues, check:
1. GitHub Actions logs in repository
2. Server nginx logs: `tail -f /var/log/nginx/crm_error.log`
3. Server systemctl status: `systemctl status nginx`

---

**Status**: ✅ Ready to deploy
**Last Updated**: $(date)
**Next Steps**: Run the immediate fix, then test auto-deployment 