#!/bin/bash
set -e

echo "🔧 Fixing current CRM deployment..."

# Check if the nested directory exists
if [ -d "/var/www/crm/TripXplo-CRM" ]; then
    echo "📁 Found nested TripXplo-CRM directory, moving files..."
    
    # Create backup
    BACKUP_DIR="/var/www/crm.backup.$(date +%Y%m%d_%H%M%S)"
    cp -r /var/www/crm $BACKUP_DIR
    echo "💾 Backup created: $BACKUP_DIR"
    
    # Move files from nested directory to root
    cp -r /var/www/crm/TripXplo-CRM/* /var/www/crm/
    
    # Remove the nested directory
    rm -rf /var/www/crm/TripXplo-CRM
    
    echo "✅ Files moved to correct location"
else
    echo "❌ Nested TripXplo-CRM directory not found"
    echo "Current structure:"
    ls -la /var/www/crm/
    exit 1
fi

# Set proper permissions
echo "🔐 Setting file permissions..."
chown -R www-data:www-data /var/www/crm
chmod -R 755 /var/www/crm
find /var/www/crm -name "*.html" -exec chmod 644 {} \;
find /var/www/crm -name "*.css" -exec chmod 644 {} \;
find /var/www/crm -name "*.js" -exec chmod 644 {} \;

# Check if index.html exists in the root
if [ -f "/var/www/crm/index.html" ]; then
    echo "✅ index.html found in correct location"
else
    echo "❌ index.html still not found in /var/www/crm/"
    echo "Contents of /var/www/crm/:"
    ls -la /var/www/crm/
    exit 1
fi

# Create/update Nginx configuration
echo "🌐 Updating Nginx configuration..."
cat > /etc/nginx/sites-available/crm.tripxplo.com << 'NGINXCONFIG'
server {
    listen 80;
    server_name crm.tripxplo.com www.crm.tripxplo.com;
    root /var/www/crm;
    index index.html;
    
    # Enable error logging
    error_log /var/log/nginx/crm_error.log;
    access_log /var/log/nginx/crm_access.log;
    
    # Handle React Router (SPA routing)
    location / {
        try_files $uri $uri/ /index.html;
        add_header Cache-Control "no-cache, no-store, must-revalidate" always;
    }
    
    # Static assets with proper caching
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot|map)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header Access-Control-Allow-Origin "*";
    }
    
    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    
    # CORS headers for Supabase
    add_header Access-Control-Allow-Origin "*" always;
    add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS" always;
    add_header Access-Control-Allow-Headers "DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization" always;
    
    # Handle preflight OPTIONS requests
    if ($request_method = 'OPTIONS') {
        add_header Access-Control-Allow-Origin "*";
        add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS";
        add_header Access-Control-Allow-Headers "DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization";
        add_header Access-Control-Max-Age 1728000;
        add_header Content-Type "text/plain charset=UTF-8";
        add_header Content-Length 0;
        return 204;
    }
    
    # Block access to sensitive files
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }
}
NGINXCONFIG

# Enable the site
ln -sf /etc/nginx/sites-available/crm.tripxplo.com /etc/nginx/sites-enabled/

# Test and reload Nginx
echo "🧪 Testing Nginx configuration..."
if nginx -t; then
    echo "✅ Nginx configuration is valid"
    systemctl reload nginx
    echo "✅ Nginx reloaded successfully"
else
    echo "❌ Nginx configuration test failed"
    nginx -t 2>&1
    exit 1
fi

echo ""
echo "✅ Deployment fix completed!"
echo "🌐 Test the site at: https://crm.tripxplo.com"
echo ""
echo "📊 Final structure:"
ls -la /var/www/crm/
echo ""
echo "🎉 CRM should now be working!" 