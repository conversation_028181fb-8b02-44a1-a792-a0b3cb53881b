-- Database Modifications for Prepaid EMI Plans
-- Add to existing family_type_prices and family_type_emi_plans tables

-- =============================================================================
-- MODIFY EXISTING TABLES FOR PREPAID EMI SUPPORT
-- =============================================================================

-- Add prepaid EMI fields to family_type_prices table
ALTER TABLE family_type_prices ADD COLUMN IF NOT EXISTS prepaid_emi_enabled BOOLEAN DEFAULT false;
ALTER TABLE family_type_prices ADD COLUMN IF NOT EXISTS prepaid_discount_percent DECIMAL(5,2) DEFAULT 0;
ALTER TABLE family_type_prices ADD COLUMN IF NOT EXISTS prepaid_processing_fee_percent DECIMAL(5,2) DEFAULT 1.0;

-- Add prepaid EMI fields to family_type_emi_plans table  
ALTER TABLE family_type_emi_plans ADD COLUMN IF NOT EXISTS is_prepaid_plan BOOLEAN DEFAULT false;
ALTER TABLE family_type_emi_plans ADD COLUMN IF NOT EXISTS prepaid_discount_amount DECIMAL(10,2) DEFAULT 0;
ALTER TABLE family_type_emi_plans ADD COLUMN IF NOT EXISTS prepaid_total_amount DECIMAL(10,2);
ALTER TABLE family_type_emi_plans ADD COLUMN IF NOT EXISTS advance_payment_required DECIMAL(10,2) DEFAULT 0;
ALTER TABLE family_type_emi_plans ADD COLUMN IF NOT EXISTS advance_payment_percent DECIMAL(5,2) DEFAULT 20.0;

-- =============================================================================
-- NEW TABLE: PREPAID EMI TRANSACTIONS
-- =============================================================================
CREATE TABLE IF NOT EXISTS prepaid_emi_transactions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    emi_plan_id UUID NOT NULL,
    customer_id UUID,
    booking_reference VARCHAR(100) NOT NULL,
    
    -- Payment Details
    advance_payment_amount DECIMAL(10,2) NOT NULL,
    advance_payment_status VARCHAR(50) DEFAULT 'pending', -- pending, completed, failed
    advance_payment_date TIMESTAMP WITH TIME ZONE,
    
    -- EMI Schedule
    total_emi_amount DECIMAL(10,2) NOT NULL,
    monthly_emi_amount DECIMAL(10,2) NOT NULL,
    remaining_emi_months INTEGER NOT NULL,
    next_emi_due_date DATE,
    
    -- Payment Tracking
    total_paid_amount DECIMAL(10,2) DEFAULT 0,
    pending_amount DECIMAL(10,2) NOT NULL,
    
    -- Status Management
    payment_status VARCHAR(50) DEFAULT 'active', -- active, completed, defaulted, cancelled
    auto_debit_enabled BOOLEAN DEFAULT false,
    payment_method VARCHAR(100), -- UPI, Bank Transfer, Card, etc.
    
    -- Notifications
    reminder_sent_count INTEGER DEFAULT 0,
    last_reminder_sent TIMESTAMP WITH TIME ZONE,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT fk_emi_plan_prepaid FOREIGN KEY (emi_plan_id) REFERENCES family_type_emi_plans(id) ON DELETE CASCADE,
    CONSTRAINT valid_payment_amounts CHECK (advance_payment_amount > 0 AND total_emi_amount > 0),
    CONSTRAINT valid_status CHECK (payment_status IN ('active', 'completed', 'defaulted', 'cancelled')),
    CONSTRAINT valid_advance_status CHECK (advance_payment_status IN ('pending', 'completed', 'failed'))
);

-- =============================================================================
-- NEW TABLE: EMI PAYMENT HISTORY
-- =============================================================================
CREATE TABLE IF NOT EXISTS emi_payment_history (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    transaction_id UUID NOT NULL,
    
    -- Payment Details
    payment_amount DECIMAL(10,2) NOT NULL,
    payment_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    payment_method VARCHAR(100),
    payment_reference VARCHAR(255),
    
    -- EMI Details
    emi_month_number INTEGER NOT NULL,
    payment_type VARCHAR(50) NOT NULL, -- advance, emi, penalty, refund
    
    -- Status
    payment_status VARCHAR(50) DEFAULT 'completed', -- completed, failed, refunded
    gateway_reference VARCHAR(255),
    
    -- Additional Info
    late_fee_amount DECIMAL(10,2) DEFAULT 0,
    discount_applied DECIMAL(10,2) DEFAULT 0,
    notes TEXT,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT fk_transaction_payment FOREIGN KEY (transaction_id) REFERENCES prepaid_emi_transactions(id) ON DELETE CASCADE,
    CONSTRAINT valid_payment_type CHECK (payment_type IN ('advance', 'emi', 'penalty', 'refund')),
    CONSTRAINT valid_payment_status CHECK (payment_status IN ('completed', 'failed', 'refunded'))
);

-- =============================================================================
-- UPDATED EMI CALCULATION FUNCTION FOR PREPAID PLANS
-- =============================================================================
CREATE OR REPLACE FUNCTION calculate_prepaid_emi_amount(
    principal DECIMAL(10,2),
    annual_rate DECIMAL(5,2),
    months INTEGER,
    advance_payment_percent DECIMAL(5,2) DEFAULT 20.0,
    processing_fee_percent DECIMAL(5,2) DEFAULT 1.0,
    discount_percent DECIMAL(5,2) DEFAULT 0
)
RETURNS TABLE (
    advance_payment DECIMAL(10,2),
    emi_principal DECIMAL(10,2),
    monthly_amount DECIMAL(10,2),
    total_amount DECIMAL(10,2),
    total_interest DECIMAL(10,2),
    processing_fee DECIMAL(10,2),
    discount_amount DECIMAL(10,2),
    final_amount DECIMAL(10,2)
) AS $$
DECLARE
    monthly_rate DECIMAL(10,6);
    advance_amount DECIMAL(10,2);
    remaining_principal DECIMAL(10,2);
    emi_amount DECIMAL(10,2);
    total_payment DECIMAL(10,2);
    interest_amount DECIMAL(10,2);
    fee_amount DECIMAL(10,2);
    discount_amt DECIMAL(10,2);
    final_amt DECIMAL(10,2);
BEGIN
    -- Calculate advance payment
    advance_amount := principal * advance_payment_percent / 100;
    
    -- Calculate discount
    discount_amt := principal * discount_percent / 100;
    
    -- Calculate processing fee
    fee_amount := principal * processing_fee_percent / 100;
    
    -- Remaining principal after advance payment
    remaining_principal := principal - advance_amount - discount_amt;
    
    -- Calculate monthly interest rate
    monthly_rate := annual_rate / (12 * 100);
    
    -- Calculate EMI using standard formula
    IF monthly_rate = 0 THEN
        emi_amount := remaining_principal / months;
    ELSE
        emi_amount := remaining_principal * monthly_rate * POWER(1 + monthly_rate, months) / (POWER(1 + monthly_rate, months) - 1);
    END IF;
    
    -- Round to 2 decimal places
    emi_amount := ROUND(emi_amount, 2);
    
    -- Calculate totals
    total_payment := advance_amount + (emi_amount * months) + fee_amount;
    interest_amount := total_payment - principal + discount_amt - fee_amount;
    final_amt := total_payment - discount_amt;
    
    RETURN QUERY SELECT advance_amount, remaining_principal, emi_amount, total_payment, interest_amount, fee_amount, discount_amt, final_amt;
END;
$$ language plpgsql;

-- =============================================================================
-- INDEXES FOR PERFORMANCE
-- =============================================================================
CREATE INDEX IF NOT EXISTS idx_prepaid_emi_transactions_booking_ref ON prepaid_emi_transactions(booking_reference);
CREATE INDEX IF NOT EXISTS idx_prepaid_emi_transactions_status ON prepaid_emi_transactions(payment_status);
CREATE INDEX IF NOT EXISTS idx_prepaid_emi_transactions_due_date ON prepaid_emi_transactions(next_emi_due_date);
CREATE INDEX IF NOT EXISTS idx_emi_payment_history_transaction ON emi_payment_history(transaction_id);
CREATE INDEX IF NOT EXISTS idx_emi_payment_history_date ON emi_payment_history(payment_date);

-- =============================================================================
-- TRIGGERS FOR AUTOMATIC UPDATES
-- =============================================================================
CREATE TRIGGER update_prepaid_emi_transactions_updated_at
    BEFORE UPDATE ON prepaid_emi_transactions
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- =============================================================================
-- SAMPLE DATA FOR PREPAID EMI PLANS
-- =============================================================================
-- Function to generate prepaid EMI plans
CREATE OR REPLACE FUNCTION generate_prepaid_emi_plans(family_price_id UUID)
RETURNS VOID AS $$
DECLARE
    price_record RECORD;
    prepaid_months_array INTEGER[] := ARRAY[3, 6, 9, 12, 18, 24];
    months INTEGER;
    prepaid_calc RECORD;
BEGIN
    -- Get the family price record
    SELECT * INTO price_record FROM family_type_prices WHERE id = family_price_id AND prepaid_emi_enabled = true;
    
    IF NOT FOUND THEN
        RAISE EXCEPTION 'Family price record not found or prepaid EMI not enabled for ID: %', family_price_id;
    END IF;
    
    -- Generate prepaid EMI plans for different months
    FOREACH months IN ARRAY prepaid_months_array
    LOOP
        IF months <= price_record.max_emi_months THEN
            -- Calculate prepaid EMI
            SELECT * INTO prepaid_calc FROM calculate_prepaid_emi_amount(
                price_record.total_price,
                price_record.emi_interest_rate_percent,
                months,
                price_record.advance_payment_percent,
                price_record.prepaid_processing_fee_percent,
                price_record.prepaid_discount_percent
            );
            
            -- Insert prepaid EMI plan
            INSERT INTO family_type_emi_plans (
                family_price_id, emi_months, monthly_amount, total_amount,
                processing_fee, total_interest, first_payment_amount,
                subsequent_payment_amount, final_payment_amount,
                effective_annual_rate, is_featured, marketing_label,
                is_prepaid_plan, prepaid_discount_amount, prepaid_total_amount,
                advance_payment_required, advance_payment_percent
            ) VALUES (
                family_price_id, months, prepaid_calc.monthly_amount, prepaid_calc.total_amount,
                prepaid_calc.processing_fee, prepaid_calc.total_interest, prepaid_calc.advance_payment,
                prepaid_calc.monthly_amount, prepaid_calc.monthly_amount,
                price_record.emi_interest_rate_percent, 
                CASE WHEN months = 12 THEN true ELSE false END,
                CASE 
                    WHEN months = 3 THEN 'Quick Prepaid'
                    WHEN months = 6 THEN 'Popular Prepaid'
                    WHEN months = 12 THEN 'Best Value Prepaid'
                    WHEN months = 24 THEN 'Low EMI Prepaid'
                    ELSE 'Prepaid Plan'
                END,
                true, prepaid_calc.discount_amount, prepaid_calc.final_amount,
                prepaid_calc.advance_payment, price_record.advance_payment_percent
            );
        END IF;
    END LOOP;
END;
$$ language plpgsql;

-- =============================================================================
-- COMMENTS FOR DOCUMENTATION
-- =============================================================================
COMMENT ON TABLE prepaid_emi_transactions IS 'Manages prepaid EMI payment transactions and schedules';
COMMENT ON TABLE emi_payment_history IS 'Tracks all EMI payments including advance payments and monthly EMIs';

COMMENT ON COLUMN family_type_prices.prepaid_emi_enabled IS 'Enable prepaid EMI plans for this package';
COMMENT ON COLUMN family_type_prices.prepaid_discount_percent IS 'Discount percentage for prepaid EMI plans';
COMMENT ON COLUMN family_type_emi_plans.is_prepaid_plan IS 'Indicates if this is a prepaid EMI plan';
COMMENT ON COLUMN prepaid_emi_transactions.advance_payment_status IS 'Status of advance payment: pending, completed, failed';
COMMENT ON COLUMN prepaid_emi_transactions.payment_status IS 'Overall payment status: active, completed, defaulted, cancelled'; 