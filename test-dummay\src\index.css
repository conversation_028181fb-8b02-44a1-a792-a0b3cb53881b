@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom styles for family booking aesthetic */
@layer base {
  body {
    font-family: 'Inter', sans-serif;
    background: linear-gradient(135deg, #f0f9ff 0%, #ffffff 50%, #faf5ff 100%);
    min-height: 100vh;
  }

  html {
    scroll-behavior: smooth;
  }
}

@layer components {
  /* Primary button style matching family booking */
  .btn-primary {
    @apply bg-gradient-to-r from-blue-600 to-purple-600 text-white px-6 py-3 rounded-lg font-medium hover:from-blue-700 hover:to-purple-700 transition-all duration-200 flex items-center justify-center gap-2;
  }

  /* Secondary button style */
  .btn-secondary {
    @apply bg-white text-gray-700 border border-gray-300 px-6 py-3 rounded-lg font-medium hover:bg-gray-50 transition-all duration-200;
  }

  /* Card style matching family booking */
  .card {
    @apply bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden hover:shadow-xl transition-shadow duration-300;
  }

  /* Input style matching family booking */
  .input-field {
    @apply w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200;
  }

  /* Logo style */
  .logo {
    @apply font-bold text-2xl;
  }

  .logo .trip {
    @apply text-blue-600;
  }

  .logo .xplo {
    @apply text-purple-600;
  }

  /* Gradient background for sections */
  .gradient-bg {
    @apply bg-gradient-to-br from-blue-50 via-white to-purple-50;
  }

  /* Loading spinner */
  .spinner {
    @apply animate-spin rounded-full border-4 border-gray-200 border-t-blue-600;
  }

  /* Modal overlay */
  .modal-overlay {
    @apply fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50;
  }

  /* Modal content */
  .modal-content {
    @apply bg-white rounded-2xl max-w-md w-full p-6 max-h-[90vh] overflow-y-auto;
  }

  /* Feature card */
  .feature-card {
    @apply text-center p-6 bg-white rounded-xl shadow-sm hover:shadow-md transition-shadow duration-300;
  }

  /* Package card */
  .package-card {
    @apply bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300;
  }

  /* EMI highlight */
  .emi-highlight {
    @apply bg-blue-50 rounded-lg p-4 border border-blue-100;
  }

  /* Status badges */
  .badge-success {
    @apply bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium;
  }

  .badge-warning {
    @apply bg-yellow-100 text-yellow-800 px-3 py-1 rounded-full text-sm font-medium;
  }

  .badge-error {
    @apply bg-red-100 text-red-800 px-3 py-1 rounded-full text-sm font-medium;
  }

  .badge-info {
    @apply bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium;
  }
}

@layer utilities {
  /* Custom animations */
  .animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }

  .animate-slide-up {
    animation: slideUp 0.3s ease-out;
  }

  .animate-bounce-in {
    animation: bounceIn 0.6s ease-out;
  }

  /* Hover effects */
  .hover-lift {
    @apply transition-transform duration-200 hover:-translate-y-1;
  }

  .hover-glow {
    @apply transition-shadow duration-200 hover:shadow-lg;
  }

  /* Text gradients */
  .text-gradient {
    @apply bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent;
  }

  /* Custom scrollbar */
  .custom-scrollbar::-webkit-scrollbar {
    width: 6px;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 3px;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 3px;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
  }
}

/* Keyframe animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}
